package com.jd.health.medical.examination.export.enterprise.enums;

/**
 * 描述
 * 解读报告的服务提供方
 * <AUTHOR>
 * @date 2022/8/9 13:53
 */
public enum ReportDoctorTypeEnum {
    
    /** 互医 */
    NET_DOCTOR(1,"互医"),
    /** 家医 */
    HOME_DOCTOR(2,"家医"),
    
    ;
    
    /**
     * ReportDoctorTypeEnum
     * @param type
     * @param desc
     */
    ReportDoctorTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    
    /**
     * code
     */
    private Integer type;
    /**
     * desc
     */
    private String desc;
    
    /**
     * getType
     * @return
     */
    public Integer getType() {
        return type;
    }
    
    /**
     * setType
     * @param type
     */
    public void setType(Integer type) {
        this.type = type;
    }
    
    /**
     * getDesc
     * @return
     */
    public String getDesc() {
        return desc;
    }
    
    /**
     * setDesc
     * @param desc
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }
}
