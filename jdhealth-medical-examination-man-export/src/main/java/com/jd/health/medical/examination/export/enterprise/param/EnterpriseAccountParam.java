package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * EnterpriseAccountParam
 *
 * <AUTHOR>
 * @date 2020-01-10 15:45
 **/
@Data
public class EnterpriseAccountParam implements Serializable {

    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 公司编码
     */
    private Long companyNo;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 账号用户名
     */
    private String accountNo;
    /**
     * 账号密码
     */
    private String accountPwd;
    /**
     * 账号手机号
     */
    private String accountPhone;
    /**
     * 失效日期
     */
    private Date expireDate;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 账号角色
     */
    private String accountRole;
    /**
     * 账号状态 1正常 2锁定 3失效
     */
    private Integer accountStatus;
    /**
     * 账号邮箱地址
     */
    private String accountEmail;
}
