package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账号和项目关联关系类
 * <AUTHOR>
 * @date 2021-03-22 14:36
 */
@Data
public class AccountCompanyParam implements Serializable {
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 项目编号
     */
    private Long companyNo;
    /**
     * 是否运营账号 1是  0 否
     */
    private Integer operateAccount;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 部门信息数组
     */
    List<AccountDeptParam> deptList;
    /**
     * 项目名称
     */
    private String companyName;
}