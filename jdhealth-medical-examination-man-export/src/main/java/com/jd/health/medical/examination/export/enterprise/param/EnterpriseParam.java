package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Please enter class description information
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/5 14:35
 */
@Data
public class EnterpriseParam implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 大客户编码
     */
    private Long companyNo;
    /**
     * 大客户名称
     */
    private String companyName;
    /**
     * 服务开始时间
     */
    private Date startDate;
    /**
     * 服务结束时间
     */
    private Date endDate;
    /**
     * 服务状态 0未开始 1服务中 2已完成
     */
    private Integer serviceStatus;
    /**
     * 客户经理
     */
    private String managerPin;

    /**
     * 运营人员列表
     */
    private List<EnterpriseOperatorParam> operatorPinList;

    /**
     * 经理类型 1-高级经理 2-普通经理
     */
    private String manageType;

    /**
     * 客户地址
     */
    private String companyAddress;

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;

    /**
     * 客户联系人
     */
    private String companyContacter;

    /**
     * 客户联系人电话
     */
    private String companyContacterPhone;

    /**
     * 员工登录账号 1-姓名 2-员工erp 3-手机号 4-邮箱
     */
    private Integer employeeLoginAccount;

    /**
     * 员工登录密码 1-身份证号后六位 2-员工erp 3-手机号 4-邮箱
     */
    private Integer employeeLoginPwd;

    /**
     * 销售人员 逗号分隔
     */
    private String salesman;

    /**
     * pin信息
     */
    private String pin;
    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 营业执照号/信用代码
     */
    private String licenseNo;
    /**
     * 创建人
     */
    private String createUser;

    /**
     * 亲属同购停启用状态
     */
    private Integer relationStatus;

    /**
     * 服务模式 0先订单后服务 1先服务后订单
     */
    private Integer serviceModel;

    /**
     * 关联订单状态 0未关联 1已关联
     */
    private Integer relOrderStatus;


    /**
     * 大客户logo
     * 公司logo
     */
    private  String  companyLogoUrl;

    /**
     * pin的级别 母 0 还是子1
     */
    private Integer accountLevel;

    /**
     * 大客户套餐名称
     */
    private String companyGroupName;


    /**
     * 下单主体编号
     */
    private  String subjectCompanyNo;

    /**
     * 下单主体名称
     */
    private  String subjectCompanyName;

    /**
     * 套餐升级购买人数开关 0 未开启  1已开启
     */
    private Integer upgradeSwitchStatus;

    /**
     * 项目执行
     */
    private String projectExecutive;

    /**
     * 所属客户编号
     */
    private Long customerNo;

    /**
     * 客户简称
     */
    private String customerSimpleName;

    /**
     * 客户运营
     */
    private String customerOperator;

    /**
     * 大客户编码集合
     */
    private List<Long> companyNos;
}
