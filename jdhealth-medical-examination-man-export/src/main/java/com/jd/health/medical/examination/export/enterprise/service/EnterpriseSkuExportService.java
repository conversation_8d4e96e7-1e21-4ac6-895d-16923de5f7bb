package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.SkuInfoUpgradeRelationDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuRelationUpgradeDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuRelationUpgradeParam;
import com.jd.health.medical.examination.export.enterprise.param.sku.PageEnterpriseSkuParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 体检套餐商品池
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/6 15:16
 */
public interface EnterpriseSkuExportService {
    /**
     * 查询所有维护完成的sku
     * 并比较是否在该公司商品池中
     * @param enterpriseSkuParam
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuDTO>> querySkuInfoPage(EnterpriseSkuParam enterpriseSkuParam, PageParam pageParam);
    /**
     * 根据企业id分页查询企销企业的体检套餐
     * 优先使用 pageEnterpriseSku
     *
     * @param companyNo
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuDTO>> queryEnterpriseSkuPage(Long companyNo, PageParam pageParam);

    /**
     * 分页查询企销体检套餐池配置的SKU
     * @param param
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuDTO>> pageEnterpriseSku(PageEnterpriseSkuParam param);

    /**
     * 根据企业id
     * 查询企业所有的体检套餐(提供给员工端接口)
     * @param enterpriseSkuParam companyNo企业编号 skuName套餐名称
     * @return
     */
    JsfResult<List<EnterpriseSkuDTO>> queryAllEnterpriseSku(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 根据skuNo和公司名称查询套餐详情
     * @param enterpriseSkuParam
     * @return
     */
    JsfResult<EnterpriseSkuDTO> queryEnterpriseSkuBySkuNoAndCompanyNo(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 添加或更新大客户结算价格
     * @param enterpriseSkuParam
     * @return
     */
    JsfResult<Boolean> updateEnterpriseSkuCompanyPrice(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 根据skuNo和companyNo删除公司商品池中套餐
     * @param enterpriseSkuParam
     * @return
     */
    JsfResult<Boolean> deleteEnterpriseSkuBySkuNoAndCompanyNo(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 添加sku到大客户商品池
     * @param enterpriseSkuParam
     * @return
     */
    JsfResult<Boolean> addSkuToEnterpriseSku(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 获取sku的主图url
     * @param skuNo
     * @return
     */
    JsfResult<String> getSkuImageUrl(String skuNo);

    /**
     * 根据companyNo批量获取企销sku信息
     * @param companyNoList
     * @return
     */
    JsfResult<List<String>> querySkuNoByCompanyNoList(List<Long> companyNoList);

    /**
     * 更新商品别名
     * @param enterpriseSkuParam 入参
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateSkuAlias(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 更新商品加项属性
     * @param enterpriseSkuParam 入参
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateSkuPluginInfo(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 提供给C端 查询sku基本信息接口
     * 包含亲属同购的和套餐升级的
     */
    JsfResult<EnterpriseSkuRelationUpgradeDTO> queryEnterpriseSkuInfo(EnterpriseSkuRelationUpgradeParam enterpriseSkuRelationUpgradeParam);


    /**
     * 三合一接口 sku  套餐升级和 亲属同购
     *
     * @param
     * @param
     * @return
     */
    JsfResult<SkuInfoUpgradeRelationDTO> querySkuUpgradeRelationBySkuNoTypeAndCompanyNo(EnterpriseSkuRelationUpgradeParam enterpriseSkuRelationUpgradeParam);

    /**
     * 更新企业体检套餐体检须知文案
     *
     * @param enterpriseSkuParam
     * @return
     */
    JsfResult<Boolean> updateEnterpriseSkuExaminationNote(EnterpriseSkuParam enterpriseSkuParam);

    /**
     * 批量更新企业体检套餐体检须知文案
     *
     * @param enterpriseSkuParam
     * @return
     */
    JsfResult<Boolean> batchUpdateEnterpriseSkuExaminationNote(EnterpriseSkuParam enterpriseSkuParam);
}
