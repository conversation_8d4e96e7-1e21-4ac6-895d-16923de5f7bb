package com.jd.health.medical.examination.export.enterprise.param;

import com.jd.health.medical.examination.export.param.PageParam;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * EnterpriseConfigParam
 *
 *
 * <AUTHOR>
 * @description
 * @date 2021-01-06
 */
@Data
public class EnterpriseConfigParam extends PageParam implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 大客户编号
     */
    private Long companyNo;

    /**
    * 健康服务首页顶部图片：默认值：每个企业的，有一个默认图片。
    */
    private String homeTopUrl;

    /**
    * 员工满意度调查问卷：默认值：空。注意：为空时，员工端没有问卷的展示入口
    */
    private String questionnaire;
    
    /**
     * 是否展示水印 1是 0否
     */
    private Integer watermarkStatus;
    
    /**
     * 引流广告是否开启 1是 0 否
     */
    private Integer drainageStatus;
    
    /**
     * 店铺logo是否开启 1是 0 否
     */
    private Integer shopLogoStatus;

    /**
     * 首页展示地区并根据地区信息过滤套餐是否开启 1是 0 否
     */
    private Integer regionFilterStatus;

    /**
     * 是否启用对外HR账号通知功能
     */
    private Integer employeeNoticeStatus;
    
    /**
     * 报告解读是否开启 1是 0 否
     */
    private Integer reportExplainStatus;
    
    /**
     * 报告解读配置
     */
    private String reportExplainConfig;
    /**
     * 报告封皮id
     */
    private Long reportCoverId;
    /**
     * 报告封皮名称
     */
    private String reportCoverName;
}
