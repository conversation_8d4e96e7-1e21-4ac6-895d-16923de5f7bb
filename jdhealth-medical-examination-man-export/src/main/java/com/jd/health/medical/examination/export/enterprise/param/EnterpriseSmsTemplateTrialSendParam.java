package com.jd.health.medical.examination.export.enterprise.param;

import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSmsTemplateParamDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021-12-06
 */
@Data
public class EnterpriseSmsTemplateTrialSendParam implements Serializable {
    
    /**
     * 所属短信账号
     */
    private String smsAccount;
    
    /**
     * 短信模板id
     */
    private String templateId;
    
    /**
     * 短信试发手机号
     */
    private String phone;
    
    /**
     * 短信模板参数
     */
    private List<EnterpriseSmsTemplateParamDTO> smsTemplateParamList;
}
