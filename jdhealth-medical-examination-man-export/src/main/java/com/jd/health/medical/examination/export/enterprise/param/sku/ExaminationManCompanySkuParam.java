package com.jd.health.medical.examination.export.enterprise.param.sku;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 描述
 * 大客户加项商品查询
 * <AUTHOR>
 * @date 2022/9/21 16:37
 */
@Data
public class ExaminationManCompanySkuParam implements Serializable {
    
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 项目id 大客户编码
     */
    private Long companyNo;
    
    /**
     * sku编码
     */
    private String skuNo;
    
    /**
     * 操作类型：1更新 2删除
     */
    private Integer operationType;
    
    /**
     * 名称
     */
    private String skuName;
    
    /**
     * 商品价格（业务维护）
     */
    private BigDecimal skuPrice;
    
    /**
     * 商品市场价格
     */
    private BigDecimal skuMarketPrice;
    
    /**
     * 商品类型，1 基因
     */
    private Integer skuType;
    
    /**
     * 商品购买人数(虚拟数量)
     */
    private String skuBuyerNum;
    
    /**
     * 介绍
     */
    private String skuDesc;
    
    /**
     * sku二级类目
     */
    private String secondCategoryId;
    
    /**
     * 二级分类名称
     */
    private String secondCategoryName;
    
    /**
     * sku三级类目
     */
    private String categoryId;
    
    /**
     * sku三级类目名称
     */
    private String categoryName;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 修改用户
     */
    private String updateUser;

    /**
     * skuJdPrice
     * 主站价
     */
    private BigDecimal skuJdPrice;
    
}
