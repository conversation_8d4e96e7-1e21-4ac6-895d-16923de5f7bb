package com.jd.health.medical.examination.export.enterprise.param;

import com.jd.health.medical.examination.export.param.PageParam;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 亲属同购套餐param
 *
 * <AUTHOR>
 * @date 2020-03-18 18:18
 **/
@Data
public class EnterpriseSkuRelationParam extends PageParam implements Serializable {

    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 大客户编号
     */
    private Long companyNo;

    /**
     * 大客户名称
     */
    private String companyName;

    /**
     * 体检套餐编码
     */
    private String skuNo;

    /**
     * 体检套餐名称
     */
    private String skuName;

    /**
     * 体检项目组编号
     */
    private Long groupNo;

    /**
     * 大客户售价,单位分
     */
    private BigDecimal skuCompanyPrice;

    /**
     * 零售价格,单位分
     */
    private BigDecimal skuPersonPrice;

    /**
     * 亲属同购套餐名称
     */
    private String relationSkuName;

    /**
     * 亲属类型 8-父母 4-子女 2-配偶 1-其他
     */
    private Integer relationType;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后一次修改人
     */
    private String updateUser;

    /**
     * 调用端 1-企销运营端前端 2-企销员工端
     */
    private Integer callerType;

    /**
     * 购买须知
     */
    private String purchaseNotes;

    /**
     * 体检须知文案
     */
    private String examinationNotes;

    /**
     * sku列表
     */
    private List<String> skuList;
    
    /**
     * 市场价格,单位分
     */
    private BigDecimal skuMarketPrice;
}
