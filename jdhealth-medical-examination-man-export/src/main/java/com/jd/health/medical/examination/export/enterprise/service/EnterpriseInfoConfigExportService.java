package com.jd.health.medical.examination.export.enterprise.service;

import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseInfoConfigDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseInfoConfigServiceItemDTO;
import com.jd.health.medical.examination.export.enterprise.dto.ServicePackageConfigDto;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseConfigItemSaveParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseConfigParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 大客户管理  配置中心
 *
 * <AUTHOR>
 * @date 2020-01-08 9:34
 **/
public interface EnterpriseInfoConfigExportService {

    /**
     * 查询配置项，提供给C端调用
     *
     * @param companyNo 大客户编号
     * @param availCrowdType 服务可用人群 类型枚举类：
     *
     *
     * @return
     */
    JsfResult<List<EnterpriseInfoConfigServiceItemDTO>> queryConfigItem(Long companyNo, Integer availCrowdType,Integer contractType);

    /**
     * 查询配置
     *
     * @return
     */
    JsfResult<EnterpriseInfoConfigDTO> queryConfig(EnterpriseConfigParam param);

    /**
     * 保存配置
     *
     * @return
     */
    JsfResult<Boolean> insertConfig(EnterpriseConfigParam param);

    /**
     * 删除配置
     *
     * @return
     */
    JsfResult<Boolean> delConfig(EnterpriseConfigParam param);

    /**
     * 更新配置 , 顶部图片
     *
     * @return
     */
    JsfResult<Boolean> updateHomeTopUrlConfig(EnterpriseConfigParam param);

    /**
     * 更新配置 , 问券调查
     *
     * @return
     */
    JsfResult<Boolean> updateQuestionnaireConfig(EnterpriseConfigParam param);

    /**
     * 新增 配置项
     *
     * @return
     */
    JsfResult<Boolean > insertConfigItem(List<EnterpriseConfigItemSaveParam> params);

    /**
     * 编辑 配置项
     *
     * @return
     */
    JsfResult<Boolean> updateConfigItem(EnterpriseConfigItemSaveParam param);

    /**
     * 删除 配置项
     *
     * @return
     */
    JsfResult<Boolean> delConfigItem(Long companyNo);

    /**
     * 处理之前没有配置的大客户信息
     * @return true
     */
    JsfResult<Boolean> beforeCompanyConfig();
    
    /**
     * 更新配置的状态(水印,logo,广告等..)
     * @param param
     * @return
     */
    JsfResult<Boolean> updateConfigStatus(EnterpriseConfigParam param);
    
    
    /**
     * 查询配置
     *
     * @return
     */
    JsfResult<EnterpriseInfoConfigDTO> queryBaseConfig(EnterpriseConfigParam param);
    
    
    /**
     * 更新报告解读信息
     * @param param
     * @return
     */
    JsfResult<Boolean> updateReportConfig(EnterpriseConfigParam param);
    
    /**
     * 按ID查询服务包配置
     *
     * @param pkgId PKG ID
     * @return {@link JsfResult}<{@link ServicePackageConfigDto}>
     */
    JsfResult<ServicePackageConfigDto> queryServicePkgConfigById(Long pkgId);
}
