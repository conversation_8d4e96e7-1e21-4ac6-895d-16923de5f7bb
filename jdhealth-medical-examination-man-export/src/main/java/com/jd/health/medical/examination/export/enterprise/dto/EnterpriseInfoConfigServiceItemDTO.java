package com.jd.health.medical.examination.export.enterprise.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * EnterpriseInfoConfigServiceItemDTO
 *
 * 大客户管理  配置中心 , 服务项实体，对接C端
 *
 *
 * <AUTHOR>
 * @description
 * @date 2021-01-07
 */
@Data
public class EnterpriseInfoConfigServiceItemDTO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 配置id，系统生成
     */
    private Long configItemId;

    /**
     * 大客户编码，（值为-1时表示默认的大客户配置）
     */
    private Long companyNo;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务说明
     */
    private String serviceDesc;

    /**
     * 服务跳转链接：必填，有些服务写死、有些服务为空并支持用户进行编辑
     */
    private String serviceLink;

    /**
     * 服务对应的权益id类型，1：无，2：消费医疗通用权益，3：权益平台
     */
    private Integer serviceEquityType;

    /**
     * 权益ID：非必填，根据vip、非vip ， 返回指定的权益ID
     */
    private String equityId;

    /**
     * 无权益时展示方式，单选（1：不展示，2：置灰，3：正常展示）
     */
    private Integer equityNoStatus;

    /**
     * 提示语，当无权益时展示方式是不展示时，支持业务填写文案。
     */
    private String equityTips;

    /**
     * 权益与体检状态关系：必填、单选，有些服务写死，有些服务支持编辑。rn（1：已到检后可用，2：已出报告后可用，3：不限）
     */
    private Integer equityExaminationRelation;

    /**
     * logo彩色（必填，根据服务写死）
     */
    private String logoColorUrl;

    /**
     * logo灰色（必填，根据服务写死）
     */
    private String logoGreyUrl;

    /**
     * 顺序
     */
    private Integer sort;
    /**
     * 履约类型 list
     */
    private List<Integer> contractTypes;

}
