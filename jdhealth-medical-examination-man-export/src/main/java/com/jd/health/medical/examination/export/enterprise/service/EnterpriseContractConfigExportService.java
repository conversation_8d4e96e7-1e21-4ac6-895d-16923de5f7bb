package com.jd.health.medical.examination.export.enterprise.service;

import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseContractConfigDTO;
import com.jd.health.medical.examination.export.enterprise.dto.ExaminationManCompanySkuDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseContractConfigParam;
import com.jd.health.medical.examination.export.enterprise.param.sku.ExaminationManCompanySkuParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * Please enter class description information
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/17 10:23
 */
public interface EnterpriseContractConfigExportService {
    /**
     * 查询公司的履约信息
     * @param param
     * @return
     */
    JsfResult<List<EnterpriseContractConfigDTO>> selectEnterpriseContract(EnterpriseContractConfigParam param);

    /**
     * 编辑项目履约信息
     * @param enterpriseContractConfigParam
     * @return
     */
    JsfResult<Boolean> updateEnterpriseContract(EnterpriseContractConfigParam enterpriseContractConfigParam);
    
    /**
     * 查询公司的实物商品信息
     * @param param
     * @return
     */
    JsfResult<List<ExaminationManCompanySkuDTO>> queryExaminationCompanySku(ExaminationManCompanySkuParam param);
    
    /**
     * 查询公司的实物商品详情
     * @param param
     * @return
     */
    JsfResult<ExaminationManCompanySkuDTO> queryCompanySkuDetail(ExaminationManCompanySkuParam param);
    
    /**
     * 修改商品信息
     * @param param
     * @return
     */
    JsfResult<Boolean> updateCompanySkuDetail(ExaminationManCompanySkuParam param);
    
    /**
     * 保存商品信息
     * @param param
     * @return
     */
    JsfResult<Boolean> saveExaminationCompanySku(ExaminationManCompanySkuParam param);

}
