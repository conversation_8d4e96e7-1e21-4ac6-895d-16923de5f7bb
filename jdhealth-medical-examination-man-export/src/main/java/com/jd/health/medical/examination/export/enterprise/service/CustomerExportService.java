package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.CustomerDTO;
import com.jd.health.medical.examination.export.enterprise.param.CustomerParam;
import com.jd.health.medical.examination.export.enterprise.param.QueryCustomerParam;
import com.jd.health.medical.examination.export.param.CustomerInfoExportParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

/**
 * 客户信息对外接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/29 17:32
 */
public interface CustomerExportService {
    /**
     * 分页查询客户信息
     * @param customerParam 查询条件
     * @param pageParam 分页页码
     * @return 分页客户数据
     */
    JsfResult<PageInfo<CustomerDTO>> queryPageCustomer(CustomerParam customerParam, PageParam pageParam);

    /**
     * 添加客户信息
     * @param customerParam 客户信息
     * @return 是否添加成功
     */
    JsfResult<Boolean> addCustomer(CustomerParam customerParam);

    /**
     * 更新客户信息
     * @param customerParam 客户信息
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateCustomer(CustomerParam customerParam);

    /**
     * 删除客户信息
     * @param customerParam 客户信息id
     * @return 是否删除成功
     */
    JsfResult<Boolean> deleteCustomer(CustomerParam customerParam);


    /**
     * 异步导出客户项目相关信息
     * @param exportParam
     * @return
     */
    JsfResult<Boolean> asyncExportCustomerInfo(CustomerInfoExportParam exportParam);


    /**
     * 分页查询客户信息
     * @param customerParam 查询条件
     * @param pageParam 分页页码
     * @return 分页客户数据
     */
    JsfResult<PageInfo<CustomerDTO>> queryPageCustomerByOperatorAndSalesMan(CustomerParam customerParam, PageParam pageParam);
    
    /**
     * 根据企业id查询企业信息
     * @return 企业信息
     */
    JsfResult<CustomerDTO> queryCustomer(QueryCustomerParam queryCustomerParam);


}
