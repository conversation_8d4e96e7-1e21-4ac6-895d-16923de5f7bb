package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.*;
import com.jd.health.medical.examination.export.enterprise.param.*;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 大客户管理ExportService
 *
 * <AUTHOR>
 * @date 2020-01-08 9:34
 **/
public interface EnterpriseInfoExportService {
    /**
     * 分页查询大客户列表
     * @param enterpriseManageParam
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseManageDTO>> queryEnterprisePage(EnterpriseManageParam enterpriseManageParam, PageParam pageParam);

    /**
     * 增加大客户信息
     * @param enterpriseManageParam
     * @return
     */
    JsfResult<Boolean> addEnterprise(EnterpriseManageParam enterpriseManageParam);

    /**
     * 更新客户信息
     * @param enterpriseManageParam
     * @return
     */
    JsfResult<Boolean> updateEnterprise(EnterpriseManageParam enterpriseManageParam);

    /**
     * 分页查询运营人员管理的大客户列表
     * @param enterpriseOperatorParam
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseManageDTO>> queryEnterprisePageByOperator(EnterpriseOperatorParam enterpriseOperatorParam, PageParam pageParam);

    /**
     * 客户经理获取运营人员pin列表
     * @param enterpriseManageParam
     * @return
     */
    JsfResult<List<String>> queryOperatorPinList(EnterpriseManageParam enterpriseManageParam);

    /**
     * 根据全地址转换经纬度信息
     */
    JsfResult<GisPointDTO> getLngLatByAddress(String address);

    /**
     * 查询公司服务周期信息（提供给企销运营端的前端使用）
     * 根据公司编号   返回格式：开始日期-结束日期
     * @param companyNo
     * @return
     */
    JsfResult<String> queryCompanyServiceDate(Long companyNo);

    /**
     * 根据公司编号查询大客户信息（提供给员工端使用）
     * @param companyNo
     * @return
     */
    JsfResult<EnterpriseManageDTO> queryEnterpriseInfoByCompanyNo(Long companyNo);

    /**
     * 根据账号pin查询公司的编号(提供给员工端使用)
     */
    JsfResult<EnterpriseManageDTO> queryCompanyInfoByPin(String pin);

    /**
     * 根据公司编码查询hr电话
     */
    JsfResult<List<String>> queryHrPhoneByCompanyNo(Long companyNo);


    /**
     * 更新服务模式为先服后单
     * @param companyNo
     * @return
     */
    JsfResult<Integer> updateServiceModel(Long companyNo);

    /**
     * 更新项目经理
     * @param managerPin
     * @return
     */
    JsfResult<Integer> updateManageInfo(Long companyNo, String managerPin);

    /**
     * 根据pin和companyNo查询 大客户信息
     * @param companyNo
     * @param userPin
     * @return
     */
    JsfResult<EnterpriseManageDTO> queryEnterpriseInfoByCompanyNoUserPin(Long companyNo,String userPin);

    /**
     * 上传企业log 到oss
     * @param fileName
     * @param file
     * @return
     */
    JsfResult<String> uploadLogoToOss(String fileName,String file);

    /**
     * 根据pin 查询 其部门权限 提供hr端
     * @param pin pin
     * @return dto
     */
    JsfResult<List<EnterpriseDeptDTO>> getDeptInfoByPin(String pin,String companyNo);

    /**
     * 查询oss路径
     */
    JsfResult<String>  getOssUrl(String fileName);

    /**
     * 查询所有企业
     * @return
     */
    JsfResult<List<EnterpriseManageDTO>> getAllEnterpriseInfo();
    /**
     * 查询公司信息(包含运营人员信息)
     */
    JsfResult<EnterpriseManageDTO> queryEnterpriseAndOperator(Long companyNo);

    /**
     * 上传企业log 到oss
     * @param fileName
     * @param file
     * @return
     */
    JsfResult<String>  uploadFileToOss(String fileName, String file);

    /**
     * 更新企业升级状态名称
     * @param enterpriseManageParam 入参
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateCompanyUpgradeStatus(EnterpriseManageParam enterpriseManageParam);

    /*企业体检项目优化新增接口*/

    /**
     * 分页查询
     * @param param 查询条件
     * @param pageParam 分页参数
     * @return 项目信息list
     */
    JsfResult<PageInfo<EnterpriseManageDTO>> queryPageEnterprise(EnterpriseParam param, PageParam pageParam);

    /**
     *
     * @param userPin
     * @return
     */
    JsfResult<Boolean> isProjectExecutive(String userPin);

    /**
     * 添加项目信息
     * @param param 项目信息
     * @return 是否添加成功
     */
    JsfResult<Boolean> addEnterpriseInfo(EnterpriseManageParam param);

    /**
     * 更新项目信息
     * @param param 项目信息
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateEnterpriseInfo(EnterpriseManageParam param);

    /**
     * 删除项目信息
     * @param param 项目信息
     * @return 是否删除成功
     */
    JsfResult<Boolean> deleteEnterprise(EnterpriseManageParam param);

    /**
     * 修改服务状态
     * @param companyNo
     * @return
     */
    JsfResult<Boolean>  updateServiceStatus(Integer status,String companyNo);
    /**
     * 上传文件
     * @param fileName 文件list base64
     * @param fileStr base64
     * @return 返回文件url
     */
    JsfResult<String> uploadFiles(String fileName,String fileStr);

    /**
     * 获取文件下载链接
     * @param jssUrl jssUrl
     * @return httpUrl
     */
    JsfResult<String> getHttpUrl(String jssUrl);

    /**
     * 更新公司的服务周期和登录方式
     * @param param 更新入参
     * @return 是否成功
     */
    JsfResult<Boolean> updateEnterpriseService(EnterpriseManageParam param);

    /**
     * 查询审核通过后的客户信息 待开始 服务中 待结束 已结束
     *
     * @param param     查询条件
     * @param pageParam 分页参数
     * @return 项目信息list
     */
    JsfResult<PageInfo<EnterpriseManageDTO>> queryPassPageEnterprise(EnterpriseParam param, PageParam pageParam);

    /**
     * 查询审核通过后的项目数据列表
     *
     * @param param     查询条件
     * @param pageParam 分页参数
     * @return 项目数据信息list
     */
    JsfResult<PageInfo<EnterpriseProjectDataDTO>> queryPassPageEnterpriseData(EnterpriseParam param, PageParam pageParam);

    /**
     * 分页查询  客户运营列表数据  不包含待提交和驳回
     *
     * @param param     查询条件
     * @param pageParam 分页参数
     * @return 项目信息list
     */
    JsfResult<PageInfo<EnterpriseManageDTO>> queryOperatorEnterprise(EnterpriseParam param, PageParam pageParam);

    /**
     * 查询项目信息（提供给HR端使用）
     * @param pin 账号体系子账号pin
     * @return
     */
    JsfResult<List<EnterpriseManageDTO>> queryProjectByAccount(String pin);
    
    /**
     * 查询项目信息
     * @param queryProjectParam 查询条件
     * @return 项目信息
     */
    JsfResult<ProjectDTO> queryProject(QueryProjectParam queryProjectParam);
    
    /**
     * 查询项目信息
     * @param companyNoList 项目编号
     * @return JsfResult<List<EnterpriseManageDTO>>
     */
    JsfResult<List<EnterpriseManageDTO>> queryProjectByCompanyNo(List<Long> companyNoList);

    /**
     * 获取部门信息
     *
     * @param pin       pin
     * @param companyNo companyNo
     * @return dto
     */
    JsfResult<EnterpriseDeptNewDTO> getNewDeptInfoByPin(String pin, String companyNo);

    /**
     * 查询企业套餐到检数、购买数列表
     *
     * @param param 查询条件
     * @return 项目数据信息list
     */
    JsfResult<PageInfo<EnterpriseSkuDataDTO>> queryEnterpriseSkuPageData(EnterpriseSkuPageDataParam param);
}
