package com.jd.health.medical.examination.export.enterprise.param;

import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSmsTemplateParamDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021-12-02
 */
@Data
public class EnterpriseSmsTemplateParam implements Serializable {
    
    /**
     * 所属短信账号
     */
    private String smsAccount;
    
    /**
     * 短信模板id
     */
    private String templateId;
    
    /**
     * 短信模板名称
     */
    private String templateName;
    
    /**
     * 模板内容
     */
    private String templateContent;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 默认支持的客户渠道 1-直签 2-京企康 12直签&京企康
     */
    private String defaultStatus;
    
    /**
     * 短信模板参数
     */
    private List<EnterpriseSmsTemplateParamDTO> smsTemplateParamList;
}
