package com.jd.health.medical.examination.export.enterprise.util;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.param.PageParam;

import java.util.List;

/**
 * PageBeanUtil
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/8 10:31
 */
public class PageBeanUtil {

    /**
     * @param pageInfo
     * @param list
     * @return
     */
    public static <T> PageInfo<T> convert2PageInfo(PageInfo pageInfo, List<T> list) {
        PageInfo targetPageInfo = new PageInfo<>();
        targetPageInfo.setPageNum(pageInfo.getPageNum());
        targetPageInfo.setPageSize(pageInfo.getPageSize());
        targetPageInfo.setSize(pageInfo.getSize());
        targetPageInfo.setStartRow(pageInfo.getStartRow());
        targetPageInfo.setEndRow(pageInfo.getEndRow());
        targetPageInfo.setTotal(pageInfo.getTotal());
        targetPageInfo.setPages(pageInfo.getPages());
        targetPageInfo.setPrePage(pageInfo.getPrePage());
        targetPageInfo.setNextPage(pageInfo.getNextPage());
        targetPageInfo.setIsFirstPage(pageInfo.isIsFirstPage());
        targetPageInfo.setIsLastPage(pageInfo.isIsLastPage());
        targetPageInfo.setHasPreviousPage(pageInfo.isHasPreviousPage());
        targetPageInfo.setHasNextPage(pageInfo.isHasNextPage());
        targetPageInfo.setNavigatePages(pageInfo.getNavigatePages());
        targetPageInfo.setNavigatepageNums(pageInfo.getNavigatepageNums());
        targetPageInfo.setNavigateFirstPage(pageInfo.getNavigateFirstPage());
        targetPageInfo.setNavigateLastPage(pageInfo.getNavigateLastPage());
        targetPageInfo.setList(list);
        return targetPageInfo;
    }

    /**
     * @param pageParam
     * @param list
     * @return
     */
    public static <T> PageInfo<T> convert2PageInfo(PageParam pageParam, List<T> list) {
        PageInfo targetPageInfo = new PageInfo<>();
        targetPageInfo.setPageNum(pageParam.getPageNum());
        targetPageInfo.setPageSize(pageParam.getPageSize());
        targetPageInfo.setList(list);
        return targetPageInfo;
    }
}
