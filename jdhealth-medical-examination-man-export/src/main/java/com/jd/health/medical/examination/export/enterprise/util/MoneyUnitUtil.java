package com.jd.health.medical.examination.export.enterprise.util;

import com.github.pagehelper.util.StringUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * 请填写类的描述
 * <AUTHOR> yintao
 * @date : 2019/11/29 20:42
 */
public class MoneyUnitUtil {
    /**
     * 把金钱(元)转换为分
     *
     * @return
     */
    public static Integer convert2Fen(BigDecimal price) {
        if (price != null) {
            return price.multiply(new BigDecimal(100)).intValue();
        }
        return null;
    }

    /**
     * 把金钱(元)转换为分
     *
     * @return
     */
    public static Integer convert2Fen(Double price) {
        if (price != null) {
            BigDecimal bigDecimal = BigDecimal.valueOf(price);
            return bigDecimal.multiply(new BigDecimal(100)).intValue();
        }
        return null;
    }


    /**
     * 将字符串金额转为分
     */
    public static Integer stringConvert2Fen(String priceStr) {
        if(priceStr == null){
            return null;
        }
        BigDecimal price = new BigDecimal(priceStr);
        return price.multiply(new BigDecimal(100)).intValue();
    }
    
    /**
     * 把金钱(分)转换为元
     *
     * @param price
     * @return
     */
    public static BigDecimal convert2Yuan(Integer price) {
        if (price !=null && price!=0){
            return new BigDecimal(price).
                    divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN);
        }else if (price != null){
            return new BigDecimal("0.00");
        }
        return null;
    }
    
    /**
     * 把金钱(分)转换为元
     *
     * @param price
     * @return
     */
    public static BigDecimal convert2Yuan(Double price) {
        if (price !=null && price!=0){
            return new BigDecimal(price).
                    divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN);
        }else if (price != null){
            return new BigDecimal("0.00");
        }
        return null;
    }
    
    /**
     * 把金钱(分)转换为元
     *
     * @param price
     * @return
     */
    public static BigDecimal convert2Yuan(BigDecimal price) {
        if (price !=null && !price.equals(0)){
            return price.divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN);
        }else if (price != null){
            return new BigDecimal("0.00");
        }
        return null;
    }
}
