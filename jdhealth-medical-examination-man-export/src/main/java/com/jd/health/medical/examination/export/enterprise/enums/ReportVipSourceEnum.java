package com.jd.health.medical.examination.export.enterprise.enums;

/**
 * 描述
 * 报告解读是否是VIP
 * <AUTHOR>
 * @date 2022/8/9 11:07
 */
public enum ReportVipSourceEnum {
    
    /** 非VIP */
    NO_VIP(1,"非VIP"),
    /** VIP */
    VIP(2,"VIP"),
    
    ;
    
    /**
     * ReportVipSourceEnum
     * @param code
     * @param desc
     */
    ReportVipSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * code
     */
    private Integer code;
    /**
     * desc
     */
    private String desc;
    
    /**
     * getCode
     * @return
     */
    public Integer getCode() {
        return code;
    }
    
    /**
     * setCode
     * @param code
     */
    public void setCode(Integer code) {
        this.code = code;
    }
    
    /**
     * getDesc
     * @return
     */
    public String getDesc() {
        return desc;
    }
    
    /**
     * setDesc
     * @param desc
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }
}
