package com.jd.health.medical.examination.export.enterprise.service.notice;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.CompanyParamDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseProjectInfoDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSmsTemplateDTO;
import com.jd.health.medical.examination.export.enterprise.dto.FormInstanceParamDTO;
import com.jd.health.medical.examination.export.enterprise.param.CompanyParamCustomParam;
import com.jd.health.medical.examination.export.enterprise.param.HistoryProjectBindTemplateParam;
import com.jd.health.medical.examination.export.enterprise.param.ProjectConfigSmsTemplateParam;
import com.jd.health.medical.examination.export.enterprise.param.SmsTemplatePageParam;
import com.jd.health.medical.examination.export.param.JoySkyApprovalBaseParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;
import java.util.Map;

/**
 * 项目与短信模板关系
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6
 */
public interface ProjectSmsTemplateExportService {

    /**
     * 查询项目绑定的模板列表
     *
     * @param companyNo 项目编号
     * @return {@link JsfResult}<{@link List}<{@link EnterpriseSmsTemplateDTO}>>
     */
    JsfResult<List<EnterpriseSmsTemplateDTO>> queryProjectBindSmsTemplate(Long companyNo);

    /**
     * 分页查询全量的模板列表
     *
     * @param pageParam 分页参数
     * @return {@link JsfResult}<{@link PageInfo}<{@link EnterpriseSmsTemplateDTO}>>
     */
    JsfResult<PageInfo<EnterpriseSmsTemplateDTO>> querySmsTemplatePage(SmsTemplatePageParam pageParam);

    /**
     * 项目添加短信模板
     * @param param param
     * @return boolean
     */
    JsfResult<Boolean> addProjectSmsTemplate(ProjectConfigSmsTemplateParam param);

    /**
     * 项目删除关联短信模板
     * @param param param
     * @return boolean
     */
    JsfResult<Boolean> deleteProjectSmsTemplate(ProjectConfigSmsTemplateParam param);

    /**
     * 查询项目关联的预设值
     * @param param param
     * @return dto
     */
    JsfResult<List<CompanyParamDTO>> queryCompanyCustomParam(CompanyParamCustomParam param);

    /**
     * 项目添加预设值
     * @param param param
     * @return boolean
     */
    JsfResult<Boolean> addCompanyCustomParam(CompanyParamCustomParam param);

    /**
     * 项目删除预设值
     * @param param param
     * @return boolean
     */
    JsfResult<Boolean> deleteCompanyCustomParam(CompanyParamCustomParam param);

    /**
     * HR端查询项目绑定的短信模板集合
     * @param companyNoStr companyNoStr
     * @return list
     */
    JsfResult<List<EnterpriseSmsTemplateDTO>> querySmsTemplateByCompanyNo(String companyNoStr);

    /**
     * HR端查询短信模板详情
     * @param param param
     * @return dto
     */
    JsfResult<EnterpriseSmsTemplateDTO> querySmsTemplateInfo(ProjectConfigSmsTemplateParam param);

    /**
     * 查询项目详情
     * @param companyNoStr companyNoStr
     * @return dto
     */
    JsfResult<EnterpriseProjectInfoDTO>  selectProjectInfoByCompanyNo(String companyNoStr);

    /**
     * 发送joy-go
     * @param stringStringMap stringStringMap
     * @return Boolean
     */
    JsfResult<FormInstanceParamDTO> smsSendApproveToJoyGo(Map<String, String> stringStringMap);

    /**
     * 发送joy-sky
     *
     * @param param
     * @return Boolean
     */
    JsfResult<FormInstanceParamDTO> smsSendApproveToJoySky(JoySkyApprovalBaseParam param);

    /**
     * 终止流程
     * @param terminationParam terminationParam
     * @return boolean
     */
    JsfResult<Boolean> sendSmsApprovalTermination(String terminationParam);

    /**
     * 终止joySky审批流程
     *
     * @param param
     * @return
     */
    JsfResult<Boolean> sendSmsApprovalJoySkyTermination(JoySkyApprovalBaseParam param);

    /**
     * 历史项目绑定短信模板
     * @return boolean
     */
    JsfResult<Boolean> historyProjectBindSmsTemplate(HistoryProjectBindTemplateParam param);
}
