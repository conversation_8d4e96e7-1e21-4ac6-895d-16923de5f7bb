package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.util.Date;

/**
 * 项目履约方式入参
 * <AUTHOR>
 * @date 2021-05-17 10:28
 */
@Data
public class EnterpriseContractConfigParam {
    /**
     * 项目编号
     */
    private Long companyNo;
    /**
     * 履约类型
     */
    private Integer contractType;
    /**
     * 履约名称
     */
    private String contractName;
    /**
     * 履约描述
     */
    private String contractDesc;
    /**
     * 履约详细描述
     */
    private String contractDetailDesc;
    /**
     * 履约url
     */
    private String contractUrl;
    /**
     * 履约状态
     */
    private Integer contractStatus;
}