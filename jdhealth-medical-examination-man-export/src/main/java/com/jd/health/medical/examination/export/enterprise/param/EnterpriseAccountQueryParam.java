package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 14:22
 */
@Data
public class EnterpriseAccountQueryParam implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    private Long companyId;
    /**
     * 账号类型 true 查子账号，false 查母账号
     */
    private Boolean isMain;
    /**
     * bPin
     */
    private String bPin;
    /**
     * 用户名
     */
    private String name;
    /**
     * 升降序 1 升序 2降序 (按修改时间)
     */
    private Integer sort;
    /**
     * 停启用状态 0停用 1启用
     */
    private Integer status;
    /**
     * 页码 和saas参数不一致
     */
    private Integer pageNum;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     *
     */
    private Integer offset;
}
