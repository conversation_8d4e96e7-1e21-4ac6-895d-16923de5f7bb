package com.jd.health.medical.examination.export.enterprise.service.notice;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.*;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSmsCustomParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSmsCustomQueryParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSmsTemplateParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSmsTemplateTrialSendParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 企业短信模板服务
 *
 * <AUTHOR>
 * @date 2021/11/30
 */
public interface EnterpriseSmsTemplateExportService {
    
    /**
     * 分页查询全量的模板列表
     *
     * @param pageParam 分页参数
     * @return {@link JsfResult}<{@link PageInfo}<{@link EnterpriseSmsTemplateDTO}>>
     */
    JsfResult<PageInfo<EnterpriseSmsTemplateDTO>> queryTemplateListPage(PageParam pageParam);
    
    /**
     * 查询固定模板参数列表
     *
     * @return {@link JsfResult}<{@link List}<{@link EnterpriseSmsTemplateParamDTO}>>
     */
    JsfResult<List<EnterpriseSmsTemplateParamDTO>> queryFixedTemplateParamList();
    
    /**
     * 查询短信账号列表
     *
     * @return {@link JsfResult}<{@link List}<{@link EnterpriseSmsAccountDTO}>>
     */
    JsfResult<List<EnterpriseSmsAccountDTO>> querySmsAccountList();
    
    /**
     * 保存短信模板
     *
     * @param param 参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> saveSmsTemplate(EnterpriseSmsTemplateParam param);
    
    /**
     * 短信试发
     *
     * @param param 参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> smsTrialSend(EnterpriseSmsTemplateTrialSendParam param);
    
    /**
     * 删除短信模板
     *
     * @param param 参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> deleteSmsTemplate(EnterpriseSmsTemplateParam param);
    
    /**
     * 更新短信模板
     *
     * @param param 参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> updateSmsTemplate(EnterpriseSmsTemplateParam param);
    
    /**
     * 检查短信模板save or update入参是否合法
     *
     * @param param 参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> checkSmsTemplateParamIsLegal(EnterpriseSmsTemplateParam param);
    
    /**
     * 保存SMS自定义参数
     *
     * @param smsCustomParam SMS自定义参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<String> saveSmsCustomParam(EnterpriseSmsCustomParam smsCustomParam);
    
    /**
     * 删除SMS自定义参数
     *
     * @param smsCustomParam SMS自定义参数
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> delSmsCustomParam(EnterpriseSmsCustomParam smsCustomParam);
    
    /**
     * 查询短信自定义参数值列表
     *
     * @param smsCustomQueryParam SMS自定义参数
     * @return {@link JsfResult}<{@link List}<{@link EnterpriseSmsCustomParamDTO}>>
     */
    JsfResult<EnterpriseSmsCustomParamDTO> querySmsCustomParamList(EnterpriseSmsCustomQueryParam smsCustomQueryParam);
    
    /**
     * 查询具有通知权限
     *
     * @param accountNo      账号
     * @param companyNoStr 公司编号
     * @return {@link JsfResult}<{@link Boolean}>
     */
    JsfResult<Boolean> queryHasNoticePrivilege(String accountNo,String companyNoStr);
    
    /**
     * 提供给hr端查询短信模板对应的账号密码信息
     *
     * @param smsAccount 短信帐户
     * @return {@link JsfResult}<{@link EnterpriseSmsAccountDTO}>
     */
    JsfResult<EnterpriseSmsAccountDTO> queryTemplateAccountInfoForHr(String smsAccount);
}
