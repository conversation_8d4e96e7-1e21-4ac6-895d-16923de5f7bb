package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.*;
import com.jd.health.medical.examination.export.enterprise.param.*;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 账号体系对外接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 14:32
 */
public interface EnterpriseAccountSystemExportService {

    /**
     * 创建母账号
     *
     * @param param
     * @return
     */
    @Deprecated
    JsfResult<EnterPriseLeaderCreateDTO> createLeaderPin(EnterpriseLeaderCreateParam param);
    /**
     * 查询账号列表
     *
     * @param param
     * @return
     */
    JsfResult<PageInfo<EnterpriseAccountInfoDTO>> queryAccountPageList(EnterpriseAccountQueryParam param);

    /**
     * 判断账号是否被停用
     *
     * @param param
     * @return
     */
    JsfResult<Boolean> checkAccountStopped(EnterpriseBuyerAccountParam param);

    /**
     * 创建子账号
     *
     * @param param
     * @return
     */
    @Deprecated
    JsfResult<Boolean> createSubUser(EnterpriseCreateSubUserParam param);

    /**
     * 更新子账号菜单权限
     *
     * @param param
     * @return
     */
    JsfResult<Boolean> updateUserMenuInfo(EnterpriseUpdateSubUserParam param);

    /**
     * 更新子账号启用状态
     *
     * @param param
     * @return
     */
    JsfResult<Boolean> updateUserStatus(EnterpriseUpdateSubUserParam param);

    /**
     * 获取子账号分配的菜单
     *
     * @param pin
     * @return
     */
    JsfResult<EnterpriseBuyerMenuResDTO> queryMenuByPin(String pin);

    /**
     * 获取所有菜单信息
     *
     * @param param
     * @return
     */
    JsfResult<List<EnterpriseBuyerMenuDTO>> queryChannelMenu(EnterpriseChannelMenuParam param);

    /**
     * 校验输入pin是否包含敏感字段
     * @param userPin
     * @return
     */
    JsfResult<Boolean> checkPin(String userPin);

    /**
     * 根据accountNo查询母账号密码
     * @param accountNo
     * @return
     */
    JsfResult<String> selectPwdByAccountNo(String accountNo);

    /**
     * 查询账号整体左侧菜单
     * @param pin
     * @return
     */
    JsfResult<EnterpriseBuyerMenuResDTO> queryMenuForCommonBuyer(String pin);

    /**
     * 添加账号和项目关联关系(包含部门权限)
     * @return
     */
    JsfResult<Boolean> addAccountCompanyRelDept(AccountCompanyParam accountCompanyParam);


    /**
     * 创建子账号 跟进客户编号
     *
     * @param param
     * @return
     */
    JsfResult<Boolean> createCustomerSubUser(EnterpriseCreateSubUserParam param);

    /**
     * 添加账号的部门权限
     * @param accountCompanyParam 入参
     * @return
     */
    JsfResult<Boolean> addAccountDeptAuth(AccountCompanyParam accountCompanyParam);

    /**
     * 查询账号的项目信息
     * @param accountCompanyParam 入参
     * @return 项目信息
     */
    JsfResult<List<AccountCompanyDTO>> queryAccountProject(AccountCompanyParam accountCompanyParam);

}
