package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Note: Describer类的描述
 * <AUTHOR> yintao
 * @date: 2019/12/10 9:53
 */
@Data
public class EnterpriseSkuParam implements Serializable {

    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * sku编码
     */
    private String skuNo;
    /**
     * 套餐名称
     */
    private String skuName;

    /**
     * 套餐编号
     */
    private Long groupNo;

    /**
     * 套餐名称
     */
    private String groupName;
    /**
     * 适用性别
     */
    private Integer skuSuitable;

    /**
     * 大客户编号
     */
    private Long companyNo;
    /**
     * 大客户名称
     */
    private String companyName;

    /**
     * 企销价格,单位元
     */
    private BigDecimal skuCompanyPrice;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后一次修改人
     */
    private String updateUser;

    /**
     * 父级体检套餐编码
     */
    private String parentSkuNo;
    /**
     * 商品别名
     */
    private String skuAlias;

    /**
     * 商品别名
     */
    private Integer pluginDiscountsType;

    /**
     * 商品别名
     */
    private Integer pluginDiscountsValue;

    /**
     * 体检须知文案
     */
    private String examinationNotes;

    /**
     * sku列表
     */
    private List<String> skuList;
}
