package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 创建母账号参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 14:16
 */
@Data
public class EnterpriseLeaderCreateParam implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;
    /**
     * pin信息
     */
    private String pin;
    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 营业执照号/信用代码
     */
    private String licenseNo;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 企业类型
     */
    private Integer companyManageType;
    /**
     *业务身份
     */
    private String identity;
    /**
     *B中台 Source值。目前药京采=4，体检=5
     */
    private Integer b2bSource;
    /**
     * 申请人erp
     */
    private String applierErp;
    /**
     * 是否免审，如果为true，则直接到成都B中台创建一个审核通过的B账号
     */
    private Boolean certPassAduit;
}
