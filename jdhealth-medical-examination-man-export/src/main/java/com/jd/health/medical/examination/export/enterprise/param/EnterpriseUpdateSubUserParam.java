package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 15:40
 */
@Data
public class EnterpriseUpdateSubUserParam implements Serializable {
    /**
     *被修改的子账号pin
     */
    private String subPin;
    /**
     * 母pin
     */
    private String superPin;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 联系人名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 创建子账号时需要绑定的菜单list，
     * key为业务标识，
     * value为该业务下需要绑定的菜单列表
     */
    private Map<String, List<String>> channelResMap;
    /**
     * 用户状态
     * 1-正常，2-冻结
     */
    private Integer userStatus;

    /**
     *
     */
    List<String>  deptList;
}
