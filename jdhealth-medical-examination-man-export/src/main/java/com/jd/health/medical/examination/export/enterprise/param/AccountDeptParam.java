package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 部门权限信息参数
 * @date 2022/1/13 上午10:04
 */
@Data
public class AccountDeptParam implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -15408918384055326L;
    /**
     *
     */
    private String deptId;

    /**
     * 部门级别 1-一级部门 2-二级部门 3-三级部门 4-四级部门 5-五级部门
     */
    private Integer deptMark;

    /**
     * 是否新权限 1是 0 否
     */
    private Integer isNewPower;

}
