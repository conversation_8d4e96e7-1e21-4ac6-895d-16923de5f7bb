package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.sku.SkuPluginInfoDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuPluginDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuUpgradeAndPluginDTO;
import com.jd.health.medical.examination.export.dto.sku.SkuCompatibilityCheckDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuPluginParam;
import com.jd.health.medical.examination.export.param.sku.SkuCompatibilityCheckParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 企销套餐插件关系管理暴露service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 9:16
 */
public interface EnterpriseSkuPluginExportService {
    
    /**
     * 分页查询插件
     *
     * @param enterpriseSkuPluginParam
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuPluginDTO>> querySkuPluginInfoPage(EnterpriseSkuPluginParam enterpriseSkuPluginParam, PageParam pageParam);
    
    /**
     * 根据商品sku编码，京东套餐的编码，获取该京东套餐下配置的所有的京东加项包列表
     *
     * @param enterpriseSkuPluginParam 必传：projectNo, skuNo, pluginType, pluginSkuNo, groupNo
     * @return
     */
    JsfResult<List<SkuPluginInfoDTO>> queryListWithBindStatus(EnterpriseSkuPluginParam enterpriseSkuPluginParam);
    
    /**
     * 根据projectNo, skuNo, pluginType, pluginSkuNoList不分页查询插件列表
     *
     * @param enterpriseSkuPluginParam
     * @return
     */
    JsfResult<List<EnterpriseSkuPluginDTO>> queryListByBatchPluginSkuNo(EnterpriseSkuPluginParam enterpriseSkuPluginParam);

    /**
     * 根据projectNo, skuNo, pluginType, needItems不分页查询插件列表
     *
     * @param enterpriseSkuPluginParam
     * @return
     */
    JsfResult<List<EnterpriseSkuPluginDTO>> querySkuPluginByProjectNoAndSkuNo(EnterpriseSkuPluginParam enterpriseSkuPluginParam);

    /**
     * 根据projectNo, skuNo, pluginType, needItems不分页查询插件列表，返回包装类
     *
     * @param enterpriseSkuPluginParam
     * @return
     */
    JsfResult<EnterpriseSkuUpgradeAndPluginDTO> querySkuPluginDTOByProjectNoAndSkuNo(EnterpriseSkuPluginParam enterpriseSkuPluginParam);

    /**
     * 添加一个企销套餐插件
     *
     * @param enterpriseSkuPluginParam
     * @return 是否添加成功
     */
    JsfResult<Boolean> addSkuPlugin(EnterpriseSkuPluginParam enterpriseSkuPluginParam);

    /**
     * 更新企销套餐插件
     *
     * @param enterpriseSkuPluginParam
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateSkuPlugin(EnterpriseSkuPluginParam enterpriseSkuPluginParam);
    
    /**
     * 删除一个企销套餐插件
     *
     * @param param
     * @return
     */
    JsfResult<Boolean> deleteSkuPlugin(EnterpriseSkuPluginParam param);

    /**
     * 加项包兼容性校验
     * @param checkParam
     * @return
     */
    JsfResult<SkuCompatibilityCheckDTO> skuCompatibilityCheck(SkuCompatibilityCheckParam checkParam);




}
