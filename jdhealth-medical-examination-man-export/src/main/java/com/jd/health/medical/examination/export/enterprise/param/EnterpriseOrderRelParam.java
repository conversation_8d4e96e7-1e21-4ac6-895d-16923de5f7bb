package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/31 20:40
 */
@Data
public class EnterpriseOrderRelParam implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 公司编号
     */
    private Long companyNo;
    /**
     * 订单编号
     */
    private Long orderId;
    /**
     * 订单价格 元
     */
    private BigDecimal orderPrice;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 最后更新人
     */
    private String updateUser;
}
