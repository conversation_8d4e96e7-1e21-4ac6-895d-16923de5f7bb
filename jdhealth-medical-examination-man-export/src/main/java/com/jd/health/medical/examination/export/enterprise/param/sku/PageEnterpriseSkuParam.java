package com.jd.health.medical.examination.export.enterprise.param.sku;

import com.jd.health.medical.examination.export.param.PageParam;
import lombok.Data;

import java.io.Serializable;

/**
 * 分页查询企销基础套餐参数
 * @author: yang<PERSON><PERSON>
 * @date: 2022/7/1 2:35 下午
 * @version: 1.0
 */
@Data
public class PageEnterpriseSkuParam extends PageParam  implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long companyNo;
    /**
     *
     */
    private String skuNo;
    /**
     *
     */
    private String skuName;

    /**
     * 商品池sku别名
     */
    private String skuAlias;
}
