package com.jd.health.medical.examination.export.enterprise.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName EnterpriseSkuUpgradeAndPluginDTO
 * @Description
 * <AUTHOR>
 * @Date 2021/9/26 14:05
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseSkuUpgradeAndPluginDTO {

    /**
     * 加项包列表
     */
    List<EnterpriseSkuPluginDTO> skuPluginDTOList;

    /**
     * 升级套餐列表
     */
    List<EnterpriseSkuUpgradeInfoDTO> skuUpgradeDTOList;

    /**
     * 运营端为该员工配置的加项包总数
     */
    private Integer allAddItemSkuNum;

    /**
     * 运营端为该员工配置的升级套餐总数
     */
    private Integer allUpgradeSkuNum;
    /**
     * 加项包优惠类型
     */
    private Integer discountsType;
}