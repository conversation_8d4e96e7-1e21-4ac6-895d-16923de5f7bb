package com.jd.health.medical.examination.export.enterprise.service;

import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseOrderDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseOrderRelDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseOrderRelParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.util.JsfResultUtil;

import java.math.BigDecimal;
import java.util.List;

/**
 * 企销大客户订单相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/31 17:38
 */
public interface EnterpriseOrderExportService {
    /**
     * 根据公司编号查询公司绑定的订单
     *
     * @param companyNo
     * @return
     */
    JsfResult<List<EnterpriseOrderDTO>> queryEnterpriseOrders(Long companyNo);

    /**
     * 关联订单 查询订单是否存在 --> 存在关联订单 --> 判断是不是首个关联订单  --> 是 下单主体pin插入公司表
     * --> 不存在 业务异常 订单不存在
     *
     * @return 是否关联成功
     */
    JsfResult<Boolean> relEnterpriseOrder(EnterpriseOrderRelParam enterpriseOrderRelParam);

    /**
     * 根据订单号查询订单
     * @return 订单
     */
    JsfResult<EnterpriseOrderDTO> queryEnterpriseOrderByOrderId(Long orderId);

    /**
     * 公司服务结束 查询公司是否是待结束状态 --> 是 结束服务
     * --> 否 不可结束服务
     * @param companyNo 公司编号
     * @return 结束成功与否 true 成功 false 失败
     */
    JsfResult<Boolean> enterpriseServiceEnd(Long companyNo);

    /**
     * 查询公司是否已经关联订单
     * @param companyNo 公司编号
     * @return 是否关联了订单 true 是 false 否
     */
    JsfResult<Boolean> enterpriseIsRelOrder(Long companyNo);

    /**
     * 根据userPin获取企业订单信息
     * @param userPin
     * @return
     */
    JsfResult<EnterpriseOrderDTO> queryEnterpriseOrderByUserPin(String userPin);

    /**
     * 查询公司到检总金额 单位分
     * @param companyNo
     * @return
     */
    JsfResult<BigDecimal> getCheckEmployeeTotalSum(Long companyNo);

    /**
     * 查询可推结算的订单
     *
     * @param companyNo
     * @return
     */
    JsfResult<List<EnterpriseOrderDTO>> querySettleOrderList(Long companyNo);


    /**
     *    先单后服 查询第一个关联订单剩余金额
     * @param companyNo
     * @param  orderSum
     * @return
     */
    JsfResult<EnterpriseOrderRelDTO> getRemnantSumOrder(Long companyNo, Integer orderSum);


}
