package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 14:59
 */
@Data
public class EnterpriseCreateSubUserParam implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;
    /**
     * 要创建子账号的母pin
     */
    private String superPin;
    /**
     * superPin对应的公司id
     */
    private Long companyId;
    /**
     * 要创建的子账号的pin
     */
    private String jdPin;
    /**
     * 子账号的密码
     */
    private String pwd;
    /**
     * 联系人姓名
     */
    private String name;
    /**
     * 用户手机号
     */
    private String mobilePhone;
    /**
     * 调用方的机器ip
     */
    private String ip;
    /**
     * 创建子账号时需要绑定的菜单list，
     * key为业务标识，
     * value为该业务下需要绑定的菜单code码列表
     */
    Map<String, List<String>> channelResMap;

    /**
     * 部门信息数组
     */
    List<String>  deptList;

    /**
     * 客户编码
     */
    private Long customerNo;
}
