package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

/**
 * 预约记录入参
 *
 * <AUTHOR>
 * @date 2020-02-18 13:24
 **/
@Data
public class AppointmentServiceFormParam {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 订单渠道
     * 1-商城
     * 2-企销
     */
    private Integer orderChannel;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 预约单号
     */
    private String appointmentNo;

    /**
     * 体检人电话
     */
    private String userPhone;

    // 下面参数是查询详情使用
    /**
     * 体检SKU
     */
    private String skuNo;

    /**
     * 体检人名称
     */
    private String userName;
}
