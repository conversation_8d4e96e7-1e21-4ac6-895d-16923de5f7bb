package com.jd.health.medical.examination.export;

import com.jd.health.medical.examination.export.enterprise.dto.ProjectCustomParamDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 项目与参数关联关系
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompanyParamDTO implements Serializable {

    /**
     *
     */
    private String paramId;

    /**
     *
     */
    private String paramName;

    /**
     *
     */
    private String paramNameDesc;

    /**
     *
     */
    private List<ProjectCustomParamDTO> paramCustomValues;
}
