package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021-11-30
 */
@Data
public class EnterpriseSmsCustomParam implements Serializable {
    
    /**
     * 所属短信账号
     */
    private String smsAccount;
    
    /**
     * 短信模板id
     */
    private String templateId;
    
    /**
     * 短信参数id
     */
    private String paramId;
    
    /**
     * customValue对应的库里Id，删除时用
     */
    private Long customValueId;
    
    /**
     * 短信参数名称
     */
    private String paramName;
    
    /**
     * 自定义参数名
     */
    private String customName;
    
    /**
     * 自定义值
     */
    private String customValue;
    
    /**
     * 创建用户
     */
    private String createUser;
    
}
