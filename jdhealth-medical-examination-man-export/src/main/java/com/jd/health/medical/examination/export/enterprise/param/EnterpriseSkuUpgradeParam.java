package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/20 9:22
 */
@Data
public class EnterpriseSkuUpgradeParam {
    /**
     * 公司名称
     */
    private Long companyNo;
    /**
     *父级sku
     */
    private String parentSkuNo;
    /**
     *sku编号
     */
    private String skuNo;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * 升级包名称
     */
    private String skuUpgradeName;
    /**
     * 体检项目组编号
     */
    private Long groupNo;
    /**
     * 父级商品价格(企销价格),单位分
     */
    private BigDecimal parentSkuCompanyPrice;
    /**
     * 零售价格(京东价),单位分
     */
    private BigDecimal skuPersonPrice;
    /**
     * 升级包的支付价格
     */
    private BigDecimal skuUpgradePrice;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 最后一次更新人
     */
    private String updateUser;

    /**
     * 省ID
     */
    private Integer provinceId;

    /**
     * 市ID
     */
    private Integer cityId;

    /**
     * 查询数据版本
     */
    private Boolean searchNewVersion = false;

    /**
     * 购买须知
     */
    private String purchaseNotes;

    /**
     * 体检须知文案
     */
    private String examinationNotes;

    /**
     * sku列表
     */
    private List<String> skuList;
    
    /**
     * 升级包市场价格
     */
    private BigDecimal skuMarketPrice;
}
