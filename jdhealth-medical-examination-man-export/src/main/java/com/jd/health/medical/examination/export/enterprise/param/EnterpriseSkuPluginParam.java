package com.jd.health.medical.examination.export.enterprise.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 9:22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseSkuPluginParam implements Serializable {
    
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 大客户编码
     */
    private Long projectNo;
    
    /**
     * 企销套餐sku编号
     */
    private String skuNo;
    
    /**
     * plugin类型 1京东加项包
     */
    private Integer pluginType;
    
    /**
     * 插件sku
     */
    private String pluginSkuNo;
    
    /**
     * 插件sku别名
     */
    private String pluginSkuAlias;
    
    /**
     * 插件原价格,单位分
     */
    private BigDecimal pluginSkuPrice;
    
    /**
     * 插件企销价格,单位分
     */
    private BigDecimal pluginSkuQxPrice;
    
    /**
     * 插件企销描述
     */
    private String pluginSkuQxDesc;
    
    /**
     * 插件已购人数描述信息
     */
    private String pluginSalesMsg;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 最后一次更新时间
     */
    private Date updateTime;
    
    /**
     * 最后一次更新人
     */
    private String updateUser;
    
    /**
     * 是否有效 0无效 1有效
     */
    private Byte yn;
    
    /**
     * 插件sku原名
     */
    private String pluginSkuName;
    
    /**
     * 插件sku
     */
    private List<String> pluginSkuNoList;
    
    /**
     * 京东体检套餐groupNo
     */
    private Long groupNo;

    /**
     * needItems 需要返回item列表
     */
    private Boolean needItems = false;

    /**
     * 渠道商家编码
     */
    private Long channelNo;

    /**
     * 省ID
     */
    private Integer provinceId;

    /**
     * 市ID
     */
    private Integer cityId;

    /**
     * 0:自付费，1:企业付费
     */
    private Integer enterprisePayStatus;

}
