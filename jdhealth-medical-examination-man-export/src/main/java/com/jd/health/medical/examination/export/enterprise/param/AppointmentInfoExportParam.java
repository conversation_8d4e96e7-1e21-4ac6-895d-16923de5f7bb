package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约服务单信息导出参数
 *
 * <AUTHOR>
 * @date 2021/9/18 - 4:41 下午
 **/
@Data
public class AppointmentInfoExportParam implements Serializable {
    
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -15408918384055326L;
    
    /**
     * operatorId 操作人id,这里是erp账号
     */
    private String operatorId;
    
    /**
     * 操作类型 3-客户管理 4-项目管理销售 5-项目管理客户运营 6-项目运营
     */
    private Integer operationType;
    
    /**
     * 大客户编码
     */
    private Long companyNo;
    
    /**
     * 业务类型
     */
    private String businessCode;
    
    /**
     * 京东预约单号
     */
    private Long jdAppointmentId;
    
    /**
     * 订单号
     */
    private Long orderId;
    
    /**
     * 体检人电话
     */
    private String userPhone;
    
    /**
     * 预约来源 0自营 1企销
     */
    private Integer appointSource;
    
    /**
     * 预约单状态
     */
    private Integer orderStatus;
    
    /**
     * 供应商编号
     */
    private Long channelNo;
    
    /**
     * 预约证件编号
     */
    private String userCredentialNo;
    
    /**
     * 预约门店ID
     */
    private String storeId;
    
    /**
     * userPin
     */
    private String userPin;
    
    /**
     * 预约单创建开始日期
     */
    private Date createTimeBegin;
    
    /**
     * 预约单创建结束日期
     */
    private Date createTimeEnd;
    
    /**
     * 预约体检开始日期
     */
    private Date appointmentTimeBegin;
    
    /**
     * 预约体检结束日期
     */
    private Date appointmentTimeEnd;
    
    /**
     * 省份编码
     */
    private Integer provinceId;
    
    /**
     * 城市编码
     */
    private Integer cityId;
    
    /**
     * 区编码
     */
    private Integer countyId;
    
    /**
     * 预约类型 1-员工基础体检，2-升级套餐，3-亲属同购
     */
    private Integer appointType;
    
    /**
     * 门店名称
     */
    private String storeName;
    
    /**
     * 供应商套餐id
     */
    private String goodsId;
    
    /**
     * 是否有加项
     */
    private Integer addItemType;
}
