package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 运营人员Param类
 *
 * <AUTHOR>
 * @date 2020-01-08 10:12
 **/
@Data
public class EnterpriseOperatorParam implements Serializable {

    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 大客户编码列表
     */
    private List<Long> companyNos;
    /**
     * 大客户主键
     */
    private Long companyNo;
    /**
     * 运营人员pin
     */
    private String operatorPin;

    /**
     * 服务状态
     */
    private Integer serviceStatus;

    /**
     * 客户名称
     */
    private String companyName;

}
