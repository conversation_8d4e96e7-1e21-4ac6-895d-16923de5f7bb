package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 项目与自定义参数预设值关系
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/8
 */
@Data
public class CompanyParamCustomParam implements Serializable {

    /**
     *
     */
    private Long companyNo;

    /**
     *
     */
    private String paramId;

    /**
     *
     */
    private String paramName;

    /**
     *
     */
    private String customValue;

    /**
     *
     */
    private String createUser;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 账号
     */
    private String smsAccount;

}
