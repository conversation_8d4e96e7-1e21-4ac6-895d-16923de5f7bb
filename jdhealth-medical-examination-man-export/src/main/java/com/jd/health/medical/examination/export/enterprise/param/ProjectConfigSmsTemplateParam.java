package com.jd.health.medical.examination.export.enterprise.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 项目短信关联参数类
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6
 */
@Data
public class ProjectConfigSmsTemplateParam implements Serializable {

    /**
     * 项目id
     */
    private Long companyNo;

    /**
     * 短信账号
     */
    private String smsAccount;

    /**
     * 模板id
     */
    private String templateId;

    /**
     *
     */
    private String createUser;
}
