package com.jd.health.medical.examination.export.enterprise.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Please enter class description information
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/7 16:18
 */
@Getter
@AllArgsConstructor
@ToString
public enum CustomerSourceEnum {
    /**
     *
     */
    XFYL(1,"消费医疗"),
    
    /**
     * 企业业务
     */
    ENTERPRISE_BUSINESS(2,"企业业务")
    ;
    
    
    /**
     * 企业来源
     */
    private Integer type;
    /**
     * 描述
     */
    private String desc;
    
    /**
     *
     */
    public static boolean checkType(Integer type){
        CustomerSourceEnum[] values = CustomerSourceEnum.values();
        for (CustomerSourceEnum value : values) {
            if (value.getType().equals(type)){
                return true;
            }
        }
        return false;
    }
    
}
