package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/30 14:13
 */
@Data
public class EnterpriseSkuRelationUpgradeParam implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 1L;

    /**
     * sku编码
     */
    private String skuNo;

    /**
     * 大客户编号
     */
    private Long companyNo;

    /**
     * 父级体检套餐编码
     */
    private String parentSkuNo;

    /**
     * 是套餐升级还是亲属同购 1 套餐升级 2 亲属同购
     */
    private Integer companySkuType;
}
