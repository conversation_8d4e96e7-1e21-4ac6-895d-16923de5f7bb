package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseAccountParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseAccountDTO;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

/**
 * 企销账号信息
 * 接口废弃 新接口见
 * @see EnterpriseAccountSystemExportService
 * <AUTHOR>
 * @date 2020-01-10 15:39
 **/
@Deprecated
public interface EnterpriseAccountExportService {

    /**
     * 分页查询企销账号
     * @param pageParam
     * @return
     */
    @Deprecated
    JsfResult<PageInfo<EnterpriseAccountDTO>> queryEnterpriseAccountPage(EnterpriseAccountParam enterpriseAccountParam, PageParam pageParam);

    /**
     * 添加企销账号信息
     * @param enterpriseAccountParam
     * @return
     */
    @Deprecated
    JsfResult<Boolean> addEnterpriseAccount(EnterpriseAccountParam enterpriseAccountParam);

    /**
     * 删除企销账号信息
     * @param enterpriseAccountParam
     * @return
     */
    @Deprecated
    JsfResult<Boolean> deleteEnterpriseAccount(EnterpriseAccountParam enterpriseAccountParam);

}
