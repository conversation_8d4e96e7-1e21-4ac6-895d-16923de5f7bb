package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuRelationDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseManageParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuRelationParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.util.List;

/**
 * 企业亲属同购套餐ExportService
 *
 * <AUTHOR>
 * @date 2020-03-18 18:09
 **/
public interface EnterpriseSkuRelationExportService {

    /**
     * 更新亲属同购停启用状态
     * @param param
     * @return
     */
    JsfResult<Integer> updateRelationStatus(EnterpriseManageParam param);

    /**
     * 分页查询亲属套餐列表
     * @param skuRelationParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuRelationDTO>> querySkuRelationPage(EnterpriseSkuRelationParam skuRelationParam);

    /**
     * 分页查询已维护的sku（比较是否在客户企业的亲属同购套餐中）
     * @param enterpriseSkuParam
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuDTO>> querySkuInfoPage(EnterpriseSkuParam enterpriseSkuParam, PageParam pageParam);

    /**
     * 新增亲属套餐
     * @param skuRelationParam
     * @return
     */
    JsfResult<Integer> addSkuRelation(EnterpriseSkuRelationParam skuRelationParam);

    /**
     * 更新亲属套餐
     * @param skuRelationParam
     * @return
     */
    JsfResult<Integer> updateSkuRelation(EnterpriseSkuRelationParam skuRelationParam);

    /**
     * 删除亲属套餐
     * @param skuRelationParam
     * @return
     */
    JsfResult<Integer> deleteSkuRelation(EnterpriseSkuRelationParam skuRelationParam);

    /**
     * 获取亲属关系类型列表
     * @param type
     * @return
     */
    JsfResult<List<Integer>> queryRelationType(Long type);

    /**
     * 是否展示亲属同购菜单
     * @param companyNo
     * @return
     */
    JsfResult<Integer> queryRelationPurchase(Long companyNo);

    /**
     * 查询亲属套餐详情
     * @param
     * @return
     */
    JsfResult<EnterpriseSkuDTO> querySkuRelationDetail(Long companyNo, String skuNo);

    /**
     * 查询亲属同购套餐基本信息 -->companyNo 公司编号  必传
     * -->skuNo SKU编号 必传
     *
     * @param param
     * @return
     */
    JsfResult<EnterpriseSkuRelationDTO> querySkuRelationBaseInfo(EnterpriseSkuRelationParam param);

    /**
     * 批量更新亲属套餐
     *
     * @param skuRelationParam
     * @return
     */
    JsfResult<Boolean> batchUpdateSkuRelation(EnterpriseSkuRelationParam skuRelationParam);

    /**
     * 根据companyNo基础信息查询企销套餐池的亲属类型
     * @param companyNo
     * @return
     */
    JsfResult<List<Integer>> queryRelationTypeByCompanyNo(Long companyNo);

}
