package com.jd.health.medical.examination.export.enterprise.param;

import lombok.Data;


/**
 * 客户信息请求入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/29 17:36
 */
@Data
public class CustomerParam {
    /**
     * 客户信息编号
     */
    private String customerNo;
    /**
     * 客户简称
     */
    private String customerSimpleName;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户营业执照编号
     */
    private String customerLicenseNo;
    /**
     *
     */
    private Integer customerScale;
    /**
     * 客户地址
     */
    private String customerAddress;
    /**
     * 客户经纬度
     */
    private Double customerLng;
    /**
     * 客户经纬度
     */
    private Double customerLat;
    /**
     * 客户联系人
     */
    private String customerContacter;
    /**
     * 客户联系人电话
     */
    private String customerContacterPhone;
    /**
     * 客户联系人邮箱
     */
    private String customerContacterEmail;
    /**
     * 客户联系人固定电话
     */
    private String customerContacterTelephone;
    /**
     * 客户联系人备注
     */
    private String customerContacterDesc;
    /**
     * 客户运营
     */
    private String customerOperator;
    /**
     * 客户销售
     */
    private String customerSalesMan;
    /**
     * 客户销售电话
     */
    private String customerSalesPhone;
    /**
     * 客户描述
     */
    private String customerDesc;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 销售权限
     */
    private String customerSalesManAuth;
    /**
     * 客户运营权限
     */
    private String customerOperatorAuth;
    /**
     *
     */
    private Integer customerSource;
}
