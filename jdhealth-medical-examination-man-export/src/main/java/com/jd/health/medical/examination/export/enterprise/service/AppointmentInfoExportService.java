package com.jd.health.medical.examination.export.enterprise.service;

import com.jd.health.medical.examination.export.enterprise.param.AppointmentInfoExportParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

/**
 * 预约服务单信息对外接口
 * <AUTHOR>
 * @date 2021/9/18 - 4:38 下午
 **/
public interface AppointmentInfoExportService {
    
    /**
     * 异步导出客户项目相关信息
     * @param exportParam
     * @return
     */
    JsfResult<Boolean> asyncExportAppointmentInfo(AppointmentInfoExportParam exportParam);
}
