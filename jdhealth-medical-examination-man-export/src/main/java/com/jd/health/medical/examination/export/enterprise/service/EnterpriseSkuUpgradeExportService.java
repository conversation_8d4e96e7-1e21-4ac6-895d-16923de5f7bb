package com.jd.health.medical.examination.export.enterprise.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.*;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuUpgradeParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/20 9:16
 */
public interface EnterpriseSkuUpgradeExportService {
    /**
     * 根据公司编号和父级商品编号查询升级包信息
     *
     * @param companyNo   公司编号
     * @param parentSkuNo 父级商品编号
     * @return 商品升级包集合
     */
    JsfResult<List<EnterpriseSkuUpgradeDTO>> querySkuUpgrade(Long companyNo, String parentSkuNo);

    /**
     * 查询可添加的sku作为升级包
     * @param enterpriseSkuParam
     * @param pageParam
     * @return
     */
    JsfResult<PageInfo<EnterpriseSkuDTO>> querySkuInfoPage(EnterpriseSkuParam enterpriseSkuParam, PageParam pageParam);


    /**
     * 更新升级包信息
     *
     * @param enterpriseSkuUpgradeParam 升级包实体
     * @return 是否更新成功
     */
    JsfResult<Boolean> updateSkuUpgrade(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);

    /**
     * 批量更新升级包信息
     *
     * @param enterpriseSkuUpgradeParam 升级包实体
     * @return 是否更新成功
     */
    JsfResult<Boolean> batchUpdateSkuUpgrade(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);

    /**
     * 查看套餐差异项
     *
     * @param parentGroupNo 父级sku的套餐
     * @param groupNo       升级包sku的套餐
     * @return 差异项
     */
    JsfResult<List<EnterpriseGroupItemDTO>> compareGroupItem(Long parentGroupNo, Long groupNo);

    /**
     * 删除一个升级包信息
     *
     * @param companyNo   公司编号
     * @param parentSkuNo 父级商品编号
     * @param skuNo       升级包编号
     * @return 是否删除成功
     */
    JsfResult<Boolean> deleteSkuUpgrade(Long companyNo, String parentSkuNo, String skuNo);

    /**
     * 添加一个升级包
     * @param enterpriseSkuUpgradeParam 实体
     * @return 是否添加成功
     */
    JsfResult<Boolean> addSkuUpgrade(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);

    /**
     * 实时查询商品的京东价格
     */
    JsfResult<BigDecimal> querySkuPersonPrice(String skuNo);

    /**
     * 提供给员工端使用接口（旧版本）
     * 根据父级sku查询升级包信息 带有差异项
     * @param enterpriseSkuUpgradeParam 父级skuNo必传 公司编号必传
     * @return 升级包信息 包含和父级sku的差异项
     */
    JsfResult<List<EnterpriseSkuUpgradeInfoDTO>> querySkuUpgradeByParentSkuNo(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);

    /**
     * 提供给员工端使用接口
     * 根据父级sku查询升级包信息 带有差异项
     *
     * @param enterpriseSkuUpgradeParam 父级skuNo必传 公司编号必传
     * @return 升级包信息 包含和父级sku的差异项
     */
    JsfResult<List<EnterpriseSkuUpgradeInfoDTO>> querySkuUpgradeByParentSkuNoNew(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);

    /**
     * 提供给员工端使用接口，返回包装类
     * 根据父级sku查询升级包信息 带有差异项
     *
     * @param enterpriseSkuUpgradeParam 父级skuNo必传 公司编号必传
     * @return 升级包信息 包含和父级sku的差异项
     */
    JsfResult<EnterpriseSkuUpgradeAndPluginDTO> querySkuUpgradeDTOByParentSkuNo(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);

    /**
     * 查询套餐升级基本信息
     *
     * @param enterpriseSkuUpgradeParam --> companyNo 必传
     *                                  --> skuNo 必传
     *                                  -->parentNo 必传
     * @return 套餐升级基础信息
     */
    JsfResult<EnterpriseSkuUpgradeInfoDTO> querySkuUpgradeBaseInfo(EnterpriseSkuUpgradeParam enterpriseSkuUpgradeParam);
}
