package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.domain.ExaminationManItemRelevanceEntity;
import com.jd.health.medical.examination.domain.ExaminationManRelevanceEntity;
import com.jd.health.medical.examination.export.param.ExaminationManItemRelevancePageParam;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 体检项项目关联关系表(ExaminationManItemRelevance)表数据库访问层
 *
 * <AUTHOR> @since 2022-03-08 15:27:29
 */
public interface ExaminationManItemRelevanceDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ExaminationManItemRelevanceEntity queryById(Long id);

    /**
     * 新增数据
     *
     * @param examinationManItemRelevance 实例对象
     * @return 影响行数
     */
    Integer insert(ExaminationManItemRelevanceEntity examinationManItemRelevance);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ExaminationManItemRelevance> 实例对象列表
     * @return 影响行数
     */
    Integer insertBatch(@Param("entities") List<ExaminationManItemRelevanceEntity> entities);

    /**
     * deleteByRelevanceNo
     *
     * @param relevanceNo 主键
     * @return 影响行数
     */
    Integer deleteByRelevanceNo(@Param("relevanceNo") Long relevanceNo, @Param("operator") String operator);

    /**
     * deleteByRelevanceNoAndItemNo
     *
     * @param relevanceNo 主键
     * @return 影响行数
     */
    Integer deleteByRelevanceNoAndItemNo(@Param("relevanceNo") Long relevanceNo, @Param("itemNo") Long itemNo, @Param("operator") String operator);

    /**
     * queryPage
     *
     * @param pageParam
     * @return
     */
    List<ExaminationManItemRelevanceEntity> queryPage(ExaminationManItemRelevancePageParam pageParam);
    
    /**
     * itemNo反查relevanceNo
     *
     * @param itemNoList
     * @return
     */
    List<Long> queryRelevanceNoByItemNo(@Param("itemNoList") Set<Long> itemNoList);
    
    /**
     * relevanceNo查询item列表
     * @param relevanceList
     * @return
     */
    List<ExaminationManItemRelevanceEntity> queryItemListByRelevance(@Param("relevanceList") List<Long> relevanceList);
}

