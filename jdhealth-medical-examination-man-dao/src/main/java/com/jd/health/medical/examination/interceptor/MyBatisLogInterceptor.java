package com.jd.health.medical.examination.interceptor;

import com.jd.health.medical.examination.common.annotation.MyBatisLogMapper;
import com.jd.medicine.base.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Properties;

/**
 * @ClassName MyBatisLogInterceptor
 * @Description
 * <AUTHOR>
 * @Date 2023/11/21 14:38
 **/
@Intercepts({
		@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
		@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
				RowBounds.class, ResultHandler.class}),
		@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
				RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
})
@Component
@Slf4j
public class MyBatisLogInterceptor implements Interceptor {

	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
		try {
			String namespace = mappedStatement.getId();
			String className = namespace.substring(0, namespace.lastIndexOf("."));
			Class<?> clazz = Class.forName(className);
			Annotation annotation = clazz.getAnnotation(MyBatisLogMapper.class);
			if (null != annotation) {
				log.info("MyBatisLogInterceptor -> intercept method: " + mappedStatement.getId());
			}
		} catch (Throwable e) {
			log.error("MyBatisLogInterceptor -> intercept error, invocation={}", JsonUtil.toJSONString(invocation), e);
		}
		return invocation.proceed();
	}

	@Override
	public Object plugin(Object o) {
		return Plugin.wrap(o, this);
	}

	@Override
	public void setProperties(Properties properties) {

	}
}
