package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.common.annotation.MyBatisLogMapper;
import com.jd.health.medical.examination.domain.ReportConclusionStatistics;

import java.util.List;

/**
 * @ClassName ReportConclusionStatisticsDao
 * @Description
 * <AUTHOR>
 * @Date 2021/3/22 11:07
 **/
@MyBatisLogMapper
public interface ReportConclusionStatisticsDao {

	/**
	 * delete by primary key
	 *
	 * @param id primaryKey
	 * @return deleteCount
	 */
	int deleteByPrimaryKey(Long id);

	/**
	 * insert record to table
	 *
	 * @param record the record
	 * @return insert count
	 */
	int insert(ReportConclusionStatistics record);

	/**
	 * insert record to table selective
	 *
	 * @param record the record
	 * @return insert count
	 */
	int insertSelective(ReportConclusionStatistics record);

	/**
	 * select by primary key
	 *
	 * @param id primary key
	 * @return object by primary key
	 */
	ReportConclusionStatistics selectByPrimaryKey(Long id);

	/**
	 * update record selective
	 *
	 * @param record the updated record
	 * @return update count
	 */
	int updateByPrimaryKeySelective(ReportConclusionStatistics record);

	/**
	 * update record
	 *
	 * @param record the updated record
	 * @return update count
	 */
	int updateByPrimaryKey(ReportConclusionStatistics record);

	/**
	 * 根据参数查询数据列表
	 * @param record
	 * @return
	 */
	List<ReportConclusionStatistics> selectReportConclusionStatisticsByParam(ReportConclusionStatistics record);
}