package com.jd.health.medical.examination.repository.product;

import com.jd.health.medical.examination.domain.merchant.value.ChannelGoodsIdValue;
import com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity;

import java.util.List;
import java.util.Set;

/**
 * SKU商品仓储
 * @author: yang<PERSON><PERSON>
 * @date: 2022/4/13 3:50 下午
 * @version: 1.0
 */
public interface SkuRepository {

    /**
     * 根据skuNo查询商品
     * @param skuNo
     * @return
     */
    SkuInfoEntity queryBySkuNo(String skuNo);


    /**
     * 根据skuNos查询套餐集合
     * @param skuNos
     * @return
     */
    List<SkuInfoEntity> listBySkuNos(Set<String> skuNos, Integer skuSpecies);

    /**
     * 根据skuName查询商品
     * @param skuName
     * @return
     */
    List<SkuInfoEntity> listByName(String skuName);

    /**
     * 创建一个商品
     * @param sku
     * @return
     */
    Boolean createSku(SkuInfoEntity sku);

    /**
     * 获取配置了指定商家套餐的sku(可用的sku)
     * @param channelGoodsIdValue
     * @return
     */
    List<SkuInfoEntity> listByGoods(ChannelGoodsIdValue channelGoodsIdValue);

}
