package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.common.annotation.MyBatisLogMapper;
import com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics;

import java.util.List;

/**
 * @ClassName ReportCheckPeopleStatisticsDao
 * @Description
 * <AUTHOR>
 * @Date 2021/3/22 11:07
 **/
@MyBatisLogMapper
public interface ReportCheckPeopleStatisticsDao {

	/**
	 * delete by primary key
	 *
	 * @param id primaryKey
	 * @return deleteCount
	 */
	int deleteByPrimaryKey(Long id);

	/**
	 * insert record to table
	 *
	 * @param record the record
	 * @return insert count
	 */
	int insert(ReportCheckPeopleStatistics record);

	/**
	 * insert record to table selective
	 *
	 * @param record the record
	 * @return insert count
	 */
	int insertSelective(ReportCheckPeopleStatistics record);

	/**
	 * select by primary key
	 *
	 * @param id primary key
	 * @return object by primary key
	 */
	ReportCheckPeopleStatistics selectByPrimaryKey(Long id);

	/**
	 * update record selective
	 *
	 * @param record the updated record
	 * @return update count
	 */
	int updateByPrimaryKeySelective(ReportCheckPeopleStatistics record);

	/**
	 * update record
	 *
	 * @param record the updated record
	 * @return update count
	 */
	int updateByPrimaryKey(ReportCheckPeopleStatistics record);

	/**
	 * selectReportCheckPeopleStatisticsList
	 * @param record
	 * @return
	 */
	List<ReportCheckPeopleStatistics> selectReportCheckPeopleStatisticsList(ReportCheckPeopleStatistics record);
}