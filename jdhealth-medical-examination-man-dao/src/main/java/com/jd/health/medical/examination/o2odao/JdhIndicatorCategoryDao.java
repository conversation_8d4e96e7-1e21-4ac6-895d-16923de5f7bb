package com.jd.health.medical.examination.o2odao;

import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhIndicatorCategoryEntity;
import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhIndicatorEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * JDH体检名称和品牌体检名称关系
 *
 * <AUTHOR>
 * @date 2022-04-11 15:08:27
 */
public interface JdhIndicatorCategoryDao {

    /**
     * insert record to table
     *
     * @param jdhIndicatorCategoryEntityList the record
     * @return insert count
     */
    int batchInsert(@Param("jdhIndicatorCategoryEntityList") List<JdhIndicatorCategoryEntity> jdhIndicatorCategoryEntityList);

    /**
     *
     * @param itemNo
     * @return
     */
    JdhIndicatorCategoryEntity queryCategory(@Param("itemNo") Long itemNo);
}