package com.jd.health.medical.examination.o2odao;

import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceEntity;
import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceGroupEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * JDH体检名称和品牌体检名称关系
 *
 * <AUTHOR>
 * @date 2022-04-11 15:08:27
 */
public interface JdhServiceGroupDao {

    /**
     * insert record to table
     *
     * @param jdhServiceGroupEntityList the record
     * @return insert count
     */
    int batchInsert(@Param("jdhServiceGroupEntityList") List<JdhServiceGroupEntity> jdhServiceGroupEntityList);

    /**
     *
     * @param groupId
     * @return
     */
    JdhServiceGroupEntity queryServiceGroup(@Param("groupId") Long groupId);
}