package com.jd.health.medical.examination.repository.report.impl;

import com.jd.health.medical.examination.dao.report.DO.ExaminationBodyPartDO;
import com.jd.health.medical.examination.dao.report.ExaminationReportBodyPartDao;
import com.jd.health.medical.examination.repository.report.ExaminationReportBodyPartRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 身体部位系统仓储层实现
 *
 * <AUTHOR>
 * @description 身体部位系统仓储层实现
 * @date 2022/12/14 15:07
 */
@Service
public class ExaminationReportBodyPartRepositoryImpl implements ExaminationReportBodyPartRepository {

    /**
     * examinationReportBodyPartDao
     */
    @Autowired
    private ExaminationReportBodyPartDao examinationReportBodyPartDao;

    /**
     * 查询身体部位系统列表
     *
     * @param examinationBodyPartDO
     * @description 查询身体部位系统列表
     * <AUTHOR>
     * @date 2022/12/14 15:05
     */
    @Override
    public List<ExaminationBodyPartDO> queryBodyPartList(ExaminationBodyPartDO examinationBodyPartDO) {
        return examinationReportBodyPartDao.queryBodyPartList(examinationBodyPartDO);
    }
}
