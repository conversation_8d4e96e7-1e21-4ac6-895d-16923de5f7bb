package com.jd.health.medical.examination.repository.merchant.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.common.enums.VendorTypeEnum;
import com.jd.health.medical.examination.common.enums.YnStatusEnum;
import com.jd.health.medical.examination.common.util.ValidateUtil;
import com.jd.health.medical.examination.dao.BaseThirdItemRelDao;
import com.jd.health.medical.examination.dao.ThirdBaseItemAttrRelationDao;
import com.jd.health.medical.examination.dao.third.ThirdBaseItemDao;
import com.jd.health.medical.examination.domain.BaseThirdItemRel;
import com.jd.health.medical.examination.domain.bo.ManThirdItemPageQueryBO;
import com.jd.health.medical.examination.domain.bo.ThirdBaseItemAttrRelationQueryBO;
import com.jd.health.medical.examination.domain.bo.ThirdBaseItemBO;
import com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemAttrRelationEntity;
import com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity;
import com.jd.health.medical.examination.repository.merchant.ThirdBaseItemRepository;
import com.jd.medicine.base.common.util.CollectionUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商基础项目 仓储实现
 * @author: yangxiyu
 * @date: 2022/4/11 5:01 下午
 * @version: 1.0
 */
@Component
public class ThirdBaseItemRepositoryImpl implements ThirdBaseItemRepository {


    /**
     *
     */
    @Resource
    private ThirdBaseItemDao thirdBaseItemDao;

    /**
     * baseThirdItemRelDao
     */
    @Resource
    private BaseThirdItemRelDao baseThirdItemRelDao;

    @Resource
    private ThirdBaseItemAttrRelationDao thirdBaseItemAttrRelationDao;

    /**
     * @return
     */
    @Override
    public Map<String, Long> queryNameNoMap(ThirdBaseItemBO baseItemBO) {
        if (VendorTypeEnum.ALL.getType().equals(baseItemBO.getVendorType())) {
            baseItemBO.setVendorType(null);
        }
        List<ThirdBaseItemEntity> list =  thirdBaseItemDao.listAllNameNo(convert2ThirdBaseItemEntity(baseItemBO));
        if (CollectionUtil.isEmpty(list)){
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(ThirdBaseItemEntity::getItemName, k -> k.getItemNo()));
    }

    /**
     *
     * @param entityList
     * @return
     */
    @Override
    public Boolean createBatch(List<ThirdBaseItemEntity> entityList) {
        ValidateUtil.verifyCollectionNotEmpty(entityList, "entityList is empty");
        for (ThirdBaseItemEntity entity : entityList) {
            ValidateUtil.verifyStringNotEmpty(entity.getItemName(), "itemName is null");
            ValidateUtil.verifyObjectNonNull(entity.getItemNo(), "itemNo is null");
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
        }
        thirdBaseItemDao.insertBatch(entityList);
        return Boolean.TRUE;
    }

    /**
     * 更新供应商体检项目
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean updateThirdBaseItem(ThirdBaseItemEntity entity) {
        ValidateUtil.verifyObjectNonNull(entity.getItemNo(), "itemNo is null");
        thirdBaseItemDao.updateByThirdBaseItem(entity);
        return Boolean.TRUE;
    }

    /**
     * selectByParam
     *
     * @param baseItemBO
     * @return
     */
    @Override
    public PageInfo<ThirdBaseItemEntity> selectByParam(ThirdBaseItemBO baseItemBO) {
        PageHelper.startPage(baseItemBO.getPageNum(), baseItemBO.getPageSize());
        PageHelper.orderBy("create_time desc");
        if (VendorTypeEnum.ALL.getType().equals(baseItemBO.getVendorType())) {
            baseItemBO.setVendorType(null);
        }
        List<ThirdBaseItemEntity> list = thirdBaseItemDao.selectByParam(convert2ThirdBaseItemEntity(baseItemBO));
        return new PageInfo<>(list);
    }

    /**
     * 根据条件查询供应商项目
     *
     * @param baseItemBO
     * @return
     */
    @Override
    public List<ThirdBaseItemEntity> getThirdBaseItemList(ThirdBaseItemBO baseItemBO) {
        if (VendorTypeEnum.ALL.getType().equals(baseItemBO.getVendorType())) {
            baseItemBO.setVendorType(null);
        }
        return thirdBaseItemDao.getThirdBaseItemList(convert2ThirdBaseItemEntity(baseItemBO));
    }

    /**
     * getThirdBaseItemListByNos
     *
     * @param baseItemBO
     * @return
     */
    @Override
    public List<ThirdBaseItemEntity> getThirdBaseItemListByNames(ThirdBaseItemBO baseItemBO) {
        if (VendorTypeEnum.ALL.getType().equals(baseItemBO.getVendorType())) {
            baseItemBO.setVendorType(null);
        }
        return thirdBaseItemDao.getThirdBaseItemListByNames(baseItemBO);
    }

    /**
     * selectByParam
     *
     * @param baseItemBO
     * @return
     */
    @Override
    public ThirdBaseItemEntity getByParam(ThirdBaseItemBO baseItemBO) {
        if (VendorTypeEnum.ALL.getType().equals(baseItemBO.getVendorType())) {
            baseItemBO.setVendorType(null);
        }
        return thirdBaseItemDao.getByParam(convert2ThirdBaseItemEntity(baseItemBO));
    }

    /**
     * insert
     *
     * @param baseItemBO baseItemBO
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(ThirdBaseItemBO baseItemBO) {
        if (VendorTypeEnum.ALL.getType().equals(baseItemBO.getVendorType())) {
            baseItemBO.setVendorType(null);
        }
        if (CollectionUtil.isNotEmpty(baseItemBO.getIndicatorNos())) {
            List<BaseThirdItemRel> itemRelList = baseItemBO.getIndicatorNos().stream().map(baseItemNo -> {
                BaseThirdItemRel itemRel = new BaseThirdItemRel();
                itemRel.setBaseItemNo(Long.parseLong(baseItemNo));
                //itemRel.setBaseItemName(itemAliasBO.getBaseItemName());
                itemRel.setThirdItemNo(baseItemBO.getItemNo());
                //itemRel.setThirdItemName(itemAliasBO.getThirdItemName());
                itemRel.setSource(baseItemBO.getSource());
                //itemRel.setRemark(itemAliasBO.getRemark());
                itemRel.setYn(YnStatusEnum.YES.getCode());
                itemRel.setCreateUser(baseItemBO.getCreateUser());
                itemRel.setCreateTime(new Date());
                itemRel.setUpdateUser(baseItemBO.getUpdateUser());
                itemRel.setUpdateTime(new Date());
                return itemRel;
            }).collect(Collectors.toList());
            baseThirdItemRelDao.insertBatch(itemRelList);
        }
        return thirdBaseItemDao.insert(convert2ThirdBaseItemEntity(baseItemBO)) > 0;
    }

    /**
     * 删除
     *
     * @param baseItemBO baseItemBO
     * @return boolean
     */
    @Override
    public Boolean delete(ThirdBaseItemBO baseItemBO) {
        return thirdBaseItemDao.delete(convert2ThirdBaseItemEntity(baseItemBO)) > 0;
    }

    /**
     * selectBaseThirdItemRelByParam
     *
     * @param record
     * @return
     */
    @Override
    public List<BaseThirdItemRel> selectBaseThirdItemRelByParam(BaseThirdItemRel record) {
        return baseThirdItemRelDao.selectByParam(record);
    }

    /**
     * 分页查询商家项目列表
     *
     * @param manThirdItemPageQueryBO
     * @return
     */
    @Override
    public PageInfo<ThirdBaseItemEntity> queryThirdItemAndCategoryPageInfo(ManThirdItemPageQueryBO manThirdItemPageQueryBO) {
        PageHelper.startPage(manThirdItemPageQueryBO.getPageNum(), manThirdItemPageQueryBO.getPageSize());
        PageHelper.orderBy("create_time desc");
        List<ThirdBaseItemEntity> list = thirdBaseItemDao.getThirdBaseItemListByItemNos(manThirdItemPageQueryBO);
        return new PageInfo<>(list);
    }

    /**
     * 查询需要清洗可换项字段的POP商家项目数据
     *
     * @return
     */
    @Override
    public List<ThirdBaseItemEntity> selectCleanThirdBaseItemReplace() {
        return thirdBaseItemDao.selectCleanThirdBaseItemReplace();
    }

    /**
     * 查询项目下的属性列表
     *
     * @param relationQueryBO
     * @return
     */
    @Override
    public List<ThirdBaseItemAttrRelationEntity> queryThirdItemCategoryList(ThirdBaseItemAttrRelationQueryBO relationQueryBO) {
        return thirdBaseItemAttrRelationDao.queryThirdBaseItemAttrList(relationQueryBO);
    }

    /**
     * 保存商家项目信息
     *
     * @param thirdBaseItemEntity
     * @param relationEntityList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertThirdItemBase(ThirdBaseItemEntity thirdBaseItemEntity, List<ThirdBaseItemAttrRelationEntity> relationEntityList) {
        thirdBaseItemDao.insert(thirdBaseItemEntity);
        thirdBaseItemAttrRelationDao.insertBatch(relationEntityList);
    }

    /**
     * convert2ThirdBaseItemEntity
     *
     * @param baseItemBO
     * @return
     */
    private ThirdBaseItemEntity convert2ThirdBaseItemEntity(ThirdBaseItemBO baseItemBO) {
        ThirdBaseItemEntity thirdBaseItemEntity = new ThirdBaseItemEntity();
        thirdBaseItemEntity.setItemNo(baseItemBO.getItemNo());
        thirdBaseItemEntity.setItemName(baseItemBO.getItemName());
        thirdBaseItemEntity.setItemType(baseItemBO.getItemType());
        thirdBaseItemEntity.setMatchStatus(baseItemBO.getMatchStatus());
        thirdBaseItemEntity.setCreateUser(baseItemBO.getCreateUser());
        thirdBaseItemEntity.setUpdateUser(baseItemBO.getUpdateUser());
        thirdBaseItemEntity.setVendorType(baseItemBO.getVendorType());
        thirdBaseItemEntity.setItemPrice(baseItemBO.getItemPrice());
        thirdBaseItemEntity.setItemCategory(baseItemBO.getItemCategory());
        thirdBaseItemEntity.setSkuCategory(baseItemBO.getSkuCategory());
        thirdBaseItemEntity.setSkuThirdCategory(baseItemBO.getSkuThirdCategory());
        thirdBaseItemEntity.setChannelNo(baseItemBO.getChannelNo());
        thirdBaseItemEntity.setItemExtra(baseItemBO.getItemExtra());
        thirdBaseItemEntity.setReplaceItemDesc(baseItemBO.getReplaceItemDesc());
        return thirdBaseItemEntity;
    }
}
