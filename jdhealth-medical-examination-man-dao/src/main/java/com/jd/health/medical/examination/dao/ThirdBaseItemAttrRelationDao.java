package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.domain.bo.ThirdBaseItemAttrRelationQueryBO;
import com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemAttrRelationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @InterfaceName:项目属性关系表DAO
 * @Description: TODO
 * @Author: yaoqinghai
 * @Date: 2023/6/7 01:09
 * @Vserion: 1.0
 **/
public interface ThirdBaseItemAttrRelationDao {

    /**
     * 查询商家项目属性列表信息
     *
     * @param relationQueryBO
     * @return
     */
    List<ThirdBaseItemAttrRelationEntity> queryThirdBaseItemAttrList(ThirdBaseItemAttrRelationQueryBO relationQueryBO);

    /**
     * 批量保存商家
     * @param relationEntityList
     */
    void insertBatch(@Param("relationEntityList") List<ThirdBaseItemAttrRelationEntity> relationEntityList);
}
