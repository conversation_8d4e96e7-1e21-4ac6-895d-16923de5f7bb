package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManBaseThirdItemRelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2022/4/9 17:07
 */
public interface ExaminationManBaseThirdItemRelDao {
    
    /**
     * insert
     * @param entity
     * @return
     */
    int insert(ExaminationManBaseThirdItemRelEntity entity);
    
    /**
     * 根据ID更新
     * @param id
     * @return
     */
    Integer updateById(Long id);
    
}
