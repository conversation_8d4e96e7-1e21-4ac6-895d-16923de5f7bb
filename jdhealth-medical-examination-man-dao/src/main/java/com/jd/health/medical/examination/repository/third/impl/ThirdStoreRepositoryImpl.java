package com.jd.health.medical.examination.repository.third.impl;

import com.jd.health.medical.examination.common.util.AssertUtils;
import com.jd.health.medical.examination.dao.third.ThirdStoreDao;
import com.jd.health.medical.examination.domain.third.ThirdStoreEntity;
import com.jd.health.medical.examination.repository.third.ThirdStoreRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 *  供应商门店仓储
 * @author: yang<PERSON>yu
 * @date: 2022/8/16 4:05 下午
 * @version: 1.0
 */
@Component
public class ThirdStoreRepositoryImpl implements ThirdStoreRepository {

    /** */
    @Resource
    private ThirdStoreDao thirdStoreDao;

    /**
     * 查询门店集合
     * @param storeIds
     * @param channelNo
     * @return
     */
    @Override
    public List<ThirdStoreEntity> listByStoreIds(Set<String> storeIds, Long channelNo) {
        AssertUtils.listNotEmpty(storeIds);
        AssertUtils.nonNull(channelNo);
        return thirdStoreDao.selectThirdStoreByIds(storeIds, channelNo);
    }
}
