package com.jd.health.medical.examination.interceptor;

import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.medicine.base.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.apache.ibatis.reflection.wrapper.DefaultObjectWrapperFactory;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Properties;

import static com.jd.health.medical.examination.common.util.OptimisticFilterHelper.OPTIMISTIC_THREAD_LOCAL;


/**
 * @ClassName OptimisticFilteringInterceptor
 * @Description 自定义筛选过滤器
 * <AUTHOR> @Date
 **/
@Intercepts(
        {
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        })
@Component
@Slf4j
public class OptimisticFilteringInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        try {
            if (OPTIMISTIC_THREAD_LOCAL.get() == null){
                return invocation.proceed();
            }

            Object[] args = invocation.getArgs();
            MappedStatement ms = null;
            if (args[0] instanceof MappedStatement) {
                ms = (MappedStatement) args[0];
            }
            Object parameter = args[1];
            BoundSql boundSql = null;
            //由于逻辑关系，只会进入一次
            if(args.length == 4){
                //4 个参数时
                boundSql = ms.getBoundSql(parameter);
            } else {
                //6 个参数时
                if(args[5] instanceof BoundSql) {
                    boundSql = (BoundSql) args[5];
                }
            }

            //获取到原始sql语句  并组装新sql
            String mSql = boundSql.getSql() + " and " +
                    OPTIMISTIC_THREAD_LOCAL.get().getFieldValue() + " " +
                    OPTIMISTIC_THREAD_LOCAL.get().getOperateType() + " (" +
                    StringUtils.join(OPTIMISTIC_THREAD_LOCAL.get().getSearchItems(), ",") + ")";

            resetSql2Invocation(invocation, mSql);

            return invocation.proceed();
        } catch (Exception e){
            log.error("OptimisticFilteringInterceptor -> intercept error:",e);
            throw new BusinessException(BaseErrorCode.UNKNOWN_ERROR);
        } finally{
            OPTIMISTIC_THREAD_LOCAL.remove();
        }
    }

    @Override
    public Object plugin(Object o) {
        return Plugin.wrap(o, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    // 定义一个内部辅助类，作用是包装sq
    static class BoundSqlSqlSource implements SqlSource {

        private BoundSql boundSql;
        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }
        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }

    /**
     * 包装sql后，重置到invocation中
     * @param invocation
     * @param sql
     * @throws SQLException
     */
    public static void resetSql2Invocation(Invocation invocation, String sql) throws SQLException {

        final Object[] args = invocation.getArgs();
        MappedStatement statement = null;
        if(args[0] instanceof MappedStatement){
            statement = (MappedStatement) args[0];
        }
        Object parameterObject = args[1];
        BoundSql boundSql = statement.getBoundSql(parameterObject);

        //ti'hua
        MappedStatement newStatement = newMappedStatement(statement, new BoundSqlSqlSource(boundSql));
        MetaObject msObject =  MetaObject.forObject(newStatement, new DefaultObjectFactory(), new DefaultObjectWrapperFactory(),new DefaultReflectorFactory());
        msObject.setValue("sqlSource.boundSql.sql", sql);
        args[0] = newStatement;
    }

    /**
     * @param ms
     * @param newSqlSource
     * @return
     */
    private static MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder =
                new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length != 0) {
            StringBuilder keyProperties = new StringBuilder();
            for (String keyProperty : ms.getKeyProperties()) {
                keyProperties.append(keyProperty).append(",");
            }
            keyProperties.delete(keyProperties.length() - 1, keyProperties.length());
            builder.keyProperty(keyProperties.toString());
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());

        return builder.build();
    }
}
