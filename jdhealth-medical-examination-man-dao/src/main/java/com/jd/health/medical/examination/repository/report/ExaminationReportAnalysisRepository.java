package com.jd.health.medical.examination.repository.report;

import com.jd.health.medical.examination.domain.enterprise.entity.ExaminationReportAnalysisEntity;

import java.util.List;

/**
 * 体检报告检后仓储
 * @description 体检报告检后仓储
 * <AUTHOR>
 * @date 2022/11/15 17:27
 */
public interface ExaminationReportAnalysisRepository {

    /**
     *
     * @param examinationReportAnalysisEntity
     * @return
     */
    void saveReportAnalysis(ExaminationReportAnalysisEntity examinationReportAnalysisEntity);

    /**
     *
     * @param examinationReportAnalysisEntity
     * @return
     */
    List<ExaminationReportAnalysisEntity> queryReportAnalysisList(ExaminationReportAnalysisEntity examinationReportAnalysisEntity);

    /**
     *
     * @param examinationReportAnalysisEntity
     * @return
     */
    ExaminationReportAnalysisEntity queryLastReportAnalysis(ExaminationReportAnalysisEntity examinationReportAnalysisEntity);
}
