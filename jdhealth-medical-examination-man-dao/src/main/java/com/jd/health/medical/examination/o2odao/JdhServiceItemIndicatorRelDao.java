package com.jd.health.medical.examination.o2odao;

import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhIndicatorCategoryEntity;
import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceItemIndicatorRelEntity;
import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceItemRelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * JDH体检名称和品牌体检名称关系
 *
 * <AUTHOR>
 * @date 2022-04-11 15:08:27
 */
public interface JdhServiceItemIndicatorRelDao {

    /**
     * insert record to table
     *
     * @param jdhServiceItemIndicatorRelEntityList the record
     * @return insert count
     */
    int batchInsert(@Param("jdhServiceItemIndicatorRelEntityList") List<JdhServiceItemIndicatorRelEntity> jdhServiceItemIndicatorRelEntityList);

    List<JdhServiceItemIndicatorRelEntity> queryByList(@Param("itemNo") Long itemNo);
}