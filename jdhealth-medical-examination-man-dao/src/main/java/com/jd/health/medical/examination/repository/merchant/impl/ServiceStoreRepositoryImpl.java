package com.jd.health.medical.examination.repository.merchant.impl;

import com.jd.health.medical.examination.common.enums.SkuSpeciesEnum;
import com.jd.health.medical.examination.common.enums.ThirdStoreStatusEnum;
import com.jd.health.medical.examination.common.enums.YnStatusEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataStatusEnum;
import com.jd.health.medical.examination.common.util.AssertUtils;
import com.jd.health.medical.examination.common.util.EsBuilderUtil;
import com.jd.health.medical.examination.dao.SkuStoreDao;
import com.jd.health.medical.examination.dao.es.datasource.EsDataSource;
import com.jd.health.medical.examination.domain.bo.SkuStoreInfoBO;
import com.jd.health.medical.examination.domain.merchant.bo.ListBySkuAreaBO;
import com.jd.health.medical.examination.domain.merchant.value.ChannelGoodsIdValue;
import com.jd.health.medical.examination.domain.merchant.value.ChannelStoreIdValue;
import com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity;
import com.jd.health.medical.examination.repository.merchant.ServiceStoreRepository;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * serviceStore 仓储服务
 * @author: yangxiyu
 * @date: 2022/3/21 10:11 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class ServiceStoreRepositoryImpl implements ServiceStoreRepository {

    /** ES默认的分页长度 */
    private static final Integer DEFAULT_PAGE_SIZE = 5000;
    /** */
    @Resource
    private SkuStoreDao skuStoreDao;

    /**
     * indexName
     */
    @Value("${es.storeMap.IndexName}")
    protected String indexName;
    /**
     * typeName
     */
    @Value("${es.storeMap.TypeName}")
    protected String typeName;

    /**
     * esDataSource
     */
    @Resource
    EsDataSource esDataSource;
    /**
     * 体检普通商品根据skuNo修改groupNo
     * @param skuNo
     * @param groupNo
     * @return
     */
    @Override
    public Boolean updateGroupNo(String skuNo, Long groupNo) {
        skuStoreDao.updateSkuStoreGroupNo(skuNo, groupNo);
        return Boolean.TRUE;
    }
    /**
     * 获取sku生效的门店数量
     */
    @Override
    public Integer queryStartStoreCount(String skuNo) {
        return skuStoreDao.selectStartCountBySkuNo(skuNo);
    }


    /**
     * 根据加项包套餐编号获取门店
     * @param channelNo
     * @param parentGoodsId
     * @param pluginGoodsId
     * @return
     */
    @Override
    public List<SkuStoreEntity> listByPluginGoodsId(Long channelNo, String parentGoodsId, String pluginGoodsId) {
        AssertUtils.nonNull(channelNo);
        AssertUtils.hasText(parentGoodsId);
        AssertUtils.hasText(pluginGoodsId);
        return skuStoreDao.listPluginSkuStoreByGoods(channelNo, parentGoodsId, pluginGoodsId);
    }

    /**
     *
     * @param areaBO
     * @return
     */
    @Override
    public List<SkuStoreInfoBO> listBySkuArea(ListBySkuAreaBO areaBO) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        EsBuilderUtil.appendMustTermNotNull(areaBO.getSkuNo(), "skuNo", boolQueryBuilder);
        EsBuilderUtil.appendMustTermNotNull(areaBO.getGroupNo(), "groupNo", boolQueryBuilder);
        EsBuilderUtil.appendMustTermNotNull(ThirdStoreStatusEnum.ON.getCode(), "thirdStoreStatus", boolQueryBuilder);
        EsBuilderUtil.appendMustTermNotNull(YnStatusEnum.YES.getCode(), "status", boolQueryBuilder);
        EsBuilderUtil.appendMustTermNotNull(areaBO.getProvinceId(), "provinceId", boolQueryBuilder);
        EsBuilderUtil.appendMustTermNotNull(areaBO.getCityId(), "cityId", boolQueryBuilder);
        EsBuilderUtil.appendMustTermNotNull(areaBO.getCountyId(), "countyId", boolQueryBuilder);

        //如果入参传了加项包Sku列表，将数据放入查询条件
        if (CollectionUtil.isNotEmpty(areaBO.getAddItemSkus())) {
            areaBO.getAddItemSkus().forEach(additionSkuNo -> {
                EsBuilderUtil.appendMustTermNotNull(additionSkuNo, "additionSkus", boolQueryBuilder);
            });
        }
        log.info("ServiceStoreRepository->listBySkuArea,boolQueryBuilder={}",boolQueryBuilder.toString());

        SearchRequestBuilder requestBuilder = esDataSource.getClient().prepareSearch(indexName).setTypes(typeName)
                .setQuery(boolQueryBuilder)
                .setFrom(NumberUtil.INTEGER_ZERO)
                .setSize(DEFAULT_PAGE_SIZE)
                .setSearchType(SearchType.DFS_QUERY_THEN_FETCH);

        log.info("ServiceStoreRepository->listBySkuArea 搜索语句：" + requestBuilder.toString());
        SearchResponse sResponse = requestBuilder.get();
        SearchHits hits = sResponse.getHits();
        List<SkuStoreInfoBO> list = new ArrayList<>();
        for (SearchHit hit : hits) {
            String json = hit.getSourceAsString();
            SkuStoreInfoBO skuStoreInfoBO = JsonUtil.parseObject(json, SkuStoreInfoBO.class);
            list.add(skuStoreInfoBO);
        }
        return list;
    }

    /**
     * 根据套餐编码下架skuStore
     * @return
     */
    @Override
    public Integer offByGoodsIds(Long channelNo, Set<String> goodsIds, SkuSpeciesEnum goodsType) {
        log.info("ServiceStoreRepositoryImpl -> offByGoodsIds channelNo={}, goodsIds={}", channelNo, JsonUtil.toJSONString(goodsIds));
        AssertUtils.nonNull(channelNo);
        AssertUtils.listNotEmpty(goodsIds);
        //根据sku门店表中，对应套餐的所有门店记录失效yn=0
        return skuStoreDao.batchOffByGoods(goodsIds, channelNo, ThirdDataStatusEnum.ON.getCode(), goodsType.getTypeNo());
    }

}
