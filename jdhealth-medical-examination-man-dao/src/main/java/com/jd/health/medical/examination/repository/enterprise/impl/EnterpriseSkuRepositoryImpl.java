package com.jd.health.medical.examination.repository.enterprise.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.dao.ExaminationManGroupDao;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuDao;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuEntity;
import com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity;
import com.jd.health.medical.examination.repository.enterprise.EnterpriseSkuRepository;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.CollectionUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 企销商品仓储
 * @author: yang<PERSON>yu
 * @date: 2022/7/3 2:56 下午
 * @version: 1.0
 */
@Component
public class EnterpriseSkuRepositoryImpl implements EnterpriseSkuRepository {

    /** */
    @Resource
    private EnterpriseSkuDao enterpriseSkuDao;
    /** */
    @Resource
    private ExaminationManGroupDao examinationManGroupDao;

    /**
     * 分页查询企销配置的基础套餐
     * @param entity
     * @param pageSize
     * @param pageNum
     * @return
     */
    @Override
    public Page pageSku(EnterpriseSkuEntity entity, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<EnterpriseSkuEntity> entities = enterpriseSkuDao.pageSkuByCompanyNo(entity);
        if (CollectionUtil.isEmpty(entities)){
            return new Page(pageNum, pageSize);
        }

        Set<Long> groupNos = entities.stream().map(EnterpriseSkuEntity::getGroupNo).collect(Collectors.toSet());
        List<ExaminationManGroupEntity> groupEntities = examinationManGroupDao.selectListByGroupNos(groupNos);
        Map<Long, ExaminationManGroupEntity> groupMap = groupEntities.stream().collect(Collectors.toMap(ExaminationManGroupEntity::getGroupNo, Function.identity(), (o, n) -> n));
        for (EnterpriseSkuEntity sku : entities) {
            ExaminationManGroupEntity groupEntity = groupMap.get(sku.getGroupNo());
            sku.setGroup(groupEntity);
        }
        if (entities instanceof  Page){
            return  (Page)entities;
        }else{
            throw new BusinessException(BaseErrorCode.UNKNOWN_ERROR);
        }
    }
}
