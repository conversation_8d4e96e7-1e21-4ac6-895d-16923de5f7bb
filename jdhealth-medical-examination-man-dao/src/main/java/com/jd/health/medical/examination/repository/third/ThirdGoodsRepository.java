package com.jd.health.medical.examination.repository.third;

import com.jd.health.medical.examination.domain.third.ThirdGoodsEntity;

import java.util.List;
import java.util.Set;

/**
 * 供应商套餐数据仓储
 * @author: yang<PERSON><PERSON>
 * @date: 2022/7/29 2:26 下午
 * @version: 1.0
 */
public interface ThirdGoodsRepository {

    /**
     *
     * @param goodsIds
     * @param channelNo
     * @return
     */
    List<ThirdGoodsEntity> listGoodsIds(Set<String> goodsIds, Long channelNo);

}
