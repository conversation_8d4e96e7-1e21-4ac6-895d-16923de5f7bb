package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.domain.report.entity.basic.ExaminationManThirdItemRelEntity;
import com.jd.health.medical.examination.export.param.report.ExaminationManThirdItemRelPageParam;

import java.util.List;

/**
 * 第三方报告项目、指标关系(ExaminationManThirdItemRel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-03 15:35:09
 */
public interface ExaminationManThirdItemRelDao {


    /**
     * 逻辑删除关系
     *
     * @param itemNo 实例对象
     * @return 影响行数
     */
    int deleteByItemNo(Long itemNo);

    /**
     * 新增数据
     *
     * @param examinationManThirdItemRelEntity 实例对象
     * @return 影响行数
     */
    int insert(ExaminationManThirdItemRelEntity examinationManThirdItemRelEntity);

    /**
     * 分页查询关系列表
     * @param pageParam
     * @return
     */
    List<ExaminationManThirdItemRelEntity> queryPage(ExaminationManThirdItemRelPageParam pageParam);
}

