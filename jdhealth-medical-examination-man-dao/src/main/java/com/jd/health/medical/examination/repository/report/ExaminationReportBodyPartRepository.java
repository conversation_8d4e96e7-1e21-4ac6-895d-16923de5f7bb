package com.jd.health.medical.examination.repository.report;

import com.jd.health.medical.examination.dao.report.DO.ExaminationBodyPartDO;

import java.util.List;

/**
 * 身体部位仓储数据
 *
 * <AUTHOR>
 * @description 身体部位仓储数据
 * @date 2022/12/14 15:05
 */
public interface ExaminationReportBodyPartRepository {

    /**
     * 身体部位仓储服务
     *
     * @description 身体部位仓储服务
     * <AUTHOR>
     * @date 2022/12/14 15:05
     */
    List<ExaminationBodyPartDO> queryBodyPartList(ExaminationBodyPartDO examinationBodyPartDO);

}
