package com.jd.health.medical.examination.repository.report.impl;

import com.jd.health.medical.examination.dao.report.ExaminationReportAnalysisDao;
import com.jd.health.medical.examination.domain.enterprise.entity.ExaminationReportAnalysisEntity;
import com.jd.health.medical.examination.repository.report.ExaminationReportAnalysisRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 检后服务
 *
 * <AUTHOR>
 * @description 检后服务
 * @date 2022/11/15 17:30
 */
@Service
public class ExaminationReportAnalysisRepositoryImpl implements ExaminationReportAnalysisRepository {

    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(ExaminationReportAnalysisRepositoryImpl.class);

    /**
     * 检后报告dao
     */
    @Autowired
    private ExaminationReportAnalysisDao examinationReportAnalysisDao;

    /**
     * 保存解析报告
     * @param examinationReportAnalysisEntity
     * @return
     */
    @Override
    public void saveReportAnalysis(ExaminationReportAnalysisEntity examinationReportAnalysisEntity) {
        log.info("ExaminationReportAnalysisRepositoryImpl -> saveReportAnalysis start, examinationReportAnalysisEntity={}", examinationReportAnalysisEntity);
        Integer count = examinationReportAnalysisDao.saveReportAnalysis(examinationReportAnalysisEntity);
//        if (count >= 1) {
//            //保存成功后将其他报告更新最后标识.如果是保存标准数据则不更新
//            if (CommonConstant.THREE==examinationReportAnalysisEntity.getLastFlag()) {
//                return;
//            }
//            examinationReportAnalysisEntity.setLastFlag(CommonConstant.TWO);
//            Integer integer = examinationReportAnalysisDao.updateReportAnalysis(examinationReportAnalysisEntity);
//            return;
//        }
//        throw new BusinessException(BaseErrorCode.DAO_CALL_ERROR);
    }

    /**
     * 查询解析报告列表
     * @param examinationReportAnalysisEntity
     * @return
     */
    @Override
    public List<ExaminationReportAnalysisEntity> queryReportAnalysisList(ExaminationReportAnalysisEntity examinationReportAnalysisEntity) {
        List<ExaminationReportAnalysisEntity> list = examinationReportAnalysisDao.queryReportAnalysisList(examinationReportAnalysisEntity);
        return list;
    }

    /**
     * 查询最新解析报告
     * @param examinationReportAnalysisEntity
     * @return
     */
    @Override
    public ExaminationReportAnalysisEntity queryLastReportAnalysis(ExaminationReportAnalysisEntity examinationReportAnalysisEntity) {
        ExaminationReportAnalysisEntity entity = examinationReportAnalysisDao.queryLastReportAnalysis(examinationReportAnalysisEntity);
        return entity;
    }
}
