package com.jd.health.medical.examination.repository.third;

import com.jd.health.medical.examination.export.param.ThirdGoodsParam;
import com.jd.health.medical.examination.export.param.third.BatchPushGoodsStoreRelParam;
import com.jd.health.medical.examination.export.param.third.PushGoodsRelParam;

/**
 * 数据推送记录保存
 * @author: yang<PERSON><PERSON>
 * @date: 2023/1/15 8:35 下午
 * @version: 1.0
 */
public interface PushDataRecordRepository {

    /**
     * 保存批量推送套餐门店关系
     * @param param
     */
    void saveRecord(BatchPushGoodsStoreRelParam param);

    /**
     * 保存推送套餐的记录
     * @param param
     */
    void saveRecord(ThirdGoodsParam param);

    /**
     * 保存推送套餐关系的记录
     * @param param
     */
    void saveRecord(PushGoodsRelParam param);
}
