package com.jd.health.medical.examination.repository.third.impl;

import com.jd.health.medical.examination.common.factory.ExecutorPoolFactory;
import com.jd.health.medical.examination.common.factory.ThreadPoolConfigEnum;
import com.jd.health.medical.examination.dao.es.datasource.EsDataSource;
import com.jd.health.medical.examination.domain.common.entity.ProviderDataRecord;
import com.jd.health.medical.examination.export.param.ThirdGoodsParam;
import com.jd.health.medical.examination.export.param.third.BatchPushGoodsStoreRelParam;
import com.jd.health.medical.examination.export.param.third.PushGoodsRelParam;
import com.jd.health.medical.examination.repository.third.PushDataRecordRepository;
import com.jd.medicine.base.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.function.Supplier;

/**
 * @author: yangxiyu
 * @date: 2023/1/15 8:35 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class PushDataRecordRepositoryImpl implements PushDataRecordRepository {

    /** */
    private static final Integer GOODS_TYPE = 1;
    /** */
    private static final Integer STORE_TYPE = 2;
    /** */
    private static final Integer GOODS_STORE_TYPE = 3;
    /** */
    private static final Integer GOODS_RELATION_TYPE = 4;
    /** */
    private static final Integer BATCH_GOODS_STORE_TYPE = 5;
    /** */
    private static final Integer BATCH_GOODS_RELATION_TYPE = 6;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;
    @Resource
    private EsDataSource esDataSource;
    /** indexName */
    @Value("${es.provider.push.data.IndexName}")
    protected String indexName;
    /** typeName */
    @Value("${es.provider.push.data.TypeName}")
    protected String typeName;

    /**
     * 保存批量推送套餐门店关系
     * @param param
     */
    @Override
    public void saveRecord(BatchPushGoodsStoreRelParam param) {
        accept(()->{
            ProviderDataRecord record = new ProviderDataRecord();
            record.setData(JsonUtil.toJSONString(param));
            record.setChannelNo(param.getChannelType());
            record.setDateType(BATCH_GOODS_STORE_TYPE);
            record.setPushTime(new Date());
            record.setSignId(param.getGoodsId());
            record.setPushUser(String.valueOf(param.getChannelType()));
            return record;
        });
    }

    /**
     * 保存推送套餐的记录
     * @param param
     */
    @Override
    public void saveRecord(ThirdGoodsParam param) {
        accept(()->{
            ProviderDataRecord record = new ProviderDataRecord();
            record.setData(JsonUtil.toJSONString(param));
            record.setChannelNo(param.getChannelType());
            record.setDateType(GOODS_TYPE);
            record.setPushTime(new Date());
            record.setSignId(param.getGoodsId());
            record.setPushUser(String.valueOf(param.getChannelType()));
            return record;
        });
    }

    /**
     * 保存推送套餐关系的记录
     * @param param
     */
    @Override
    public void saveRecord(PushGoodsRelParam param) {
        accept(()->{
            ProviderDataRecord record = new ProviderDataRecord();
            record.setData(JsonUtil.toJSONString(param));
            record.setChannelNo(param.getChannelType());
            record.setDateType(BATCH_GOODS_RELATION_TYPE);
            record.setPushTime(new Date());
            record.setSignId(param.getGoodsId());
            record.setPushUser(String.valueOf(param.getChannelType()));
            return record;
        });
    }


    /**
     *
     * @param supplier
     * @param <T>
     */
    private <T>void accept(Supplier<ProviderDataRecord> supplier){
        executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
            try {
                log.info("ProviderDataRecordRepository->accept  start");
                doSave(supplier.get());
            }catch (Throwable e){
                log.info("ProviderDataRecordRepository->accept error", e);
            }

        });
    }
    /**
     *
     * @param record
     */
    private void doSave(ProviderDataRecord record){
        String json = JsonUtil.toJSONString(record);
        log.info("ProviderDataRecordRepository->doSave record={}", json);
        try{
            esDataSource.index(indexName, typeName, record.getUniqueId() , json);
        }catch (Throwable e){
            log.info("ProviderDataRecordRepository->doSave error", e);
        }

    }
}
