package com.jd.health.medical.examination.repository.product.impl;

import com.google.common.collect.Lists;
import com.jd.health.medical.examination.common.util.ValidateUtil;
import com.jd.health.medical.examination.dao.ExaminationManGroupDao;
import com.jd.health.medical.examination.dao.ExaminationManGroupItemDao;
import com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity;
import com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity;
import com.jd.health.medical.examination.repository.product.GroupRepository;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.NumberUtil;
import com.jd.medicine.base.common.util.StringUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检套餐仓储实现
 * @author: yang<PERSON>yu
 * @date: 2022/4/12 9:55 上午
 * @version: 1.0
 */
@Component
public class GroupRepositoryImpl implements GroupRepository {

    /** */
    @Resource
    private ExaminationManGroupDao examinationManGroupDao;
    /** */
    @Resource
    private ExaminationManGroupItemDao examinationManGroupItemDao;

    /**
     * 根据名称和适用性别查询套餐列表
     * @param groupNos
     * @return
     */
    @Override
    public List<ExaminationManGroupEntity> listByGroupNos(List<Long> groupNos) {
        if (CollectionUtil.isEmpty(groupNos)){
            return Collections.emptyList();
        }

        List<ExaminationManGroupEntity>  groupEntities = examinationManGroupDao.selectGroupNos(groupNos);
        buildItems(groupEntities);
        return groupEntities;
    }

    /**
     * 根据groupNo 查询JD套餐
     */
    @Override
    public ExaminationManGroupEntity queryByGroupNo(Long groupNo) {
        ValidateUtil.verifyObjectNonNull(groupNo, "groupNo is null");
        ExaminationManGroupEntity entity = examinationManGroupDao.selectManGroupByGroupNo(groupNo);
        if (Objects.isNull(entity)){
            return null;
        }
        List<ExaminationManGroupEntity> list = Lists.newArrayList(entity);
        buildItems(list);
        return list.get(NumberUtil.INTEGER_ZERO);
    }

    /**
     *
     * @param name
     * @return
     */
    @Override
    public List<ExaminationManGroupEntity> listByName(String name) {
        List<ExaminationManGroupEntity> groupEntities = examinationManGroupDao.selectManGroupByGroupName(name);
        buildItems(groupEntities);
        return groupEntities;
    }

    /**
     * 根据条件 查询京东套餐
     *
     * @param entity
     * @return
     */
    @Override
    public List<ExaminationManGroupEntity> queryExaminationManGroupList(ExaminationManGroupEntity entity) {
        return examinationManGroupDao.selectOne(entity);
    }


    /**
     * 构建套餐的检查项目
     *
     * @param groupEntities
     */
    private void buildItems(List<ExaminationManGroupEntity> groupEntities) {
        if (CollectionUtil.isEmpty(groupEntities)) {
            return;
        }
        Set<Long> groupNos = groupEntities.stream().map(ExaminationManGroupEntity::getGroupNo).collect(Collectors.toSet());
        List<ExaminationManGroupItemEntity> examinationManGroupItemEntities = examinationManGroupItemDao.selectByGroupNos(groupNos);
        Map<Long, List<ExaminationManGroupItemEntity>> map = examinationManGroupItemEntities.stream().collect(Collectors.groupingBy(ExaminationManGroupItemEntity::getGroupNo));
        for (ExaminationManGroupEntity entity : groupEntities) {
            List<ExaminationManGroupItemEntity> items = map.get(entity.getGroupNo());
            entity.setGroupItems(items);
            entity.setGroupItemNum(CollectionUtil.isEmpty(items) ? NumberUtil.INTEGER_ZERO : items.size());
        }
    }

}
