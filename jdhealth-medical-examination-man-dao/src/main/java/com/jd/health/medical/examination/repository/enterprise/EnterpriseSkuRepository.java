package com.jd.health.medical.examination.repository.enterprise;

import com.github.pagehelper.Page;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuEntity;

/**
 * 企销商品仓储
 * @author: yang<PERSON><PERSON>
 * @date: 2022/7/3 2:56 下午
 * @version: 1.0
 */
public interface EnterpriseSkuRepository {

    /**
     * 分页查询企销配置的基础套餐
     * @param entity
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<EnterpriseSkuEntity> pageSku(EnterpriseSkuEntity entity, Integer pageSize, Integer pageNum);

}
