package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.domain.apparatus.XfylApparatusSkuRelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述
 * 服务和实物的商品关系表Dao
 * <AUTHOR>
 * @date 2022/5/26 14:33
 */
public interface XfylApparatusSkuRelDao {
    
    /**
     * 新增
     * @param entity
     * @return
     */
    int insert(XfylApparatusSkuRelEntity entity);
    
    /**
     * 更新
     * @param entity
     * @return
     */
    int updateBySkuNo(XfylApparatusSkuRelEntity entity);
    
    /**
     * 查询列表
     * mbggenerated
     */
    List<XfylApparatusSkuRelEntity> queryApparatusSkuRelList(XfylApparatusSkuRelEntity entity);
    
    /**
     * 查询单个
     * @param skuNo
     * @return
     */
    XfylApparatusSkuRelEntity queryApparatusSkuRelBySku(@Param("skuNo") String skuNo);
}
