package com.jd.health.medical.examination.repository.report.impl;

import com.jd.health.medical.examination.common.constant.CommonConstant;
import com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO;
import com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO;
import com.jd.health.medical.examination.dao.report.ExaminationIndicatorAliasDao;
import com.jd.health.medical.examination.dao.report.ExaminationIndicatorDao;
import com.jd.health.medical.examination.repository.report.ExaminationIndicatorRepository;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: huqifu1
 * @Date: 2023/2/2
 * @Description: 检后指标仓储
 */
@Service
public class ExaminationIndicatorRepositoryImpl implements ExaminationIndicatorRepository {

    /**
     * examinationIndicatorDao
     */
    @Resource
    private ExaminationIndicatorDao examinationIndicatorDao;

    /**
     * examinationIndicatorAliasDao
     */
    @Resource
    private ExaminationIndicatorAliasDao examinationIndicatorAliasDao;

    /**
     * 新增标准指标
     * @param examinationIndicatorDO
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void insertExaminationStandardIndicator(ExaminationIndicatorDO examinationIndicatorDO) {
        examinationIndicatorDao.insert(examinationIndicatorDO);
        examinationIndicatorAliasDao.insert(this.convertExaminationIndicatorAliasDO(examinationIndicatorDO));
    }

    /**
     * 更新标准指标
     * @param examinationIndicatorDO
     * @return
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void updateExaminationStandardIndicator(ExaminationIndicatorDO examinationIndicatorDO) {
        ExaminationIndicatorDO source = examinationIndicatorDao.selectByParam(examinationIndicatorDO).get(CommonConstant.ZERO);
        examinationIndicatorDao.updateBySelective(examinationIndicatorDO);
        if (source.getIndicatorName().equals(examinationIndicatorDO.getIndicatorName()) == Boolean.FALSE || source.getWsCode().equals(examinationIndicatorDO.getWsCode()) == Boolean.FALSE) {
            ExaminationIndicatorAliasDO examinationIndicatorAliasDO = new ExaminationIndicatorAliasDO();
            examinationIndicatorAliasDO.setIndicatorCode(examinationIndicatorDO.getIndicatorCode());
            examinationIndicatorAliasDO.setIndicatorName(examinationIndicatorDO.getIndicatorName());
            examinationIndicatorAliasDO.setWsCode(examinationIndicatorDO.getWsCode());
            examinationIndicatorAliasDao.updateByIndicatorCode(examinationIndicatorAliasDO);
        }
    }

    /**
     * 删除标准指标
     *
     * @param examinationIndicatorDO
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void deleteExaminationStandardIndicator(ExaminationIndicatorDO examinationIndicatorDO) {
        examinationIndicatorDao.updateBySelective(examinationIndicatorDO);
        ExaminationIndicatorAliasDO examinationIndicatorAliasDO = new ExaminationIndicatorAliasDO();
        examinationIndicatorAliasDO.setIndicatorCode(examinationIndicatorDO.getIndicatorCode());
        examinationIndicatorAliasDO.setYn(CommonConstant.ZERO);
        examinationIndicatorAliasDO.setUpdateTime(new Date());
        examinationIndicatorAliasDO.setUpdateUser(examinationIndicatorDO.getUpdateUser());
        examinationIndicatorAliasDao.updateByIndicatorCode(examinationIndicatorAliasDO);
    }

    /**
     * ExaminationIndicatorDO -> ExaminationIndicatorAliasDO
     * @param examinationIndicatorDO
     * @return
     */
    private ExaminationIndicatorAliasDO convertExaminationIndicatorAliasDO(ExaminationIndicatorDO examinationIndicatorDO) {
        ExaminationIndicatorAliasDO examinationIndicatorAliasDO = new ExaminationIndicatorAliasDO();
        examinationIndicatorAliasDO.setIndicatorCode(examinationIndicatorDO.getIndicatorCode());
        examinationIndicatorAliasDO.setIndicatorName(examinationIndicatorDO.getIndicatorName());
        examinationIndicatorAliasDO.setWsCode(examinationIndicatorDO.getWsCode());
        examinationIndicatorAliasDO.setIndicatorAliasName(examinationIndicatorDO.getIndicatorName());
        examinationIndicatorAliasDO.setRelevanceStatus(CommonConstant.ONE);
        examinationIndicatorAliasDO.setOperationUser(examinationIndicatorDO.getCreateUser());
        examinationIndicatorAliasDO.setOperationTime(new Date());
        examinationIndicatorAliasDO.setSource(examinationIndicatorDO.getSource());
        examinationIndicatorAliasDO.setYn(CommonConstant.ONE);
        examinationIndicatorAliasDO.setCreateTime(new Date());
        examinationIndicatorAliasDO.setUpdateTime(new Date());
        examinationIndicatorAliasDO.setCreateUser(examinationIndicatorDO.getCreateUser());
        examinationIndicatorAliasDO.setUpdateUser(examinationIndicatorDO.getUpdateUser());
        return examinationIndicatorAliasDO;
    }
}
