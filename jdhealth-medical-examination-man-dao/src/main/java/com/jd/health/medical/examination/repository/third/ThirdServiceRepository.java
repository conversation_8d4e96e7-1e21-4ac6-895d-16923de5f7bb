package com.jd.health.medical.examination.repository.third;

import com.jd.health.medical.examination.domain.merchant.value.ChannelStoreIdValue;
import com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity;

import java.util.List;

/**
 * 商家服务查询
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/15 11:15 上午
 * @version: 1.0
 */
public interface ThirdServiceRepository {


    /**
     * 获取可用的商家服务（套餐-门店关系）
     * @return
     */
    List<ThirdGoodsStoreEntity> listAvailableByStore(ChannelStoreIdValue storeIdValue);



    List<ThirdGoodsStoreEntity> batchSave(ChannelStoreIdValue storeIdValue);

}
