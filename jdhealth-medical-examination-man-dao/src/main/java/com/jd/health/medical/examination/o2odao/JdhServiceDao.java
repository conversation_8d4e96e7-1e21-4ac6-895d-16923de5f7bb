package com.jd.health.medical.examination.o2odao;

import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceEntity;
import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * JDH体检名称和品牌体检名称关系
 *
 * <AUTHOR>
 * @date 2022-04-11 15:08:27
 */
public interface JdhServiceDao {

    /**
     * insert record to table
     *
     * @param jdhServiceEntityList the record
     * @return insert count
     */
    int batchInsert(@Param("jdhServiceEntityList") List<JdhServiceEntity> jdhServiceEntityList);

    /**
     *
     * @param serviceId
     * @return
     */
    JdhServiceEntity queryService(@Param("serviceId") Long serviceId);
}