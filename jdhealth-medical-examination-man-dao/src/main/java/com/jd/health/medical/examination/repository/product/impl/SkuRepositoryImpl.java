package com.jd.health.medical.examination.repository.product.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.health.medical.examination.common.enums.SkuSpeciesEnum;
import com.jd.health.medical.examination.common.enums.SkuUnionTypeEnum;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.common.util.AssertUtils;
import com.jd.health.medical.examination.common.util.ValidateUtil;
import com.jd.health.medical.examination.dao.ExaminationManGroupItemDao;
import com.jd.health.medical.examination.dao.SkuInfoDao;
import com.jd.health.medical.examination.dao.SkuStoreThirdRelDao;
import com.jd.health.medical.examination.domain.merchant.value.ChannelGoodsIdValue;
import com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity;
import com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity;
import com.jd.health.medical.examination.domain.personal.entity.SkuStoreThirdRel;
import com.jd.health.medical.examination.repository.product.SkuRepository;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.*;

/**
 * 体检商品仓储一个体检商品包含配置的检查项目：ExaminationManGroupItemEntity
 * 包含配置的供应商套餐ChannelGoodsIdValue
 *
 * @author: yangxiyu
 * @date: 2022/4/13 3:51 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class SkuRepositoryImpl implements SkuRepository {
    /** */
    @Resource
    private SkuInfoDao skuInfoDao;
    /** sku-goods绑定关系  */
    @Resource
    private SkuStoreThirdRelDao skuStoreThirdRelDao;
    /**
     *
     */
    @Resource
    private ExaminationManGroupItemDao examinationManGroupItemDao;

    /**
     * 1、获取商品基础信息；
     * 2、获取商品关联的供应商套餐；
     * @param skuNo
     * @return
     */
    @Override
    public SkuInfoEntity queryBySkuNo(String skuNo) {
        ValidateUtil.verifyStringNotEmpty(skuNo, "skuNo is null");
        SkuInfoEntity skuInfoEntity = skuInfoDao.selectSkuInfoBySkuNo(skuNo);
        if (Objects.isNull(skuInfoEntity)){
            return null;
        }
        if (Objects.equals(skuInfoEntity.getSkuSpecies(), SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())){
            List<ExaminationManGroupItemEntity> list = examinationManGroupItemDao.listByRelationNo(skuNo);
            skuInfoEntity.setGroupItems(list);
        }else if (Objects.equals(skuInfoEntity.getSkuSpecies(), SkuSpeciesEnum.GROUP_SKU.getTypeNo())
                && Objects.equals(SkuUnionTypeEnum.NORMAL.getTypeNo(), skuInfoEntity.getSkuUnionType())){
            List<ExaminationManGroupItemEntity> list = examinationManGroupItemDao.selectByGroupNo(skuInfoEntity.getGroupNo());
            skuInfoEntity.setGroupItems(list);
        }
        return skuInfoEntity;
    }

    /**
     * 根据skuNos查询套餐集合
     * @param skuNos
     * @return
     */
    @Override
    public List<SkuInfoEntity> listBySkuNos(Set<String> skuNos, Integer skuSpecies) {
        if (CollectionUtil.isEmpty(skuNos)){
            return Collections.emptyList();
        }
        AssertUtils.nonNull(skuSpecies);
        List<SkuInfoEntity> skuInfoEntities = skuInfoDao.listValidBySkuNos(skuNos, skuSpecies);
        if (CollectionUtil.isEmpty(skuInfoEntities)){
            return Collections.emptyList();
        }
        Set<String> relationNos = Sets.newHashSet();
        Set<Long> groupNos = Sets.newHashSet();
        List<SkuInfoEntity> res = Lists.newArrayListWithExpectedSize(skuInfoEntities.size());

        List<SkuInfoEntity> pluginSkus = Lists.newArrayList();
        List<SkuInfoEntity> baseSkus = Lists.newArrayList();
        // 加项包商品使用relationNo关联检查项目，普通套餐使用groupNo关联检查项目；此处为考虑组合套餐
        for (SkuInfoEntity sku : skuInfoEntities) {
            if (Objects.equals(sku.getSkuSpecies(), SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())){
                relationNos.add(sku.getSkuNo());
                pluginSkus.add(sku);
            }else if (Objects.equals(sku.getSkuSpecies(), SkuSpeciesEnum.GROUP_SKU.getTypeNo())
                    && Objects.equals(SkuUnionTypeEnum.NORMAL.getTypeNo(), sku.getSkuUnionType())){
                groupNos.add(sku.getGroupNo());
                baseSkus.add(sku);
            }else{
                res.add(sku);

            }
        }
        // 填充加项包商品的检查项目
        if (CollectionUtil.isNotEmpty(relationNos)){
            List<ExaminationManGroupItemEntity> items = examinationManGroupItemDao.selectItemByRelationNos(skuNos);
            Set<Long> skuNoSet = skuNos.stream().map(Long::valueOf).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(items)){
                Map<String, List<ExaminationManGroupItemEntity>> relationMap = items.stream().collect(Collectors.groupingBy(ExaminationManGroupItemEntity::getRelationNo));
                for (SkuInfoEntity plugin : pluginSkus) {
                    List<ExaminationManGroupItemEntity> list = relationMap.get(plugin.getSkuNo());
                    if (CollectionUtil.isNotEmpty(list)){
                        plugin.setGroupItems(list);
                    }
                }
            }
        }

        if (CollectionUtil.isNotEmpty(groupNos)){
            List<ExaminationManGroupItemEntity> items = examinationManGroupItemDao.selectByGroupNos(groupNos);
            if (CollectionUtil.isNotEmpty(items)){
                Map<Long, List<ExaminationManGroupItemEntity>> groupMap = items.stream().collect(Collectors.groupingBy(ExaminationManGroupItemEntity::getGroupNo));
                for (SkuInfoEntity baseSku : baseSkus) {
                    List<ExaminationManGroupItemEntity> list = groupMap.get(baseSku.getGroupNo());
                    if (CollectionUtil.isNotEmpty(list)){
                        baseSku.setGroupItems(list);
                    }
                }
            }
        }
        res.addAll(pluginSkus);
        res.addAll(baseSkus);
        return res;
    }

    /**
     * 根据skuName查询商品
     * @param skuName
     * @return
     */
    @Override
    public List<SkuInfoEntity> listByName(String skuName) {
        return skuInfoDao.listByName(skuName);
    }

    /**
     * 创建一个商品
     * @param sku
     * @return
     */
    @Override
    public Boolean createSku(SkuInfoEntity sku) {
        log.info("SkuRepository->createSku start sku={}", JsonUtil.toJSONString(sku));
        ValidateUtil.verifyObjectNonNull(sku, "sku is null");
        ValidateUtil.verifyObjectNonNull(sku.getSkuNo(), "skuNo is null");
        ValidateUtil.verifyStringNotEmpty(sku.getSkuName(), "skuName is null");
        ValidateUtil.verifyObjectNonNull(sku.getSkuPrice(), "skuPrice is null");
        ValidateUtil.verifyObjectNonNull(sku.getSkuStatus(), "skuStatus is null");
        ValidateUtil.verifyObjectNonNull(sku.getSkuType(), "skuType is null");

        // 查询并加锁，当前读，避免并发插入
        SkuInfoEntity entity = skuInfoDao.selectLockBySkuNo(sku.getSkuNo());
        if (Objects.nonNull(entity)){
            throw new BusinessException(BaseErrorCode.SKU_EXIST);
        }
        Integer count = skuInfoDao.insertSkuInfo(sku);
        log.info("SkuRepository->createSku skuNo={}, count={}", sku.getSkuNo(), count);
        return Boolean.TRUE;
    }


    /**
     * 获取配置了指定商家套餐的sku(可用的sku)
     * @param channelGoodsIdValue
     * @return
     */
    @Override
    public List<SkuInfoEntity> listByGoods(ChannelGoodsIdValue channelGoodsIdValue) {
        AssertUtils.nonNull(channelGoodsIdValue);
        SkuStoreThirdRel rel = new SkuStoreThirdRel();
        rel.setGoodsId(channelGoodsIdValue.getGoodsId());
        rel.setChannelNo(channelGoodsIdValue.getChannelNo());
        List<SkuStoreThirdRel> relations = skuStoreThirdRelDao.selectList(rel);
        if (CollectionUtil.isEmpty(relations)){
            return Collections.emptyList();
        }
        Set<String> skuNos = relations.stream().map(SkuStoreThirdRel::getSkuNo).collect(Collectors.toSet());
        return skuInfoDao.listValidBySkuNos(skuNos, SkuSpeciesEnum.GROUP_SKU.getTypeNo());
    }
}
