package com.jd.health.medical.examination.repository.third;

import com.jd.health.medical.examination.domain.third.ThirdStoreEntity;

import java.util.List;
import java.util.Set;

/**
 * 供应商门店仓储
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/16 3:55 下午
 * @version: 1.0
 */
public interface ThirdStoreRepository {


    /**
     * 查询门店集合
     * @param storeIds
     * @param channelNo
     * @return
     */
    List<ThirdStoreEntity> listByStoreIds(Set<String> storeIds, Long channelNo);

}
