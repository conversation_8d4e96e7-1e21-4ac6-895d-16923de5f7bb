package com.jd.health.medical.examination.repository.product;

import com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity;

import java.util.List;

/**
 * 体检套餐仓储
 * @author: yang<PERSON><PERSON>
 * @date: 2022/4/12 9:50 上午
 * @version: 1.0
 */
public interface GroupRepository {

    /**
     * 根据groupNos查询JD套餐列表
     * @param groupNos
     * @return
     */
    List<ExaminationManGroupEntity> listByGroupNos(List<Long> groupNos);

    /**
     * 根据groupNo 查询JD套餐
     */
    ExaminationManGroupEntity queryByGroupNo(Long groupNo);

    /**
     * @param name
     * @return
     */
    List<ExaminationManGroupEntity> listByName(String name);

    /**
     * 根据条件 查询京东套餐
     *
     * @param entity
     * @return
     */
    List<ExaminationManGroupEntity> queryExaminationManGroupList(ExaminationManGroupEntity entity);

}
