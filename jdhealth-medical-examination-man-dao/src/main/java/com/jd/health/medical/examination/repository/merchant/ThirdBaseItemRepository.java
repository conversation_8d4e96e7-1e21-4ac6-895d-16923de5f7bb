package com.jd.health.medical.examination.repository.merchant;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.domain.BaseThirdItemRel;
import com.jd.health.medical.examination.domain.bo.ManThirdItemPageQueryBO;
import com.jd.health.medical.examination.domain.bo.ThirdBaseItemAttrRelationQueryBO;
import com.jd.health.medical.examination.domain.bo.ThirdBaseItemBO;
import com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemAttrRelationEntity;
import com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity;

import java.util.List;
import java.util.Map;

/**
 * 供应商基础项目 仓储
 * @author: yang<PERSON>yu
 * @date: 2022/4/11 5:01 下午
 * @version: 1.0
 */
public interface ThirdBaseItemRepository {

    /**
     * 获取全量的名称和itemNo map
     * @return
     */
    Map<String, Long> queryNameNoMap(ThirdBaseItemBO baseItemBO);

    /**
     * 批量创建
     *
     * @param entityList
     * @return
     */
    Boolean createBatch(List<ThirdBaseItemEntity> entityList);

    /**
     * 更新供应商体检项目
     *
     * @param entity
     * @return
     */
    Boolean updateThirdBaseItem(ThirdBaseItemEntity entity);

    /**
     * 根据条件分页查询
     *
     * @param baseItemBO
     * @return
     */
    PageInfo<ThirdBaseItemEntity> selectByParam(ThirdBaseItemBO baseItemBO);

    /**
     * 根据条件查询供应商项目
     *
     * @param baseItemBO
     * @return
     */
    List<ThirdBaseItemEntity> getThirdBaseItemList(ThirdBaseItemBO baseItemBO);

    /**
     * getThirdBaseItemListByNos
     *
     * @param baseItemBO
     * @return
     */
    List<ThirdBaseItemEntity> getThirdBaseItemListByNames(ThirdBaseItemBO baseItemBO);

    /**
     * 三方项目明细
     *
     * @param baseItemBO bo
     * @return entity
     */
    ThirdBaseItemEntity getByParam(ThirdBaseItemBO baseItemBO);

    /**
     * 插入
     *
     * @param baseItemBO baseItemBO
     * @return boolean
     */
    Boolean insert(ThirdBaseItemBO baseItemBO);

    /**
     * 删除
     *
     * @param baseItemBO baseItemBO
     * @return boolean
     */
    Boolean delete(ThirdBaseItemBO baseItemBO);

    /**
     * selectBaseThirdItemRelByParam
     *
     * @param record
     * @return
     */
    List<BaseThirdItemRel> selectBaseThirdItemRelByParam(BaseThirdItemRel record);

    /**
     * 分页查询商家项目列表
     *
     * @param manThirdItemPageQueryBO
     * @return
     */
    PageInfo<ThirdBaseItemEntity> queryThirdItemAndCategoryPageInfo(ManThirdItemPageQueryBO manThirdItemPageQueryBO);

    /**
     * 查询需要清洗可换项字段的POP商家项目数据
     *
     * @return
     */
    List<ThirdBaseItemEntity> selectCleanThirdBaseItemReplace();
    /**
     * 查询项目下的属性列表
     *
     * @param relationQueryBO
     * @return
     */
    List<ThirdBaseItemAttrRelationEntity> queryThirdItemCategoryList(ThirdBaseItemAttrRelationQueryBO relationQueryBO);

    /**
     * 保存商家项目信息
     *
     * @param thirdBaseItemEntity
     * @param relationEntityList
     */
    void insertThirdItemBase(ThirdBaseItemEntity thirdBaseItemEntity, List<ThirdBaseItemAttrRelationEntity> relationEntityList);
}
