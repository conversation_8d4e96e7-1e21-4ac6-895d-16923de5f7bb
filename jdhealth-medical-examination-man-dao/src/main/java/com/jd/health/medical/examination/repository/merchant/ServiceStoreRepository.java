package com.jd.health.medical.examination.repository.merchant;


import com.jd.health.medical.examination.common.enums.SkuSpeciesEnum;
import com.jd.health.medical.examination.domain.bo.SkuStoreInfoBO;
import com.jd.health.medical.examination.domain.merchant.bo.ListBySkuAreaBO;
import com.jd.health.medical.examination.domain.merchant.value.ChannelGoodsIdValue;
import com.jd.health.medical.examination.domain.merchant.value.ChannelStoreIdValue;
import com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity;

import java.util.List;
import java.util.Set;

/**
 * 服务（service）门店仓储服务
 * @author: yangxiyu
 * @date: 2022/3/20 6:55 下午
 * @version: 1.0
 */
public interface ServiceStoreRepository {


    //================================= 普通体检套餐存储 ======================//

    /**
     * 体检普通商品根据skuNo修改groupNo
     * @param skuNo
     * @param groupNo
     * @return
     */
    Boolean updateGroupNo(String skuNo, Long groupNo);

    /**
     * 获取sku生效的门店数量
     */
    Integer queryStartStoreCount(String skuNo);

    /**
     * 根据加项包套餐编号获取门店
     * @param channelNo
     * @param parentGoodsId
     * @param pluginGoodsId
     * @return
     */
    List<SkuStoreEntity> listByPluginGoodsId(Long channelNo, String parentGoodsId, String pluginGoodsId);


    /**
     * 查询SKU在制定区域内的门店里恩
     * @param areaBO
     * @return
     */
    List<SkuStoreInfoBO> listBySkuArea(ListBySkuAreaBO areaBO);
    //=========================================== 供应商数据变更，引起的存储调整 ==========================================//

    /**
     * 根据套餐编码下架skuStore
     * @return
     */
    Integer offByGoodsIds(Long channelNo, Set<String> goodsIds, SkuSpeciesEnum goodsType);


}
