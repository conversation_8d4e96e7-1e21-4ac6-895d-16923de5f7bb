package com.jd.health.medical.examination.dao;

import com.jd.health.medical.examination.domain.report.entity.basic.ExaminationManThirdIndicatorEntity;
import com.jd.health.medical.examination.export.param.report.ExaminationManThirdIndicatorPageParam;

import java.util.List;

/**
 * 第三方报告指标库(ExaminationManThirdIndicator)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-03 15:35:04
 */
public interface ExaminationManThirdIndicatorDao {

    /**
     * 通过indicatorNo查询单条数据
     *
     * @param indicatorNo 主键
     * @return 实例对象
     */
    ExaminationManThirdIndicatorEntity queryByIndicatorNo(Long indicatorNo);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param pageParam 实例对象
     * @return 对象列表
     */
    List<ExaminationManThirdIndicatorEntity> queryPage(ExaminationManThirdIndicatorPageParam pageParam);

    /**
     * 新增数据
     *
     * @param examinationManThirdIndicator 实例对象
     * @return 影响行数
     */
    int insert(ExaminationManThirdIndicatorEntity examinationManThirdIndicator);

    /**
     * 修改数据
     *
     * @param examinationManThirdIndicator 实例对象
     * @return 影响行数
     */
    int updateByIndicatorNo(ExaminationManThirdIndicatorEntity examinationManThirdIndicator);


}

