package com.jd.health.medical.examination.repository.third.impl;

import com.jd.health.medical.examination.common.util.AssertUtils;
import com.jd.health.medical.examination.dao.third.ThirdGoodsDao;
import com.jd.health.medical.examination.domain.third.ThirdGoodsEntity;
import com.jd.health.medical.examination.repository.third.ThirdGoodsRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 供应商套餐数据仓储
 * @author: yang<PERSON><PERSON>
 * @date: 2022/7/29 2:28 下午
 * @version: 1.0
 */
@Component
public class ThirdGoodsRepositoryImpl implements ThirdGoodsRepository {

    /** */
    @Resource
    private ThirdGoodsDao thirdGoodsDao;

    /**
     *
     * @param goodsIds
     * @param channelNo
     * @return
     */
    @Override
    public List<ThirdGoodsEntity> listGoodsIds(Set<String> goodsIds, Long channelNo) {
        AssertUtils.nonNull(channelNo);
        AssertUtils.listNotEmpty(goodsIds);
        return thirdGoodsDao.selectByGoodsIds(goodsIds, channelNo);
    }
}
