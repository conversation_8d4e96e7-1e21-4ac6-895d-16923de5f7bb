package com.jd.health.medical.examination.repository.report;


import com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO;
import com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO;
import com.jd.health.medical.examination.domain.bo.report.ExaminationIndicatorBO;

/**
 * @Author: huqifu1
 * @Date: 2023/2/2
 * @Description: 检后指标相关仓储
 */
public interface ExaminationIndicatorRepository {

    /**
     * 新增标准指标
     * @param examinationIndicatorDO
     */
    void insertExaminationStandardIndicator(ExaminationIndicatorDO examinationIndicatorDO);

    /**
     * 更新标准指标
     * @param examinationIndicatorDO
     * @return
     */
    void updateExaminationStandardIndicator(ExaminationIndicatorDO examinationIndicatorDO);

    /**
     * 删除标准指标
     * @param examinationIndicatorDO
     */
    void deleteExaminationStandardIndicator(ExaminationIndicatorDO examinationIndicatorDO);

}
