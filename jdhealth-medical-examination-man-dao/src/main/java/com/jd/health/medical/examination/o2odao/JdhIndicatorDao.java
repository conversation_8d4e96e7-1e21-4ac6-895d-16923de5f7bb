package com.jd.health.medical.examination.o2odao;

import com.jd.health.medical.examination.domain.BaseThirdItemRel;
import com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhIndicatorEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * JDH体检名称和品牌体检名称关系
 *
 * <AUTHOR>
 * @date 2022-04-11 15:08:27
 */
public interface JdhIndicatorDao {

    /**
     * insert record to table
     *
     * @param indicatorEntityList the record
     * @return insert count
     */
    int batchInsert(@Param("indicatorEntityList") List<JdhIndicatorEntity> indicatorEntityList);

    JdhIndicatorEntity queryByIndicator(@Param("indicator") Long itemNo);
}