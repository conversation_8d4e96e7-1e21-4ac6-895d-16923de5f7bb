<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
		PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
		"http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- 配置生成器 -->
<generatorConfiguration>

	<context id="mysql" defaultModelType="hierarchical"
			 targetRuntime="MyBatis3Simple">

		<!-- 自动识别数据库关键字，
		默认false，如果设置为true，
		根据SqlReservedWords中定义的关键字列表；
		一般保留默认值，
		遇到数据库关键字（Java关键字），
		使用columnOverride覆盖 -->
		<property name="autoDelimitKeywords" value="false" />
		<!-- 生成的Java文件的编码 -->
		<property name="javaFileEncoding" value="UTF-8" />
		<!-- 格式化java代码 -->
		<property name="javaFormatter"
				  value="org.mybatis.generator.api.dom.DefaultJavaFormatter" />
		<!-- 格式化XML代码 -->
		<property name="xmlFormatter"
				  value="org.mybatis.generator.api.dom.DefaultXmlFormatter" />

		<!-- beginningDelimiter和endingDelimiter：指明数据库的用于标记数据库对象名的符号，
		比如ORACLE就是双引号，MYSQL默认是`反引号； -->
		<property name="beginningDelimiter" value="`" />
		<property name="endingDelimiter" value="`" />

		<!--阻止生成日期和注释-->
		<commentGenerator>
			<property name="suppressDate" value="false" />
			<property name="suppressAllComments" value="true" />
		</commentGenerator>

		<!-- 数据库连接信息 -->
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
						connectionURL="****************************************************" userId="healthcare_admin" password="r3kenGvYLBN7j3gW">
		</jdbcConnection>

		<!-- java类型处理器 用于处理数据库中的类型到Java中的类型，
			 默认使用JavaTypeResolverDefaultImpl；
			 注意一点，默认会先尝试使用Integer，Long，Short等来对应DECIMAL和
			NUMERIC数据类型； -->
		<javaTypeResolver
				type="org.mybatis.generator.internal.types.JavaTypeResolverDefaultImpl">
			<!-- true：使用BigDecimal对应DECIMAL和 NUMERIC数据类型 f
				 alse：默认, scale>0;length>18：使用BigDecimal;
				scale=0;length[10,18]：使用Long； scale=0;length[5,9]：
				使用Integer； scale=0;length<5：使用Short； -->
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>


		<!--
		配置domain生成策略
        targetProject:把自动生成的domian放在哪个工程里面
        targetPackage:哪个包下
		-->
		<javaModelGenerator targetPackage="com.jd.health.medical.examination.domain"
							targetProject="src/main/java">
			<!-- for MyBatis3/MyBatis3Simple
			自动为每一个生成的类创建一个构造方法，
			构造方法包含了所有的field；而不是使用setter； -->
			<property name="constructorBased" value="false" />

			<!-- for MyBatis3 / MyBatis3Simple
			是否创建一个不可变的类，如果为true，
			那么MBG会创建一个没有setter方法的类，
			取而代之的是类似constructorBased的类 -->
			<property name="immutable" value="false" />

			<!-- 设置是否在getter方法中，
			对String类型字段调用trim()方法 -->
			<property name="trimStrings" value="true" />
		</javaModelGenerator>

		<!--
            配置mapper.xml的生成策略
            targetPackage:把自动生成的mapper放在哪个工程里面
            targetProject:哪个包下
        -->
		<sqlMapGenerator targetPackage="com.jd.health.medical.examination.dao"
						 targetProject="src/main/java">
			<!-- 在targetPackage的基础上，
			根据数据库的schema再生成一层package，
			最终生成的类放在这个package下，默认为false -->
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<!--
           mapper接口生成策略
        -->
		<javaClientGenerator targetPackage="com.jd.health.medical.examination.dao"
							 type="XMLMAPPER" targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<table tableName="xfyl_service_bill_record" domainObjectName="BillRecordEntity"/>

	</context>
</generatorConfiguration>