<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="mapUnderscoreToCamelCase" value="true" />
		<setting name="defaultStatementTimeout" value="30" /><!--超时时间 单位s-->
		<setting name="logImpl" value="STDOUT_LOGGING"/>
		<setting name="logImpl" value="SLF4J"/>  <!--指定使用slf4j日志框架-->
		<setting name="logPrefix" value="org.mybatis.sqlmap."/>
	</settings>

	<typeHandlers>
		<!--对明文生成hash256索引值，用于where等于查询-->
		<typeHandler handler="com.jd.security.aces.mybatis.handle.AcesIndexHandle" javaType="com.jd.security.aces.mybatis.type.AcesJavaType"/>
		<!---对明文做加密，密文做解密用-->
		<typeHandler handler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle" javaType="com.jd.security.aces.mybatis.type.AcesJavaType"/>
		<!--aces.writePlaintext=true时写明文，aces.writePlaintext=false时不写明文-->
		<typeHandler handler="com.jd.security.aces.mybatis.handle.AcesPlainTextHandle" javaType="com.jd.security.aces.mybatis.type.AcesJavaType"/>
	</typeHandlers>

</configuration>