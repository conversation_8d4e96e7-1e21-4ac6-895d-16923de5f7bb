<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.AppointmentLogDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.AppointmentLogEntity"
               id="examinationAppointmentInfoMap">
        <result property="id" column="id"/>
        <result property="userPin" column="user_pin"/>
        <result property="orderId" column="order_id"/>
        <result property="jdAppointmentId" column="jd_appointment_id"/>
        <result property="appointmentNo" column="appointment_no"/>
        <result property="appointmentStatus" column="appointment_status"/>
        <result property="checkStatus" column="check_status"/>
        <result property="checkTime" column="check_time"/>
        <result property="reportStatus" column="report_status"/>
        <result property="reportUrl" column="report_url"/>
        <result property="reportTime" column="report_time"/>
        <result property="cancelTimes" column="cancel_times"/>
        <result property="cancelDate" column="cancel_date"/>
        <result property="modifyDate" column="modify_date"/>
        <result property="goodsId" column="goods_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="userName" column="user_name"/>
        <result property="userPhone" column="user_phone"/>
        <result property="userCredentialType" column="user_credential_type"/>
        <result property="userCredentialNo" column="user_credential_no"/>
        <result property="userGender" column="user_gender"/>
        <result property="userMarriage" column="user_marriage"/>
        <result property="storeId" column="store_id"/>
        <result property="storeAddr" column="store_addr"/>
        <result property="storeName" column="store_name"/>
        <result property="storePhone" column="store_phone"/>
        <result property="appointmentDate" column="appointment_date"/>
        <result property="channelNo" column="channel_no"/>
        <result property="cancelStatus" column="cancel_status"/>
        <result property="orderStatus" column="order_status"/>
        <result property="randomCode" column="random_code"/>
        <result property="orderType" column="order_type"/>
        <result property="appointSource" column="appoint_source"/>
        <result property="companyNo" column="company_no"/>
        <result property="enterpriseCostType" column="enterprise_cost_type"/>
        <result property="userId" column="user_id"/>
        <result property="empId" column="emp_id"/>
        <result property="goodsTotalAmount" column="goods_total_amount"/>
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="orderUserPhone" column="order_user_phone"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="relativesType" column="relatives_type"/>
        <result property="skuInfoExtend" column="sku_info_extend"/>
        <result property="addItems" column="add_items"/>
        <result property="reportAuthorize" column="report_authorize"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id, user_pin, order_id, jd_appointment_id, appointment_no, appointment_status, check_status, check_time,
            report_status, report_url,
            report_time, cancel_times, cancel_date, modify_date, patient_id, goods_id, user_name, user_phone,
            user_credential_type, user_credential_no,
            user_gender, store_id, store_addr, store_name, store_phone, appointment_date, channel_no, cancel_status,
            order_status, yn, create_time, update_time,
            appoint_source, company_no, enterprise_cost_type, goods_total_amount,
            user_marriage,random_code, sku_no, sku_name, add_items,report_authorize
        </trim>
    </sql>

    <sql id="Simple_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id, user_pin, order_id, jd_appointment_id, appointment_no, appointment_status, check_status, check_time,
            report_status, goods_id, store_id, appointment_date, order_status,channel_no,add_items,appoint_source
        </trim>
    </sql>

    <insert id="insertExaminationAppointment"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.AppointmentLogEntity">
        insert into examination_man_appointment_log
        (user_pin,order_id,jd_appointment_id,appointment_no,goods_id,create_time,sku_no,sku_name,order_user_phone,user_id,company_no,appoint_source,
        relatives_type,enterprise_cost_type,order_type,goods_total_amount,user_name,user_phone,user_credential_type,user_credential_no,user_gender,
        store_id,store_name,appointment_date,channel_no,patient_id,add_items,order_status,check_status)
        values
            (#{userPin},#{orderId},#{jdAppointmentId},#{appointmentNo},#{goodsId},now(),#{skuNo,jdbcType=VARCHAR},#{skuName,jdbcType=VARCHAR},
            #{orderUserPhone,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR},#{companyNo,jdbcType=VARCHAR},#{appointSource,jdbcType=INTEGER},
            #{relativesType,jdbcType=INTEGER},#{enterpriseCostType,jdbcType=INTEGER},#{orderType,jdbcType=INTEGER},#{goodsTotalAmount,jdbcType=INTEGER},
            #{userName,jdbcType=VARCHAR},#{userPhone,jdbcType=VARCHAR},#{userCredentialType},#{userCredentialNo},#{userGender},#{storeId},#{storeName},
            #{appointmentDate},#{channelNo},#{patientId},#{addItems},#{orderStatus},#{checkStatus})
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.AppointmentLogEntity">
        update examination_man_appointment_log
        <set>
            <if test="userPin != null">
                user_pin = #{userPin,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="appointmentNo != null">
                appointment_no = #{appointmentNo,jdbcType=VARCHAR},
            </if>
            <if test="appointmentStatus != null">
                appointment_status = #{appointmentStatus,jdbcType=INTEGER},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus,jdbcType=INTEGER},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reportStatus != null">
                report_status = #{reportStatus,jdbcType=INTEGER},
            </if>
            <if test="reportUrl != null">
                report_url = #{reportUrl,jdbcType=VARCHAR},
            </if>
            <if test="reportTime != null">
                report_time = #{reportTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTimes != null">
                cancel_times = #{cancelTimes,jdbcType=INTEGER},
            </if>
            <if test="cancelDate != null">
                cancel_date = #{cancelDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyDate != null">
                modify_date = #{modifyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="patientId != null">
                patient_id = #{patientId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userPhone != null">
                user_phone = #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userCredentialType != null">
                user_credential_type = #{userCredentialType,jdbcType=INTEGER},
            </if>
            <if test="userCredentialNo != null">
                user_credential_no = #{userCredentialNo,jdbcType=VARCHAR},
            </if>
            <if test="userGender != null">
                user_gender = #{userGender,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=VARCHAR},
            </if>
            <if test="storeAddr != null">
                store_addr = #{storeAddr,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="storePhone != null">
                store_phone = #{storePhone,jdbcType=VARCHAR},
            </if>
            <if test="appointmentDate != null">
                appointment_date = #{appointmentDate,jdbcType=TIMESTAMP},
            </if>
            <if test="channelNo != null">
                channel_no = #{channelNo,jdbcType=BIGINT},
            </if>
            <if test="cancelStatus != null">
                cancel_status = #{cancelStatus,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="randomCode != null">
                random_code = #{randomCode,jdbcType=INTEGER},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="appointSource != null">
                appoint_source = #{appointSource,jdbcType=INTEGER},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo,jdbcType=BIGINT},
            </if>
            <if test="enterpriseCostType != null">
                enterprise_cost_type = #{enterpriseCostType,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="goodsTotalAmount != null">
                goods_total_amount = #{goodsTotalAmount,jdbcType=INTEGER},
            </if>
            <if test="userMarriage != null">
                user_marriage = #{userMarriage,jdbcType=INTEGER},
            </if>
            <if test="skuNo != null">
                sku_no = #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="orderUserPhone != null">
                order_user_phone = #{orderUserPhone,jdbcType=VARCHAR},
            </if>
            <if test="relativesType != null">
                relatives_type = #{relativesType,jdbcType=INTEGER},
            </if>
            <if test="skuInfoExtend != null">
                sku_info_extend = #{skuInfoExtend,jdbcType=VARCHAR},
            </if>
            <if test="addItems != null and addItems != ''">
                add_items = #{addItems,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    </update>

    <update id="updateAppointmentDate">
        update examination_man_appointment_log
        set
        appointment_date=#{appointDate,jdbcType=DATE},
        modify_date=now()
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>
    <!-- 预约中 操作 -->
    <update id="updateStatusOfCancelOperator" parameterType="java.lang.Long">
        UPDATE examination_man_appointment_log
         set   order_status = 2,
            cancel_date=now()
        WHERE jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        AND check_status = 0
        AND yn = 1
    </update>

    <!--更新预约单号 -->
    <update id="updateAppointmentNoByjdAppointmentId">
        UPDATE examination_man_appointment_log
        <set>
            appointment_no = #{appointmentNo,jdbcType=VARCHAR},
            appointment_status=3,
            order_status= 3
        </set>
        WHERE jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        AND yn = 1
    </update>

    <!-- 根据jdAppointmentId 查询体检预约单 状态 -->
    <select id="selectAppointInfoByjdAppointmentId" resultMap="examinationAppointmentInfoMap"
            parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from examination_man_appointment_log
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>
    <update id="updateOrderStatusByjdAppointmentId">
        update examination_man_appointment_log
        set
        appointment_status= #{orderStatus,jdbcType=INTEGER},
        order_status= #{orderStatus,jdbcType=INTEGER}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="getAppointmentLogTodayFinish" resultMap="examinationAppointmentInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_appointment_log
        where order_status in (3,14,15)
        and DATE_SUB(CURDATE(), INTERVAL 1 DAY)  <![CDATA[>=date(create_time)]]>
        and yn=1
    </select>

    <select id="getAppointmentLogByOrderStatus" resultMap="examinationAppointmentInfoMap"
            parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from examination_man_appointment_log
        where (order_status=#{orderStatus,jdbcType=INTEGER}  or check_status=1)
        and report_status=0
        and yn=1
    </select>

    <update id="updateAppointmentStatusAndDateByJdAppointmentid">
        update examination_man_appointment_log
        set
        appointment_status= #{orderStatus,jdbcType=INTEGER},
        order_status= #{orderStatus,jdbcType=INTEGER},
        check_time=#{appointDate}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <update id="updateReportAuthorizeByJdAppointmentid">
        update examination_man_appointment_log
        set report_authorize = 0
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and report_authorize = 1
        and yn = 1
    </update>

    <update id="updateReportStatusByJdAppointmentid">
        update examination_man_appointment_log
        set report_status=1
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="getAppointmentLogThreeDaysFinish" resultMap="examinationAppointmentInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_appointment_log
        where order_status in (3,15)
        and DATE_SUB(CURDATE(), INTERVAL 1 DAY)  <![CDATA[>=date(create_time)]]>
        and yn=1
    </select>

    <update id="consoleSqlExecute" parameterType="map" statementType="STATEMENT">
        <![CDATA[
         ${sql}
        ]]>
    </update>

    <!--查询预约成功而且预约日期小于今天，未出报告的记录-->
    <select id="queryAppointSuccessNoReportRecord"  parameterType="com.jd.health.medical.examination.domain.bo.JobDataBo"
            resultMap="examinationAppointmentInfoMap">
        select
        <include refid="Simple_Column_List"/>
        from examination_man_appointment_log
        where order_status in (3,4,14,15)
        and report_status = 0
        and DATE_SUB(CURDATE(), INTERVAL 1 DAY)  <![CDATA[>=date(create_time)]]>
        and mod(jd_appointment_id,#{itemCount,jdbcType=INTEGER}) = #{itemNo,jdbcType=INTEGER}
        <if test="maxQueryDays != null">
            and appointment_date > DATE_SUB(CURDATE(), INTERVAL #{maxQueryDays} DAY)
        </if>
        and report_authorize = 1
        and yn=1
    </select>

    <!--查询已到检，未出报告的记录-->
    <select id="queryAppointCheckedNoReportRecord"  parameterType="com.jd.health.medical.examination.domain.bo.JobDataBo"
            resultMap="examinationAppointmentInfoMap">
        select
        <include refid="Simple_Column_List"/>
        from examination_man_appointment_log
        where report_status = 0
        and check_status = 0
        and DATE_SUB(CURDATE(), INTERVAL 1 DAY)  <![CDATA[>=date(create_time)]]>
        and yn=1
        and mod(jd_appointment_id,#{itemCount,jdbcType=INTEGER}) = #{itemNo,jdbcType=INTEGER}
    </select>

    <!--查询已到检，未出报告的记录-->
    <delete id="deleteAppointSuccessRecord">
        delete
        from examination_man_appointment_log
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and report_status = 1
    </delete>

    <select id="queryRecordById" resultMap="examinationAppointmentInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_appointment_log
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    </select>


</mapper>