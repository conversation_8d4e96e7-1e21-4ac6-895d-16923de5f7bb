<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.ReportShareDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.ReportShareEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_pin" property="userPin" />
    <result column="jd_appointment_id" property="jdAppointmentId" />
    <result column="share_time" property="shareTime"/>
    <result column="report_verify_status" property="reportVerifyStatus" />
    <result column="user_phone_encrypt" property="userPhone"
            typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
    <result column="report_url" property="reportUrl" />
    <result column="client_info" property="clientInfo"/>
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="report_share_source" property="reportShareSource" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_pin, jd_appointment_id,share_time,report_verify_status,user_phone,user_phone_encrypt,
     user_phone_index,report_url,client_info,yn, create_time, update_time,report_share_source
  </sql>

  <insert id="saveReportShare" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.AccountCompanyEntity" >
    insert into examination_report_share (user_pin, jd_appointment_id,share_time,
        user_phone,user_phone_encrypt,user_phone_index,report_url,report_share_source)
    values (#{userPin,jdbcType=VARCHAR}, #{jdAppointmentId,jdbcType=BIGINT}, #{shareTime,jdbcType=BIGINT},
      #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
      #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
      #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
      #{reportUrl,jdbcType=VARCHAR},#{reportShareSource,jdbcType=INTEGER})
  </insert>

    <select id="queryReportShare" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from examination_report_share
      where jd_appointment_id = #{jdAppointmentId}
      and share_time = #{shareTime}
      and yn = 1
    </select>

  <update id="updateReportShare">
    update examination_report_share
    set report_verify_status = 1
    where jd_appointment_id = #{jdAppointmentId}
    and share_time = #{shareTime}
    and yn = 1
  </update>

</mapper>