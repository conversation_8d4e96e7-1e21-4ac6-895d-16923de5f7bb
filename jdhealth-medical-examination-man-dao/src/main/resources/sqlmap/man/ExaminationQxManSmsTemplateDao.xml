<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.ExaminationQxManSmsTemplateDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateEntity">
    <id column="id" property="id" />
    <result column="sms_account" property="smsAccount" />
    <result column="template_id" property="templateId" />
    <result column="template_name" property="templateName" />
    <result column="template_content" property="templateContent" />
    <result column="default_status" property="defaultStatus" />
    <result column="remark"  property="remark" />
    <result column="create_user" property="createUser" />
    <result column="create_time" property="createTime" />
    <result column="update_user" property="updateUser" />
    <result column="update_time"  property="updateTime" />
    <result column="yn"  property="yn" />
  </resultMap>
  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
      id, sms_account, template_id, template_name, template_content, default_status, remark,
      create_user, create_time, update_user, update_time, yn
    </trim>
  </sql>

  <select id="selectByAccountAndTemplateId" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateEntity" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from examination_qx_man_sms_template
      where yn = 1 and sms_account = #{smsAccount} and template_id = #{templateId}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from examination_qx_man_sms_template
      where yn = 1
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateEntity" useGeneratedKeys="true">
    insert into examination_qx_man_sms_template (sms_account, template_id, template_name,
      template_content, default_status, remark,
      create_user, create_time, update_user,
      update_time, yn)
    values (#{smsAccount}, #{templateId}, #{templateName},#{templateContent}, #{defaultStatus}, #{remark},#{createUser}, #{createTime}, #{updateUser},#{updateTime},#{yn})
  </insert>

  <update id="updateByAccountAndTemplateId" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateEntity">
    update examination_qx_man_sms_template
    <set>
      <if test="templateName != null">
        template_name = #{templateName},
      </if>
      <if test="templateContent != null">
        template_content = #{templateContent},
      </if>
      <if test="defaultStatus != null">
        default_status = #{defaultStatus},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="yn != null">
        yn = #{yn},
      </if>
    </set>
    where sms_account = #{smsAccount} and template_id = #{templateId} and yn = 1
  </update>

  <select id="selectByAccountAndTemplateBo" resultMap="BaseResultMap" parameterType="java.util.List">
    select
    <include refid="Base_Column_List"></include>
    from examination_qx_man_sms_template
    where yn = 1 and (sms_account, template_id) in
    <foreach collection="boList" item = "item" open="(" separator="," close=")">(#{item.smsAccount}, #{item.templateId})</foreach>
  </select>

  <select id="querySmsTemplatePage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_qx_man_sms_template
    where yn = 1
    <if test="templateId != null and templateId != ''">
        and template_id = #{templateId}
    </if>
    <if test="templateName != null and templateName != ''">
      and template_name like concat ('%',#{templateName},'%')
    </if>
    <if test="smsTemplateEntityList.size > 0">
      and (sms_account, template_id) not in
      <foreach collection="smsTemplateEntityList" item = "item" open="(" separator="," close=")">(#{item.smsAccount}, #{item.templateId})</foreach>
    </if>
    order by create_time desc
  </select>

  <select id="selectBySmsTemplateType" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from examination_qx_man_sms_template
    where yn = 1
    and default_status like concat('%', #{customerSource}, '%')
  </select>
</mapper>