<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.doctor.DoctorStoreSkuRelDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.doctor.DoctorStoreSkuRelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="doctor_id" property="doctorId" jdbcType="VARCHAR"/>
        <result column="store_id" property="storeId" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="VARCHAR"/>
        <result column="sku_category" property="skuCategory" jdbcType="VARCHAR"/>
        <result column="approval_status" property="approvalStatus" jdbcType="TINYINT"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="insert">
        insert into manage_doctor_store_sku_rel (doctor_id, store_id, sku_id, sku_category, approval_status, yn,
        create_time, create_user, update_time, update_user
        )
        values (#{doctorId}, #{storeId}, #{skuId}, #{skuCategory}, #{approvalStatus}, 1, now(), #{createUser}, now(),
        #{createUser}
        )
    </insert>
    <update id="update">
        update manage_doctor_store_sku_rel
        <set>
            <if test="skuCategory != null and skuCategory != ''">
                sku_category = #{skuCategory},
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                approval_status = #{approvalStatus},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
        </set>
        where sku_id = #{skuId} and doctor_id = #{doctorId} and store_id = #{storeId}
    </update>
    <select id="query" resultMap="BaseResultMap">
        select id, doctor_id, store_id, sku_id, sku_category, approval_status, yn, create_time, create_user,
        update_time, update_user
        from manage_doctor_store_sku_rel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="doctorId != null and doctorId != ''">
                and doctor_id = #{doctorId}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId}
            </if>
            <if test="skuId != null and skuId != ''">
                and sku_id = #{skuId}
            </if>
            <if test="skuCategory != null and skuCategory != ''">
                and sku_category = #{skuCategory}
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <choose>
                <when test="yn != null">
                    and yn = #{yn}
                </when>
                <otherwise>
                    and yn = 1
                </otherwise>
            </choose>
        </where>
        order by create_time desc
    </select>
    <select id="get" resultMap="BaseResultMap">
        select id, doctor_id, store_id, sku_id, sku_category, approval_status, yn, create_time, create_user,
        update_time, update_user
        from manage_doctor_store_sku_rel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="doctorId != null and doctorId != ''">
                and doctor_id = #{doctorId}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId}
            </if>
            <if test="skuId != null and skuId != ''">
                and sku_id = #{skuId}
            </if>
            <if test="skuCategory != null and skuCategory != ''">
                and sku_category = #{skuCategory}
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <choose>
                <when test="yn != null">
                    and yn = #{yn}
                </when>
                <otherwise>
                    and yn = 1
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>