<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdBaseItemDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity">
    <!--@mbg.generated-->
    <!--@Table examination_man_third_base_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="item_no" jdbcType="BIGINT" property="itemNo" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="match_status" jdbcType="INTEGER" property="matchStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="item_price" jdbcType="INTEGER" property="itemPrice" />
    <result column="item_category" jdbcType="VARCHAR" property="itemCategory" />
    <result column="sku_category" jdbcType="VARCHAR" property="skuCategory" />
    <result column="channel_no" jdbcType="VARCHAR" property="channelNo" />
    <result column="vendor_type" jdbcType="INTEGER" property="vendorType" />
    <result column="item_extra" jdbcType="VARCHAR" property="itemExtra"/>
    <result column="replace_item_desc" jdbcType="VARCHAR" property="replaceItemDesc"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, item_no, item_name, item_type, match_status, yn, create_user, create_time, update_user, update_time, item_price, item_category, sku_category, channel_no, vendor_type, item_extra, replace_item_desc
  </sql>


  <select id="listAllNameNo" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="vendorType != null">
        and vendor_type = #{vendorType,jdbcType=INTEGER}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="selectByParam" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="itemNo != null">
        and item_no = #{itemNo,jdbcType=BIGINT}
      </if>
      <if test="itemName != null">
        and item_name like concat('%',#{itemName,jdbcType=VARCHAR},'%')
      </if>
      <if test="matchStatus != null">
        and match_status = #{matchStatus,jdbcType=INTEGER}
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType,jdbcType=INTEGER}
      </if>
      <if test="itemCategory != null and itemCategory != ''">
        and item_category = #{itemCategory}
      </if>
      <if test="skuCategory != null and skuCategory != ''">
        and sku_category = #{skuCategory}
      </if>
      <if test="channelNo != null and channelNo != ''">
        and channel_no = #{channelNo}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="getThirdBaseItemList" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="itemNo != null">
        and item_no = #{itemNo,jdbcType=BIGINT}
      </if>
      <if test="itemName != null">
        and item_name = #{itemName,jdbcType=VARCHAR}
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType,jdbcType=INTEGER}
      </if>
      <if test="itemCategory != null and itemCategory != ''">
        and item_category = #{itemCategory}
      </if>
      <if test="skuCategory != null and skuCategory != ''">
        and sku_category = #{skuCategory}
      </if>
      <if test="channelNo != null and channelNo != ''">
        and channel_no = #{channelNo}
      </if>
      and yn = 1
    </where>
  </select>
  <select id="getThirdBaseItemListByNames" parameterType="com.jd.health.medical.examination.domain.bo.ThirdBaseItemBO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="itemNames != null and itemNames.size() != 0">
        and item_name in
        <foreach collection="itemNames" item="itemName" open="(" close=")" separator=",">
          #{itemName,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType,jdbcType=INTEGER}
      </if>
      <if test="itemCategory != null and itemCategory != ''">
        and item_category = #{itemCategory}
      </if>
      <if test="skuCategory != null and skuCategory != ''">
        and sku_category = #{skuCategory}
      </if>
      <if test="channelNo != null and channelNo != ''">
        and channel_no = #{channelNo}
      </if>
      and yn = 1
    </where>
  </select>
  <update id="updateByThirdBaseItem" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity">
    update examination_man_third_base_item
    <set>
      <if test="itemName != null and itemName != ''">
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="matchStatus != null">
        match_status = #{matchStatus,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemPrice != null">
        item_price = #{itemPrice},
      </if>
      <if test="itemCategory != null and itemCategory != ''">
        item_category = #{itemCategory},
      </if>
      <if test="skuCategory != null and skuCategory != ''">
        sku_category = #{skuCategory},
      </if>
      <if test="channelNo != null and channelNo != ''">
        channel_no = #{channelNo},
      </if>
      <if test="vendorType != null">
        vendor_type = #{vendorType},
      </if>
      <if test="replaceItemDesc != null and replaceItemDesc != ''">
        replace_item_desc = #{replaceItemDesc,jdbcType=VARCHAR}
      </if>
    </set>
    where yn = 1
    and item_no = #{itemNo,jdbcType=BIGINT}
  </update>
  <insert id="insertBatch" keyProperty="id" keyColumn="id" parameterType="java.util.List" >
    insert into examination_man_third_base_item (
    item_no, item_name, item_type,
    create_user, create_time,update_time, item_price, item_category, sku_category, channel_no, vendor_type, item_extra, replace_item_desc)
    values
    <foreach collection="list" item="entity" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{entity.itemNo,jdbcType=BIGINT},
        #{entity.itemName,jdbcType=VARCHAR},
        #{entity.itemType,jdbcType=INTEGER},
        #{entity.createUser,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.updateTime,jdbcType=TIMESTAMP},
        #{entity.itemPrice},
        #{entity.itemCategory},
        #{entity.skuCategory},
        #{entity.channelNo},
        #{entity.vendorType},
        #{entity.itemExtra},
        #{entity.replaceItemDesc,jdbcType=VARCHAR}
      </trim>
    </foreach>
  </insert>

  <select id="getByParam" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="itemNo != null">
        and item_no = #{itemNo,jdbcType=BIGINT}
      </if>
      <if test="itemName != null">
        and item_name like concat('%',#{itemName,jdbcType=VARCHAR},'%')
      </if>
      <if test="matchStatus != null">
        and match_status = #{matchStatus,jdbcType=INTEGER}
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType,jdbcType=INTEGER}
      </if>
      <if test="itemCategory != null and itemCategory != ''">
        and item_category = #{itemCategory}
      </if>
      <if test="skuCategory != null and skuCategory != ''">
        and sku_category = #{skuCategory}
      </if>
      <if test="channelNo != null and channelNo != ''">
        and channel_no = #{channelNo}
      </if>
      and yn = 1
    </where>
  </select>

  <insert id="insert" keyProperty="id" keyColumn="id" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity">
    insert into examination_man_third_base_item (
    item_no, item_name, item_type,
    create_user, create_time, item_price, item_category, sku_category, channel_no, vendor_type, item_extra, replace_item_desc)
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{itemNo,jdbcType=BIGINT},
      #{itemName,jdbcType=VARCHAR},
      #{itemType,jdbcType=INTEGER},
      #{createUser,jdbcType=VARCHAR},
      now(),
      #{itemPrice},
      #{itemCategory},
      #{skuCategory},
      #{channelNo},
      #{vendorType},
      #{itemExtra},
      #{replaceItemDesc}
    </trim>
  </insert>

  <update id="delete" parameterType="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemEntity">
    update examination_man_third_base_item
    <set>
      yn = 0
    </set>
    where yn = 1
    and item_no = #{itemNo,jdbcType=BIGINT}
  </update>

  <!-- 分页查询商家项目列表 -->
  <select id="getThirdBaseItemListByItemNos" parameterType="com.jd.health.medical.examination.domain.bo.ThirdBaseItemBO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="itemNameList != null and itemNameList.size() != 0">
        and item_name in
        <foreach collection="itemNameList" item="itemName" open="(" close=")" separator=",">
          #{itemName, jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="thirdCategoryId != null and thirdCategoryId != ''">
        and sku_category = #{thirdCategoryId, jdbcType=VARCHAR}
      </if>
      <if test="channelNo != null and channelNo != ''">
        and channel_no = #{channelNo, jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </select>
  <!-- 查询需要清洗可换项字段的POP商家项目数据 -->
  <select id="selectCleanThirdBaseItemReplace" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    where vendor_type = 2 and yn = 1 and sku_category in ("","0","27451") and replace_item_desc is null
  </select>


  <select id="getThirdBaseItemListByItemCategorys" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_third_base_item
    <where>
      <if test="itemCategorys != null and itemCategorys.size() != 0">
        and item_category in
        <foreach collection="itemCategorys" item="category" open="(" close=")" separator=",">
          #{category,jdbcType=VARCHAR}
        </foreach>
      </if>
      and yn = 1
    </where>
  </select>
</mapper>