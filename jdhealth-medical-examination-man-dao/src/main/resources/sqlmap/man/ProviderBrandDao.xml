<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.ProviderBrandDao">


    <select id="selectAll" resultType="com.jd.health.medical.examination.domain.ProviderBrandEntity">
        select id          as id,
               brand_id    as brandId,
               brand_name  as brandName,
               `order`     as orderSort,
               create_time as createTime,
               update_time as updateTime,
               yn          as yn
        from examination_man_provider_brand
        where yn = 1
    </select>

    <select id="queryProviderBrandByBrandId"
            resultType="com.jd.health.medical.examination.domain.ProviderBrandEntity">
        select id          as id,
               brand_id    as brandId,
               brand_name  as brandName,
               `order`     as orderSort,
               create_time as createTime,
               update_time as updateTime,
               yn          as yn
        from examination_man_provider_brand
        where yn = 1
          and brand_id = #{brandId}
    </select>
</mapper>