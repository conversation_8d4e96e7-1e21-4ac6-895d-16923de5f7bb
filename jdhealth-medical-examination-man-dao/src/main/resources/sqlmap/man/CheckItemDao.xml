<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.CheckItemDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.CheckItemEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="item_no" property="itemNo" jdbcType="BIGINT" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="item_desc" property="itemDesc" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="addCheckItem">
     insert into examination_man_check_item (item_no, item_name,
      item_desc, yn, create_time,
      create_user, update_time, update_user
      )
    values (#{itemNo,jdbcType=BIGINT}, #{itemName,jdbcType=VARCHAR},
      #{itemDesc,jdbcType=VARCHAR}, 1, now(),
      #{createUser,jdbcType=VARCHAR}, now(), #{createUser,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateCheckItem">
    update examination_man_check_item
    set item_name = #{itemName,jdbcType=VARCHAR},
      item_desc = #{itemDesc,jdbcType=VARCHAR},
      update_time = now(),
      update_user = #{updateUser,jdbcType=VARCHAR}
    where item_no = #{itemNo,jdbcType=BIGINT}
  </update>
  <select id="queryCheckItemPage" resultMap="BaseResultMap">
    select id, item_no, item_name, item_desc, yn, create_time, create_user, update_time,
    update_user
    from examination_man_check_item
    <where>
      <if test="itemName != null and itemName != ''">
        and item_name like CONCAT("%",#{itemName,jdbcType=VARCHAR},"%")
      </if>
      and yn = 1
    </where>
  </select>
</mapper>