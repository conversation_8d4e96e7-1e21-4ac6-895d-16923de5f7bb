<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationReportBodyPartDao">

    <!-- 数据库返回字段 -->
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.report.DO.ExaminationBodyPartDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="body_part_code" jdbcType="VARCHAR" property="bodyPartCode"/>
        <result column="body_part_name" jdbcType="VARCHAR" property="bodyPartName"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <result column="body_part_level" jdbcType="TINYINT" property="bodyPartLevel"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
    </resultMap>

    <!-- 通用字段 -->
    <sql id="Base_Column_List">
       `id`,`body_part_code`,`body_part_name`,`parent_code`,`body_part_level`,
       `create_user`, `create_time`, `yn`
    </sql>

    <!-- 查询身体部位系统列表 -->
    <select id="queryBodyPartList"
            resultType="com.jd.health.medical.examination.dao.report.DO.ExaminationBodyPartDO">
        select
        <include refid="Base_Column_List"/>
        FROM
        examination_report_body_part
        <where>
            <if test="null != bodyPartCode and bodyPartCode !=''">
                and body_part_code = #{bodyPartCode}
            </if>
            <if test="null != parentCode">
                and parent_code = #{parentCode}
            </if>
            <if test="null != bodyPartLevel">
                and body_part_level = #{bodyPartLevel}
            </if>
            <if test="true">
                and yn = 1
            </if>
        </where>
    </select>

</mapper>