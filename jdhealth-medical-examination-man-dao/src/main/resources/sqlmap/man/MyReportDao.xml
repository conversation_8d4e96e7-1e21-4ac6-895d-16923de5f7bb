<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.MyReportDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.MyReportEntity" id="myReportMap">
        <result property="id" column="id"/>
        <result property="userPin" column="user_pin"/>
        <result property="userName" column="user_name_encrypt"
                typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
        <result property="orderId" column="order_id"/>
        <result property="businessType" column="business_type"/>
        <result property="userGender" column="user_gender"/>
        <result property="userPhone" column="user_phone_encrypt"
                typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
        <result property="jdAppointmentId" column="jd_appointment_id"/>
        <result property="orderType" column="order_type"/>
        <result property="appointDate" column="appoint_date"/>
        <result property="skuName" column="sku_name"/>
        <result property="storeName" column="store_name"/>
        <result property="reportVerifyStatus" column="report_verify_status"/>
        <result property="reportUrl" column="report_url"/>
        <result property="reportSource" column="report_source"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="patientId" column="patient_id"/>
        <result property="userId" column="user_id"/>
        <result property="relativesType" column="relatives_type"/>
        <result property="expireDate" column="expire_date"/>
        <result property="userMarriage" column="user_marriage"/>
        <result property="companyNo" column="company_no"/>
        <result property="channelNo" column="channel_no"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="structReportId" column="struct_report_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="reportStatus" column="report_status"/>
        <result property="maskReportUrl" column="mask_report_url"/>
        <result property="reportId" column="report_id"/>
        <result property="bizCode" column="biz_code"/>
        <result property="extendId" column="extend_id"/>
        <result property="reportType" column="report_type"/>
        <result property="reportLabel" column="report_label"/>
        <result property="userAge" column="user_age"/>
        <result property="userCredentialType" column="user_credential_type"/>
        <result property="userCredentialNo" column="user_credential_no_encrypt"
                typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
        <result property="examinationTime" column="examination_time"/>
        <result property="birthday" column="birthday"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="parentJdReportId" column="parent_jd_report_id"/>
        <result property="fileType" column="file_type"/>
        <result property="bizType" column="biz_type"/>
        <result property="authorization" column="authorization"/>
        <result property="reportShowOrder" column="reportShowOrder"/>
        <result property="titleId" column="title_id"/>
        <result property="creator" column="creator"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="reportCenterId" column="report_center_id"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            user_pin,
            user_name,
            user_name_encrypt,
            order_id,
            business_type,
            user_gender,
            user_phone,
            user_phone_encrypt,
            jd_appointment_id,
            order_type,
            appoint_date,
            sku_name,
            store_name,
            report_verify_status,
            report_url,
            report_source,
            apply_status,
            patient_id,
            user_id,
            relatives_type,
            expire_date,
            user_marriage,
            company_no,
            channel_no,
            yn,
            create_time,
            update_time,
            struct_report_id,
            brand_id,
            report_status,
            mask_report_url,
            report_id,
            biz_code,
            extend_id,
            report_type,
            report_label,
            user_age,
            user_credential_type,
            user_credential_no,
            user_credential_no_encrypt,
            examination_time,
            birthday,
            file_type,
            parent_jd_report_id,
            biz_type,
            authorization,
            report_show_order,
            file_md5,
            title_id,
            creator,
            enterprise_id,
            report_center_id
        </trim>
    </sql>

    <update id="deleteReport">
        update examination_report
        set yn = 0
        where yn = 1
        <if test="jdAppointmentId != null">
            and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        </if>
        <if test="jdAppointmentIds != null and jdAppointmentIds.size()>0">
            and jd_appointment_id in
            <foreach collection="jdAppointmentIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </update>


    <!--根据userPin和预约号查询-->
    <select id="getReportInfoByNo" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="userPin != null and userPin!=''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
            and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            and report_type = 1
            and yn = 1
        </where>
    </select>

    <!--根据userPin sourceType orderType查询列表-->
    <select id="queryMyReportList" resultMap="myReportMap"
            parameterType="com.jd.health.medical.examination.export.param.report.QueryReportPageParam">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="userPin != null and userPin!=''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
            <if test="bizCode != null">
                and biz_code = #{bizCode,jdbcType=INTEGER}
            </if>
            <if test="reportSource != null">
                and report_source = #{reportSource,jdbcType=INTEGER}
            </if>
            <if test="orderType != null and orderType !='' ">
                and order_type = #{orderType,jdbcType=VARCHAR}
            </if>
            <if test="jdAppointmentId != null">
                and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            </if>
            <if test="reportIdList != null">
                and jd_appointment_id in
                <foreach collection="reportIdList" item="itemId" open="(" close=")" separator=",">
                    #{itemId}
                </foreach>
            </if>
            <if test="reportStatus != null">
                and report_status = #{reportStatus}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="null != appointDateBegin">
                and appoint_date &gt;= #{appointDateBegin}
            </if>
            <if test="null != appointDateEnd">
                and appoint_date &lt;= #{appointDateEnd}
            </if>
            <if test="notInPatientIdList != null">
                and patient_id not in
                <foreach collection="notInPatientIdList" item="patientId" open="(" close=")" separator=",">
                    #{patientId}
                </foreach>
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="null != createTimeBegin">
                and create_time &gt;= #{createTimeBegin}
            </if>
            <if test="null != createTimeEnd">
                and create_time &lt;= #{createTimeEnd}
            </if>
            <if test="brandId != null">
                and brand_id = #{brandId,jdbcType=BIGINT}
            </if>
            <if test="notInBizType != null">
                and (biz_type not in
                <foreach collection="notInBizType" item="bizType" open="(" close=")" separator=",">
                    #{bizType}
                </foreach>
                or biz_type is null)
            </if>
            <if test="reportType != null">
                and report_type = #{reportType,jdbcType=TINYINT}
            </if>
            and yn = 1
        </where>
    </select>


    <!--根据userPin sourceType orderType查询列表-->
    <select id="queryMyBizReportList" resultMap="myReportMap"
            parameterType="com.jd.health.medical.examination.export.param.report.QueryReportPageParam">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="userPin != null and userPin!=''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
            <if test="bizCodeList != null">
                and biz_code in
                <foreach collection="bizCodeList" item="bizCode" open="(" close=")" separator=",">
                    #{bizCode}
                </foreach>
            </if>
            <if test="reportSource != null">
                and report_source = #{reportSource,jdbcType=INTEGER}
            </if>
            <if test="orderType != null and orderType !='' ">
                and order_type = #{orderType,jdbcType=VARCHAR}
            </if>
            <if test="jdAppointmentId != null">
                and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            </if>
            <if test="reportIdList != null">
                and jd_appointment_id in
                <foreach collection="reportIdList" item="itemId" open="(" close=")" separator=",">
                    #{itemId}
                </foreach>
            </if>
            <if test="reportStatus != null">
                and report_status = #{reportStatus}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="null != appointDateBegin">
                and appoint_date &gt;= #{appointDateBegin}
            </if>
            <if test="null != appointDateEnd">
                and appoint_date &lt;= #{appointDateEnd}
            </if>
            <if test="notInPatientIdList != null">
                and patient_id not in
                <foreach collection="notInPatientIdList" item="patientId" open="(" close=")" separator=",">
                    #{patientId}
                </foreach>
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="null != createTimeBegin">
                and create_time &gt;= #{createTimeBegin}
            </if>
            <if test="null != createTimeEnd">
                and create_time &lt;= #{createTimeEnd}
            </if>
            <if test="brandId != null">
                and brand_id = #{brandId,jdbcType=BIGINT}
            </if>
            <if test="notInBizType != null">
                and (biz_type not in
                <foreach collection="notInBizType" item="bizType" open="(" close=")" separator=",">
                    #{bizType}
                </foreach>
                or biz_type is null)
            </if>
            <if test="reportType != null">
                and report_type = #{reportType,jdbcType=TINYINT}
            </if>
            and yn = 1
        </where>
    </select>

    <!--根据userPin patient_id-->
    <select id="queryReportList" resultMap="myReportMap"
            parameterType="com.jd.health.medical.examination.export.param.report.QueryReportPageParam">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where user_pin = #{userPin,jdbcType=VARCHAR}
        and patient_id = #{patientId}
        <if test="notInBizType != null">
            and (biz_type not in
            <foreach collection="notInBizType" item="bizType" open="(" close=")" separator=",">
                #{bizType}
            </foreach>
            or biz_type is null)
        </if>
        <if test="reportType != null">
            and report_type = #{reportType}
        </if>
        and yn = 1
    </select>

    <!--根据入参查询报告列表 -->
    <select id="querySimpleReportList" resultMap="myReportMap"
            parameterType="com.jd.health.medical.examination.export.param.ExaminationReportParam">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="null !=reportParam.userPin and reportParam.userPin !=''">
                and user_pin =#{reportParam.userPin}
            </if>
            <if test="null !=reportParam.patientId and reportParam.patientId !=''">
                and patient_id =#{reportParam.patientId}
            </if>
            <if test="null !=reportParam.jdAppointmentId">
                and jd_appointment_id =#{reportParam.jdAppointmentId}
            </if>
            <if test="null !=reportParam.bizCode">
                and biz_code =#{reportParam.bizCode}
            </if>
            <if test="null !=reportParam.reportSource">
                and report_source =#{reportParam.reportSource}
            </if>
            <if test="null !=reportParam.fileMd5 and reportParam.fileMd5!=''">
                and file_md5 = #{reportParam.fileMd5}
            </if>
            <if test="null !=reportParam.reportIdList and reportParam.reportIdList.size !=0">
                and jd_appointment_id in
                <foreach collection="reportParam.reportIdList" item="itemId" open="(" close=")" separator=",">
                    #{itemId}
                </foreach>
            </if>
            <if test="reportParam.reportStatus != null">
                and report_status = #{reportParam.reportStatus}
            </if>
            <if test="reportParam.notInBizType != null">
                and (biz_type not in
                <foreach collection="reportParam.notInBizType" item="bizType" open="(" close=")" separator=",">
                    #{bizType}
                </foreach>
                or biz_type is null)
            </if>
            <if test="reportParam.reportType != null">
                and report_type = #{reportParam.reportType}
            </if>
            <if test="true">
                and yn = 1
            </if>
            <if test="null !=reportParam.jdAppointmentIds and reportParam.jdAppointmentIds.size !=0">
                and jd_appointment_id in
                <foreach collection="reportParam.jdAppointmentIds" item="itemId" open="(" close=")" separator=",">
                    #{itemId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 插入实体 -->
    <insert id="insertMyReport" parameterType="com.jd.health.medical.examination.domain.MyReportEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            user_pin,
            order_id,
            business_type,
            user_name,
            user_name_encrypt,
            user_name_index,
            user_gender,
            user_phone,
            user_phone_encrypt,
            user_phone_index,
            jd_appointment_id,
            order_type,
            appoint_date,
            sku_name,
            store_name,
            report_verify_status,
            report_url,
            report_source,
            patient_id,
            user_id,
            relatives_type,
            user_marriage,
            company_no,
            channel_no,
            yn,
            create_time,
            update_time,
            <if test="reportStatus != null">
                report_status,
            </if>
            struct_report_id,
            report_id,
            <if test="bizCode != null">
                biz_code,
            </if>
            extend_id,
            <if test="reportType != null">
                report_type,
            </if>
            <if test="reportLabel != null">
                report_label,
            </if>
            user_age,
            user_credential_type,
            user_credential_no,
            user_credential_no_encrypt,
            user_credential_no_index,
            examination_time,
            birthday,file_md5,
            parent_jd_report_id,
            biz_type,
            authorization,
            report_show_order,
            file_type,
            enterprise_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{userPin,jdbcType=VARCHAR},
            #{orderId,jdbcType=BIGINT},
            #{businessType,jdbcType=INTEGER},
            #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
            #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
            #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            #{userGender,jdbcType=INTEGER},
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            #{jdAppointmentId,jdbcType=BIGINT},
            #{orderType,jdbcType=VARCHAR},
            #{appointDate,jdbcType=TIMESTAMP},
            #{skuName,jdbcType=VARCHAR},
            #{storeName,jdbcType=VARCHAR},
            #{reportVerifyStatus,jdbcType=INTEGER},
            #{reportUrl,jdbcType=VARCHAR},
            #{reportSource,jdbcType=INTEGER},
            #{patientId,jdbcType=BIGINT},
            #{userId,jdbcType=VARCHAR},
            #{relativesType,jdbcType=INTEGER},
            #{userMarriage,jdbcType=INTEGER},
            #{companyNo,jdbcType=VARCHAR},
            #{channelNo,jdbcType=BIGINT},
            1,
            <choose>
                <when test="createTime != null">
                    #{createTime},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            now(),
            <if test="reportStatus != null">
                #{reportStatus},
            </if>
            #{structReportId,jdbcType=VARCHAR},
            #{reportId,jdbcType=VARCHAR},
            <if test="bizCode != null">
                #{bizCode,jdbcType=INTEGER},
            </if>
            #{extendId,jdbcType=VARCHAR},
            <if test="reportType != null">
                #{reportType,jdbcType=TINYINT},
            </if>
            <if test="reportLabel != null">
                #{reportLabel,jdbcType=TINYINT},
            </if>
            #{userAge,jdbcType=INTEGER},
            #{userCredentialType,jdbcType=INTEGER},
            #{userCredentialNo,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
            #{userCredentialNo,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
            #{userCredentialNo,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            #{examinationTime,jdbcType=VARCHAR},
            #{birthday},#{fileMd5},#{parentJdReportId},
            #{bizType},
            #{authorization,jdbcType=VARCHAR},
            #{reportShowOrder,jdbcType=INTEGER},
            #{fileType,jdbcType=INTEGER},
            #{enterpriseId,jdbcType=VARCHAR}
        </trim>
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.MyReportEntity">
        <!--@mbg.generated-->
        insert into examination_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="channelNo != null">
                channel_no,
            </if>
            <if test="jdAppointmentId != null">
                jd_appointment_id,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="appointDate != null">
                appoint_date,
            </if>
            <if test="skuName != null">
                sku_name,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="userName != null">
                user_name,
                user_name_encrypt,
                user_name_index,
            </if>
            <if test="userGender != null">
                user_gender,
            </if>
            <if test="userPhone != null">
                user_phone,
                user_phone_encrypt,
                user_phone_index,
            </if>
            <if test="reportVerifyStatus != null">
                report_verify_status,
            </if>
            <if test="reportUrl != null">
                report_url,
            </if>
            <if test="maskReportUrl != null">
                mask_report_url,
            </if>
            <if test="userPin != null">
                user_pin,
            </if>
            <if test="patientId != null">
                patient_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="relativesType != null">
                relatives_type,
            </if>
            <if test="expireDate != null">
                expire_date,
            </if>
            <if test="reportSource != null">
                report_source,
            </if>
            <if test="companyNo != null">
                company_no,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="userMarriage != null">
                user_marriage,
            </if>
            <if test="applyStatus != null">
                apply_status,
            </if>
            <if test="structReportId != null">
                struct_report_id,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="reportStatus != null">
                report_status,
            </if>
            <if test="reportId != null">
                report_id,
            </if>
            <if test="bizCode != null">
                biz_code,
            </if>
            <if test="extendId != null">
                extend_id,
            </if>
            <if test="reportType != null">
                report_type,
            </if>
            <if test="reportLabel != null">
                report_label,
            </if>
            <if test="userAge != null">
                user_age,
            </if>
            <if test="userCredentialType != null">
                user_credential_type,
            </if>
            <if test="userCredentialNo != null">
                user_credential_no,
                user_credential_no_encrypt,
                user_credential_no_index,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="examinationTime != null">
                examination_time,
            </if>
            <if test="fileMd5 != null and fileMd5 != ''">
                file_md5,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="reportShowOrder != null">
                report_show_order,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="channelNo != null">
                #{channelNo,jdbcType=BIGINT},
            </if>
            <if test="jdAppointmentId != null">
                #{jdAppointmentId,jdbcType=BIGINT},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="appointDate != null">
                #{appointDate,jdbcType=TIMESTAMP},
            </if>
            <if test="skuName != null">
                #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
                #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            </if>
            <if test="userGender != null">
                #{userGender,jdbcType=TINYINT},
            </if>
            <if test="userPhone != null">
                #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
                #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            </if>
            <if test="reportVerifyStatus != null">
                #{reportVerifyStatus,jdbcType=TINYINT},
            </if>
            <if test="reportUrl != null">
                #{reportUrl,jdbcType=VARCHAR},
            </if>
            <if test="maskReportUrl != null">
                #{maskReportUrl,jdbcType=VARCHAR},
            </if>
            <if test="userPin != null">
                #{userPin,jdbcType=VARCHAR},
            </if>
            <if test="patientId != null">
                #{patientId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="relativesType != null">
                #{relativesType,jdbcType=TINYINT},
            </if>
            <if test="expireDate != null">
                #{expireDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportSource != null">
                #{reportSource,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userMarriage != null">
                #{userMarriage,jdbcType=TINYINT},
            </if>
            <if test="applyStatus != null">
                #{applyStatus,jdbcType=TINYINT},
            </if>
            <if test="structReportId != null">
                #{structReportId,jdbcType=VARCHAR},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=BIGINT},
            </if>
            <if test="reportStatus != null">
                #{reportStatus,jdbcType=TINYINT},
            </if>
            <if test="reportId != null">
                #{reportId,jdbcType=VARCHAR},
            </if>
            <if test="bizCode != null">
                #{bizCode,jdbcType=INTEGER},
            </if>
            <if test="extendId != null">
                #{extendId,jdbcType=VARCHAR},
            </if>
            <if test="reportType != null">
                #{reportType,jdbcType=TINYINT},
            </if>
            <if test="reportLabel != null">
                #{reportLabel,jdbcType=TINYINT},
            </if>
            <if test="userAge != null">
                #{userAge,jdbcType=INTEGER},
            </if>
            <if test="userCredentialType != null">
                #{userCredentialType,jdbcType=INTEGER},
            </if>
            <if test="userCredentialNo != null">
                #{userCredentialNo,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
                #{userCredentialNo,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                #{userCredentialNo,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="examinationTime != null">
                #{examinationTime,jdbcType=VARCHAR},
            </if>
            <if test="fileMd5 != null and fileMd5 != ''">
                #{fileMd5,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=INTEGER},
            </if>
            <if test="reportShowOrder != null">
                #{reportShowOrder,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 插入实体 -->
    <insert id="simpleInsertMyReport" parameterType="com.jd.health.medical.examination.domain.MyReportEntity">
        insert into examination_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            user_pin,
            order_id,
            business_type,
            user_name,
            user_name_encrypt,
            user_name_index,
            user_gender,
            user_phone,
            user_phone_encrypt,
            user_phone_index,
            jd_appointment_id,
            order_type,
            appoint_date,
            sku_name,
            store_name,
            report_verify_status,
            report_url,
            <if test="bizCode != null">
                biz_code,
            </if>
            report_source,
            patient_id,
            user_id,
            relatives_type,
            user_marriage,
            brand_id,
            birthday,
            company_no,
            channel_no,
            file_md5,
            file_type,
            yn,
            create_time,
            update_time,
            <if test="reportStatus != null">
                report_status,
            </if>
            biz_type,
            report_type,
            report_label,
            report_show_order,
            extend_id,
            title_id,
            examination_time,
            creator,
            enterprise_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{userPin,jdbcType=VARCHAR},
            #{orderId,jdbcType=BIGINT},
            #{businessType,jdbcType=INTEGER},
            #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
            #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
            #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            #{userGender,jdbcType=INTEGER},
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            #{jdAppointmentId,jdbcType=BIGINT},
            #{orderType,jdbcType=VARCHAR},
            #{appointDate,jdbcType=TIMESTAMP},
            #{skuName,jdbcType=VARCHAR},
            #{storeName,jdbcType=VARCHAR},
            #{reportVerifyStatus,jdbcType=INTEGER},
            #{reportUrl,jdbcType=VARCHAR},
            <if test="bizCode != null">
                #{bizCode,jdbcType=INTEGER},
            </if>
            #{reportSource,jdbcType=INTEGER},
            #{patientId,jdbcType=BIGINT},
            #{userId,jdbcType=VARCHAR},
            #{relativesType,jdbcType=INTEGER},
            #{userMarriage,jdbcType=INTEGER},
            #{brandId,jdbcType=VARCHAR},
            #{birthday,jdbcType=TIMESTAMP},
            #{companyNo,jdbcType=VARCHAR},
            #{channelNo,jdbcType=BIGINT},
            #{fileMd5,jdbcType=VARCHAR},
            #{fileType,jdbcType=INTEGER},
            1,
            now(),
            now(),
            <if test="reportStatus != null">
                #{reportStatus},
            </if>
            #{bizType},
            #{reportType,jdbcType=TINYINT},
            #{reportLabel,jdbcType=TINYINT},
            #{reportShowOrder,jdbcType=INTEGER},
            #{extendId,jdbcType=VARCHAR},
            #{titleId,jdbcType=BIGINT},
            #{examinationTime,jdbcType=VARCHAR},
            #{creator,jdbcType=VARCHAR},
            #{enterpriseId,jdbcType=VARCHAR}
        </trim>
    </insert>

    <!-- 修改查看状态和有效期 -->
    <update id="updateMyReportStatus" parameterType="com.jd.health.medical.examination.domain.MyReportEntity">
        update examination_report
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                report_verify_status = 1,
                <if test="expireDate != null ">
                    expire_date = #{expireDate,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </set>
        where user_pin = #{userPin,jdbcType=VARCHAR}
        and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    </update>

    <update id="updateReport">
        update examination_report
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="reportUrl != null and reportUrl !='' ">
                    report_url = #{reportUrl},
                </if>
                <if test="patientId != null ">
                    patient_id = #{patientId},
                </if>
                <if test="userName != null and userName != ''">
                    user_name = #{userName},
                    user_name_encrypt =
                    #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                    user_name_index =
                    #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
                </if>
                <if test="userPhone != null and userPhone != ''">
                    user_phone = #{userPhone},
                    user_phone_encrypt =
                    #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                    user_phone_index =
                    #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
                </if>
                <if test="userGender != null">
                    user_gender = #{userGender},
                </if>
                <if test="reportStatus != null">
                    report_status = #{reportStatus,jdbcType=INTEGER},
                </if>
                <if test="structReportId != null and structReportId != ''">
                    struct_report_id = #{structReportId,jdbcType=VARCHAR},
                </if>
                <if test="brandId != null">
                    brand_id = #{brandId,jdbcType=BIGINT},
                </if>
                <if test="relativesType != null">
                    relatives_type = #{relativesType,jdbcType=INTEGER},
                </if>
                <if test="userAge != null">
                    user_age = #{userAge,jdbcType=INTEGER},
                </if>
                <if test="fileMd5 != null and fileMd5!=''">
                    file_md5 = #{fileMd5,jdbcType=VARCHAR},
                </if>
                <if test="examinationTime != null and examinationTime != ''">
                    examination_time =#{examinationTime,jdbcType=VARCHAR},
                </if>
                <if test="extendId != null and extendId != ''">
                    extend_id =#{extendId,jdbcType=VARCHAR},
                </if>
                <if test="authorization != null">
                    authorization = #{authorization,jdbcType=VARCHAR},
                </if>
                <if test="userPin != null and userPin != ''">
                    user_pin = #{userPin,jdbcType=VARCHAR},
                </if>
                <if test="reportShowOrder != null">
                    report_show_order = #{reportShowOrder,jdbcType=INTEGER},
                </if>
                <if test="userId != null">
                    user_id = #{userId},
                </if>
                <if test="titleId != null">
                    title_id = #{titleId},
                </if>
                <if test="companyNo != null">
                    company_no = #{companyNo},
                </if>
                <if test="enterpriseId != null">
                    enterprise_id = #{enterpriseId,jdbcType=VARCHAR},
                </if>
                <if test="reportCenterId != null and reportCenterId != ''">
                    report_center_id = #{reportCenterId,jdbcType=VARCHAR}
                </if>
            </trim>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT} and yn = 1
            <if test="reportType != null">
                and report_type = #{reportType,jdbcType=TINYINT}
            </if>
    </update>

    <!--根据userPin sourceType orderType查询列表-->
    <select id="queryEmReportList" resultMap="myReportMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where
        yn = 1 and report_type = 1
        and order_type = #{orderType,jdbcType=VARCHAR}
        and jd_appointment_id in
        <foreach collection="jdAppointmentIdList" item="jdAppointmentId" open="(" close=")" separator=",">
            #{jdAppointmentId}
        </foreach>
    </select>
    <select id="queryReport" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where
        jd_appointment_id=#{jdAppointmentId} and report_type = 1
        and yn = 1
    </select>


    <!--根据userPin(必传)、patientId(非必传)查询报告列表-->
    <select id="queryReportByUserPin" resultMap="myReportMap"
            parameterType="com.jd.health.medical.examination.export.param.ReportParam">
        select
        user_marriage,
        <include refid="Base_Column_List"/>
        from examination_report
        where
        user_pin = #{userPin,jdbcType=VARCHAR}
        <if test="patientId != null">
            and patient_id = #{patientId,jdbcType=BIGINT}
        </if>
        <choose>
            <when test="reportSource != null">
                and report_source = #{reportSource,jdbcType=INTEGER}
            </when>
            <otherwise>
                <if test="reportSourceSets != null">
                    and report_source in
                    <foreach collection="reportSourceSets" item="reportSourceItem" open="(" close=")" separator=",">
                        #{reportSourceItem,jdbcType=INTEGER}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test="notInBizType != null">
            and (biz_type not in
            <foreach collection="notInBizType" item="bizType" open="(" close=")" separator=",">
                #{bizType}
            </foreach>
            or biz_type is null)
        </if>
        <if test="reportType != null">
            and report_type = #{reportType,jdbcType=TINYINT}
        </if>
        and report_verify_status = 1
        and yn = 1
    </select>

    <!-- 解绑报告 -->
    <update id="unbindReport">
        update examination_report
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="userPin !=null">
                    user_pin = '',
                </if>
                <if test="relativesType !=null">
                    relatives_type = 0,
                </if>
                <if test="patientId !=null">
                    patient_id = null,
                </if>
            </trim>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and user_pin = #{userPin,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 绑定报告 -->
    <update id="bindReport">
        update examination_report
        set user_pin = #{userPin,jdbcType=VARCHAR}
        where jd_appointment_id in
        <foreach item="item" index="index" collection="jdAppointmentIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and yn = 1
    </update>

    <!-- 绑定报告和健康档案ID -->
    <update id="bindPinAndPatientId">
        update examination_report
        set user_pin = #{userPin,jdbcType=VARCHAR},patient_id = #{patientId,jdbcType=BIGINT}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!--查看未绑定userPin的报告列表-->
    <select id="queryUnbindMyReportList" resultMap="myReportMap"
            parameterType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where (user_pin = '' or user_pin != #{userPin,jdbcType=VARCHAR} or user_pin is null )
        <if test="userName != null and userName != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_name=#{userName,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_name_index = #{userName, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userPhone != null and userPhone != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_phone=#{userPhone,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_phone_index = #{userPhone, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="notInBizType != null">
            and (biz_type not in
            <foreach collection="notInBizType" item="bizType" open="(" close=")" separator=",">
                #{bizType}
            </foreach>
            or biz_type is null)
        </if>
        <if test="notInBizCode != null">
            and (biz_code not in
            <foreach collection="notInBizCode" item="bizCode" open="(" close=")" separator=",">
                #{bizCode}
            </foreach>
            or biz_code is null)
        </if>
        <if test="reportType != null">
            and report_type = #{reportType,jdbcType=TINYINT}
        </if>
        and yn = 1
    </select>

    <!-- 选择性更新报告字段 -->
    <update id="updateSelective">
        update examination_report
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="relativesType != null">
                relatives_type = #{relativesType,jdbcType=INTEGER},
            </if>
            <if test="userName != null and userName != ''">
                user_name_encrypt =
                #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                user_name_index =
                #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            </if>
            <if test="userPhone != null and userPhone != ''">
                user_phone_encrypt =
                #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                user_phone_index =
                #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            </if>
            <if test="channelNo != null">
                channel_no = #{channelNo},
            </if>
            <if test="patientId != null">
                patient_id = #{patientId},
            </if>
            <if test="companyNo != null and companyNo != ''">
                company_no = #{companyNo,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="userPin != null and userPin != ''">
                user_pin = #{userPin,jdbcType=VARCHAR},
            </if>
            <if test="examinationTime != null and examinationTime != ''">
                examination_time = #{examinationTime,jdbcType=VARCHAR},
            </if>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        <if test="reportId != null and reportId != ''">
            and report_id = #{reportId,jdbcType=VARCHAR}
        </if>
        and yn = 1
    </update>

    <!-- 更新Pin和PatientId -->
    <update id="updateReportPinAndPatient">
        update examination_report
        <set>
            user_pin = #{userPin},
            <if test="patientId != null">
                patient_id = #{patientId}
            </if>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>


    <update id="updateApplyStatus">
        update examination_report
        set
        apply_status = #{applyStatus}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <update id="updatePhoneAndStatus">
        update examination_report
        set
        <if test="userPhone != null and userPhone != ''">
            user_phone =
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
            user_phone_encrypt =
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
            user_phone_index =
            #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
        </if>
        apply_status = #{status,jdbcType=INTEGER}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>
    <update id="deleteReportByJdAppointmentId">
        update examination_report
        set yn=0
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        <if test="patientId != null">
            and patient_id = #{patientId}
        </if>
        <if test="userPin != null">
            and user_pin = #{userPin}
        </if>
        and yn = 1
    </update>

    <update id="updateReportStatusByJdId">
        update examination_report
        set report_status = #{reportStatus, jdbcType=INTEGER}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!--查询报告 binlake使用-->
    <select id="getReportInfoByPage" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where id >= #{startId,jdbcType=BIGINT} and report_type = 1 order by id asc limit #{limitSize}
    </select>

    <update id="updateStructIdAndBrandByJdId">
        update examination_report
        <set>
            brand_id = #{brandId,jdbcType=BIGINT},
            struct_report_id = #{structReportId,jdbcType=VARCHAR},
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and report_type = 1
        and yn = 1
    </update>

    <select id="queryReportCountBetweenId" resultType="java.lang.Integer">
        select count(1)
        from examination_report
        where id &gt;= #{minId,jdbcType=INTEGER}
        and id &lt; #{maxId,jdbcType=INTEGER}
        and report_type = 1
        and yn=1
    </select>

    <select id="queryReportBetweenId" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            and id &gt;= #{minId,jdbcType=INTEGER}
            and id &lt; #{maxId,jdbcType=INTEGER}
            and report_type = 1
            and yn=1
        </where>
    </select>

    <select id="queryMyReportCount" resultType="com.jd.health.medical.examination.domain.MyReportCountEntity"
            parameterType="com.jd.health.medical.examination.export.param.report.QueryReportPageParam">
        select
        patient_id as patientId,count(*) as reportCount
        from examination_report
        where
        yn = 1
        <if test="patientIdList != null">
            and patient_id in
            <foreach collection="patientIdList" item="patientId" open="(" close=")" separator=",">
                #{patientId}
            </foreach>
        </if>
        <if test="userPin != null">
            and user_pin = #{userPin,jdbcType=VARCHAR}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test="orderType != null and orderType !='' ">
            and order_type = #{orderType,jdbcType=VARCHAR}
        </if>
        <if test="jdAppointmentId != null">
            and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        </if>
        <if test="reportStatus != null">
            and report_status = #{reportStatus}
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="bizCode != null">
            and biz_code = #{bizCode}
        </if>
        <if test="notInBizType != null">
            and (biz_type not in
            <foreach collection="notInBizType" item="bizType" open="(" close=")" separator=",">
                #{bizType}
            </foreach>
            or biz_type is null)
        </if>
        <if test="reportType != null">
            and report_type = #{reportType}
        </if>
        group by patient_id
    </select>

    <update id="updateReportMaskUrl">
        update examination_report
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                mask_report_url = #{maskReportUrl},
            </trim>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
        <if test="reportType != null">
            and report_type = #{reportType}
        </if>
    </update>

    <select id="queryById" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1
        and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    </select>

    <select id="listByIds" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1
        and jd_appointment_id in
        <foreach collection="jdAppointmentIds" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="queryByExtendId" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1 and report_type = 1
        and extend_id = #{extendId,jdbcType=VARCHAR}
        and biz_code = #{bizCode,jdbcType=INTEGER}
        and report_status = #{reportStatus,jdbcType=INTEGER}

    </select>

    <select id="queryByBizPinChannelUniq" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1
        and user_pin = #{userPin,jdbcType=VARCHAR}
        and report_id = #{reportId,jdbcType=VARCHAR}
        and biz_code = #{bizCode,jdbcType=INTEGER}
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </select>

    <select id="querySuyuHistoryRecords" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1
        and parent_jd_report_id is not null
        and channel_no = #{channelNo,jdbcType=BIGINT}
        order by create_time desc
        limit #{limit}
        offset #{offset}
    </select>

    <select id="queryByBizPinUniq" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1 and report_type = 1
        and user_pin = #{userPin,jdbcType=VARCHAR}
        and biz_code = #{bizCode,jdbcType=INTEGER}
    </select>

    <select id="pageByUser" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1 and report_type = 1 and (business_type != 16 or business_type is null )
        <if test="userPin != null">
            and user_pin = #{userPin,jdbcType=VARCHAR,jdbcType=VARCHAR}
        </if>
        <if test="reportStatus != null">
            and report_status = #{reportStatus,jdbcType=INTEGER}
        </if>
        <if test="bizCode != null and bizCode != 30">
            and biz_code = #{bizCode,jdbcType=INTEGER}
        </if>
        <if test="userName != null and userName != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_name=#{userName,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_name_index = #{userName, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userPhone != null and userPhone != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_phone=#{userPhone,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_phone_index = #{userPhone, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userCredentialNo != null and userCredentialNo != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_credential_no=#{userCredentialNo,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_credential_no_index = #{userCredentialNo, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="patientId != null">
            and patient_id = #{patientId,jdbcType=BIGINT}
        </if>
        <if test="userCredentialType != null">
            and user_credential_type = #{userCredentialType,jdbcType=BIGINT}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test = "notInFileType != null and notInFileType.size()>0">
            and (file_type not in
            <foreach collection="notInFileType" item="fileType" open="(" close=")" separator=",">
                #{fileType}
            </foreach> or file_type is null)
        </if>

        <if test="reportEndTime != null">
            and create_time &lt; #{reportEndTime}
        </if>

        <if test="reportStartTime != null">
            and create_time > #{reportStartTime}
        </if>


    </select>

    <!-- 根据条件获取报告信息 -->
    <select id="queryReportByParam" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="examinationReportParam.userPin != null">
                and user_pin = #{examinationReportParam.userPin,jdbcType=VARCHAR}
            </if>
            <if test="examinationReportParam.patientId != null">
                and patient_id = #{examinationReportParam.patientId,jdbcType=BIGINT}
            </if>
            <if test="examinationReportParam.patientIdList != null">
                and patient_id in
                <foreach collection="examinationReportParam.patientIdList" item="patientId" open="(" close=")"
                         separator=",">
                    #{patientId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="examinationReportParam.reportSource != null">
                and report_source = #{examinationReportParam.reportSource,jdbcType=INTEGER}
            </if>
            <if test="examinationReportParam.orderType != null and examinationReportParam.orderType !='' ">
                and order_type = #{examinationReportParam.orderType,jdbcType=VARCHAR}
            </if>
            <if test="examinationReportParam.jdAppointmentId != null">
                and jd_appointment_id = #{examinationReportParam.jdAppointmentId,jdbcType=BIGINT}
            </if>
            <if test="examinationReportParam.reportIdList != null">
                and jd_appointment_id in
                <foreach collection="examinationReportParam.reportIdList" item="reportId" open="(" close=")"
                         separator=",">
                    #{reportId}
                </foreach>
            </if>
            <if test="examinationReportParam.reportStatus != null">
                and report_status = #{examinationReportParam.reportStatus}
            </if>
            <if test="examinationReportParam.businessType != null">
                and business_type = #{examinationReportParam.businessType}
            </if>
            <if test="examinationReportParam.notInBizType != null">
                and (biz_type not in
                <foreach collection="examinationReportParam.notInBizType" item="bizType" open="(" close=")" separator=",">
                    #{bizType}
                </foreach>
                or biz_type is null)
            </if>
            <if test="examinationReportParam.reportType != null">
                and report_type = #{examinationReportParam.reportType,jdbcType=TINYINT}
            </if>
            <if test="true">
                and yn = 1
            </if>
        </where>
    </select>

    <update id="confirm">
        update examination_report
        <set>
            <if test="name != null and name != ''">
                user_name = #{name,jdbcType=VARCHAR},
                user_name_encrypt =
                #{name,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                user_name_index =
                #{name,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
            </if>
            <if test="phone != null and phone != ''">
                user_phone = #{phone,jdbcType=VARCHAR},
                user_phone_encrypt =
                #{phone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                user_phone_index =
                #{phone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},

            </if>

            <if test="patientId != null">
                patient_id = #{patientId,jdbcType=BIGINT},
            </if>
            <if test="gender != null">
                user_gender = #{gender,jdbcType=INTEGER},
            </if>
            <if test="age != null">
                user_age = #{age,jdbcType=INTEGER},
            </if>
            <if test="marriage != null">
                user_marriage = #{marriage,jdbcType=INTEGER},
            </if>
            <if test="reportUrl != null and reportUrl != ''">
                report_url = #{reportUrl,jdbcType=VARCHAR},
            </if>
            <if test="reportStatus != null">
                report_status = #{reportStatus,jdbcType=INTEGER},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="examinationTime != null and examinationTime != ''">
                examination_time = #{examinationTime,jdbcType=VARCHAR},
            </if>
            <if test="appointDate != null">
                appoint_date = #{appointDate,jdbcType=TIMESTAMP}
            </if>
        </set>
        where jd_appointment_id = #{id}
        and yn = 1
    </update>
    <!--根据userPin和预约号查询获取报告详情-包含逻辑删除数据-->
    <select id="getReportInfoByNo4DB" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where user_pin = #{userPin,jdbcType=VARCHAR}
        and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 逻辑删除报告信息-->
    <update id="deleteReportByParam">
        update examination_report
        set yn=0
        <where>
            <if test="jdAppointmentId != null">
                and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            </if>
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="userPin != null">
                and user_pin = #{userPin}
            </if>
            <if test="bizCode != null">
                and biz_code = #{bizCode}
            </if>
            <if test="reportSource != null">
                and report_source = #{reportSource}
            </if>
            and yn = 1
        </where>

    </update>

    <select id="queryMyReportListForInterpretation" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where report_source != 10 and yn = 1 and company_no in ('22965482', '**********', '**********') and report_type = 1
    </select>

    <!--根据userPin和预约号查询获取报告详情-包含逻辑删除数据-->
    <select id="listByRelative" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1
        and biz_code = #{bizCode,jdbcType=INTEGER}
        and user_pin = #{pin,jdbcType=VARCHAR}
        and relatives_type = #{relativesType,jdbcType=INTEGER}
        and report_status = #{reportStatus,jdbcType=INTEGER}
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test="companyNos != null and companyNos.size() != 0">
            and company_no in
            <foreach collection="companyNos" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="reportType != null">
            and report_type = #{reportType,jdbcType=TINYINT}
        </if>
    </select>
    <!--根据userPin和预约号查询获取报告详情-包含逻辑删除数据-->
    <select id="listReportIds" resultType="java.lang.Long">
        select jd_appointment_id
        from examination_report
        where yn = 1
        and company_no = #{companyNo,jdbcType=BIGINT} and report_type = 1
    </select>

    <select id="queryReportListOfJdAppointmentIds" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where jd_appointment_id in
        <foreach collection="jdAppointmentIds" item="jdAppointmentId" open="(" close=")" separator=",">
            #{jdAppointmentId,jdbcType=BIGINT}
        </foreach>
        and parent_jd_report_id > 0
        and yn = 1
    </select>

    <select id="queryBizTypeNull" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where
        biz_type is null
        and yn = 1 and report_type = 1
        limit #{pageSize}
    </select>

    <update id="updateBizType">
        update examination_report
        set
        biz_type = #{bizType}
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <update id="updateSubReport">
        update examination_report
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="reportUrl != null and reportUrl !='' ">
                    report_url = #{reportUrl},
                </if>
                <if test="patientId != null ">
                    patient_id = #{patientId},
                </if>
                <if test="userName != null and userName != ''">
                    user_name_encrypt =
                    #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                    user_name_index =
                    #{userName,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
                </if>
                <if test="userPhone != null and userPhone != ''">
                    user_phone = #{userPhone},
                    user_phone_encrypt =
                    #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
                    user_phone_index =
                    #{userPhone,jdbcType=VARCHAR,typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle},
                </if>
                <if test="userGender != null">
                    user_gender = #{userGender},
                </if>
                <if test="reportStatus != null">
                    report_status = #{reportStatus,jdbcType=INTEGER},
                </if>
                <if test="structReportId != null and structReportId != ''">
                    struct_report_id = #{structReportId,jdbcType=VARCHAR},
                </if>
                <if test="brandId != null">
                    brand_id = #{brandId,jdbcType=BIGINT},
                </if>
                <if test="relativesType != null">
                    relatives_type = #{relativesType,jdbcType=INTEGER},
                </if>
                <if test="userAge != null">
                    user_age = #{userAge,jdbcType=INTEGER},
                </if>
                <if test="fileMd5 != null and fileMd5!=''">
                    file_md5 = #{fileMd5,jdbcType=VARCHAR},
                </if>
                <if test="examinationTime != null and examinationTime != ''">
                    examination_time =#{examinationTime,jdbcType=VARCHAR},
                </if>
                <if test="extendId != null and extendId != ''">
                    extend_id =#{extendId,jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and parent_jd_report_id = #{parentJdReportId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <update id="bindPin">

        update examination_report
        set user_pin = #{userPin,jdbcType=VARCHAR},
        patient_id = #{patientId,jdbcType=BIGINT}
        where yn = 1
        and user_pin = #{oldPin,jdbcType=VARCHAR}
        and jd_appointment_id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateReportVerifyStatus">
        update examination_report
        set report_verify_status = 1
        where jd_appointment_id = #{id}
        and yn = 1
    </update>
    <update id="updateReportBatch">
        <if test="ids != null and ids.size()>0">
            update examination_report
            set report_status = #{reportStatus}
            where jd_appointment_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <!-- 根据条件获取报告信息 -->
    <select id="queryReportEntityByParam" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="jdAppointmentId != null">
                and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            </if>
            <if test="reportId != null and reportId != ''">
                and report_id = #{reportId,jdbcType=VARCHAR}
            </if>
            <if test="extendId != null and extendId != ''">
                and extend_id = #{extendId,jdbcType=VARCHAR}
            </if>
            <if test="true">
                and yn = 1
            </if>
        </where>
    </select>

    <!-- 线下体检历史数据同步 -->
    <select id="listByCreateTimeRange" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            biz_code in (10,14) and report_status = 1 and yn = 1
            <if test="null != beginDate">
                and create_time &gt;= #{beginDate}
            </if>
            <if test="null != endDate">
                and create_time &lt;= #{endDate}
            </if>
            <if test="userPin != null and userPin != ''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
            <if test="bizCode != null">
                and biz_code = #{bizCode,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryListByExtendId" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            yn = 1
            and extend_id = #{extendId,jdbcType=VARCHAR}
            and report_status = 1
            <if test="userPin != null and userPin != ''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
        </where>


    </select>

    <select id="queryByNameAndPhone" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where jd_appointment_id in
        (
            select jd_appointment_id
            from examination_report
        <where>
            report_status = 1 and yn = 1
            <if test="userName != null and userName != ''">
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>

            <if test="userPhone != null and userPhone != ''">
                and user_phone = #{userPhone,jdbcType=VARCHAR}
            </if>
            <if test="reportType != null and reportType != ''">
                and report_type = #{reportType,jdbcType=INTEGER}
            </if>

        </where>
        )

    </select>

    <select id="queryReportByReportUrlAndPin"
            resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            yn = 1
            <if test="userPin != null and userPin != ''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
            <if test="reportUrl != null and reportUrl.size() != 0">
                and report_url in
                <foreach collection="reportUrl" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>

    </select>
    <select id="queryPatientAnalyseNumByUserPin" resultType="com.jd.health.medical.examination.domain.MyReportEntity">

        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1
          and report_status = 1
          and report_type = 1
          and struct_report_id is not null
          and patient_id is not null
          and user_pin = #{userPin}
    </select>

    <select id="queryReportOffset" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where id > #{offset}
        and yn = 1 and report_status = 1 and patient_id is not null and struct_report_id is not null and report_type = 1
        order by id asc
        limit #{pageSize}
    </select>
    <select id="queryReportOffsetAll" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where id > #{offset}
        and yn = 1 and report_type = 1
        order by id asc
        limit #{pageSize}
    </select>
    <select id="pageByUserFromAPP" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1 and report_type = 1 and (business_type != 16 or business_type is  null)
        <if test="userPin != null">
            and user_pin = #{userPin,jdbcType=VARCHAR,jdbcType=VARCHAR}
        </if>
        <if test="reportStatus != null">
            and report_status = #{reportStatus,jdbcType=INTEGER}
        </if>
        <if test="bizCode != null and bizCode != 30 and bizCode != 50">
            and biz_code = #{bizCode,jdbcType=INTEGER}
        </if>
        <if test="userName != null and userName != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_name=#{userName,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_name_index = #{userName, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userPhone != null and userPhone != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_phone=#{userPhone,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_phone_index = #{userPhone, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userCredentialNo != null and userCredentialNo != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_credential_no=#{userCredentialNo,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_credential_no_index = #{userCredentialNo, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="patientId != null">
            and patient_id = #{patientId,jdbcType=BIGINT}
        </if>
        <if test="userCredentialType != null">
            and user_credential_type = #{userCredentialType,jdbcType=BIGINT}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test = "notInFileType != null and notInFileType.size()>0">
            and (file_type not in
            <foreach collection="notInFileType" item="fileType" open="(" close=")" separator=",">
                #{fileType}
            </foreach> or file_type is null)
        </if>
        <if test="reportEndTime != null">
            and examination_time &lt;= #{reportEndTimeStr}
        </if>

        <if test="reportStartTime != null">
            and examination_time >= #{reportStartTimeStr}
        </if>



    </select>

    <select id="pageByCondition" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1 and report_type = 1 and (business_type != 16 or business_type is null )
        <if test="userPin != null">
            and user_pin = #{userPin,jdbcType=VARCHAR,jdbcType=VARCHAR}
        </if>
        <if test="reportStatus != null">
            and report_status = #{reportStatus,jdbcType=INTEGER}
        </if>
        <if test="bizCode != null and bizCode != 70">
            and biz_code = #{bizCode,jdbcType=INTEGER}
        </if>
        <if test="userName != null and userName != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_name=#{userName,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_name_index = #{userName, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userPhone != null and userPhone != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_phone=#{userPhone,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_phone_index = #{userPhone, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userCredentialNo != null and userCredentialNo != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_credential_no=#{userCredentialNo,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_credential_no_index = #{userCredentialNo, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="patientId != null">
            and patient_id = #{patientId,jdbcType=BIGINT}
        </if>
        <if test="userCredentialType != null">
            and user_credential_type = #{userCredentialType,jdbcType=BIGINT}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test = "notInFileType != null and notInFileType.size()>0">
            and (file_type not in
            <foreach collection="notInFileType" item="fileType" open="(" close=")" separator=",">
                #{fileType}
            </foreach> or file_type is null)
        </if>

        <if test="reportEndTime != null">
            and create_time &lt; #{reportEndTime}
        </if>

        <if test="reportStartTime != null">
            and create_time > #{reportStartTime}
        </if>

        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>

        <if test="titleId != null">
            and title_id = #{titleId}
        </if>

        <if test = "extendIdList != null and extendIdList.size()>0">
            and extend_id  in
            <foreach collection="extendIdList" item="fileType" open="(" close=")" separator=",">
                #{fileType}
            </foreach>
        </if>

        <if test="companyNo != null">
            and company_no = #{companyNo}
        </if>
        <if test="enterpriseId != null">
            and enterprise_id = #{enterpriseId}
        </if>

    </select>
    <select id="queryReportListByCondition"
            resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where yn = 1 and report_type = 1 and (business_type != 16 or business_type is null )
        <if test="userPin != null">
            and user_pin = #{userPin,jdbcType=VARCHAR,jdbcType=VARCHAR}
        </if>
        <if test="reportStatus != null">
            and report_status = #{reportStatus,jdbcType=INTEGER}
        </if>
        <if test="bizCode != null and bizCode != 70">
            and biz_code = #{bizCode,jdbcType=INTEGER}
        </if>
        <if test="userName != null and userName != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_name=#{userName,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_name_index = #{userName, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userPhone != null and userPhone != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_phone=#{userPhone,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_phone_index = #{userPhone, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="userCredentialNo != null and userCredentialNo != ''">
            <choose>
                <when test="@com.jd.security.aces.mybatis.vo.AcesProps@isWritePlaintext()">
                    and user_credential_no=#{userCredentialNo,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and user_credential_no_index = #{userCredentialNo, typeHandler=com.jd.security.aces.mybatis.handle.AcesIndexHandle}
                </otherwise>
            </choose>
        </if>
        <if test="patientId != null">
            and patient_id = #{patientId,jdbcType=BIGINT}
        </if>
        <if test="userCredentialType != null">
            and user_credential_type = #{userCredentialType,jdbcType=BIGINT}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test = "notInFileType != null and notInFileType.size()>0">
            and (file_type not in
            <foreach collection="notInFileType" item="fileType" open="(" close=")" separator=",">
                #{fileType}
            </foreach> or file_type is null)
        </if>

        <if test="reportEndTime != null">
            and create_time &lt; #{reportEndTime}
        </if>

        <if test="reportStartTime != null">
            and create_time > #{reportStartTime}
        </if>

        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>

        <if test="titleId != null">
            and title_id = #{titleId}
        </if>

        <if test = "extendIdList != null and extendIdList.size()>0">
            and extend_id  in
            <foreach collection="extendIdList" item="fileType" open="(" close=")" separator=",">
                #{fileType}
            </foreach>
        </if>

        <if test="companyNo != null">
            and company_no = #{companyNo}
        </if>
        <if test="enterpriseId != null">
            and enterprise_id = #{enterpriseId}
        </if>
    </select>
    <select id="queryDeleteById" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    </select>
    <select id="getReportInfoByCenterNo" resultType="com.jd.health.medical.examination.domain.MyReportEntity">
         select
        <include refid="Base_Column_List"/>
        from examination_report
        <where>
            <if test="userPin != null and userPin!=''">
                and user_pin = #{userPin,jdbcType=VARCHAR}
            </if>
            and report_center_id = #{reportCenterId,jdbcType=VARCHAR}
            and report_type = 1
            and yn = 1
            and report_status = 1
        </where>
    </select>

    <select id="queryReportForCenter" resultMap="myReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_report
        where
        report_center_id=#{reportCenterId}
        and user_pin = #{userPin}
        <if test="patientId != null">
            and patient_id = #{patientId}
        </if>
        and biz_code = 0
        and report_source = 1
        and yn = 1
        order by create_time desc
    </select>


</mapper>