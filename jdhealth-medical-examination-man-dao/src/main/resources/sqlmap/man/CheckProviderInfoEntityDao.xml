<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.CheckProviderInfoEntityDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.CheckProviderInfoEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="channel_no" property="channelNo" jdbcType="VARCHAR" />
    <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
    <result column="onsite_service_time" property="onsiteServiceTime" jdbcType="VARCHAR" />
    <result column="available_days_max" property="availableDaysMax" jdbcType="INTEGER" />
    <result column="onsite_service_sku" property="onsiteServiceSku" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="validity_days" property="validityDays" jdbcType="INTEGER" />
    <result column="vender_id" property="venderId" jdbcType="VARCHAR" />
    <result column="vender_name" property="venderName" jdbcType="VARCHAR" />
    <result column="service_url" property="serviceUrl" jdbcType="VARCHAR" />
    <result column="provider_desc" property="providerDesc" jdbcType="VARCHAR" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from examination_man_check_provider_onsite_services_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.CheckProviderInfoEntity" >
    insert into examination_man_check_provider_onsite_services_info (id, channel_no, channel_name, 
      onsite_service_time, available_days_max, onsite_service_sku, 
      yn, create_user, create_time, 
      update_user, update_time)
    values (#{id,jdbcType=BIGINT}, #{channelNo,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR},
      #{onsiteServiceTime,jdbcType=VARCHAR}, #{availableDaysMax,jdbcType=INTEGER}, #{onsiteServiceSku,jdbcType=VARCHAR}, 
      #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.CheckProviderInfoEntity" >
    update examination_man_check_provider_onsite_services_info
    set channel_no = #{channelNo,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      onsite_service_time = #{onsiteServiceTime,jdbcType=VARCHAR},
      available_days_max = #{availableDaysMax,jdbcType=INTEGER},
      onsite_service_sku = #{onsiteServiceSku,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select id, channel_no, channel_name, onsite_service_time, available_days_max, onsite_service_sku, 
    yn, create_user, create_time, update_user, update_time,validity_days,vender_id,vender_name,service_url,provider_desc
    from examination_man_check_provider_onsite_services_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, channel_no, channel_name, onsite_service_time, available_days_max, onsite_service_sku, 
    yn, create_user, create_time, update_user, update_time,validity_days,vender_id,vender_name,service_url,provider_desc
    from examination_man_check_provider_onsite_services_info
    where yn = 1
  </select>


  <select id="getCheckProvider" parameterType="com.jd.health.medical.examination.domain.CheckProviderInfoEntity" resultMap="BaseResultMap">
    select  channel_no, channel_name,validity_days,vender_id,vender_name,service_url,provider_desc
    from examination_man_check_provider_onsite_services_info
    where yn = 1
  </select>

  <update id="updateCheckProviderInfo" parameterType="com.jd.health.medical.examination.domain.CheckProviderInfoEntity">
    update examination_man_check_provider_onsite_services_info
    <set>
      <if test="channelNo != null and channelNo != ''">
        channel_no = #{channelNo,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="onsiteServiceTime != null and onsiteServiceTime != ''">
        onsite_service_time = #{onsiteServiceTime,jdbcType=VARCHAR},
      </if>
      <if test="availableDaysMax != null ">
        available_days_max = #{availableDaysMax,jdbcType=INTEGER},
      </if>
      <if test="onsiteServiceSku != null and onsiteServiceSku != ''">
        onsite_service_sku = #{onsiteServiceSku,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null ">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where yn = 1
    and channel_no = #{channelNo}
  </update>

  <select id="queryCheckProviderByChannelNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select channel_no, channel_name, onsite_service_time, available_days_max, onsite_service_sku,validity_days,vender_id,vender_name,service_url,provider_desc
    from  examination_man_check_provider_onsite_services_info
    where yn = 1
    and channel_no = #{channelNo}
  </select>

  <select id="queryCheckProviderByVenderId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select channel_no, channel_name, onsite_service_time, available_days_max, onsite_service_sku,validity_days,vender_id,vender_name,service_url,provider_desc
    from  examination_man_check_provider_onsite_services_info
    where yn = 1
      and vender_id = #{venderId}
  </select>


  <select id="queryReachDoorSkuByProviderNo" resultMap="BaseResultMap" parameterType="java.util.List">
        select channel_no, channel_name, onsite_service_time, available_days_max, onsite_service_sku,validity_days,vender_id,vender_name,service_url,provider_desc
        from  examination_man_check_provider_onsite_services_info
        where channel_no in <foreach collection="channelNoList" item="item" open="(" separator="," close=")">#{item}</foreach>
        and yn = 1
  </select>

  <select id="checkOnsiteServiceSkuRepeat" resultType="java.lang.Integer">
    select count(1)
    from examination_man_check_provider_onsite_services_info
    where channel_no != #{channelNo}
    and onsite_service_sku = #{onsiteServiceSku}
    and yn = 1
    limit 1
  </select>
</mapper>