<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.ExaminationQxManSmsTemplateParamDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sms_account" jdbcType="VARCHAR" property="smsAccount" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="param_id" jdbcType="VARCHAR" property="paramId" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_type" jdbcType="TINYINT" property="paramType" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sms_account, template_id, param_id, param_name, param_type, param_value, create_user, 
    create_time, yn
  </sql>

  <update id="updateByAccountAndTemplateIdAndParamName" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamEntity">
    update examination_qx_man_sms_template_param
    <set>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        param_type = #{paramType,jdbcType=TINYINT},
      </if>
      <if test="paramValue != null">
        param_value = #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
    </set>
    where yn = 1 and sms_account = #{smsAccount,jdbcType=VARCHAR} and template_id = #{templateId,jdbcType=VARCHAR} and param_name = #{paramName,jdbcType=VARCHAR}
  </update>
  <delete id="deleteByAccountAndTemplateIdAndParamName" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamEntity">
    update examination_qx_man_sms_template_param set yn = 0
    where yn = 1 and sms_account = #{smsAccount,jdbcType=VARCHAR} and template_id = #{templateId,jdbcType=VARCHAR} and param_name = #{paramName,jdbcType=VARCHAR}
  </delete>

    <delete id="deleteByAccountAndTemplateId">
      update examination_qx_man_sms_template_param set yn = 0
        where sms_account = #{smsAccount,jdbcType=VARCHAR} and template_id = #{templateId,jdbcType=VARCHAR}
    </delete>

    <select id="selectByAccountAndTemplateId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from examination_qx_man_sms_template_param
      where yn = 1 and sms_account = #{smsAccount} and template_id = #{templateId}
  </select>


  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamEntity" useGeneratedKeys="true">
    insert into examination_qx_man_sms_template_param (sms_account, template_id, param_id, 
      param_name, param_type, param_value, 
      create_user, create_time, yn
      )
    values (#{smsAccount,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{paramId,jdbcType=VARCHAR}, 
      #{paramName,jdbcType=VARCHAR}, #{paramType,jdbcType=TINYINT}, #{paramValue,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{yn,jdbcType=TINYINT}
      )
  </insert>

  <select id="selectByAccountAndTemplateBo" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_qx_man_sms_template_param
    where yn = 1
    and (sms_account, template_id) in
    <foreach collection="boList" item ="item" open="(" separator="," close=")">(#{item.smsAccount}, #{item.templateId})</foreach>
  </select>

  <select id="selectByParamIdList" parameterType="java.util.Set" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_qx_man_sms_template_param
    where yn = 1
    and param_id in
    <foreach collection="paramIdSet" item="item" open="(" separator="," close=")">#{item}</foreach>
  </select>
    <select id="selectByAccountAndTemplateIdAndParamId"
            resultType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamEntity">
      select
      <include refid="Base_Column_List" />
      from examination_qx_man_sms_template_param
      where yn = 1 and sms_account = #{smsAccount} and template_id = #{templateId} and param_id = #{paramId}
    </select>

  <select id="selectByAccountAndTemplateIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_qx_man_sms_template_param
    where yn = 1
    and (sms_account, template_id) = (#{smsAccount}, #{templateId})
    and param_type = #{code}
  </select>
</mapper>