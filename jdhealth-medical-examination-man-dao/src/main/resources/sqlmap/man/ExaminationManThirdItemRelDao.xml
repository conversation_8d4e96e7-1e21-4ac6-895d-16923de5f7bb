<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManThirdItemRelDao">
    <resultMap type="com.jd.health.medical.examination.domain.report.entity.basic.ExaminationManThirdItemRelEntity" id="ExaminationManThirdItemRelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="parentNo" column="parent_no" jdbcType="INTEGER"/>
        <result property="itemNo" column="item_no" jdbcType="INTEGER"/>
        <result property="yn" column="yn" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
         id,brand_id,parent_no, item_no, yn, create_user, create_time,update_user, update_time
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into examination_man_third_item_rel(brand_id, parent_no, item_no,create_user, create_time,
                                                         update_user, update_time)
        values (#{brandId}, #{parentNo}, #{itemNo}, #{createUser}, #{createTime}, #{updateUser},
                #{updateTime})
    </insert>

    <!--通过实体作为筛选条件分页查询-->
    <select id="queryPage" resultMap="ExaminationManThirdItemRelMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_item_rel
        <where>
            yn = 1
            <if test="brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
            <if test="itemNo != null">
                and item_no = #{itemNo}
            </if>
            <if test="parentNo != null">
                and parent_no = #{parentNo}
            </if>
        </where>
    </select>

    <delete id="deleteByItemNo">
        update
                examination_man_third_item_rel
        set yn = 0
        where item_no = #{itemNo}
        and yn = 1
    </delete>
</mapper>

