<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuPluginDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuPluginEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_no" jdbcType="BIGINT" property="projectNo"/>
        <result column="sku_no" jdbcType="VARCHAR" property="skuNo"/>
        <result column="group_no" jdbcType="BIGINT" property="groupNo"/>
        <result column="plugin_type" jdbcType="TINYINT" property="pluginType"/>
        <result column="plugin_sku_no" jdbcType="VARCHAR" property="pluginSkuNo"/>
        <result column="plugin_sku_name" jdbcType="VARCHAR" property="pluginSkuName"/>
        <result column="plugin_sku_alias" jdbcType="VARCHAR" property="pluginSkuAlias"/>
        <result column="plugin_sku_price" jdbcType="DECIMAL" property="pluginSkuPrice"/>
        <result column="plugin_sku_qx_price" jdbcType="DECIMAL" property="pluginSkuQxPrice"/>
        <result column="plugin_sku_qx_desc" jdbcType="VARCHAR" property="pluginSkuQxDesc"/>
        <result column="plugin_sales_msg" jdbcType="VARCHAR" property="pluginSalesMsg"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
        <result column="enterprise_pay_status" jdbcType="TINYINT" property="enterprisePayStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        project_no,
        sku_no,
        group_no,
        plugin_type,
        plugin_sku_no,
        plugin_sku_name,
        plugin_sku_alias,
        plugin_sku_price,
        plugin_sku_qx_price,
        plugin_sku_qx_desc,
        plugin_sales_msg,
        create_time,
        create_user,
        update_time,
        update_user,
        yn,
        enterprise_pay_status
    </sql>

    <!-- 根据条件查询列表 -->
    <select id="querySkuPluginInfoList" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuPluginEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_plugin_rel
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="projectNo != null and projectNo != ''">
                and project_no = #{projectNo}
            </if>
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="groupNo != null and groupNo != ''">
                and group_no = #{groupNo}
            </if>
            <if test="pluginType != null and pluginType != ''">
                and plugin_type = #{pluginType}
            </if>
            <if test="pluginSkuNo != null and pluginSkuNo != ''">
                and plugin_sku_no = #{pluginSkuNo}
            </if>
            <if test="pluginSkuName != null and pluginSkuName != ''">
                and plugin_sku_name LIKE CONCAT('%', #{pluginSkuName}, '%')
            </if>
            <if test="pluginSkuAlias != null and pluginSkuAlias != ''">
                and plugin_sku_alias LIKE CONCAT('%', #{pluginSkuAlias}, '%')
            </if>
            <if test="pluginSkuPrice != null and pluginSkuPrice != ''">
                and plugin_sku_price = #{pluginSkuPrice}
            </if>
            <if test="pluginSkuQxPrice != null and pluginSkuQxPrice != ''">
                and plugin_sku_qx_price = #{pluginSkuQxPrice}
            </if>
            <if test="pluginSkuQxDesc != null and pluginSkuQxDesc != ''">
                and plugin_sku_qx_desc = #{pluginSkuQxDesc,jdbcType=VARCHAR}
            </if>
            <if test="pluginSalesMsg != null and pluginSalesMsg != ''">
                and plugin_sales_msg = #{pluginSalesMsg,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser}
            </if>
            <if test="enterprisePayStatus != null">
                and enterprise_pay_status = #{enterprisePayStatus}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryListByBatchPluginSkuNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_plugin_rel
        where
        yn = 1
        and project_no = #{projectNo}
        and sku_no = #{skuNo}
        and plugin_type = #{pluginType}
        and plugin_sku_no in
        <foreach collection="list" item="pluginSkuNo" open="(" close=")" separator=",">
            #{pluginSkuNo}
        </foreach>
    </select>

    <!--根据参数动态插入-->
    <insert id="addSkuPlugin"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuPluginEntity">
        insert into examination_qx_man_sku_plugin_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectNo != null">
                project_no,
            </if>
            <if test="skuNo != null">
                sku_no,
            </if>
            <if test="groupNo != null">
                group_no,
            </if>
            <if test="pluginType != null">
                plugin_type,
            </if>
            <if test="pluginSkuNo != null">
                plugin_sku_no,
            </if>
            <if test="pluginSkuName != null">
                plugin_sku_name,
            </if>
            <if test="pluginSkuAlias != null">
                plugin_sku_alias,
            </if>
            <if test="pluginSkuPrice != null">
                plugin_sku_price,
            </if>
            <if test="pluginSkuQxPrice != null">
                plugin_sku_qx_price,
            </if>
            <if test="pluginSkuQxDesc != null">
                plugin_sku_qx_desc,
            </if>
            <if test="pluginSalesMsg != null">
                plugin_sales_msg,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="enterprisePayStatus != null">
                enterprise_pay_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectNo != null">
                #{projectNo, jdbcType=BIGINT},
            </if>
            <if test="skuNo != null">
                #{skuNo, jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                #{groupNo, jdbcType=BIGINT},
            </if>
            <if test="pluginType != null">
                #{pluginType, jdbcType=TINYINT},
            </if>
            <if test="pluginSkuNo != null">
                #{pluginSkuNo, jdbcType=VARCHAR},
            </if>
            <if test="pluginSkuName != null">
                #{pluginSkuName, jdbcType=VARCHAR},
            </if>
            <if test="pluginSkuAlias != null">
                #{pluginSkuAlias, jdbcType=VARCHAR},
            </if>
            <if test="pluginSkuPrice != null">
                #{pluginSkuPrice, jdbcType=DECIMAL},
            </if>
            <if test="pluginSkuQxPrice != null">
                #{pluginSkuQxPrice, jdbcType=DECIMAL},
            </if>
            <if test="pluginSkuQxDesc != null">
                #{pluginSkuQxDesc, jdbcType=VARCHAR},
            </if>
            <if test="pluginSalesMsg != null">
                #{pluginSalesMsg, jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser, jdbcType=VARCHAR},
            </if>
            <if test="enterprisePayStatus != null">
                #{enterprisePayStatus},
            </if>
        </trim>
    </insert>

    <!--根据参数动态更新-->
    <update id="updateSkuPlugin"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuPluginEntity">
        update examination_qx_man_sku_plugin_rel
        <set>
            <if test="projectNo != null">
                project_no = #{projectNo},
            </if>
            <if test="skuNo != null">
                sku_no = #{skuNo},
            </if>
            <if test="groupNo != null">
                group_no = #{groupNo},
            </if>
            <if test="pluginType != null">
                plugin_type = #{pluginType},
            </if>
            <if test="pluginSkuNo != null">
                plugin_sku_no = #{pluginSkuNo},
            </if>
            <if test="pluginSkuAlias != null and pluginSkuAlias != ''">
                plugin_sku_alias = #{pluginSkuAlias},
            </if>
            <!--            原插件价格（PluginSkuPrice）及企销插件价格（PluginSkuQxPrice）校验规则：-->
            <!--            新增插件时，可以输入原插件的数值，插入完成后，该数值按照业务要求，不可变更-->
            <!--            企销插件价格不可为0，不可低于参考价()的50%，可以变更-->
            <!--            <if test="pluginSkuPrice != null">-->
            <!--                plugin_sku_price = #{pluginSkuPrice},-->
            <!--            </if>-->
            <if test="pluginSkuQxPrice != null and pluginSkuQxPrice != 0">
                plugin_sku_qx_price = #{pluginSkuQxPrice},
            </if>
            <if test="pluginSkuQxDesc != null and pluginSkuQxDesc != ''">
                plugin_sku_qx_desc = #{pluginSkuQxDesc},
            </if>
            <if test="pluginSalesMsg != null">
                plugin_sales_msg = #{pluginSalesMsg},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="enterprisePayStatus != null">
                enterprise_pay_status = #{enterprisePayStatus},
            </if>
        </set>
        <!--根据skuNo，companyNo，plugin skuNO-->
        where project_no = #{projectNo}
        and sku_no = #{skuNo}
        and plugin_type = #{pluginType}
        and plugin_sku_no = #{pluginSkuNo}
        and yn = 1
    </update>


    <!--删除一个企销套餐插件-->
    <update id="deleteSkuPlugin">
        update examination_qx_man_sku_plugin_rel
        set yn = 0
        where project_no = #{projectNo}
        and sku_no = #{skuNo}
        and plugin_type = #{pluginType}
        and plugin_sku_no = #{pluginSkuNo}
        and yn = 1
    </update>

    <!--    查询插件sku是否被使用-->
    <select id="isSkuPluginInUsed"
            resultType="Integer">
        select count(1)
        from examination_qx_man_sku_plugin_rel
        where sku_no = #{skuNo}
        and plugin_type = #{pluginType}
        and plugin_sku_no = #{pluginSkuNo}
        and yn = 1
    </select>

    <!--    查询插件sku是否被使用-->
    <select id="isSkuPluginExists"
            resultType="Integer">
        select count(1)
        from examination_qx_man_sku_plugin_rel
        where project_no = #{projectNo}
        and sku_no = #{skuNo}
        and plugin_type = #{pluginType}
        and plugin_sku_no = #{pluginSkuNo}
        and yn = 1
    </select>

    <update id="updateEnterprisePayStatusBySkuNo">
        update examination_qx_man_sku_plugin_rel
        set
            enterprise_pay_status = #{enterprisePayStatus}
        where project_no = #{projectNo}
        and sku_no = #{skuNo}
        and plugin_type = #{pluginType}
        and yn = 1
    </update>

    <update id="deleteSkuPluginBySkuNo">
        update examination_qx_man_sku_plugin_rel
        set yn = 0
        where project_no = #{projectNo}
          and sku_no = #{skuNo}
          and plugin_type = #{pluginType}
          and yn = 1
    </update>

    <select id="countSkuPlugin" resultType="java.lang.Integer">
        select count(*)
        from examination_qx_man_sku_plugin_rel
        where project_no = #{projectNo}
          and sku_no = #{skuNo}
          and plugin_type = #{pluginType}
          and yn = 1
    </select>
</mapper>