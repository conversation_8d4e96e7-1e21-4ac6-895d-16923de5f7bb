<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.XfylApparatusSkuRelDao" >

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.apparatus.XfylApparatusSkuRelEntity">
        <result property="id" column="id"/>
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="venderId"  column="vender_id"/>
        <result property="venderCode"  column="vender_code"/>
        <result property="venderName"  column="vender_name"/>
        <result property="consumableId"  column="consumable_id"/>
        <result property="consumableName"  column="consumable_name"/>
        <result property="consumableImage"  column="consumable_image"/>
        <result property="consumableAmount"  column="consumable_amount"/>
        <result property="consumableNum"  column="consumable_num"/>
        <result property="remark" column="remark"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, sku_no, sku_name, vender_id, vender_code, vender_name, consumable_id, consumable_name,consumable_image,
        consumable_amount,consumable_num, remark, create_user, create_time, update_user, update_time, yn
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id"  useGeneratedKeys="true"
            parameterType="com.jd.health.medical.examination.domain.apparatus.XfylApparatusSkuRelEntity">
        insert into xfyl_apparatus_sku_rel (sku_no, sku_name, vender_id, vender_code, vender_name, consumable_id,
        consumable_name,consumable_image, consumable_amount,consumable_num, remark, create_user, create_time, update_user, update_time, yn)
        values (#{skuNo}, #{skuName}, #{venderId}, #{venderCode},#{venderName}, #{consumableId},
        #{consumableName}, #{consumableImage},#{consumableAmount},#{consumableNum}, #{remark}, #{createUser}, now(), #{updateUser},now(),1)
    </insert>

    <update id="updateBySkuNo">
        update xfyl_apparatus_sku_rel
        <set>
            <if test="consumableId != null and consumableId != ''">
                consumable_id = #{consumableId},
            </if>
            <if test="consumableName != null and consumableName != ''">
                consumable_name = #{consumableName},
            </if>
            <if test="consumableImage != null and consumableImage != ''">
                consumable_image = #{consumableImage},
            </if>
            <if test="consumableNum != null">
                consumable_num = #{consumableNum},
            </if>
            <if test="consumableAmount != null">
                consumable_amount = #{consumableAmount},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            update_time = now()
        </set>
        where sku_no = #{skuNo}
        and yn = 1
    </update>

    <!--通过实体作为筛选条件查询-->
    <select id="queryApparatusSkuRelList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xfyl_apparatus_sku_rel
        <where>
            yn = 1
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="consumableId != null and consumableId != ''">
                and consumable_id = #{consumableId}
            </if>
            <if test="skuName != null and skuName != ''">
                and sku_name LIKE CONCAT('%',#{skuName}, '%')
            </if>
        </where>
    </select>

    <select id="queryApparatusSkuRelBySku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xfyl_apparatus_sku_rel
        where yn = 1
        and sku_no = #{skuNo}
    </select>

</mapper>