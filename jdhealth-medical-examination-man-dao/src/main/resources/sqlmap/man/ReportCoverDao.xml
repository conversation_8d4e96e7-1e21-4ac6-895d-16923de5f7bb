<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ReportCoverDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_cover_type" jdbcType="INTEGER" property="reportCoverType" />
    <result column="report_cover_name" jdbcType="VARCHAR" property="reportCoverName" />
    <result column="report_title_name" jdbcType="VARCHAR" property="reportTitleName" />
    <result column="report_display_field" jdbcType="VARCHAR" property="reportDisplayField" />
    <result column="report_share" jdbcType="VARCHAR" property="reportShare" />
    <result column="report_cover_color" jdbcType="INTEGER" property="reportCoverColor" />
    <result column="yn" jdbcType="INTEGER" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <sql id="Base_Column_List">
    id, report_cover_type, report_cover_name, report_title_name, report_display_field,report_share,report_cover_color,yn,
    create_time, create_user, update_time,update_user
  </sql>

  <insert id="insertReportCover" parameterType="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity">
    insert into examination_man_report_cover(report_cover_type,report_cover_name, report_title_name,
        report_display_field,report_share,report_cover_color,create_user,update_user)
    values
        (#{reportCoverType},#{reportCoverName},#{reportTitleName},#{reportDisplayField},
         #{reportShare},#{reportCoverColor},#{createUser},#{updateUser})
  </insert>

  <update id="updateReportCoverConfig" parameterType="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity">
    update examination_man_report_cover
    <set>
      <if test="reportCoverType != null">
        report_cover_type = #{reportCoverType},
      </if>
      <if test="reportCoverName != null and reportCoverName != ''">
        report_cover_name = #{reportCoverName},
      </if>
      <if test="reportTitleName != null and reportTitleName != ''">
        report_title_name = #{reportTitleName},
      </if>
      <if test="reportDisplayField != null and reportDisplayField != ''">
        report_display_field = #{reportDisplayField},
      </if>
      <if test="reportShare != null and reportShare != ''">
        report_share = #{reportShare},
      </if>
      <if test="reportCoverColor != null">
        report_cover_color = #{reportCoverColor},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
    </set>
    <where>
      id = #{id}
      and yn = 1
    </where>
  </update>

  <select id="queryReportCoverConfigList" parameterType="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity"
          resultType="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity">
    select  <include refid="Base_Column_List"/>
    from examination_man_report_cover
    <where>
      <if test="reportCoverType != null">
        and report_cover_type = #{reportCoverType}
      </if>
      <if test="reportCoverName != null and reportCoverName != ''">
        and report_cover_name like concat('%',#{reportCoverName,jdbcType=VARCHAR},'%')
      </if>
      <if test="createUser != null and createUser!=''">
        and create_user = #{createUser,jdbcType=VARCHAR}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      and yn = 1
      order by create_time desc
    </where>
  </select>

  <select id="queryReportCoverConfig"
          resultType="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity">
    select  <include refid="Base_Column_List"/>
    from examination_man_report_cover
    <where>
      id = #{id}
      and yn = 1
    </where>
  </select>

  <select id="queryReportCoverConfigByType"
          resultType="com.jd.health.medical.examination.domain.personal.entity.ReportCoverEntity">
    select  <include refid="Base_Column_List"/>
    from examination_man_report_cover
    <where>
      report_cover_type = #{reportCoverType}
      and yn = 1
      order by create_time
      limit 1;
    </where>
  </select>

</mapper>