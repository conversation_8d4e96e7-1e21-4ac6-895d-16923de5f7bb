<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManThirdIndicatorDao">
    <resultMap type="com.jd.health.medical.examination.domain.report.entity.basic.ExaminationManThirdIndicatorEntity" id="ExaminationManThirdIndicatorMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="indicatorNo" column="indicator_no" jdbcType="INTEGER"/>
        <result property="indicatorName" column="indicator_name" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="upper" column="upper" jdbcType="VARCHAR"/>
        <result property="lower" column="lower" jdbcType="VARCHAR"/>
        <result property="normalRangeValue" column="normal_range_value" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="INTEGER"/>
        <result property="yn" column="yn" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
               id,
               brand_id,
               indicator_no,
               indicator_name,
               unit,
               upper,
               lower,
               normal_range_value,
               `source`,
               yn,
               create_user,
               create_time,
               update_user,
               update_time
    </sql>

    <!--查询单个-->
    <select id="queryByIndicatorNo" resultMap="ExaminationManThirdIndicatorMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_indicator
        where indicator_no = #{indicatorNo}
        and yn = 1
    </select>

    <!--通过实体作为筛选条件分页查询-->
    <select id="queryPage" resultMap="ExaminationManThirdIndicatorMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_indicator
        <where>
            yn = 1
            <if test="brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
            <if test="indicatorNo != null">
                and indicator_no = #{indicatorNo}
            </if>
            <if test="fuzzyIndicatorName != null and fuzzyIndicatorName != ''">
                and indicator_name like CONCAT('%',#{fuzzyIndicatorName,jdbcType=VARCHAR},'%')
            </if>
            <if test="indicatorName != null and indicatorName != ''">
                and indicator_name = #{indicatorName,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into examination_man_third_indicator(brand_id, indicator_no, indicator_name, unit, upper, lower,normal_range_value,source,
                                                      create_user, create_time, update_user, update_time)
        values (#{brandId}, #{indicatorNo}, #{indicatorName}, #{unit}, #{upper}, #{lower},#{normalRangeValue}, #{source}, #{createUser},
                #{createTime}, #{updateUser}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="updateByIndicatorNo">
        update examination_man_third_indicator
        <set>
            <if test="brandId != null and brandId != ''">
                brand_id = #{brandId},
            </if>
            <if test="indicatorName != null and indicatorName != ''">
                indicator_name = #{indicatorName},
            </if>
            <if test="unit != null and unit != ''">
                unit = #{unit},
            </if>
            <if test="upper != null and upper != ''">
                upper = #{upper},
            </if>
            <if test="lower != null and lower != ''">
                lower = #{lower},
            </if>
            <if test="normalRangeValue != null and normalRangeValue != ''">
                normal_range_value = #{normalRangeValue},
            </if>
            <if test="source != null">
                source = #{source},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where indicator_no = #{indicatorNo}
    </update>


</mapper>

