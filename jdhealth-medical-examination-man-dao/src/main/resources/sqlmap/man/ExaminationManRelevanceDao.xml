<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManRelevanceDao">
    <resultMap type="com.jd.health.medical.examination.domain.ExaminationManRelevanceEntity" id="ExaminationManRelevanceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="relevanceNo" column="relevance_no" jdbcType="INTEGER"/>
        <result property="relevanceType" column="relevance_type" jdbcType="VARCHAR"/>
        <result property="relevanceName" column="relevance_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="yn" column="yn" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
               relevance_no,
               relevance_type,
               relevance_name,
               yn,
               create_user,
               create_time,
               update_user,
               update_time
    </sql>

    <!--查询单个-->
    <select id="queryByRelevanceNo" resultMap="ExaminationManRelevanceMap">
        select id,
               relevance_no,
               relevance_type,
               relevance_name,
               create_time,
               create_user,
               update_time,
               update_user,
               yn
        from examination_man_relevance
        where relevance_no =  #{relevanceNo}
        and yn = 1
    </select>

    <select id="queryPage" resultType="com.jd.health.medical.examination.domain.ExaminationManRelevanceEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_relevance
        <where>
            yn = 1
            <if test="relevanceName != null and relevanceName != ''">
                and relevance_name like CONCAT('%',#{relevanceName,jdbcType=VARCHAR},'%')
            </if>
            <if test="relevanceType != null">
                and relevance_type = #{relevanceType}
            </if>
        </where>
        order by create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into examination_man_relevance(relevance_no, relevance_type, relevance_name, create_user,
                                              update_user)
        values (#{relevanceNo}, #{relevanceType}, #{relevanceName}, #{createUser},
                #{updateUser})
    </insert>

    <insert id="insertBatch">
        insert into examination_man_relevance(relevance_no, relevance_type, relevance_name,create_user,
        update_user, yn)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.relevanceNo}, #{entity.relevanceType}, #{entity.relevanceName},
            #{entity.createUser}, #{entity.updateUser}, #{entity.yn})
        </foreach>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateByRelevanceNo">
        update examination_man_relevance
        <set>
            <if test="relevanceType != null">
                relevance_type = #{relevanceType},
            </if>
            <if test="relevanceName != null and relevanceName != ''">
                relevance_name = #{relevanceName},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
        </set>
        where relevance_no = #{relevanceNo}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByRelevanceNo">
        update
         examination_man_relevance
        set yn = 0,
        update_user = #{operator}
        where relevance_no = #{relevanceNo}
        and yn = 1
    </delete>
</mapper>

