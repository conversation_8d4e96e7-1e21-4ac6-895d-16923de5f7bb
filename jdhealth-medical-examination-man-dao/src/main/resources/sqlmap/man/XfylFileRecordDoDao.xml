<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.basic.XfylFileRecordDoDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.basic.pojo.XfylFileRecordDo">
    <!--@mbg.generated-->
    <!--@Table xfyl_file_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_id" jdbcType="BIGINT" property="fileId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="extend_id" jdbcType="VARCHAR" property="extendId" />
    <result column="content_type" jdbcType="VARCHAR" property="contentType" />
    <result column="extend_file_name" jdbcType="VARCHAR" property="extendFileName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="biz_code" jdbcType="INTEGER" property="bizCode" />
    <result column="channel_no" jdbcType="VARCHAR" property="channelNo" />
    <result column="bucket_name" jdbcType="VARCHAR" property="bucketName" />
    <result column="yn" jdbcType="BIGINT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_id, file_name, extend_id, content_type, extend_file_name, `status`, expire_time,
    biz_code, channel_no, bucket_name, yn, create_time, update_time
  </sql>
  <select id="listByFileIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from xfyl_file_record
    where yn = 1
    and file_id in
    <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
      #{fileId,jdbcType=VARCHAR}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from xfyl_file_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.basic.pojo.XfylFileRecordDo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into xfyl_file_record (file_id, extend_id, content_type, 
    extend_file_name, `status`,
      expire_time, biz_code, channel_no, 
      bucket_name, yn, create_time, 
      update_time)
    values (#{fileId,jdbcType=BIGINT}, #{extendId,jdbcType=VARCHAR}, #{contentType,jdbcType=VARCHAR}, 
    #{extendFileName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{expireTime,jdbcType=TIMESTAMP}, #{bizCode,jdbcType=INTEGER}, #{channelNo,jdbcType=VARCHAR},
      #{bucketName,jdbcType=VARCHAR}, #{yn,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.basic.pojo.XfylFileRecordDo">
    <!--@mbg.generated-->
    insert into xfyl_file_record
    (file_id, file_name, extend_id,  content_type,  extend_file_name,  `status`,  expire_time,  biz_code,
    channel_no,  bucket_name,  yn,  create_time, update_time)
    values
    <foreach collection="list" index="index" item="item" separator=",">
          (#{item.fileId,jdbcType=BIGINT},
          #{item.fileName,jdbcType=VARCHAR},
          #{item.extendId,jdbcType=VARCHAR},
          #{item.contentType,jdbcType=VARCHAR},
          #{item.extendFileName,jdbcType=VARCHAR},
          #{item.status,jdbcType=TINYINT},
          #{item.expireTime,jdbcType=TIMESTAMP},
          #{item.bizCode,jdbcType=INTEGER},
          #{item.channelNo,jdbcType=BIGINT},
          #{item.bucketName,jdbcType=VARCHAR},
          #{item.yn,jdbcType=BIGINT},
          #{item.createTime,jdbcType=TIMESTAMP},
          #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>

  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.dao.basic.pojo.XfylFileRecordDo">
    <!--@mbg.generated-->
    update xfyl_file_record
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=BIGINT},
      </if>
      <if test="extendId != null">
        extend_id = #{extendId,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=VARCHAR},
      </if>
      <if test="extendFileName != null">
        extend_file_name = #{extendFileName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=INTEGER},
      </if>
      <if test="channelNo != null">
        channel_no = #{channelNo,jdbcType=VARCHAR},
      </if>
      <if test="bucketName != null">
        bucket_name = #{bucketName,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.dao.basic.pojo.XfylFileRecordDo">
    <!--@mbg.generated-->
    update xfyl_file_record
    set file_id = #{fileId,jdbcType=BIGINT},
      extend_id = #{extendId,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=VARCHAR},
      extend_file_name = #{extendFileName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      biz_code = #{bizCode,jdbcType=INTEGER},
      channel_no = #{channelNo,jdbcType=VARCHAR},
      bucket_name = #{bucketName,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>