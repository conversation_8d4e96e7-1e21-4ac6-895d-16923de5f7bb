<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.jd.health.medical.examination.dao.PackageItemDao" >

  <resultMap id="CompositionMap" type="com.jd.health.medical.examination.domain.personal.entity.PackageItemEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="pkg_id" property="pkgId" jdbcType="BIGINT" />
    <result column="pkg_item_id" property="pkgItemId" jdbcType="VARCHAR" />
    <result column="pkg_item_name" property="pkgItemName" jdbcType="VARCHAR" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="item_desc" property="itemDesc" jdbcType="VARCHAR" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
      id,
      pkg_id,
      pkg_item_id,
      pkg_item_name,
      item_name,
      item_desc,
      create_user,
      yn,
      create_time,
      update_time,
    </trim>
  </sql>

  <insert id="insertPackageItem" parameterType="com.jd.health.medical.examination.domain.personal.entity.PackageItemEntity" >
    insert into examination_man_equity_package_item (pkg_id,pkg_item_id,pkg_item_name,item_name,item_desc,create_user,yn,create_time,update_time)
    values (#{pkgId},
      #{pkgItemId,jdbcType=VARCHAR},#{pkgItemName,jdbcType=VARCHAR},#{itemName,jdbcType=VARCHAR},#{itemDesc,jdbcType=VARCHAR},#{createUser,jdbcType=VARCHAR},
      1,now(),now())
  </insert>
  <insert id="insertPackageItemList" parameterType="com.jd.health.medical.examination.domain.personal.entity.PackageItemEntity">
    insert into examination_man_equity_package_item (pkg_id,pkg_item_id,pkg_item_name,item_name,item_desc,create_user,yn,create_time,update_time)
    values
    <foreach collection="pkg" item="item">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{pkgId},
        #{item.pkgItemId,jdbcType=VARCHAR},#{item.pkgItemName,jdbcType=VARCHAR},#{item.itemName,jdbcType=VARCHAR},#{item.itemDesc,jdbcType=VARCHAR},#{item.createUser,jdbcType=VARCHAR},
        1,now(),now()
      </trim>
    </foreach>
  </insert>

  <select id="getPackageItemBypkgId" resultMap="CompositionMap"
                     parameterType="com.jd.health.medical.examination.domain.personal.entity.PackageItemEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_equity_package_item
    where pkg_id=#{pkgId,jdbcType=BIGINT} and yn = 1
  </select>
    <select id="selectAll"
            resultType="com.jd.health.medical.examination.domain.personal.entity.PackageItemEntity">
      select
      <include refid="Base_Column_List"/>
      from examination_man_equity_package_item
      where yn = 1
    </select>

    <delete id="deletePackageItemBypkgId" parameterType="java.lang.Long" >
        delete from examination_man_equity_package_item
        where pkg_id = #{pkgId,jdbcType=BIGINT}
    </delete>

</mapper>