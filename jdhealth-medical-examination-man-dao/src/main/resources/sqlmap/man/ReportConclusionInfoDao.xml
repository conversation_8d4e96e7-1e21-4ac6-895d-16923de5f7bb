<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ReportConclusionInfoDao">

    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.conclusion.ExaminationThirdConclusionEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="jd_appointment_id" jdbcType="BIGINT" property="jdAppointmentId"/>
        <result column="company_no" jdbcType="BIGINT" property="companyNo"/>
        <result column="user_pin" jdbcType="VARCHAR" property="userPin"/>
        <result column="conclusion_no" jdbcType="BIGINT" property="conclusionNo"/>
        <result column="age" jdbcType="INTEGER" property="age"/>
        <result column="sex" jdbcType="INTEGER" property="sex"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="strust_report_str" jdbcType="LONGVARCHAR" property="structReportStr"/>
        <result property="examinationTime" column="examination_time"/>
        <result property="userCredentialType" column="user_credential_type"/>
        <result property="userCredentialNo" column="user_credential_no"
              />
        <result property="userPhone" column="user_phone"
                />
        <result property="userName" column="user_name"
                />
        <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId"/>
        <result column="biz_code" jdbcType="INTEGER" property="bizCode"/>
    </resultMap>


    <sql id="Base_Column_List">
  id, jd_appointment_id, company_no, user_pin, conclusion_no,
  age, sex, strust_report_str, yn, create_time, update_time
</sql>


    <insert id="insert"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationThirdConclusionEntity">
        insert into report_conclusion_info (jd_appointment_id, company_no,
                                            user_pin, conclusion_no,
                                            age, sex, yn, create_time,
                                            update_time, strust_report_str,
                                            user_credential_type,
                                            user_credential_no,
                                            user_name,
                                            examination_time,
                                            user_phone,
                                            enterprise_id,
                                            biz_code
                                            )
        values (#{jdAppointmentId,jdbcType=BIGINT}, #{companyNo,jdbcType=BIGINT},
                #{userPin,jdbcType=VARCHAR}, #{conclusionNo,jdbcType=BIGINT},
                #{age,jdbcType=INTEGER}, #{sex,jdbcType=INTEGER}, #{yn,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{structReportStr,jdbcType=VARCHAR}, #{userCredentialType,jdbcType=INTEGER},
                #{userCredentialNo},
                #{userName,jdbcType=VARCHAR},
                #{examinationTime,jdbcType=VARCHAR},
                #{userPhone,jdbcType=VARCHAR},
                #{enterpriseId,jdbcType=VARCHAR},
                #{bizCode,jdbcType=INTEGER}
                )
    </insert>


    <insert id="insertDynamic"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationThirdConclusionEntity">
        insert into report_conclusion_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="jdAppointmentId != null">
                jd_appointment_id,
            </if>
            <if test="companyNo != null">
                company_no,
            </if>
            <if test="userPin != null">
                user_pin,
            </if>
            <if test="conclusionNo != null">
                conclusion_no,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="jdAppointmentId != null">
                #{jdAppointmentId,jdbcType=BIGINT},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=BIGINT},
            </if>
            <if test="userPin != null">
                #{userPin,jdbcType=VARCHAR},
            </if>
            <if test="conclusionNo != null">
                #{conclusionNo,jdbcType=BIGINT},
            </if>
            <if test="age != null">
                #{age,jdbcType=INTEGER},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>


    <update id="updateDynamic"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationThirdConclusionEntity">
        update report_conclusion_info
        <set>
            <if test="jdAppointmentId != null">
                jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo,jdbcType=BIGINT},
            </if>
            <if test="userPin != null">
                user_pin = #{userPin,jdbcType=VARCHAR},
            </if>
            <if test="conclusionNo != null">
                conclusion_no = #{conclusionNo,jdbcType=BIGINT},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=INTEGER},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=INTEGER},
            </if>
            <if test="structReportStr != null">
                strust_report_str = #{structReportStr,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn=1
    </update>


    <update id="update"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationThirdConclusionEntity">
  update report_conclusion_info
  set jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT},
    company_no = #{companyNo,jdbcType=BIGINT},
    user_pin = #{userPin,jdbcType=VARCHAR},
    conclusion_no = #{conclusionNo,jdbcType=BIGINT},
    age = #{age,jdbcType=INTEGER},
    sex = #{sex,jdbcType=INTEGER},
    yn = #{yn,jdbcType=INTEGER},
    strust_report_str = #{structReportStr,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
  where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT} and yn=1
</update>

    <select id="selectReportConclusionByJDid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from report_conclusion_info
        where jd_appointment_id=#{jdAppointmentId}
        and yn=1
        limit 1
    </select>

    <select id="selectById" parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationThirdConclusionEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from report_conclusion_info
        <where>
            <if test="jdAppointmentId != null">
                and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            </if>
            and yn=1
        </where>
        limit 1
    </select>


</mapper>