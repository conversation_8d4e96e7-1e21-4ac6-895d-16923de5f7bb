<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.SkuStoreDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity" id="skuStoreMap">
        <result property="id" column="id"/>
        <result property="skuNo" column="sku_no"/>
        <result property="storeName" column="store_name"/>
        <result property="goodsId" column="goods_id"/>
        <result property="storeId" column="store_id"/>
        <result property="channelNo" column="channel_no"/>
        <result property="skuStorePrice" column="sku_store_price"/>
        <result property="storePhone" column="store_phone"/>
        <result property="storeType" column="store_type"/>
        <result property="storeLevel" column="store_level"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="countyId" column="county_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityName" column="city_name"/>
        <result property="countyName" column="county_name"/>
        <result property="storeAddr" column="store_addr"/>
        <result property="status" column="status"/>
        <result property="thirdStoreStatus" column="third_store_status"/>
        <result property="vipType" column="vip_type"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="brandId" column="brand_id"/>
        <result property="brandName" column="brand_name"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="reportSupport" column="report_support"/>
        <result property="supportWriteOff" column="support_write_off"/>
        <result property="storeHours" column="store_hours"/>
        <result property="storeRemark" column="store_remark"/>
        <result property="groupNo" column="group_no"/>
        <result property="parentSkuNo" column="parent_sku_no"/>
        <result property="parentGoodsId" column="parent_goods_id"/>
        <result property="skuSpecies" column="sku_species"/>
        <result property="topStatus" column="top_status"/>
        <result property="topText" column="top_text"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            sku_no,
            store_name,
            goods_id,
            store_id,
            channel_no,
            sku_store_price,
            store_phone,
            store_type,
            store_level,
            province_id,
            city_id,
            county_id,
            province_name,
            city_name,
            county_name,
            status,
            third_store_status,
            vip_type,
            store_addr,
            lng,
            lat,
            brand_id,
            brand_name,
            yn,
            create_time,
            update_time,
            store_hours,
            store_remark,
            group_no,
            parent_sku_no,
            parent_goods_id,
            sku_species,
            top_status,
            top_text,
        </trim>
    </sql>
    <delete id="deleteSkuStore" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        delete from examination_man_sku_store
        <where>
            sku_no=#{skuNo}
            and channel_no=#{channelNo}
            and goods_id=#{goodsId}
            <if test="groupNo!=null">
                and group_no=#{groupNo}
            </if>
            and yn = 0
        </where>
    </delete>
    <delete id="deleteSkuStoreBySkuNo">
        delete
        from examination_man_sku_store
        where sku_no = #{skuNo}
    </delete>

    <update id="logicDeleteSkuStoreBySkuNo">
        update
            examination_man_sku_store
        set yn=0
        where sku_no = #{skuNo}
          and yn = 1
    </update>
    <!-- 根据Id查询 -->
    <!--<select id="selectSkuStoreById" resultMap="skuStoreMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>-->

    <!-- 根据条件查询列表 -->
    <select id="querySkuStoreList" resultMap="skuStoreMap"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="skuNo != null and skuNo!=''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="skuNoSets != null and skuNoSets.size() > 0">
                and sku_no in
                <foreach collection="skuNoSets" item="sku" open="(" separator="," close=")">
                    #{sku}
                </foreach>
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="storeName != null and storeName!=''">
                and store_name like CONCAT(#{storeName},'%')
            </if>
            <if test="goodsId != null and goodsId!=''">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="storeId != null">
                and store_id = #{storeId,jdbcType=BIGINT}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="skuStorePrice != null">
                and sku_store_price = #{skuStorePrice,jdbcType=INTEGER}
            </if>
            <if test="storePhone != null and storePhone!=''">
                and store_phone = #{storePhone,jdbcType=VARCHAR}
            </if>
            <if test="storeType != null">
                and store_type = #{storeType,jdbcType=INTEGER}
            </if>
            <if test="storeLevel != null">
                and store_level = #{storeLevel,jdbcType=INTEGER}
            </if>
            <if test="provinceId != null">
                and province_id = #{provinceId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and city_id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="countyId != null">
                and county_id = #{countyId,jdbcType=INTEGER}
            </if>
            <if test="provinceName != null and provinceName!=''">
                and province_name = #{provinceName,jdbcType=VARCHAR}
            </if>
            <if test="cityName != null and cityName!=''">
                and city_name = #{cityName,jdbcType=VARCHAR}
            </if>
            <if test="countyName != null and countyName!=''">
                and county_name = #{countyName,jdbcType=VARCHAR}
            </if>
            <if test="storeAddr != null and storeAddr != ''">
                and store_addr = #{storeAddr,jdbcType=VARCHAR}
            </if>
            <if test="lng != null">
                and lng = #{lng,jdbcType=DOUBLE}
            </if>
            <if test="lat != null">
                and lat = #{lat,jdbcType=DOUBLE}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="storeRemarkStatus != null and storeRemarkStatus==0">
                and store_remark = #{storeRemark}
            </if>
            <if test="storeRemarkStatus != null and storeRemarkStatus==1">
                and store_remark &lt;&gt; #{storeRemark}
            </if>
            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies}
            </if>
            <if test="parentGoodsId != null and parentGoodsId != ''">
                and parent_goods_id = #{parentGoodsId}
            </if>
            <if test="thirdStoreStatus != null">
                and third_store_status = #{thirdStoreStatus}
            </if>
            <if test="vipType!=null">
                and vip_type = #{vipType}
            </if>
            and yn = 1
        </where>
    </select>


    <select id="querySkuStoreByGoodsIdAndAddr"
            resultMap="skuStoreMap"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        select
        s.sku_no,s.channel_no,s.store_name,s.goods_id,s.store_id,s.store_phone,s.store_type,s.store_level,s.store_addr,s.store_hours,
        t.report_support,t.support_write_off,s.city_name,s.province_id,s.store_remark, s.parent_sku_no, s.sku_species,s.vip_type,
        s.brand_id,s.brand_name from examination_man_sku_store s join examination_man_third_store t on s.store_id = t.store_id and
        s.channel_no=t.channel_no
        <where>
            s.status = 1 AND s.yn = 1
            and s.third_store_status = 1
            AND s.sku_no = #{skuNo,jdbcType=VARCHAR}
            <if test="provinceId != null">
                and s.province_id = #{provinceId,jdbcType=VARCHAR}
            </if>
            <if test="cityId != null">
                and s.city_id = #{cityId,jdbcType=VARCHAR}
            </if>
            <if test="countyId != null">
                and s.county_id = #{countyId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and s.channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="groupNo != null">
                and s.group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="channelNo != null">
                and s.channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="skuSpecies != null">
                and s.sku_species = #{skuSpecies}
            </if>
            <if test="vipType != null">
                and s.vip_type = #{vipType}
            </if>
        </where>
    </select>

    <!--根据门店和商家编码查门店数据-->
    <select id="getSkuStoreByPrimaryKey" resultMap="skuStoreMap" parameterType="java.lang.Long">
        select s.id,
               s.sku_no,
               s.store_name,
               s.goods_id,
               s.store_addr,
               s.store_id,
               s.channel_no,
               s.sku_store_price,
               s.store_phone,
               s.store_type,
               s.store_level,
               s.store_hours,
               s.province_id,
               s.province_name,
               s.city_id,
               s.city_name,
               s.county_id,
               s.county_name,
               s.third_store_status,
               s.store_remark,
               s.lng,
               s.lat,
               t.report_support,
               t.support_write_off,
               s.yn,
               s.status,
               s.group_no,
               s.parent_goods_id,
               s.parent_sku_no,
               s.sku_species,
               s.vip_type,
               s.brand_id,
               s.brand_name,
               s.top_status,
               s.top_text
        from examination_man_sku_store s
                 join examination_man_third_store t on s.store_id = t.store_id and s.channel_no = t.channel_no
        where s.id = #{id,jdbcType=BIGINT}
    </select>

    <!--根据门店和商家编码查门店数据-->
    <select id="getSkuStoreByTest" resultMap="skuStoreMap">
        select s.sku_no,
               s.store_name,
               s.goods_id,
               s.store_addr,
               s.store_id,
               s.channel_no,
               s.sku_store_price,
               s.store_phone,
               s.store_type,
               s.store_level,
               s.store_hours,
               s.province_id,
               s.city_id,
               s.city_name,
               s.county_id,
               s.third_store_status,
               s.lng,
               s.lat,
               t.report_support,
               t.support_write_off,
               s.yn,
               s.status,
               s.brand_id,
               s.brand_name
        from examination_man_sku_store s
                 join examination_man_third_store t on s.store_id = t.store_id and s.channel_no = t.channel_no
            and s.third_store_status = 1 and s.status = 1 and s.yn = 1
    </select>

    <select id="getTotalCount" resultType="java.lang.Integer">
        select
        count(*)
        from examination_man_sku_store
        <where>
            sku_no=#{skuNo}
            and goods_id=#{goodsId}
            and channel_no=#{channelNo}
            <if test="flag==false">
                and status = 1
                and third_store_status = 1
            </if>
            <if test="groupNo!=null">
                and group_no=#{groupNo}
            </if>
            and yn = 1
        </where>
    </select>

    <select id="getTotalCountBySkuPlugin" resultType="java.lang.Integer">
        select
        count(*)
        from examination_man_sku_store
        <where>
            parent_sku_no= #{parentSkuNo}
            and sku_no=#{skuNo}
            and group_no = #{groupNo}
            <!-- 获取该JD加项包下，固定的商家门店数量-->
            <if test="channelNo != null">
                and channel_no= #{channelNo}
            </if>
            <!-- 获取固定的商家 加项包下属门店-->
            <if test="goodsId != null and goodsId != ''">
                and goods_id = #{goodsId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="thirdStoreStatus != null">
                and third_store_status = #{thirdStoreStatus}
            </if>
            and sku_species = 2
            and yn = 1
        </where>
    </select>

    <select id="getStoresBySkuPlugin" resultMap="skuStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        <where>
            parent_sku_no= #{parentSkuNo}
            and sku_no=#{skuNo}
            and group_no = #{groupNo}
            and sku_species = #{skuSpecies}
            <!-- 获取该JD加项包下，固定的商家门店-->
            <if test="channelNo != null">
                and channel_no= #{channelNo}
            </if>
            <!-- 获取固定的商家 加项包下属门店-->
            <if test="goodsId != null and goodsId != ''">
                and goods_id = #{goodsId}
            </if>
            <if test="parentGoodsId != null and parentGoodsId != ''">
                and parent_goods_id = #{parentGoodsId}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="thirdStoreStatus != null">
                and third_store_status = #{thirdStoreStatus}
            </if>
            <if test="storeType != null">
                and store_type = #{storeType}
            </if>
            <if test="provinceId != null and provinceId !=''">
                and province_id = #{provinceId}
            </if>
            <if test="cityId != null and cityId !=''">
                and city_id = #{cityId}
            </if>
            <if test="countyId != null and countyId !=''">
                and county_id = #{countyId}
            </if>
            <if test="storeName != null and storeName!=''">
                and store_name like CONCAT(#{storeName},'%')
            </if>
            and yn = 1
        </where>
    </select>

    <select id="querySkuNoByChannelNoAddGoodsId"
            resultType="java.lang.String">
        select distinct sku_no
        from examination_man_sku_store
        where channel_no = #{channelNo}
          and goods_id = #{goodsId}
          and sku_species = #{skuSpecies}
          and yn = 1
    </select>

    <!--查询sku预约记录的结算价格-->
    <select id="querySkuStorePrice" resultType="java.lang.Integer">
        select sku_store_price from examination_man_sku_store
        <where>
            sku_no = #{skuNo}
            and channel_no = #{channelNo}
            and store_id = #{storeId}
            and status = 1
            and yn = #{yn}
            and sku_species = #{skuSpecies}
            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo}
            </if>
            <if test="goodsId != null and goodsId !=''">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
        </where>
        order by third_store_status,status desc limit 1
    </select>

    <select id="querySkuStoreBySkuNoAndChannelNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        <where>
            <if test="skuNo != null and skuNo!=''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="groupNo != null and groupNo!=''">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies}
            </if>
            and yn = 1 limit 1
        </where>
    </select>

    <select id="querySkuStoreEntity"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity"
            resultType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        <where>
            <if test="skuNo != null and skuNo!=''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="groupNo != null and groupNo!=''">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 插入实体 -->
    <insert id="insertSkuStore" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_sku_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            sku_no,
            store_name,
            goods_id,
            store_id,
            channel_no,
            sku_store_price,
            store_phone,
            store_type,
            store_level,
            province_id,
            city_id,
            county_id,
            province_name,
            city_name,
            county_name,
            store_addr,
            status,
            third_store_status,
            lng,
            lat,
            brand_id,
            brand_name,
            yn,
            create_time,
            update_time,
            group_no,
            parent_sku_no,
            parent_goods_id,
            sku_species,
            <if test="topStatus != null">
                top_status,
            </if>
            <if test="topText != null">
                top_text,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{skuNo,jdbcType=VARCHAR},
            #{storeName,jdbcType=VARCHAR},
            #{goodsId,jdbcType=BIGINT},
            #{storeId,jdbcType=BIGINT},
            #{channelNo,jdbcType=BIGINT},
            #{skuStorePrice,jdbcType=INTEGER},
            #{storePhone,jdbcType=VARCHAR},
            #{storeType,jdbcType=INTEGER},
            #{storeLevel,jdbcType=INTEGER},
            #{provinceId,jdbcType=INTEGER},
            #{cityId,jdbcType=INTEGER},
            #{countyId,jdbcType=INTEGER},
            #{provinceName,jdbcType=VARCHAR},
            #{cityName,jdbcType=VARCHAR},
            #{countyName,jdbcType=VARCHAR},
            #{storeAddr,jdbcType=VARCHAR},
            #{status,jdbcType=INTEGER},
            #{thirdStoreStatus,jdbcType=INTEGER},
            #{lng,jdbcType=DOUBLE},
            #{lat,jdbcType=DOUBLE},
            #{brandId,jdbcType=BIGINT},
            #{brandName,jdbcType=VARCHAR},
            1,
            now(),
            now(),
            #{groupNo,jdbcType=BIGINT},
            #{parentSkuNo},
            #{parentGoodsId},
            #{skuSpecies},
            <if test="topStatus != null">
                #{topStatus,jdbcType=INTEGER},
            </if>
            <if test="topText != null">
                #{topText,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertSkuStoreList">
        insert into examination_man_sku_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            sku_no,
            store_name,
            goods_id,
            store_id,
            channel_no,
            sku_store_price,
            store_phone,
            store_type,
            store_level,
            province_id,
            city_id,
            county_id,
            province_name,
            city_name,
            county_name,
            store_addr,
            status,
            lng,
            lat,
            brand_id,
            brand_name,
            create_time,
            update_time,
            store_hours,
            group_no,
            parent_sku_no,
            parent_goods_id,
            sku_species,
            third_store_status,
            vip_type,
            top_status,
            top_text,
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.skuNo,jdbcType=VARCHAR},
                #{item.storeName,jdbcType=VARCHAR},
                #{item.goodsId,jdbcType=BIGINT},
                #{item.storeId,jdbcType=BIGINT},
                #{item.channelNo,jdbcType=BIGINT},
                #{item.skuStorePrice,jdbcType=INTEGER},
                #{item.storePhone,jdbcType=VARCHAR},
                #{item.storeType,jdbcType=INTEGER},
                #{item.storeLevel,jdbcType=INTEGER},
                #{item.provinceId,jdbcType=INTEGER},
                #{item.cityId,jdbcType=INTEGER},
                #{item.countyId,jdbcType=INTEGER},
                #{item.provinceName,jdbcType=VARCHAR},
                #{item.cityName,jdbcType=VARCHAR},
                #{item.countyName,jdbcType=VARCHAR},
                #{item.storeAddr,jdbcType=VARCHAR},
                #{item.status,jdbcType=INTEGER},
                #{item.lng,jdbcType=DOUBLE},
                #{item.lat,jdbcType=DOUBLE},
                #{item.brandId,jdbcType=BIGINT},
                #{item.brandName,jdbcType=VARCHAR},
                now(),
                now(),
                #{item.storeHours},
                #{item.groupNo,jdbcType=BIGINT},
                #{item.parentSkuNo},
                #{item.parentGoodsId},
                #{item.skuSpecies},
                #{item.thirdStoreStatus},
                #{item.vipType},
                #{item.topStatus,jdbcType=INTEGER},
                #{item.topText,jdbcType=VARCHAR},
            </trim>
        </foreach>
    </insert>
    <!-- 修改实体 -->
    <update id="updateSkuStoreSelective"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        update examination_man_sku_store
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="skuNo != null and skuNo != ''">
                    sku_no = #{skuNo,jdbcType=VARCHAR},
                </if>
                <if test="storeName != null and storeName != ''">
                    store_name = #{storeName,jdbcType=VARCHAR},
                </if>
                <if test="goodsId != null and goodsId != ''">
                    goods_id = #{goodsId,jdbcType=BIGINT},
                </if>
                <if test="skuStorePrice != null and skuStorePrice != ''">
                    sku_store_price = #{skuStorePrice,jdbcType=INTEGER},
                </if>
                <if test="storePhone != null and storePhone != ''">
                    store_phone = #{storePhone,jdbcType=VARCHAR},
                </if>
                <if test="storeType != null and storeType != ''">
                    store_type = #{storeType,jdbcType=INTEGER},
                </if>
                <if test="storeLevel != null and storeLevel != ''">
                    store_level = #{storeLevel,jdbcType=INTEGER},
                </if>
                <if test="provinceId != null and provinceId != ''">
                    province_id = #{provinceId,jdbcType=INTEGER},
                </if>
                <if test="cityId != null and cityId != ''">
                    city_id = #{cityId,jdbcType=INTEGER},
                </if>
                <if test="countyId != null and countyId != ''">
                    county_id = #{countyId,jdbcType=INTEGER},
                </if>
                <if test="provinceName != null and provinceName != ''">
                    province_name = #{provinceName,jdbcType=VARCHAR},
                </if>
                <if test="cityName != null and cityName != ''">
                    city_name = #{cityName,jdbcType=VARCHAR},
                </if>
                <if test="countyName != null and countyName != ''">
                    county_name = #{countyName,jdbcType=VARCHAR},
                </if>
                <if test="storeAddr != null and storeAddr != ''">
                    store_addr = #{storeAddr,jdbcType=VARCHAR},
                </if>
                <if test="status != null and status != ''">
                    status = #{status,jdbcType=INTEGER},
                </if>
                <if test="lng != null and lng != ''">
                    lng = #{lng,jdbcType=DOUBLE},
                </if>
                <if test="lat != null and lat != ''">
                    lat = #{lat,jdbcType=DOUBLE},
                </if>
                <if test="brandId != null">
                    brand_id = #{brandId,jdbcType=BIGINT},
                </if>
                <if test="brandName != null and brandName != ''">
                    brand_name = #{brandName,jdbcType=VARCHAR},
                </if>
                <if test="groupNo != null and groupNo != ''">
                    group_no = #{groupNo,jdbcType=BIGINT},
                </if>
                <if test="storeHours != null and storeHours != ''">
                    store_hours = #{storeHours,jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and store_id = #{storeId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 根据storeId更新门店基础信息 -->
    <update id="updateSkuStore" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        update examination_man_sku_store
        <set>
            brand_id = #{brandId,jdbcType=BIGINT},
            brand_name = #{brandName,jdbcType=VARCHAR},
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and store_id = #{storeId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 根据storeId更新门店基础信息 -->
    <update id="updateByGoodsStores">
        update examination_man_sku_store
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="vip_type = case store_id" suffix="end,">
                <foreach collection="entities" index="index" item="item">
                    <if test="item.vipType != null">
                        when #{item.storeId,jdbcType=VARCHAR} then #{item.vipType,jdbcType=INTEGER}
                    </if>
                </foreach>
                else vip_type
            </trim>
        </trim>
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id = #{goodsId,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 套餐下架、门店下架、关系解绑更新-->
    <update id="skuStoreOff">
        update examination_man_sku_store
        set third_store_status = 2
        <where>
            yn = 1
            <if test="parentGoodsId != null and parentGoodsId != ''">
                and parent_goods_id = #{parentGoodsId,jdbcType=VARCHAR}
            </if>
            <if test="goodsId != null and goodsId != ''">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null and channelNo != ''">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies, jdbcType=INTEGER}
            </if>
            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo,jdbcType=VARCHAR}
            </if>
            <if test="thirdStoreStatus != null">
                and third_store_status = #{thirdStoreStatus,jdbcType=INTEGER}
            </if>
        </where>
    </update>


    <update id="updateSkuStorePriceById">
        update examination_man_sku_store
        <set>
            sku_store_price = #{skuStorePrice},
            <if test="skuStorePrice!=null and skuStorePrice!=0">
                status=1,
            </if>
            <if test="skuStorePrice==null or skuStorePrice==0">
                status=0,
            </if>
            store_remark = #{storeRemark}
        </set>
        where id=#{id} and yn=1
    </update>
    <update id="updateSkuStoreAddr">
        update examination_man_sku_store
        set province_id=#{provinceId},
            province_name=#{provinceName},
            city_id=#{cityId},
            city_name=#{cityName},
            county_Id=#{countyId},
            county_name=#{countyName},
            store_addr=#{storeAddr},
            lng=#{lng},
            lat=#{lat}
        where channel_no = #{channelNo}
          and store_id = #{storeId}
          and yn = 1
    </update>

    <!--查询三级地址-->
    <select id="queryStoreAddressList"
            resultMap="skuStoreMap"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        select province_id, city_id, county_id, province_name, city_name, county_name
        from examination_man_sku_store
        <where>
            yn = 1
            and third_store_status = 1
            and status = 1
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="provinceId != null">
                and province_id = #{provinceId,jdbcType=VARCHAR}
            </if>
            <if test="cityId != null">
                and city_id = #{cityId,jdbcType=VARCHAR}
            </if>
            <if test="countyId != null">
                and county_id = #{countyId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no=#{channelNo}
            </if>
            <if test="storeId != null and storeId != '' ">
                and store_id=#{storeId}
            </if>
            <if test="groupNo!=null">
                and group_no=#{groupNo}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies, jdbcType=INTEGER}
            </if>
        </where>
    </select>
    <select id="selectStartCountBySkuNo"
            resultType="java.lang.Integer">
        select
        count(*)
        from examination_man_sku_store
        where sku_no=#{skuNo}
        and status= 1
        and third_store_status = 1
        and yn = 1
    </select>

    <select id="selectSkuStoreAndThirdStoreBySkuNo"
            resultMap="skuStoreMap">
        select s.id,
               s.sku_no,
               s.store_name,
               s.goods_id,
               s.store_id,
               s.channel_no,
               s.sku_store_price,
               s.store_phone,
               s.store_type,
               s.store_level,
               s.province_id,
               s.city_id,
               s.county_id,
               s.province_name,
               s.city_name,
               s.county_name,
               s.status,
               s.third_store_status,
               s.store_addr,
               s.lng,
               s.lat,
               s.brand_id,
               s.brand_name,
               s.yn,
               s.create_time,
               s.update_time,
               s.store_hours,
               s.store_remark,
               s.group_no,
               s.parent_sku_no,
               s.parent_goods_id,
               s.sku_species,
               t.report_support,
               t.support_write_off
        from examination_man_sku_store s
                 left join examination_man_third_store t on s.store_id = t.store_id and s.channel_no = t.channel_no
        where s.sku_no = #{skuNo}
          and s.sku_species = #{skuSpecies}
          and s.yn = 1
          and t.yn = 1
          and s.status = 1
    </select>

    <select id="selectValidSkuStoreCountBySkuNo" resultType="java.lang.Integer">
        select count(1)
        from examination_man_sku_store
        where sku_no = #{skuNo}
          and status = 1
          and third_store_status = 1
          and yn = 1
    </select>

    <!-- 套餐下架更新-->
    <update id="batchOffByGoods">
        update examination_man_sku_store
        set third_store_status = 2
        <where>
            yn = 1
            and goods_id in
            <foreach collection="goodsIds" item="goodsId" open="(" close=")" separator=",">
                #{goodsId,jdbcType=VARCHAR}
            </foreach>
            and channel_no = #{channelNo,jdbcType=BIGINT}
            and third_store_status = #{thirdDataStatus,jdbcType=INTEGER}
            and sku_species = #{goodsType,jdbcType=INTEGER}
        </where>
    </update>
    <!-- 套餐下架更新-->
    <update id="batchOffByGoodsStores">
        update examination_man_sku_store
        set third_store_status = 2
        <where>
            yn = 1
            and channel_no = #{channelNo,jdbcType=BIGINT}
            and goods_id = #{goodsId,jdbcType=VARCHAR}
            and third_store_status  =#{thirdDataStatus,jdbcType=INTEGER}
            and sku_species = #{skuSpecies,jdbcType=INTEGER}
            and store_id in
            <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId,jdbcType=VARCHAR}
            </foreach>
        </where>
    </update>
    <!-- 套餐关系下架，加项包门店失效 -->
    <update id="batchOffByGoodsRelPackage">
        update examination_man_sku_store
        set third_store_status = 2
        <where>
            yn = 1
            and channel_no = #{channelNo,jdbcType=BIGINT}
            and goods_id = #{packageId,jdbcType=VARCHAR}
            and third_store_status  =#{thirdDataStatus,jdbcType=INTEGER}
            and parent_goods_id in
            <foreach collection="parentGoodsIds" item="parentGoodsId" open="(" close=")" separator=",">
                #{parentGoodsId,jdbcType=VARCHAR}
            </foreach>
        </where>
    </update>
    <!-- 套餐关系下架，加项包门店失效 -->
    <update id="batchOffByGoodsRelParent">
        update examination_man_sku_store
        set third_store_status = 2
        <where>
            yn = 1
            and channel_no = #{channelNo,jdbcType=BIGINT}
            and parent_goods_id = #{parentGoodsId,jdbcType=VARCHAR}
            and third_store_status  =#{thirdDataStatus,jdbcType=INTEGER}
            and goods_id in
            <foreach collection="packageIds" item="packageId" open="(" close=")" separator=",">
                #{packageId,jdbcType=VARCHAR}
            </foreach>
        </where>
    </update>

    <select id="listByGoodsRelPackage" resultMap="skuStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        where yn = 1
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id = #{packageId,jdbcType=VARCHAR}
        and parent_goods_id in
        <foreach collection="parentGoodsIds" item="parentGoodsId" open="(" separator="," close=")">
            #{parentGoodsId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="listByGoodsRelParent" resultMap="skuStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        where yn = 1
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and parent_goods_id = #{parentGoodsId,jdbcType=VARCHAR}
        and goods_id in
        <foreach collection="packageIds" item="packageId" open="(" separator="," close=")">
            #{packageId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="updateSkuStoreYn">
        update examination_man_sku_store
        <set>
            yn = 0
        </set>
        <where>
            sku_no=#{skuNo}
            and channel_no=#{channelNo}
            and goods_id=#{goodsId}
            <if test="groupNo!=null">
                and group_no=#{groupNo}
            </if>
            and yn = 1
        </where>
    </update>


    <!--批量更新结算价-->
    <update id="updateSkuStorePrice" >
            update examination_man_sku_store
            <set>
                <if test="skuStorePrice != null and skuStorePrice != 0">
                    sku_store_price = #{skuStorePrice,jdbcType=INTEGER},
                    status = 1,
                </if>
                <if test="skuStorePrice == null">
                    sku_store_price = NULL,
                    status = 0,
                </if>
                <if test="skuStorePrice == 0">
                    sku_store_price = 0,
                    status = 0,
                </if>

                <if test="storeRemark != null and storeRemark != ''">
                    store_remark = #{storeRemark,jdbcType=VARCHAR},
                </if>
                <if test="storeRemark == null or storeRemark == ''">
                    store_remark = '',
                </if>
                <if test="skuSpecies != null">
                    sku_species = #{skuSpecies},
                </if>
            </set>
            where yn=1
            <if test="skuNo != null">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="storeId != null">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
    </update>

    <!-- 更新预约门店表的vip状态 -->
    <update id="updateSkuStoreVipType">
        update examination_man_sku_store
        set vip_type =#{vipType,jdbcType=INTEGER}
        <where>
            <if test="goodsId != null">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="storeId != null">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="parentGoodsId != null">
                and parent_goods_id = #{parentGoodsId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            and yn = 1
        </where>
    </update>

    <update id="updateSkuStoreGroupNo">
        update examination_man_sku_store
        set group_no =#{groupNo}
        where sku_no = #{skuNo}
          and yn = 1
    </update>


    <update id="deleteBindByGoods"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        update examination_man_sku_store
        set yn = 0
        <where>
            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo}
            </if>
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo}
            </if>
            <if test="goodsId != null and goodsId != ''">
                and goods_id = #{goodsId}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies}
            </if>
            and yn = 1
        </where>
    </update>




    <update id="updateStoreStatusById">
        <if test="ids != null and ids.size() > 0">
            update examination_man_sku_store
            set third_store_status = #{storeStatus}
            where id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="batchSetPriceById">
        update examination_man_sku_store
        <set>
            sku_store_price = #{skuStorePrice},
            <if test="skuStorePrice!=null and skuStorePrice!=0">
                status=1,
            </if>
            <if test="skuStorePrice==null or skuStorePrice==0">
                status=0,
            </if>
        </set>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and yn=1
    </update>

    <update id="setPriceByPackageId">
        update examination_man_sku_store
        <set>
            sku_store_price = #{price,jdbcType=INTEGER},
            <if test="price!=null and price!=0">
                status=1,
            </if>
            <if test="price==null or price==0">
                status=0,
            </if>
        </set>
        where yn = 1
        and parent_sku_no = #{parentSkuNo,jdbcType=VARCHAR}
        and sku_no = #{pluginSkuNo,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id = #{pluginGoodsId,jdbcType=VARCHAR}
    </update>
    <update id="updateSkuStoreLngAndLat" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
        update examination_man_sku_store
        <set>
            <if test="skuStoreEntity.lat !=null">
                lat=#{skuStoreEntity.lat},
            </if>
            <if test="skuStoreEntity.lng !=null">
                lng=#{skuStoreEntity.lng},
            </if>
            top_status = #{skuStoreEntity.topStatus},
            top_text = #{skuStoreEntity.topText}
        </set>
        where channel_no = #{skuStoreEntity.channelNo}
        and store_id = #{skuStoreEntity.storeId}
        and yn = 1
    </update>

    <update id="batchSetPriceByGoods">
        update examination_man_sku_store
        <set>
            sku_store_price = #{priceBO.price},
            status = #{status},
            store_remark = #{priceBO.remark}
        </set>
        where
        sku_no = #{priceBO.skuNo}
        and channel_no = #{priceBO.channelNo}
        and goods_id = #{priceBO.goodsId}
        <if test="priceBO.groupNo != null">
            and group_no = #{priceBO.groupNo,jdbcType=BIGINT}
        </if>
        <if test="priceBO.provinceId != null">
            and province_id = #{priceBO.provinceId}
        </if>
        <if test="priceBO.cityId != null">
            and city_id = #{priceBO.cityId}
        </if>
        and yn=1
    </update>
    <update id="updateSkuStoreList" parameterType="java.util.List">
        <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
            UPDATE examination_man_sku_store
            <set>
                <if test="bean.lat !=null">
                    lat=#{bean.lat},
                </if>
                <if test="bean.lng !=null">
                    lng=#{bean.lng}
                </if>
            </set>
            <where>
                <if test="bean.storeId != null and bean.storeId != ''">
                    and store_id = #{bean.storeId}
                </if>
                and channel_no = #{bean.channelNo}
                and yn = 1
            </where>

        </foreach>
    </update>

    <select id="listPluginSkuStoreByGoods" resultMap="skuStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        where yn = 1
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and parent_goods_id = #{parentGoodsId,jdbcType=VARCHAR}
        and goods_id = #{goodsId,jdbcType=VARCHAR}
    </select>

    <select id="listByGoodsStores" resultMap="skuStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        where yn = 1
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id = #{goodsId,jdbcType=VARCHAR}
        and store_id in
        <foreach collection="storeIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 根据条件查询列表 -->
    <select id="querySkuStoreListByParam" resultMap="skuStoreMap"
            parameterType="com.jd.health.medical.examination.domain.query.sku.param.SkuStoreQueryParam">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku_store
        <where>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="storeId != null">
                and store_id = #{storeId,jdbcType=BIGINT}
            </if>
            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo}
            </if>
            <if test="parentSkuNoList != null">
                and parent_sku_no in
                <foreach collection="parentSkuNoList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="parentGoodsId != null and parentGoodsId != ''">
                and parent_goods_id = #{parentGoodsId}
            </if>
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="addItemList != null">
                and sku_no in
                <foreach collection="addItemList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="thirdStoreStatus != null">
                and third_store_status = #{thirdStoreStatus,jdbcType=INTEGER}
            </if>
            <if test="skuNoList != null">
                and sku_no in
                <foreach collection="skuNoList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="channelNoList != null">
                and channel_no in
                <foreach collection="channelNoList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            and status = 1 and yn = 1
        </where>
    </select>

    <select id="querySkuStoreIdListByParam" resultType="java.lang.String"
            parameterType="com.jd.health.medical.examination.domain.query.sku.param.SkuStoreQueryParam">
        select
        DISTINCT CONCAT(0 ,'-',channel_no, '-', store_id)
        from examination_man_sku_store
        <where>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="storeId != null">
                and store_id = #{storeId,jdbcType=BIGINT}
            </if>
            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo}
            </if>
            <if test="parentGoodsId != null and parentGoodsId != ''">
                and parent_goods_id = #{parentGoodsId}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="thirdStoreStatus != null">
                and third_store_status = #{thirdStoreStatus,jdbcType=INTEGER}
            </if>
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="skuNoList != null">
                and sku_no in
                <foreach collection="skuNoList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="channelNoList != null">
                and channel_no in
                <foreach collection="channelNoList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            and yn = 1
        </where>
    </select>

    <select id="querySkuNoByStore"
            resultType="java.lang.String">
        select distinct sku_no
        from examination_man_sku_store
        <where>
            store_id = #{storeId}
            and channel_no = #{channelNo,jdbcType=BIGINT}
            and sku_no in
            <foreach collection="skuNoList" item="skuNo" open="(" close=")" separator=",">
                #{skuNo,jdbcType=VARCHAR}
            </foreach>

            <if test="parentSkuNo != null and parentSkuNo != ''">
                and parent_sku_no = #{parentSkuNo}
            </if>
            and third_store_status = 1
            and status = 1
            and yn = 1
        </where>
    </select>

</mapper>