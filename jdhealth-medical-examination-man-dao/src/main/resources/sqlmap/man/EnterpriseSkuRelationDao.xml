<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuRelationDao">

    <!-- 常用返回结果 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuRelationEntity" id="enterpriseSkuRelation">
        <result property="id" column="id"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="groupNo" column="group_no"/>
        <result property="skuCompanyPrice" column="sku_company_price"/>
        <result property="skuPersonPrice" column="sku_person_price"/>
        <result property="skuMarketPrice" column="sku_market_price"/>
        <result property="relationSkuName" column="relation_sku_name"/>
        <result property="relationType" column="relation_type"/>
        <result property="examinationNotes" column="examination_notes"/>
        <result property="purchaseNotes" column="purchase_notes"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            company_no,
            company_name,
            sku_no,
            sku_name,
            group_no,
            sku_company_price,
            sku_person_price,
            sku_market_price,
            relation_sku_name,
            relation_type,
            examination_notes,
            purchase_notes,
            yn,
            create_time,
            update_time,
            create_user,
            update_user
        </trim>
    </sql>

    <sql id="Base_Column_Insert">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            company_no,
            company_name,
            sku_no,
            sku_name,
            group_no,
            sku_company_price,
            sku_person_price,
            sku_market_price,
            relation_sku_name,
            relation_type,
            examination_notes,
            purchase_notes,
            yn,
            create_time,
            update_time,
            create_user,
            update_user
        </trim>
    </sql>

    <!-- 根据条件查询列表 -->
    <select id="querySkuRelationPage" resultMap="enterpriseSkuRelation">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_relation
        <where>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="skuName != null and skuName != ''">
                and sku_name LIKE CONCAT('%',#{skuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="relationSkuName != null and relationSkuName != ''">
                and relation_sku_name LIKE CONCAT('%',#{relationSkuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="relationTypeList != null">
                and relation_type in
                <foreach collection="relationTypeList" item="relationType" open="(" close=")" separator=",">
                    #{relationType,jdbcType=INTEGER}
                </foreach>
            </if>
            and yn = 1
        </where>
    </select>


    <!-- 新增亲属套餐 -->
    <insert id="insertSkuRelation" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuRelationEntity">
        insert into examination_qx_man_sku_relation
        <include refid="Base_Column_Insert"/>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{companyNo,jdbcType=BIGINT},
            #{companyName,jdbcType=VARCHAR},
            #{skuNo,jdbcType=VARCHAR},
            #{skuName,jdbcType=VARCHAR},
            #{groupNo,jdbcType=BIGINT},
            #{skuCompanyPrice,jdbcType=INTEGER},
            #{skuPersonPrice,jdbcType=INTEGER},
            #{skuMarketPrice,jdbcType=INTEGER},
            #{relationSkuName,jdbcType=VARCHAR},
            #{relationType,jdbcType=INTEGER},
            #{examinationNotes,jdbcType=VARCHAR},
            #{purchaseNotes,jdbcType=VARCHAR},
            1,
            now(),
            now(),
            #{createUser,jdbcType=VARCHAR},
            #{updateUser,jdbcType=VARCHAR}
        </trim>
    </insert>

    <!-- 更新亲属套餐 -->
    <update id="updateSkuRelation" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuRelationEntity">
        update examination_qx_man_sku_relation
        <set>
            purchase_notes = #{purchaseNotes,jdbcType=VARCHAR},
            <if test="relationSkuName != null and relationSkuName != ''">
                relation_sku_name = #{relationSkuName,jdbcType=VARCHAR},
            </if>
            <if test="skuCompanyPrice != null">
                sku_company_price = #{skuCompanyPrice,jdbcType=INTEGER},
            </if>
            <if test="skuPersonPrice != null">
                sku_person_price = #{skuPersonPrice,jdbcType=INTEGER},
            </if>
            <if test="relationType != null">
                relation_type = #{relationType,jdbcType=INTEGER},
            </if>
            <if test="examinationNotes != null">
                examination_notes = #{examinationNotes,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
        </set>
        where
        company_no = #{companyNo,jdbcType=BIGINT}
        and sku_no = #{skuNo,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 删除亲属套餐 -->
    <update id="deleteSkuRelation" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuRelationEntity">
        update examination_qx_man_sku_relation
        <set>
            yn = 0
        </set>
        where
        company_no = #{companyNo,jdbcType=BIGINT}
        and sku_no = #{skuNo,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 批量获取已维护的亲属同购套餐 -->
    <select id="batchSelectSkuRelation" resultMap="enterpriseSkuRelation">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_relation
        <where>
            company_no = #{companyNo,jdbcType=BIGINT}
            and yn = 1
            and sku_no in
            <foreach collection="skuNoList" item="skuNo" open="(" close=")" separator=",">
                #{skuNo,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>
    <select id="querySkuRelationBaseInfo"
            resultMap="enterpriseSkuRelation">
        select
        company_no,
        company_name,
        sku_no,
        sku_name,
        group_no,
        sku_company_price,
        sku_person_price,
        sku_market_price,
        relation_sku_name,
        relation_type,
        purchase_notes,
        examination_notes
        from examination_qx_man_sku_relation
        <where>
            company_no = #{companyNo,jdbcType=BIGINT}
            and sku_no=#{skuNo}
            and yn = 1
        </where>
    </select>

    <update id="updateEnterpriseSkuByBaseSkuInfo">
        update examination_qx_man_sku_relation
        <set>
            <if test="skuName != null and skuName != ''">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                group_no = #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="skuPersonPrice != null">
                sku_person_price = #{skuPersonPrice},
            </if>
            <if test="skuMarketPrice != null">
                sku_market_price = #{skuMarketPrice},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where sku_no = #{skuNo}
        and yn = 1
    </update>

    <select id="querySkuRelationBySkuNo" resultType="java.lang.Long">
        select distinct company_no
        from examination_qx_man_sku_relation
        where sku_no = #{skuNo}
          and yn = 1
    </select>

    <update id="removeEnterpriseSkuNo">
        update examination_qx_man_sku_relation
        set yn = 0
        where sku_no = #{skuNo}
        and yn = 1
    </update>

    <select id="queryRelationTypeByCompanyNo" resultType="java.lang.Integer" >
        select distinct relation_type
        from examination_qx_man_sku_relation
        where company_no = #{companyNo}
          and yn = 1
    </select>
    <select id="selectAll" resultMap="enterpriseSkuRelation">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_relation
        where yn = 1 and sku_market_price is null
    </select>

</mapper>