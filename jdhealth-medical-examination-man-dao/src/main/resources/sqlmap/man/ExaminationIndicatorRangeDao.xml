<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationIndicatorRangeDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorRangeDO">
    <!--@mbg.generated-->
    <!--@Table examination_indicator_range-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="indicator_code" jdbcType="VARCHAR" property="indicatorCode" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="normal_range_value" jdbcType="VARCHAR" property="normalRangeValue" />
    <result column="normal_range_upper" jdbcType="VARCHAR" property="normalRangeUpper" />
    <result column="normal_range_lower" jdbcType="VARCHAR" property="normalRangeLower" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="people_suitable" jdbcType="VARCHAR" property="peopleSuitable" />
    <result column="examination_method" jdbcType="VARCHAR" property="examinationMethod" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, indicator_code, indicator_name, normal_range_value, normal_range_upper, normal_range_lower, 
    unit, people_suitable, examination_method, yn, create_user, create_time, update_user, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from examination_indicator_range
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from examination_indicator_range
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorRangeDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_indicator_range (indicator_code, indicator_name, normal_range_value, 
      normal_range_upper, normal_range_lower, unit, 
      people_suitable, examination_method, yn, 
      create_user, create_time, update_user, 
      update_time)
    values (#{indicatorCode,jdbcType=VARCHAR}, #{indicatorName,jdbcType=VARCHAR}, #{normalRangeValue,jdbcType=VARCHAR}, 
      #{normalRangeUpper,jdbcType=VARCHAR}, #{normalRangeLower,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, 
      #{peopleSuitable,jdbcType=VARCHAR}, #{examinationMethod,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorRangeDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_indicator_range
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="indicatorCode != null">
        indicator_code,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="normalRangeValue != null">
        normal_range_value,
      </if>
      <if test="normalRangeUpper != null">
        normal_range_upper,
      </if>
      <if test="normalRangeLower != null">
        normal_range_lower,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="peopleSuitable != null">
        people_suitable,
      </if>
      <if test="examinationMethod != null">
        examination_method,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="indicatorCode != null">
        #{indicatorCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="normalRangeValue != null">
        #{normalRangeValue,jdbcType=VARCHAR},
      </if>
      <if test="normalRangeUpper != null">
        #{normalRangeUpper,jdbcType=VARCHAR},
      </if>
      <if test="normalRangeLower != null">
        #{normalRangeLower,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="peopleSuitable != null">
        #{peopleSuitable,jdbcType=VARCHAR},
      </if>
      <if test="examinationMethod != null">
        #{examinationMethod,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorRangeDO">
    <!--@mbg.generated-->
    update examination_indicator_range
    <set>
      <if test="indicatorCode != null">
        indicator_code = #{indicatorCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="normalRangeValue != null">
        normal_range_value = #{normalRangeValue,jdbcType=VARCHAR},
      </if>
      <if test="normalRangeUpper != null">
        normal_range_upper = #{normalRangeUpper,jdbcType=VARCHAR},
      </if>
      <if test="normalRangeLower != null">
        normal_range_lower = #{normalRangeLower,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="peopleSuitable != null">
        people_suitable = #{peopleSuitable,jdbcType=VARCHAR},
      </if>
      <if test="examinationMethod != null">
        examination_method = #{examinationMethod,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorRangeDO">
    <!--@mbg.generated-->
    update examination_indicator_range
    set indicator_code = #{indicatorCode,jdbcType=VARCHAR},
      indicator_name = #{indicatorName,jdbcType=VARCHAR},
      normal_range_value = #{normalRangeValue,jdbcType=VARCHAR},
      normal_range_upper = #{normalRangeUpper,jdbcType=VARCHAR},
      normal_range_lower = #{normalRangeLower,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      people_suitable = #{peopleSuitable,jdbcType=VARCHAR},
      examination_method = #{examinationMethod,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>