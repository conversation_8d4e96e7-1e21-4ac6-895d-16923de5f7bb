<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.CheckGroupTemplateDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="group_no" jdbcType="BIGINT" property="groupNo"/>
        <result column="template_id" jdbcType="BIGINT" property="templateId"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id, group_no, template_id, template_name, yn, create_user, create_time, update_user,
            update_time
        </trim>
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        examination_man_check_group_template
        where
        id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <select id="queryByGroupNo" resultType="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity">
        select
        <include refid="Base_Column_List"/>
        from
        examination_man_check_group_template
        where
        group_no = #{groupNo,jdbcType=BIGINT}
        and
        yn = 1
    </select>
    <select id="queryByTemplateId" resultType="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity">
        select
        <include refid="Base_Column_List"/>
        from
        examination_man_check_group_template
        where
        template_id = #{templateId,jdbcType=BIGINT}
        and
        yn = 1

    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity" useGeneratedKeys="true">
        insert into examination_man_check_group_template (group_no, template_id, template_name,
                                                          yn, create_user, create_time,
                                                          update_user, update_time)
        values (#{groupNo,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, #{templateName,jdbcType=VARCHAR},
                1, #{createUser,jdbcType=VARCHAR}, now(),
                #{updateUser,jdbcType=VARCHAR}, now())
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity" useGeneratedKeys="true">
        insert into examination_man_check_group_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupNo != null">
                group_no,
            </if>
            <if test="templateId != null">
                template_id,
            </if>
            <if test="templateName != null">
                template_name,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupNo != null">
                #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="templateId != null">
                #{templateId,jdbcType=BIGINT},
            </if>
            <if test="templateName != null">
                #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity">
        update examination_man_check_group_template
        <set>
            <if test="groupNo != null">
                group_no = #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=BIGINT},
            </if>
            <if test="templateName != null">
                template_name = #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="update" parameterType="com.jd.health.medical.examination.domain.CheckGroupTemplateEntity">
        update examination_man_check_group_template
        set group_no      = #{groupNo,jdbcType=BIGINT},
            template_id   = #{templateId,jdbcType=BIGINT},
            template_name = #{templateName,jdbcType=VARCHAR},
            update_user   = #{updateUser,jdbcType=VARCHAR},
            update_time   = now()
        where group_no = #{groupNo}
          and yn = 1
    </update>

    <update id="updateTemplateNameByTemplateId">
        update examination_man_check_group_template
        set  template_name = #{templateName,jdbcType=VARCHAR}
        where template_id   = #{templateId,jdbcType=BIGINT}
        and yn = 1
    </update>

</mapper>