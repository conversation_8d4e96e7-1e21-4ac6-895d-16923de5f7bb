<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.BillDetailDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.personal.entity.BillDetailEntity" id="billRecordMap">
        <result property="id" column="id"/>
        <result property="billId" column="bill_id"/>
        <result property="billDate" column="bill_date"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT" />
        <result column="jd_appointment_id" property="jdAppointmentId" jdbcType="BIGINT" />
        <result property="storeId" column="store_id"/>
        <result property="storeName" column="store_name"/>
        <result property="channelNo" column="channel_no"/>
        <result property="channelName" column="channel_name"/>
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result column="random_code" property="randomCode"/>
        <result column="report_time" property="reportTime"/>
        <result column="check_time" property="checkTime" />
        <result column="order_create_time" property="orderCreateTime"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="skuInfoExtend" column="sku_info_extend"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            bill_id,bill_date,order_id,jd_appointment_id,store_id,store_name,channel_no,channel_name,sku_no,sku_name,
            random_code,report_time,check_time,order_create_time,yn,create_time,update_time,sku_info_extend
        </trim>
    </sql>

    <!-- 根据条件查询列表 -->
    <select id="queryBillDetailList" resultMap="billRecordMap"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.BillDetailEntity">
        select
        <include refid="Base_Column_List"/>
        from xfyl_service_bill_detail
        <where>
            <if test="billId != null and billId!=''">
                and bill_id = #{billId}
            </if>
            <if test="storeId != null and storeId!=''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null and channelNo!=''">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 保存数据 -->
    <insert id="insertBillDetail" parameterType="com.jd.health.medical.examination.domain.personal.entity.BillDetailEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into xfyl_service_bill_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            bill_id,bill_date,order_id,jd_appointment_id,store_id,store_name,channel_no,channel_name,
            random_code,report_time,check_time,order_create_time,sku_info_extend,sku_no,sku_name
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{billId,jdbcType=BIGINT},#{billDate,jdbcType=DATE},#{orderId,jdbcType=BIGINT},#{jdAppointmentId,jdbcType=BIGINT},
            #{storeId,jdbcType=VARCHAR},#{storeName,jdbcType=VARCHAR},#{channelNo,jdbcType=BIGINT},#{channelName,jdbcType=VARCHAR},
            #{randomCode,jdbcType=VARCHAR},#{reportTime,jdbcType=TIMESTAMP},#{checkTime,jdbcType=TIMESTAMP},#{orderCreateTime,jdbcType=TIMESTAMP},
            #{skuInfoExtend,jdbcType=VARCHAR},#{item.skuNo,jdbcType=VARCHAR},#{item.skuName,jdbcType=VARCHAR}
        </trim>
    </insert>
    <!-- 批量保存数据 -->
    <insert id="insertBillDetailList">
        insert into xfyl_service_bill_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            bill_id,bill_date,order_id,jd_appointment_id,store_id,store_name,channel_no,channel_name,
            random_code,report_time,check_time,order_create_time,sku_info_extend,sku_no,sku_name
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.billId,jdbcType=BIGINT},#{item.billDate,jdbcType=DATE},#{item.orderId,jdbcType=BIGINT},#{item.jdAppointmentId,jdbcType=BIGINT},
                #{item.storeId,jdbcType=VARCHAR},#{item.storeName,jdbcType=VARCHAR},#{item.channelNo,jdbcType=BIGINT},#{item.channelName,jdbcType=VARCHAR},
                #{item.randomCode,jdbcType=VARCHAR},#{item.reportTime,jdbcType=TIMESTAMP},
                #{item.checkTime,jdbcType=TIMESTAMP},#{item.orderCreateTime,jdbcType=TIMESTAMP},#{item.skuInfoExtend,jdbcType=VARCHAR},
                #{item.skuNo,jdbcType=VARCHAR},#{item.skuName,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

</mapper>