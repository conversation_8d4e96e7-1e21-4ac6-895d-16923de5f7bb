<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.SkuStoreThirdRelDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.SkuStoreThirdRel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku_no" jdbcType="VARCHAR" property="skuNo" />
    <result column="channel_no" jdbcType="BIGINT" property="channelNo" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="group_no" jdbcType="BIGINT" property="groupNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku_no, channel_no, goods_id, group_no, create_time, create_user, update_time,
    update_user
  </sql>
  <insert id="insertSkuStoreRel">
    insert into examination_man_sku_store_third_rel(sku_no, channel_no, goods_id, group_no, create_time)
    values(#{skuNo},#{channelNo},#{goodsId},#{groupNo},now())
  </insert>
  <delete id="deleteSkuStoreRel">
    update examination_man_sku_store_third_rel
    <set>
      yn = 0
    </set>
    <where>
      sku_no=#{skuNo}
      and channel_no=#{channelNo}
      and goods_id=#{goodsId}
      <if test="groupNo!=null">
        and group_no=#{groupNo}
      </if>
      and yn = 1
    </where>
  </delete>


  <update id="deleteBySkuNo">
    update examination_man_sku_store_third_rel
    <set>
      yn = 0
    </set>
    <where>
      sku_no=#{skuNo}
    </where>
  </update>


  <select id="selectSkuStoreRel"
          resultType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
    select
    goods_id,
    channel_no
    from examination_man_sku_store_third_rel
    <where>
      <if test="skuNo != null and skuNo!=''">
        and sku_no = #{skuNo,jdbcType=VARCHAR}
      </if>
      <if test="groupNo != null">
        and group_no = #{groupNo}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="selectListByProvider"
          resultType="com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_sku_store_third_rel
    where channel_no = #{channelNo}
      <if test="goods_id != null and goods_id !=''">
        and goods_id = #{goodsId}
      </if>
      and yn = 1
  </select>

  <select id="selectList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from examination_man_sku_store_third_rel
    <where>
      <if test="skuNo != null and skuNo != ''">
        and sku_no = #{skuNo}
      </if>
      <if test="channelNo != null">
        and channel_no = #{channelNo}
      </if>
      <if test="goodsId != null and goodsId != ''">
        and goods_id = #{goodsId}
      </if>
      <if test="groupNo != null">
        and group_no = #{groupNo}
      </if>
      <if test="skuNoList != null and skuNoList.size() != 0">
        and sku_no in
        <foreach collection="skuNoList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    and yn = 1
  </select>

  <update id="updateGroup">
    update examination_man_sku_store_third_rel
    set group_no = #{groupNo,jdbcType=BIGINT}
    where sku_no = #{skuNo,jdbcType=VARCHAR}
    and yn = 1
  </update>
</mapper>