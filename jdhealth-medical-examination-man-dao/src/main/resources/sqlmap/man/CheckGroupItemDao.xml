<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.CheckGroupItemDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.CheckGroupItemEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="group_no" property="groupNo" jdbcType="BIGINT" />
    <result column="item_no" property="itemNo" jdbcType="BIGINT" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="item_desc" property="itemDesc" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
    <insert id="addCheckGroupItems">
      insert into examination_man_check_group_item(group_no,
      item_no,
      item_name,
      item_desc,
      yn,
      create_user,
      create_time,
      update_user,
      update_time)
      values
      <foreach collection="itemEntities" item="checkGroupItemEntity" separator=",">
        <trim prefix="(" suffix=")" suffixOverrides=",">
          #{checkGroupItemEntity.groupNo},
          #{checkGroupItemEntity.itemNo},
          #{checkGroupItemEntity.itemName},
          #{checkGroupItemEntity.itemDesc},
          1,
          #{checkGroupItemEntity.createUser},
          now(),
          #{checkGroupItemEntity.createUser},
          now(),
        </trim>
      </foreach>
    </insert>
  <update id="updateCheckGroupItem">
    update examination_man_check_group_item
    set item_name=#{itemName},
    item_desc = #{itemName},
    update_time=now(),
    update_user=#{updateUser}
    where item_no = #{itemNo}
    and yn = 1
  </update>
  <delete id="deleteInvalidCheckGroupItem">
      delete from examination_man_check_group_item
      where group_no = #{groupNo}
      and yn = 1
    </delete>
  <select id="queryCheckGroupItemByGroupNos"
          resultMap="BaseResultMap">
    select group_no,item_no,item_name,item_desc
    from examination_man_check_group_item
    <where>
      group_no in
      <foreach collection="groupNos" item="groupNo" open="(" close=")" separator=",">
        #{groupNo}
      </foreach>
      and yn = 1
    </where>
  </select>
    <select id="queryGroupItemByGroupNo"
            resultMap="BaseResultMap">
      select group_no,item_no,item_name,item_desc
    from examination_man_check_group_item
    where group_no = #{groupNo}
    and yn = 1
    </select>
    <select id="queryByItemId" resultMap="BaseResultMap">
      select group_no,item_no,item_name,item_desc
    from examination_man_check_group_item
    where id = #{id}
    and yn = 1
    </select>
    <update id="deleteCheckGroupItem">
    update  examination_man_check_group_item
    set yn = 0, update_time  = now()
    where group_no = #{groupNo}
    and yn = 1
  </update>

</mapper>