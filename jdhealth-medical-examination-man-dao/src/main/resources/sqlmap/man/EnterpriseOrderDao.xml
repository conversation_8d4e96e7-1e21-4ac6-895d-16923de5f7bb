<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseOrderDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOrderEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="order_price" property="orderPrice" jdbcType="INTEGER" />
    <result column="order_pay_time" property="orderPayTime" jdbcType="TIMESTAMP" />
    <result column="company_order_type" property="companyOrderType" jdbcType="TINYINT" />
    <result column="company_license_no" property="companyLicenseNo" jdbcType="VARCHAR" />
    <result column="company_addr" property="companyAddr" jdbcType="VARCHAR" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="user_pin" property="userPin" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>
    <insert id="addEnterpriseOrder">
      insert into examination_qx_man_company_order(order_id, order_price, order_pay_time, company_order_type, company_license_no,
    company_addr, company_name, sku_no, sku_name, user_pin,create_time)
    values (#{orderId},#{orderPrice},#{orderPayTime},#{companyOrderType},#{companyLicenseNo},#{companyAddr},#{companyName},#{skuNo},#{skuName},#{userPin},now())
    </insert>

    <select id="selectAll" resultMap="BaseResultMap" >
    select id, order_id, order_price, order_pay_time, company_order_type, company_license_no, 
    company_addr, company_name, sku_no, sku_name, user_pin, yn, create_time, create_user, 
    update_time, update_user
    from examination_qx_man_company_order
  </select>

  <!--查询公司中所有的订单信息-->
  <select id="queryEnterpriseOrders"
          resultType="com.jd.health.medical.examination.domain.enterprise.bo.EnterpriseOrderBO">
    SELECT
        r.order_id,o.order_price,o.order_pay_time,o.company_name,r.order_rel_time
    FROM
        examination_qx_man_company_order_rel r
    LEFT JOIN examination_qx_man_company_order o ON r.order_id = o.order_id
    WHERE
        r.company_no = #{companyNo}
    AND
        r.yn = 1
    ORDER BY order_rel_time ASC
  </select>

  <!--根据订单号查询订单信息-->
    <select id="selectOrderByOrderId"
            resultMap="BaseResultMap">
      select order_id, order_price, order_pay_time, company_order_type, company_license_no,
      company_addr, company_name, sku_no, sku_name, user_pin
      from examination_qx_man_company_order
      where order_id=#{orderId}
      and yn = 1
      limit 1
    </select>

    <!--根据订单号查询订单信息-->
    <select id="selectOrderByUserPin" resultMap="BaseResultMap">
      select order_id, order_price, order_pay_time, company_order_type, company_license_no,
      company_addr, company_name, sku_no, sku_name, user_pin
      from examination_qx_man_company_order
      where user_pin = #{userPin,jdbcType=VARCHAR}
      and yn = 1
      limit 1
    </select>

</mapper>