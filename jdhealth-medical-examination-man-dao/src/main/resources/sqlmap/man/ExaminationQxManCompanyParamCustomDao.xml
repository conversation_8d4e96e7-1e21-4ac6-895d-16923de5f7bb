<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.ExaminationQxManCompanyParamCustomDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_no" jdbcType="BIGINT" property="companyNo" />
    <result column="param_id" jdbcType="VARCHAR" property="paramId" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="custom_value" jdbcType="VARCHAR" property="customValue" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_no, param_id, param_name, custom_value, create_user, create_time, yn
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from examination_qx_man_company_param_custom
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from examination_qx_man_company_param_custom
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity" useGeneratedKeys="true">
    insert into examination_qx_man_company_param_custom (company_no, param_id, param_name, 
      custom_value, create_user, create_time, 
      yn)
    values (#{companyNo,jdbcType=BIGINT}, #{paramId,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, 
      #{customValue,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{yn,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity" useGeneratedKeys="true">
    insert into examination_qx_man_company_param_custom
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        company_no,
      </if>
      <if test="paramId != null">
        param_id,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="customValue != null">
        custom_value,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="yn != null">
        yn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        #{companyNo,jdbcType=BIGINT},
      </if>
      <if test="paramId != null">
        #{paramId,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="customValue != null">
        #{customValue,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity">
    update examination_qx_man_company_param_custom
    <set>
      <if test="companyNo != null">
        company_no = #{companyNo,jdbcType=BIGINT},
      </if>
      <if test="paramId != null">
        param_id = #{paramId,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="customValue != null">
        custom_value = #{customValue,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity">
    update examination_qx_man_company_param_custom
    set company_no = #{companyNo,jdbcType=BIGINT},
      param_id = #{paramId,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      custom_value = #{customValue,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      yn = #{yn,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCompanyNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_qx_man_company_param_custom
    where company_no = #{companyNo}
    and yn = 1
  </select>

  <update id="deleteCompanyCustomParam" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity">
    update examination_qx_man_company_param_custom
    set yn = 0
    where
      company_no = #{companyNo,jdbcType=BIGINT}
      and param_id = #{paramId,jdbcType=VARCHAR}
      and custom_value = #{customValue,jdbcType=VARCHAR}
      and yn = 1
  </update>

  <insert id="batchInsert" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanyParamCustomEntity">
    insert into examination_qx_man_company_param_custom (company_no, param_id, param_name,
                                                         custom_value, create_user, create_time,
                                                         yn)
    values
    <foreach collection="companyParamCustomEntities" item="item"  open="" separator="," close="">
      (#{item.companyNo,jdbcType=BIGINT}, #{item.paramId,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR},
      #{item.customValue,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.yn,jdbcType=TINYINT})
    </foreach>
  </insert>

  <update id="deleteByParamIds" parameterType="java.util.List">
    update examination_qx_man_company_param_custom
    set yn = 0
    where yn = 1
    and param_id in
    <foreach collection="paramIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  </update>

  <select id="selectByCompanyNoAndParamIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_qx_man_company_param_custom
    where yn = 1
    and company_no = #{companyNo}
    and param_id in
    <foreach collection="paramIds" item = "item" open="(" separator="," close=")">#{item}</foreach>
  </select>

  <select id="checkParamCustomExist" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_qx_man_company_param_custom
    where yn = 1
    and company_no = #{companyNo}
    and param_id = #{paramId}
    and custom_value = #{customValue}
    limit 1
  </select>
</mapper>