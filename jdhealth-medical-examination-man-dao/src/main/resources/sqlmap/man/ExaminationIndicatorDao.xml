<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationIndicatorDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO">
        <!--@mbg.generated-->
        <!--@Table examination_indicator-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="indicator_code" jdbcType="VARCHAR" property="indicatorCode"/>
        <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName"/>
        <result column="ws_code" jdbcType="VARCHAR" property="wsCode"/>
        <result column="abbreviation" jdbcType="VARCHAR" property="abbreviation"/>
        <result column="first_category" jdbcType="VARCHAR" property="firstCategory"/>
        <result column="second_category" jdbcType="VARCHAR" property="secondCategory"/>
        <result column="third_category" jdbcType="VARCHAR" property="thirdCategory"/>
        <result column="normal_range_value" jdbcType="VARCHAR" property="normalRangeValue"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="sample" jdbcType="VARCHAR" property="sample"/>
        <result column="examination_method" jdbcType="VARCHAR" property="examinationMethod"/>
        <result column="precision" jdbcType="VARCHAR" property="precision"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="body_system_code" jdbcType="VARCHAR" property="bodySystemCode"/>
        <result column="body_part_code" jdbcType="VARCHAR" property="bodyPartCode"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="first_depart_code" jdbcType="INTEGER" property="firstDepartCode"/>
        <result column="first_depart_name" jdbcType="VARCHAR" property="firstDepartName"/>
        <result column="second_depart_code" jdbcType="TIMESTAMP" property="secondDepartCode"/>
        <result column="second_depart_name" jdbcType="TIMESTAMP" property="secondDepartName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, indicator_code, indicator_name, ws_code, abbreviation, first_category, second_category,
        third_category, normal_range_value, unit, sample, examination_method, `precision`,
        `source`, remark, body_system_code, body_part_code, yn, create_user, create_time, update_user, update_time,
        first_depart_code,first_depart_name,second_depart_code,second_depart_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from examination_indicator
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into examination_indicator (indicator_code, indicator_name, ws_code,
        abbreviation, first_category, second_category,
        third_category, normal_range_value, unit,
        sample, examination_method, `precision`,
        `source`, remark, body_system_code, body_part_code, yn,
        create_user, create_time, update_user,
        update_time)
        values (#{indicatorCode,jdbcType=VARCHAR}, #{indicatorName,jdbcType=VARCHAR}, #{wsCode,jdbcType=VARCHAR},
        #{abbreviation,jdbcType=VARCHAR}, #{firstCategory,jdbcType=VARCHAR}, #{secondCategory,jdbcType=VARCHAR},
        #{thirdCategory,jdbcType=VARCHAR}, #{normalRangeValue,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR},
        #{sample,jdbcType=VARCHAR}, #{examinationMethod,jdbcType=VARCHAR}, #{precision,jdbcType=VARCHAR},
        #{source,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{bodySystemCode,jdbcType=VARCHAR},
        #{bodyPartCode,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT},
        #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into examination_indicator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="indicatorCode != null">
                indicator_code,
            </if>
            <if test="indicatorName != null">
                indicator_name,
            </if>
            <if test="wsCode != null">
                ws_code,
            </if>
            <if test="abbreviation != null">
                abbreviation,
            </if>
            <if test="firstCategory != null">
                first_category,
            </if>
            <if test="secondCategory != null">
                second_category,
            </if>
            <if test="thirdCategory != null">
                third_category,
            </if>
            <if test="normalRangeValue != null">
                normal_range_value,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="sample != null">
                sample,
            </if>
            <if test="examinationMethod != null">
                examination_method,
            </if>
            <if test="precision != null">
                `precision`,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="bodySystemCode != null and bodySystemCode != ''">
                body_system_code,
            </if>
            <if test="bodyPartCode != null and bodyPartCode != ''">
                body_part_code,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="indicatorCode != null">
                #{indicatorCode,jdbcType=VARCHAR},
            </if>
            <if test="indicatorName != null">
                #{indicatorName,jdbcType=VARCHAR},
            </if>
            <if test="wsCode != null">
                #{wsCode,jdbcType=VARCHAR},
            </if>
            <if test="abbreviation != null">
                #{abbreviation,jdbcType=VARCHAR},
            </if>
            <if test="firstCategory != null">
                #{firstCategory,jdbcType=VARCHAR},
            </if>
            <if test="secondCategory != null">
                #{secondCategory,jdbcType=VARCHAR},
            </if>
            <if test="thirdCategory != null">
                #{thirdCategory,jdbcType=VARCHAR},
            </if>
            <if test="normalRangeValue != null">
                #{normalRangeValue,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="sample != null">
                #{sample,jdbcType=VARCHAR},
            </if>
            <if test="examinationMethod != null">
                #{examinationMethod,jdbcType=VARCHAR},
            </if>
            <if test="precision != null">
                #{precision,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bodySystemCode != null and bodySystemCode != ''">
                #{bodySystemCode,jdbcType=VARCHAR},
            </if>
            <if test="bodyPartCode != null and bodyPartCode != ''">
                #{bodyPartCode,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateBySelective"
            parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO">
        <!--@mbg.generated-->
        update examination_indicator
        <set>
            <if test="indicatorCode != null">
                indicator_code = #{indicatorCode,jdbcType=VARCHAR},
            </if>
            <if test="indicatorName != null">
                indicator_name = #{indicatorName,jdbcType=VARCHAR},
            </if>
            <if test="wsCode != null">
                ws_code = #{wsCode,jdbcType=VARCHAR},
            </if>
            <if test="abbreviation != null">
                abbreviation = #{abbreviation,jdbcType=VARCHAR},
            </if>
            <if test="firstCategory != null">
                first_category = #{firstCategory,jdbcType=VARCHAR},
            </if>
            <if test="secondCategory != null">
                second_category = #{secondCategory,jdbcType=VARCHAR},
            </if>
            <if test="thirdCategory != null">
                third_category = #{thirdCategory,jdbcType=VARCHAR},
            </if>
            <if test="normalRangeValue != null">
                normal_range_value = #{normalRangeValue,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="sample != null">
                sample = #{sample,jdbcType=VARCHAR},
            </if>
            <if test="examinationMethod != null">
                examination_method = #{examinationMethod,jdbcType=VARCHAR},
            </if>
            <if test="precision != null">
                `precision` = #{precision,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                `source` = #{source,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bodySystemCode != null and bodySystemCode != ''">
                body_system_code = #{bodySystemCode,jdbcType=VARCHAR},
            </if>
            <if test="bodyPartCode != null and bodyPartCode != ''">
                body_part_code = #{bodyPartCode,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstDepartCode != null">
                first_depart_code = #{firstDepartCode,jdbcType=INTEGER},
            </if>
            <if test="firstDepartName != null and firstDepartName != ''">
                first_depart_name = #{firstDepartName,jdbcType=VARCHAR},
            </if>
            <if test="secondDepartCode != null">
                second_depart_code = #{secondDepartCode,jdbcType=INTEGER},
            </if>
            <if test="secondDepartName != null and secondDepartName != ''">
                second_depart_name = #{secondDepartName,jdbcType=VARCHAR},
            </if>
        </set>
        where indicator_code = #{indicatorCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorDO">
        <!--@mbg.generated-->
        update examination_indicator
        set indicator_code = #{indicatorCode,jdbcType=VARCHAR},
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
        ws_code = #{wsCode,jdbcType=VARCHAR},
        abbreviation = #{abbreviation,jdbcType=VARCHAR},
        first_category = #{firstCategory,jdbcType=VARCHAR},
        second_category = #{secondCategory,jdbcType=VARCHAR},
        third_category = #{thirdCategory,jdbcType=VARCHAR},
        normal_range_value = #{normalRangeValue,jdbcType=VARCHAR},
        unit = #{unit,jdbcType=VARCHAR},
        sample = #{sample,jdbcType=VARCHAR},
        examination_method = #{examinationMethod,jdbcType=VARCHAR},
        `precision` = #{precision,jdbcType=VARCHAR},
        `source` = #{source,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        body_system_code = #{bodySystemCode,jdbcType=VARCHAR},
        body_part_code = #{bodyPartCode,jdbcType=VARCHAR},
        yn = #{yn,jdbcType=TINYINT},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        where yn = 1
    </select>

    <select id="selectIndicatorByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        <where>
            <if test="indicatorCode != null and indicatorCode != ''">
                and indicator_code = #{indicatorCode,jdbcType=VARCHAR}
            </if>
            <if test="fuzzyIndicatorName != null and fuzzyIndicatorName != ''">
                and indicator_name like CONCAT('%', #{fuzzyIndicatorName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="firstCategory != null and firstCategory != ''">
                and first_category = #{firstCategory,jdbcType=VARCHAR}
            </if>
            <if test="secondCategory != null and secondCategory != ''">
                and second_category = #{secondCategory,jdbcType=VARCHAR}
            </if>
            <if test="thirdCategory != null and thirdCategory != ''">
                and third_category = #{thirdCategory,jdbcType=VARCHAR}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 查询标准指标详情 -->
    <select id="queryIndicatorDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        <where>
            <if test="indicatorCode != null and indicatorCode != ''">
                and indicator_code = #{indicatorCode,jdbcType=VARCHAR}
            </if>
            <if test="indicatorName != null and indicatorName != ''">
                and indicator_name = #{indicatorName,jdbcType=VARCHAR}
            </if>
            <if test="fuzzyIndicatorName != null and fuzzyIndicatorName != ''">
                and indicator_name like CONCAT('%', #{fuzzyIndicatorName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="firstCategory != null and firstCategory != ''">
                and first_category = #{firstCategory,jdbcType=VARCHAR}
            </if>
            <if test="secondCategory != null and secondCategory != ''">
                and second_category = #{secondCategory,jdbcType=VARCHAR}
            </if>
            <if test="thirdCategory != null and thirdCategory != ''">
                and third_category = #{thirdCategory,jdbcType=VARCHAR}
            </if>
            and yn = 1 limit 1
        </where>
    </select>

    <select id="selectByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        <where>
            <if test="indicatorCode != null and indicatorCode != ''">
                and indicator_code = #{indicatorCode,jdbcType=VARCHAR}
            </if>
            <if test="wsCode != null and wsCode != ''">
                and ws_code = #{wsCode,jdbcType=VARCHAR}
            </if>
            and yn = 1
        </where>
    </select>
    <select id="listByIndicatorCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        where yn = 1
        and indicator_code in
        <foreach collection="indicatorCodes" item="code" open="(" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 根据条件查询标准指标信息列表 -->
    <select id="queryStandardIndicatorList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_indicator
        <where>
            <if test="indicatorCode != null and indicatorCode != ''">
                and indicator_code = #{indicatorCode,jdbcType=VARCHAR}
            </if>
            <if test="fuzzyIndicatorName != null and fuzzyIndicatorName != ''">
                and indicator_name like CONCAT('%', #{fuzzyIndicatorName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="firstCategory != null and firstCategory != ''">
                and first_category = #{firstCategory,jdbcType=VARCHAR}
            </if>
            <if test="secondCategory != null and secondCategory != ''">
                and second_category = #{secondCategory,jdbcType=VARCHAR}
            </if>
            <if test="thirdCategory != null and thirdCategory != ''">
                and third_category = #{thirdCategory,jdbcType=VARCHAR}
            </if>
            and yn = 1
        </where>
    </select>
</mapper>