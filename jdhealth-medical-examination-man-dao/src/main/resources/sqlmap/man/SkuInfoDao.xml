<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.SkuInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity" id="skuInfoMap">
        <result property="id" column="id"/>
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="skuPrice" column="sku_price"/>
        <result property="skuDesc" column="sku_desc"/>
        <result property="skuSuitable" column="sku_suitable"/>
        <result property="skuPeople" column="sku_people"/>
        <result property="skuType" column="sku_type"/>
        <result property="groupNo" column="group_no"/>
        <result property="skuStatus" column="sku_status"/>
        <result property="skuVindicateStatus" column="sku_vindicate_status"/>
        <result property="validType" column="valid_type"/>
        <result property="skuValid" column="sku_valid"/>
        <result property="validEnd" column="valid_end"/>
        <result property="skuUnionType" column="sku_union_type"/>
        <result property="composeId" column="compose_id"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="skuSpecies" column="sku_species"/>
        <result property="signType" column="sign_type"/>
        <result property="signFlag" column="sign_flag"/>
    </resultMap>

    <!-- skuInfo -->
    <resultMap type="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity" id="skuGroupMap">
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="groupName" column="group_name"/>
        <result property="groupNo" column="group_no"/>
        <result property="groupSuitable" column="group_suitable"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            sku_no,
            sku_name,
            sku_price,
            sku_desc,
            sku_suitable,
            sku_people,
            sku_type,
            group_no,
            sku_status,
            sku_vindicate_status,
            valid_type,
            sku_valid,
            valid_end,
            sku_union_type,
            compose_id,
            sku_species,
            update_user,
            update_time,
            create_time,
            create_user,
            yn,
            sign_type,
            sign_flag
        </trim>
    </sql>

    <!--根据skuNo删除-->
    <delete id="deleteSkuInfoBySkuNo">
        delete from examination_man_sku
        where sku_no = #{skuNo,jdbcType=BIGINT}
    </delete>

    <!-- 根据条件查询列表 -->
    <select id="querySkuInfoList" resultMap="skuInfoMap"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        <where>
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="skuName != null and skuName != ''">
                and sku_name LIKE CONCAT('%',#{skuName}, '%')
            </if>
            <if test="skuPrice != null">
                and sku_price = #{skuPrice,jdbcType=INTEGER}
            </if>
            <if test="skuDesc != null and skuDesc != ''">
                and sku_desc = #{skuDesc,jdbcType=VARCHAR}
            </if>
            <if test="skuSuitable != null and skuSuitable != ''">
                and sku_suitable = #{skuSuitable,jdbcType=VARCHAR}
            </if>
            <if test="skuPeople != null">
                and sku_people = #{skuPeople,jdbcType=INTEGER}
            </if>
            <if test="skuType != null">
                and sku_type = #{skuType,jdbcType=INTEGER}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="skuVindicateStatus != null">
                and sku_vindicate_status = #{skuVindicateStatus}
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies}
            </if>
            <choose>
                <when test="skuStatus != null">
                    and sku_status = #{skuStatus}
                </when>
                <otherwise>
                    and sku_status != 3
                </otherwise>
            </choose>
            and yn = 1
        </where>
        <choose>
            <when test="skuStatus != null and skuStatus == 3">
                order by update_time desc
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

    <!--根据skuNo查询-->
    <select id="selectSkuInfoBySkuNo" resultMap="skuInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where sku_no=#{skuNo}
        and yn = 1 and sku_status != 3
    </select>

    <!--根据skuNo查询-->
    <select id="selectSkuInfoSkippingStatus" resultMap="skuInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where sku_no = #{skuNo}
          and yn = 1
    </select>

    <select id="querySkuDesc"
            resultType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity">
        select sku_no,sku_desc,sku_suitable,group_no
        from examination_man_sku
        where sku_no in
        <foreach collection="skuNos" item="skuNo" open="(" close=")" separator=",">
            #{skuNo}
        </foreach>
        and yn = 1 and sku_status != 3
    </select>


    <!-- 插入实体 -->
    <insert id="insertSkuInfo" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity"
            keyProperty="id">
        insert into examination_man_sku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            sku_no,
            sku_name,
            sku_price,
            sku_status,
            sku_desc,
            sku_suitable,
            sku_people,
            sku_type,
            sku_union_type,
            create_time,
            create_user,
            sku_species,
            sign_type,
            sign_flag,
            group_no,
            valid_type,
            valid_end,
            sku_valid
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{skuNo,jdbcType=VARCHAR},
            #{skuName,jdbcType=VARCHAR},
            #{skuPrice,jdbcType=INTEGER},
            #{skuStatus,jdbcType=INTEGER},
            #{skuDesc,jdbcType=VARCHAR},
            #{skuSuitable,jdbcType=VARCHAR},
            #{skuPeople,jdbcType=INTEGER},
            #{skuType,jdbcType=INTEGER},
            #{skuUnionType,jdbcType=INTEGER},
            now(),
            #{createUser,jdbcType=VARCHAR},
            #{skuSpecies,jdbcType=INTEGER},
            #{signType,jdbcType=INTEGER},
            #{signFlag,jdbcType=INTEGER},
            #{groupNo,jdbcType=BIGINT},
            #{validType,jdbcType=INTEGER},
            #{validEnd,jdbcType=TIMESTAMP},
            #{skuValid,jdbcType=INTEGER}
        </trim>
    </insert>


    <select id="selectLockBySkuNo" resultMap="skuInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where sku_no=#{skuNo}
        and yn = 1 and sku_status != 3
        for update
    </select>
    <!-- 修改实体 -->
    <update id="updateSkuInfo" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity">
        update examination_man_sku
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="id != null and id != ''">
                    id = #{id,jdbcType=BIGINT},
                </if>
                <if test="skuNo != null and skuNo != ''">
                    sku_no = #{skuNo,jdbcType=VARCHAR},
                </if>
                <if test="skuName != null and skuName != ''">
                    sku_name = #{skuName,jdbcType=VARCHAR},
                </if>
                <if test="skuPrice != null and skuPrice != ''">
                    sku_price = #{skuPrice,jdbcType=INTEGER},
                </if>
                <if test="skuStatus != null">
                    sku_status = #{skuStatus,jdbcType=INTEGER},
                </if>
                <if test="yn != null">
                    yn = #{yn,jdbcType=TINYINT},
                </if>
                <if test="skuDesc != null and skuDesc != ''">
                    sku_desc = #{skuDesc,jdbcType=VARCHAR},
                </if>
                <if test="skuSuitable != null and skuSuitable != ''">
                    sku_suitable = #{skuSuitable,jdbcType=VARCHAR},
                </if>
                <if test="skuPeople != null and skuPeople != ''">
                    sku_people = #{skuPeople,jdbcType=INTEGER},
                </if>

                <if test="skuType != null and skuType != ''">
                    sku_type = #{skuType,jdbcType=INTEGER},
                </if>
                <if test="skuType != null and skuType != ''">
                    sku_type = #{skuType,jdbcType=INTEGER},
                </if>
                <if test="createUser != null and createUser != ''">
                    create_user = #{createUser,jdbcType=VARCHAR},
                </if>

                <if test="updateUser != null and updateUser != ''">
                    update_user = #{updateUser,jdbcType=VARCHAR},
                </if>
                <if test="skuSpecies != null">
                    sku_species = #{skuSpecies,jdbcType=INTEGER},
                </if>
            </trim>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <!-- 修改实体 -->
    <update id="updateSkuInfoBySkuNo" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity">
        update examination_man_sku
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="skuNo != null and skuNo != ''">
                    sku_no = #{skuNo,jdbcType=VARCHAR},
                </if>
                <if test="skuName != null and skuName != ''">
                    sku_name = #{skuName,jdbcType=VARCHAR},
                </if>
                <if test="skuPrice != null and skuPrice != ''">
                    sku_price = #{skuPrice,jdbcType=INTEGER},
                </if>
                <if test="skuStatus != null">
                    sku_status = #{skuStatus,jdbcType=INTEGER},
                </if>
                <if test="yn != null">
                    yn = #{yn,jdbcType=TINYINT},
                </if>
                <if test="skuDesc != null and skuDesc != ''">
                    sku_desc = #{skuDesc,jdbcType=VARCHAR},
                </if>
                <if test="skuSuitable != null and skuSuitable != ''">
                    sku_suitable = #{skuSuitable,jdbcType=VARCHAR},
                </if>
                <if test="skuPeople != null and skuPeople != ''">
                    sku_people = #{skuPeople,jdbcType=INTEGER},
                </if>

                <if test="skuType != null and skuType != ''">
                    sku_type = #{skuType,jdbcType=INTEGER},
                </if>
                <if test="skuType != null and skuType != ''">
                    sku_type = #{skuType,jdbcType=INTEGER},
                </if>
                <if test="createUser != null and createUser != ''">
                    create_user = #{createUser,jdbcType=VARCHAR},
                </if>

                <if test="updateUser != null and updateUser != ''">
                    update_user = #{updateUser,jdbcType=VARCHAR},
                </if>
                <if test="skuSpecies != null">
                    sku_species = #{skuSpecies,jdbcType=INTEGER},
                </if>
            </trim>
        </set>
        where sku_no = #{skuNo,jdbcType=VARCHAR}
    </update>

    <!-- 删除实体 -->
   <!-- <update id="deleteSkuInfoById" parameterType="LONG">
        update examination_man_sku
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>-->
    <update id="updateSkuVindicateStatus">
        update examination_man_sku
        set  sku_vindicate_status=#{skuVindicateStatus}
        where sku_no=#{skuNo}
        and yn = 1
    </update>
    <update id="updateSkuGroup">
        update examination_man_sku
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="groupNo !=null">
                    group_no=#{groupNo},
                </if>
                <if test="skuValid !=null">
                    sku_valid=#{skuValid},
                </if>
                <if test="validType !=null">
                    valid_type=#{validType},
                </if>
                <if test="validEnd !=null">
                    valid_end=#{validEnd},
                </if>
                <if test="skuSuitable !=null">
                    sku_suitable=#{skuSuitable},
                </if>
                <if test="skuDesc !=null">
                    sku_desc=#{skuDesc},
                </if>
                <if test="composeId !=null">
                    compose_id=#{composeId},
                </if>
                <if test="updateUser != null">
                    update_user=#{updateUser},
                </if>
            </trim>
        </set>
        where sku_no=#{skuNo}
        and yn = 1
    </update>

    <!--更新sku的描述-->
    <update id="updateSkuSuitableAndDesc">
        update examination_man_sku set sku_desc=#{groupDesc},
        sku_suitable=#{skuSuitable}
        where group_no = #{groupNo}
        and yn = 1
    </update>
    <!--根据composeId查询-->
    <select id="checkSkuInfoByComposeId" resultMap="skuInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where compose_id=#{composeId}
        and yn = 1 and sku_status != 3
        limit 1
    </select>
    <select id="selectNormalSkuList"
            resultMap="skuGroupMap">
        SELECT
        sku_no,
        sku_name,
        s.group_no,
        g.group_name,
        g.group_suitable,
        sku_price
        FROM
        examination_man_sku s
        LEFT JOIN examination_man_group g ON s.group_no=g.group_no
        <where>
            <if test="skuNo != null and skuNo!=''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="skuName != null and skuName!=''">
                and sku_name LIKE CONCAT('%',#{skuName}, '%')
            </if>
            <if test="groupName != null and groupName!=''">
                and g.group_name LIKE CONCAT('%',#{groupName}, '%')
            </if>
            <if test="groupNo != null">
                and s.group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="groupSuitable != null">
                and sku_suitable LIKE CONCAT('%',#{groupSuitable}, '%')
            </if>
            <if test="groupSuitable != null and groupSuitable=='13'">
                or sku_suitable LIKE CONCAT('%','123', '%')
            </if>
            <if test="skuSpecies != null">
                and s.sku_species = #{skuSpecies,jdbcType=INTEGER}
            </if>
            and sku_union_type = 1
            and s.yn = 1 and g.yn = 1 and sku_vindicate_status = 1 and s.sku_status != 3
        </where>
    </select>

    <select id="queryExpiredSkuInfoPage" resultMap="skuInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where
        yn = 1
        <if test="skuSpecies != null">
            and sku_species = #{skuSpecies}
        </if>
        and valid_type = 2
        and #{expireDate,jdbcType=TIMESTAMP} >= valid_end
        and sku_vindicate_status = 1 and sku_status != 3
        and valid_end > now()
    </select>

    <select id="listValidBySkuNos"
            resultType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where sku_no in
        <foreach collection="skuNos" item="skuNo" open="(" close=")" separator=",">
            #{skuNo}
        </foreach>
        <if test="skuSpecies != null">
            and sku_species = #{skuSpecies,jdbcType=INTEGER}
        </if>
        and yn = 1 and sku_status != 3
    </select>

    <select id="querySkuNameValueList" resultType="com.jd.health.medical.examination.domain.NameValueEntity">
        select
            sku_name as name,
            sku_no as value
        from examination_man_sku
        <where>
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="skuName != null and skuName != ''">
                and sku_name LIKE CONCAT('%',#{skuName}, '%')
            </if>
            <if test="skuSpecies != null">
                and sku_species = #{skuSpecies}
            </if>
            and yn = 1 and sku_status != 3
            limit  #{maxCount}
        </where>
    </select>

    <!--根据skuNo删除-->
    <update id="deleteSkuInfoLogicBySkuNo">
        update examination_man_sku
        set yn = 0, update_time = now(), update_user = #{updateUser}
        where sku_no = #{skuNo}
        and yn = 1
    </update>
    
    <select id="listByName" resultType="com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_sku
        where sku_name = #{skuName}
        and yn = 1 and sku_status != 3
    </select>

    <update id="abandonSkuBySkuNo">
        update examination_man_sku
        set sku_status  = 3,
            update_user = #{updateUser, jdbcType=VARCHAR}
        where sku_no = #{skuNo}
          and yn = 1
    </update>
</mapper>