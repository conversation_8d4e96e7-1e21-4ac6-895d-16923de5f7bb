<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.ExaminationQxManAccountDeptMapper">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManAccountDeptEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="user_pin" jdbcType="VARCHAR" property="userPin"/>
        <result column="company_no" jdbcType="BIGINT" property="companyNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="department_mark" jdbcType="TINYINT" property="departmentMark"/>
        <result column="is_new_power" jdbcType="TINYINT" property="isNewPower"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
    </resultMap>
    <sql id="Base_Column_List">
    `id`, `dept_id`, `user_pin`, `company_no`, `department_mark`, `create_time`, `is_new_power`, `update_time`, `yn`
  </sql>
    <select id="selectByCompanyNoAndUserPin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_account_dept
        where `company_no` = #{companyNo,jdbcType=BIGINT}
        and user_pin=#{userPin,jdbcType=VARCHAR}
        and yn=1
    </select>
    <insert id="insert"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManAccountDeptEntity">
    insert into examination_qx_man_account_dept (`id`, `dept_id`, `user_pin`,
      `company_no`, `create_time`, `update_time`,
      `yn`)
    values (#{id,jdbcType=BIGINT}, #{deptId,jdbcType=VARCHAR}, #{userPin,jdbcType=VARCHAR},
      #{companyNo,jdbcType=BIGINT},now(),now(),
      #{yn,jdbcType=TINYINT})
  </insert>
    <insert id="insertSelective"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManAccountDeptEntity">
        insert into examination_qx_man_account_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>
            <if test="deptId != null">
                `dept_id`,
            </if>
            <if test="userPin != null">
                `user_pin`,
            </if>
            <if test="companyNo != null">
                `company_no`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
            <if test="yn != null">
                `yn`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="userPin != null">
                #{userPin,jdbcType=VARCHAR},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch"
            parameterType="java.util.List">
        insert into examination_qx_man_account_dept (
        `dept_id`,`department_mark`,`is_new_power`, `user_pin`,`company_no`, `create_time`,`update_time`,`yn`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId,jdbcType=VARCHAR},#{item.departmentMark,jdbcType=TINYINT},
            #{item.isNewPower,jdbcType=TINYINT}, #{item.userPin,jdbcType=VARCHAR},
            #{item.companyNo,jdbcType=BIGINT}, now(),now(), #{item.yn,jdbcType=TINYINT})
        </foreach>
    </insert>
    <update id="updateByExampleSelective" parameterType="map">
        update examination_qx_man_account_dept
        <set>
            <if test="record.deptId != null">
                `dept_id` = #{record.deptId,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                `create_time` = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                `update_time` = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.yn != null">
                `yn` = #{record.yn,jdbcType=TINYINT},
            </if>
        </set>
        where
        `user_pin` = #{record.userPin,jdbcType=VARCHAR} and
        `company_no` = #{record.companyNo,jdbcType=BIGINT}
        and yn=1
    </update>

    <select id="selectNewByCompanyAndUserPin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from examination_qx_man_account_dept
        where yn = 1
        and user_pin = #{pin}
        and company_no = #{companyNo}
        and is_new_power = #{isNewPower}
    </select>
</mapper>