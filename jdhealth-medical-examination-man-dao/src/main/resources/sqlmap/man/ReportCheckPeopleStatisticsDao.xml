<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ReportCheckPeopleStatisticsDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics">
    <!--@mbg.generated-->
    <!--@Table report_check_people_statistics-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="main_statistic_type" jdbcType="INTEGER" property="mainStatisticType" />
    <result column="sub_statistic_type" jdbcType="INTEGER" property="subStatisticType" />
    <result column="company_no" jdbcType="BIGINT" property="companyNo" />
    <result column="age_range_type" jdbcType="INTEGER" property="ageRangeType" />
    <result column="female_number" jdbcType="INTEGER" property="femaleNumber" />
    <result column="male_number" jdbcType="INTEGER" property="maleNumber" />
    <result column="total_number" jdbcType="INTEGER" property="totalNumber" />
    <result column="female_number_rate" jdbcType="DOUBLE" property="femaleNumberRate" />
    <result column="male_number_rate" jdbcType="DOUBLE" property="maleNumberRate" />
    <result column="total_number_rate" jdbcType="DOUBLE" property="totalNumberRate" />
    <result column="main_statistic_unit_id" jdbcType="BIGINT" property="mainStatisticUnitId" />
    <result column="sub_statistic_unit_id" jdbcType="BIGINT" property="subStatisticUnitId" />
    <result column="yn" jdbcType="INTEGER" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, main_statistic_type, sub_statistic_type, company_no, age_range_type, female_number, 
    male_number, total_number, female_number_rate, male_number_rate, total_number_rate, 
    main_statistic_unit_id, sub_statistic_unit_id, yn, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from report_check_people_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectReportCheckPeopleStatisticsList" parameterType="com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from report_check_people_statistics
    <where>
      <if test="companyNo != null">
        and company_no = #{companyNo,jdbcType=BIGINT}
      </if>
      <if test="ageRangeType != null">
        and age_range_type = #{ageRangeType,jdbcType=INTEGER}
      </if>
      <if test="mainStatisticType != null">
        and main_statistic_type = #{mainStatisticType,jdbcType=INTEGER}
      </if>
      <if test="mainStatisticUnitId != null">
        and main_statistic_unit_id = #{mainStatisticUnitId,jdbcType=BIGINT}
      </if>
      <if test="subStatisticType != null">
        and sub_statistic_type = #{subStatisticType,jdbcType=INTEGER}
      </if>
      <if test="subStatisticUnitId != null">
        and sub_statistic_unit_id = #{subStatisticUnitId,jdbcType=BIGINT}
      </if>
      and yn = 1
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from report_check_people_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into report_check_people_statistics (main_statistic_type, sub_statistic_type, 
      company_no, age_range_type, female_number, 
      male_number, total_number, female_number_rate, 
      male_number_rate, total_number_rate, main_statistic_unit_id, 
      sub_statistic_unit_id, yn, create_time, 
      update_time)
    values (#{mainStatisticType,jdbcType=INTEGER}, #{subStatisticType,jdbcType=INTEGER}, 
      #{companyNo,jdbcType=BIGINT}, #{ageRangeType,jdbcType=INTEGER}, #{femaleNumber,jdbcType=INTEGER}, 
      #{maleNumber,jdbcType=INTEGER}, #{totalNumber,jdbcType=INTEGER}, #{femaleNumberRate,jdbcType=DOUBLE}, 
      #{maleNumberRate,jdbcType=DOUBLE}, #{totalNumberRate,jdbcType=DOUBLE}, #{mainStatisticUnitId,jdbcType=BIGINT}, 
      #{subStatisticUnitId,jdbcType=BIGINT}, #{yn,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into report_check_people_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mainStatisticType != null">
        main_statistic_type,
      </if>
      <if test="subStatisticType != null">
        sub_statistic_type,
      </if>
      <if test="companyNo != null">
        company_no,
      </if>
      <if test="ageRangeType != null">
        age_range_type,
      </if>
      <if test="femaleNumber != null">
        female_number,
      </if>
      <if test="maleNumber != null">
        male_number,
      </if>
      <if test="totalNumber != null">
        total_number,
      </if>
      <if test="femaleNumberRate != null">
        female_number_rate,
      </if>
      <if test="maleNumberRate != null">
        male_number_rate,
      </if>
      <if test="totalNumberRate != null">
        total_number_rate,
      </if>
      <if test="mainStatisticUnitId != null">
        main_statistic_unit_id,
      </if>
      <if test="subStatisticUnitId != null">
        sub_statistic_unit_id,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mainStatisticType != null">
        #{mainStatisticType,jdbcType=INTEGER},
      </if>
      <if test="subStatisticType != null">
        #{subStatisticType,jdbcType=INTEGER},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=BIGINT},
      </if>
      <if test="ageRangeType != null">
        #{ageRangeType,jdbcType=INTEGER},
      </if>
      <if test="femaleNumber != null">
        #{femaleNumber,jdbcType=INTEGER},
      </if>
      <if test="maleNumber != null">
        #{maleNumber,jdbcType=INTEGER},
      </if>
      <if test="totalNumber != null">
        #{totalNumber,jdbcType=INTEGER},
      </if>
      <if test="femaleNumberRate != null">
        #{femaleNumberRate,jdbcType=DOUBLE},
      </if>
      <if test="maleNumberRate != null">
        #{maleNumberRate,jdbcType=DOUBLE},
      </if>
      <if test="totalNumberRate != null">
        #{totalNumberRate,jdbcType=DOUBLE},
      </if>
      <if test="mainStatisticUnitId != null">
        #{mainStatisticUnitId,jdbcType=BIGINT},
      </if>
      <if test="subStatisticUnitId != null">
        #{subStatisticUnitId,jdbcType=BIGINT},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics">
    <!--@mbg.generated-->
    update report_check_people_statistics
    <set>
      <if test="mainStatisticType != null">
        main_statistic_type = #{mainStatisticType,jdbcType=INTEGER},
      </if>
      <if test="subStatisticType != null">
        sub_statistic_type = #{subStatisticType,jdbcType=INTEGER},
      </if>
      <if test="companyNo != null">
        company_no = #{companyNo,jdbcType=BIGINT},
      </if>
      <if test="ageRangeType != null">
        age_range_type = #{ageRangeType,jdbcType=INTEGER},
      </if>
      <if test="femaleNumber != null">
        female_number = #{femaleNumber,jdbcType=INTEGER},
      </if>
      <if test="maleNumber != null">
        male_number = #{maleNumber,jdbcType=INTEGER},
      </if>
      <if test="totalNumber != null">
        total_number = #{totalNumber,jdbcType=INTEGER},
      </if>
      <if test="femaleNumberRate != null">
        female_number_rate = #{femaleNumberRate,jdbcType=DOUBLE},
      </if>
      <if test="maleNumberRate != null">
        male_number_rate = #{maleNumberRate,jdbcType=DOUBLE},
      </if>
      <if test="totalNumberRate != null">
        total_number_rate = #{totalNumberRate,jdbcType=DOUBLE},
      </if>
      <if test="mainStatisticUnitId != null">
        main_statistic_unit_id = #{mainStatisticUnitId,jdbcType=BIGINT},
      </if>
      <if test="subStatisticUnitId != null">
        sub_statistic_unit_id = #{subStatisticUnitId,jdbcType=BIGINT},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.ReportCheckPeopleStatistics">
    <!--@mbg.generated-->
    update report_check_people_statistics
    set main_statistic_type = #{mainStatisticType,jdbcType=INTEGER},
      sub_statistic_type = #{subStatisticType,jdbcType=INTEGER},
      company_no = #{companyNo,jdbcType=BIGINT},
      age_range_type = #{ageRangeType,jdbcType=INTEGER},
      female_number = #{femaleNumber,jdbcType=INTEGER},
      male_number = #{maleNumber,jdbcType=INTEGER},
      total_number = #{totalNumber,jdbcType=INTEGER},
      female_number_rate = #{femaleNumberRate,jdbcType=DOUBLE},
      male_number_rate = #{maleNumberRate,jdbcType=DOUBLE},
      total_number_rate = #{totalNumberRate,jdbcType=DOUBLE},
      main_statistic_unit_id = #{mainStatisticUnitId,jdbcType=BIGINT},
      sub_statistic_unit_id = #{subStatisticUnitId,jdbcType=BIGINT},
      yn = #{yn,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>