<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.CheckTemplateDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="template_id" jdbcType="BIGINT" property="templateId"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="channel_no" jdbcType="BIGINT" property="channelNo"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="group_type" jdbcType="INTEGER" property="groupType"/>
        <result column="province_area" jdbcType="VARCHAR" property="provinceArea"/>
        <result column="city_area" jdbcType="VARCHAR" property="cityArea"/>
        <result column="district_area" jdbcType="VARCHAR" property="districtArea"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id, template_id, template_name, channel_no, channel_name, group_type, province_area,
            city_area, district_area, yn, create_user, create_time, update_user, update_time
        </trim>
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_check_template
        where id = #{id}
        and yn = 1
    </select>

    <select id="queryByTemplateId" resultType="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        select
        <include refid="Base_Column_List"/>
        from
        examination_man_check_template
        where
        template_id = #{templateId,jdbcType=BIGINT}
        and yn = 1
    </select>
    <select id="queryCheckTemplatePageList"
            resultType="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        SELECT
        id as id,
        template_id AS templateId,
        template_name AS templateName,
        channel_no AS channelNo,
        channel_name AS channelName,
        group_type AS groupType
        FROM examination_man_check_template
        <where>
            yn = 1
            <if test="templateId != null">
                and template_id = #{templateId}
            </if>
            <if test="templateName != null and templateName !=''">
                and template_name like concat('%',#{templateName},'%')
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo}
            </if>
            <if test="channelName != null and channelName != ''">
                and channel_name = #{channelName}
            </if>
            <if test="groupType != null">
                and group_type = #{groupType}
            </if>
        </where>
    </select>
    <select id="queryCheckTemplateByChannelNo"
            resultType="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_check_template
        where yn = 1
        <if test="groupType != null">
            and group_type = #{groupType}
        </if>
        and channel_no = #{channelNo}
    </select>
    <select id="queryCheckTemplateByTemplateName"
            resultType="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_check_template
        where yn = 1 and
        template_name = #{templateName}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update examination_man_check_template
        set yn = 0
        where template_id = #{templateId}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.CheckTemplateEntity" useGeneratedKeys="true">
        insert into examination_man_check_template (template_id, template_name, channel_no,
                                                    channel_name, group_type, province_area,
                                                    city_area, district_area, yn,
                                                    create_user, create_time, update_user,
                                                    update_time)
        values (#{templateId,jdbcType=BIGINT}, #{templateName,jdbcType=VARCHAR}, #{channelNo,jdbcType=BIGINT},
                #{channelName,jdbcType=VARCHAR}, #{groupType,jdbcType=TINYINT}, #{provinceArea,jdbcType=VARCHAR},
                #{cityArea,jdbcType=VARCHAR}, #{districtArea,jdbcType=VARCHAR}, 1,
                #{createUser,jdbcType=VARCHAR}, now(), #{updateUser,jdbcType=VARCHAR},
                now())
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.CheckTemplateEntity" useGeneratedKeys="true">
        insert into examination_man_check_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">
                template_id,
            </if>
            <if test="templateName != null">
                template_name,
            </if>
            <if test="channelNo != null">
                channel_no,
            </if>
            <if test="channelName != null">
                channel_name,
            </if>
            <if test="groupType != null">
                group_type,
            </if>
            <if test="provinceArea != null">
                province_area,
            </if>
            <if test="cityArea != null">
                city_area,
            </if>
            <if test="districtArea != null">
                district_area,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">
                #{templateId,jdbcType=BIGINT},
            </if>
            <if test="templateName != null">
                #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="channelNo != null">
                #{channelNo,jdbcType=BIGINT},
            </if>
            <if test="channelName != null">
                #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="groupType != null">
                #{groupType,jdbcType=TINYINT},
            </if>
            <if test="provinceArea != null">
                #{provinceArea,jdbcType=VARCHAR},
            </if>
            <if test="cityArea != null">
                #{cityArea,jdbcType=VARCHAR},
            </if>
            <if test="districtArea != null">
                #{districtArea,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        update examination_man_check_template
        <set>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=BIGINT},
            </if>
            <if test="templateName != null">
                template_name = #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="channelNo != null">
                channel_no = #{channelNo,jdbcType=BIGINT},
            </if>
            <if test="channelName != null">
                channel_name = #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="groupType != null">
                group_type = #{groupType,jdbcType=TINYINT},
            </if>
            <if test="provinceArea != null">
                province_area = #{provinceArea,jdbcType=VARCHAR},
            </if>
            <if test="cityArea != null">
                city_area = #{cityArea,jdbcType=VARCHAR},
            </if>
            <if test="districtArea != null">
                district_area = #{districtArea,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.CheckTemplateEntity">
        update examination_man_check_template
        set template_id   = #{templateId,jdbcType=BIGINT},
            template_name = #{templateName,jdbcType=VARCHAR},
            channel_no    = #{channelNo,jdbcType=BIGINT},
            channel_name  = #{channelName,jdbcType=VARCHAR},
            group_type    = #{groupType,jdbcType=TINYINT},
            province_area = #{provinceArea,jdbcType=VARCHAR},
            city_area     = #{cityArea,jdbcType=VARCHAR},
            district_area = #{districtArea,jdbcType=VARCHAR},
            update_user   = #{updateUser,jdbcType=VARCHAR},
            update_time   = now()
        where id = #{id,jdbcType=BIGINT}
          and yn = 1
    </update>
</mapper>