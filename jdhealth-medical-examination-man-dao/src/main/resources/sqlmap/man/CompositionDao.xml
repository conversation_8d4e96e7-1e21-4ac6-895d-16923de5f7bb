<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.jd.health.medical.examination.dao.CompositionDao" >

  <resultMap id="CompositionMap" type="com.jd.health.medical.examination.domain.personal.entity.CompositionEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="equity_composition_id" property="equityCompositionId" jdbcType="BIGINT" />
    <result column="equity_composition_name" property="equityCompositionName" jdbcType="VARCHAR" />
    <result column="equity_desc" property="equityDesc" jdbcType="VARCHAR" />
    <result column="equity_num" property="equityNum" jdbcType="INTEGER" />
    <result column="validity_period_type" property="validityPeriodType" jdbcType="INTEGER" />
    <result column="validity_days" property="validityDays" jdbcType="INTEGER" />
    <result column="validity_end" property="validityEnd" jdbcType="TIMESTAMP" />
    <result column="equity_composition_type" property="equityCompositionType" jdbcType="INTEGER" />
    <result column="equity_composition_remark" property="equityCompositionRemark" jdbcType="VARCHAR" />
    <result column="company_no" property="companyNo" jdbcType="BIGINT" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
      id,
      equity_composition_id,
      equity_composition_name,
      equity_desc,
      equity_num,
      validity_period_type,
      equity_composition_remark,
      validity_days,
      validity_end,
      equity_composition_type,
      company_no,
      company_name,
      yn,
      create_time,
      create_user,
      update_time,
    </trim>
  </sql>

  <select id="selectAll" resultMap="CompositionMap"
          parameterType="com.jd.health.medical.examination.domain.personal.entity.CompositionEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_equity_composition
      <where>
          <if test="equityCompositionId != null">
              and equity_composition_id = #{equityCompositionId,jdbcType=BIGINT}
          </if>
          and yn = 1
      </where>
  </select>

  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.personal.entity.CompositionEntity" >
    insert into examination_man_equity_composition (equity_composition_id,equity_composition_name,equity_desc,equity_num,validity_period_type,validity_days,validity_end,yn,create_time,update_time,create_user,equity_composition_remark,equity_composition_type)
    values (#{equityCompositionId},#{equityCompositionName,jdbcType=VARCHAR},
      #{equityDesc,jdbcType=VARCHAR},#{equityNum,jdbcType=INTEGER},#{validityPeriodType,jdbcType=INTEGER},#{validityDays,jdbcType=INTEGER},#{validityEnd,jdbcType=TIMESTAMP},1,now(),now(),#{createUser},#{equityCompositionRemark},#{equityCompositionType})
  </insert>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from examination_man_equity_composition
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="deleteByEquityCompositionId">
    update examination_man_equity_composition
    set yn = 0, update_time = now()
    where equity_composition_id=#{equityCompositionId}
    and yn =1
  </update>

  <select id="selectByPrimaryKey" resultMap="CompositionMap"
          parameterType="com.jd.health.medical.examination.domain.personal.entity.CompositionEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_equity_composition
    where id=#{id,jdbcType=BIGINT} and yn = 1
  </select>

  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.personal.entity.CompositionEntity" >
    update examination_man_equity_composition
    set equity_composition_name = #{equityCompositionName,jdbcType=VARCHAR},
      equity_desc = #{equityDesc,jdbcType=VARCHAR},
      equity_num = #{equityNum,jdbcType=INTEGER},
      update_time = now()
    where equity_composition_id = #{equityCompositionId,jdbcType=BIGINT}
  </update>
  <update id="updateByByEquityCompositionId">
     update examination_man_equity_composition
    set equity_composition_name = #{equityCompositionName,jdbcType=VARCHAR},
      equity_desc = #{equityDesc,jdbcType=VARCHAR},
      equity_num = #{equityNum,jdbcType=INTEGER},
      validity_period_type = #{validityPeriodType},
      validity_days = #{validityDays},
      validity_end = #{validityEnd},
      equity_composition_remark = #{equityCompositionRemark},
      update_time = now()
    where equity_composition_id = #{equityCompositionId,jdbcType=BIGINT}
  </update>

  <select id="getCompositionByEquityCompositionId" resultMap="CompositionMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List"/>
    from
    examination_man_equity_composition
    where equity_composition_id = #{equityCompositionId, jdbcType=BIGINT} and yn = 1
  </select>
  <select id="selectByCompositionEntity"
          resultMap="CompositionMap">
    select
    <include refid="Base_Column_List"/>
    from
    examination_man_equity_composition
    <where>
      <if test="equityCompositionId != null">
        and equity_composition_id = #{equityCompositionId}
      </if>
      <if test="equityCompositionName != null and equityCompositionName != ''">
        and equity_composition_name like CONCAT('%',#{equityCompositionName},"%")
      </if>
      <if test="equityCompositionRemark != null and equityCompositionRemark != ''">
        and equity_composition_remark like CONCAT('%',#{equityCompositionRemark},"%")
      </if>
      <if test="equityCompositionType != null">
        and equity_composition_type = #{equityCompositionType}
      </if>
      and yn = 1
    </where>
    order by create_time
    desc
  </select>
  <select id="selectByCompositionId" resultMap="CompositionMap">
    select
    <include refid="Base_Column_List"/>
    from
    examination_man_equity_composition
    where
    equity_composition_id = #{equityCompositionId, jdbcType=BIGINT}
    and yn = 1
  </select>

</mapper>