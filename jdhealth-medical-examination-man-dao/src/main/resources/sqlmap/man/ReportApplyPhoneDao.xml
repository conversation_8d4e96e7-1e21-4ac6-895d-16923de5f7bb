<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ReportApplyPhoneDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ReportApplyPhoneEntity">
    <!--@mbg.generated-->
    <!--@Table report_apply_phone-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="jd_appointment_id" jdbcType="BIGINT" property="jdAppointmentId" />
    <result column="apply_id" jdbcType="INTEGER" property="applyId" />
    <result column="user_pin" jdbcType="VARCHAR" property="userPin" />
    <result column="user_name_encrypt" property="userName" typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
    <result column="credential_no_encrypt" property="credentialNo" typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
    <result column="old_phone_encrypt" property="oldPhone" typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
    <result column="phone_encrypt" property="phone" typeHandler="com.jd.security.aces.mybatis.handle.AcesCipherTextHandle"/>
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="report_source" jdbcType="TINYINT" property="reportSource" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="check_pin" jdbcType="VARCHAR" property="checkPin" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
  </resultMap>
  <sql id="Base_Column_List">
      id,
      jd_appointment_id,
      apply_id,
      user_pin,
      user_name,
      user_name_encrypt,
      credential_no,
      credential_no_encrypt,
      old_phone,
      old_phone_encrypt,
      phone,
      phone_encrypt,
      status,
      reason,
      report_source,
      apply_time,
      check_pin,
      check_time,
      create_time,
      update_time,
      yn
  </sql>
  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.personal.entity.ReportApplyPhoneEntity" >
    <!--@mbg.generated-->
    insert into report_apply_phone (
      jd_appointment_id,
      apply_id,
      user_pin,
      user_name,
      user_name_encrypt,
      credential_no,
      credential_no_encrypt,
      phone,
      phone_encrypt,
      old_phone,
      old_phone_encrypt,
      status,
      reason,
      report_source,
      apply_time
    )
    values (
      #{jdAppointmentId,jdbcType=BIGINT},
      #{applyId,jdbcType=INTEGER},
      #{userPin,jdbcType=VARCHAR},
      #{userName,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
      #{userName,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
      #{credentialNo,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
      #{credentialNo,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
      #{phone,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
      #{phone,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
      #{oldPhone,typeHandler=com.jd.security.aces.mybatis.handle.AcesPlainTextHandle},
      #{oldPhone,typeHandler=com.jd.security.aces.mybatis.handle.AcesCipherTextHandle},
      #{status,jdbcType=TINYINT},
      #{reason,jdbcType=VARCHAR},
      #{reportSource,jdbcType=TINYINT},
      now()
    )
  </insert>
  <select id="queryReportPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from report_apply_phone
    <where>
        <if test="phone != null and phone != ''">
            and phone = #{phone}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
        <if test="userName != null and userName != ''">
            and user_name = #{userName,jdbcType=VARCHAR}
        </if>
        and yn = 1
        order by create_time desc
    </where>
  </select>
  <update id="updateReportStatus">
    update report_apply_phone
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="checkPin != null">
        check_pin = #{checkPin,jdbcType=VARCHAR},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP}
      </if>
    </set>
    where apply_id = #{applyId,jdbcType=BIGINT} and yn = 1
  </update>


  <select id="countApplying" resultType="int">
    select
     count(1)
    from report_apply_phone
    where
    user_pin = #{userPin,jdbcType=VARCHAR}
    and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    and `status` = 1
    and yn = 1
  </select>
</mapper>