<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.ExaminationQxManSmsTemplateParamCustomDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamCustomEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_id" jdbcType="VARCHAR" property="paramId" />
    <result column="custom_value" jdbcType="VARCHAR" property="customValue" />
    <result column="custom_name" jdbcType="VARCHAR" property="customName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_id, custom_name,custom_value, create_user, create_time, yn
  </sql>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    update  examination_qx_man_sms_template_param_custom set yn = 0
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByParamId">
    update  examination_qx_man_sms_template_param_custom set yn = 0
    where yn = 1 and param_id = #{paramId}
  </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManSmsTemplateParamCustomEntity" useGeneratedKeys="true">
    insert into examination_qx_man_sms_template_param_custom (param_id,custom_name, custom_value, create_user,
      create_time, yn)
    values (#{paramId,jdbcType=VARCHAR}, #{customName,jdbcType=VARCHAR},#{customValue,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{yn,jdbcType=TINYINT})
  </insert>


  <select id="selectByParamIdList" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_qx_man_sms_template_param_custom
    where yn = 1
    and param_id in
    <foreach collection="paramIdList" item = "item" open="(" close=")" separator=",">#{item}</foreach>
  </select>
</mapper>