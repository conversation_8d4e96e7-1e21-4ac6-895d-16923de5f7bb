<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdGoodsItemDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.third.ThirdGoodsItemEntity" id="thirdGoodsItemMap">
        <result property="id" column="id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemNo" column="item_no"/>
        <result property="channelNo" column="channel_no"/>
        <result property="itemMeans" column="item_means"/>
        <result property="itemTopCategory" column="item_top_category"/>
        <result property="itemSecCategory" column="item_sec_category"/>
        <result property="itemSuitable" column="item_suitable"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="vendorType" column="vendor_type"/>
        <result property="sort" column="sort"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            goods_id,
            item_name,
            item_no,
            channel_no,
            item_means,
            item_top_category,
            item_sec_category,
            item_suitable,
            yn,
            create_time,
            update_time,
            vendor_type,
            sort
        </trim>
    </sql>

    <!-- 根据Id查询 -->
    <select id="selectThirdGoodsItemById" resultMap="thirdGoodsItemMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods_item
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryThirdGoodsItemList" resultMap="thirdGoodsItemMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods_item
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="itemName != null">
                and item_name = #{itemName,jdbcType=VARCHAR}
            </if>
            <if test="itemNo != null">
                and item_no = #{itemNo,jdbcType=VARCHAR}
            </if>
            <if test="itemMeans != null">
                and item_means = #{itemMeans,jdbcType=VARCHAR}
            </if>
            <if test="itemTopCategory != null">
                and item_top_category = #{itemTopCategory,jdbcType=VARCHAR}
            </if>
            <if test="itemSecCategory != null">
                and item_sec_category = #{itemSecCategory,jdbcType=VARCHAR}
            </if>
            <if test="itemSuitable != null">
                and item_suitable = #{itemSuitable,jdbcType=VARCHAR}
            </if>
            <if test="yn != null">
                and yn = #{yn,jdbcType=INTEGER}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo}
            </if>
            <if test="vendorType != null">
                and vendor_type = #{vendorType}
            </if>
            and yn = 1
        </where>
    </select>
    <select id="selectThirdGoodsItemByGoodsId"
            resultMap="thirdGoodsItemMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods_item
        where
        channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id=#{goodsId,jdbcType=VARCHAR}
        <if test="vendorType != null">
            and vendor_type = #{vendorType}
        </if>
        and yn = 1
    </select>


    <!-- 插入实体 -->
    <insert id="insertThirdGoodsItemList" parameterType="java.util.List" keyProperty="id" keyColumn="id">
        insert into examination_man_third_goods_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            goods_id,
            item_name,
            item_no,
            channel_no,
            item_means,
            item_top_category,
            item_sec_category,
            item_suitable,
            yn,
            create_time,
            update_time,
            vendor_type,
            sort
        </trim>
        values
        <foreach collection="list" index="index" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.goodsId,jdbcType=VARCHAR},
                #{item.itemName,jdbcType=VARCHAR},
                #{item.itemNo,jdbcType=VARCHAR},
                #{item.channelNo,jdbcType=BIGINT},
                #{item.itemMeans,jdbcType=VARCHAR},
                #{item.itemTopCategory,jdbcType=VARCHAR},
                #{item.itemSecCategory,jdbcType=VARCHAR},
                #{item.itemSuitable,jdbcType=VARCHAR},
                1,
                now(),
                now(),
                #{item.vendorType},
                #{item.sort}
            </trim>
        </foreach>
    </insert>
    <!-- 修改实体 -->
    <update id="updateThirdGoodsItem" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsItemEntity">
        update examination_man_third_goods_item
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="goodsId != null and goodsId != ''">
                    goods_id = #{goodsId,jdbcType=VARCHAR},
                </if>
                <if test="itemName != null and itemName != ''">
                    item_name = #{itemName,jdbcType=VARCHAR},
                </if>
                <if test="itemNo != null and itemNo != ''">
                    item_no = #{itemNo,jdbcType=VARCHAR},
                </if>
                <if test="itemMeans != null and itemMeans != ''">
                    item_means = #{itemMeans,jdbcType=VARCHAR},
                </if>
                <if test="itemTopCategory != null and itemTopCategory != ''">
                    item_top_category = #{itemTopCategory,jdbcType=VARCHAR},
                </if>
                <if test="itemSecCategory != null and itemSecCategory != ''">
                    item_sec_category = #{itemSecCategory,jdbcType=VARCHAR},
                </if>
                <if test="itemSuitable != null and itemSuitable != ''">
                    item_suitable = #{itemSuitable,jdbcType=VARCHAR},
                </if>
                <if test="vendorType != null">
                    vendor_type = #{vendorType},
                </if>
                <if test="sort != null">
                    sort = #{sort},
                </if>
            </trim>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除实体 -->
    <update id="deleteThirdGoodsItemById" parameterType="LONG">
        update examination_man_third_goods_item
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除实体 -->
    <update id="deleteThirdGoodsItemByGoodsId" parameterType="String">
        update examination_man_third_goods_item
        <set>
            yn = 0
        </set>
        where goods_id = #{goodsId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <update id="refreshItemNameTrim">
        update examination_man_third_goods_item
        set item_name = #{itemName,jdbcType=VARCHAR}
        where id  = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>


</mapper>