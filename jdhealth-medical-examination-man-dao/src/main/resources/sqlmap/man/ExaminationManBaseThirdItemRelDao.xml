<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManBaseThirdItemRelDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManBaseThirdItemRelEntity">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="base_item_no" property="baseItemNo" jdbcType="BIGINT" />
        <result column="base_item_name" property="baseItemName" jdbcType="VARCHAR" />
        <result column="third_item_no" property="thirdItemNo" jdbcType="BIGINT" />
        <result column="third_item_name" property="thirdItemName" jdbcType="VARCHAR" />
        <result column="source" property="source" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="yn" property="yn" jdbcType="TINYINT" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, base_item_no, base_item_name, third_item_no, third_item_name, source, remark,
        yn, create_user, create_time, update_user, update_time
    </sql>

    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManBaseThirdItemRelEntity" >
        insert into examination_man_base_third_item_rel (base_item_no, base_item_name,
            third_item_no, third_item_name, source,
            remark, yn, create_user,
            create_time, update_user, update_time
            )
        values (#{baseItemNo,jdbcType=BIGINT}, #{baseItemName,jdbcType=VARCHAR},
            #{thirdItemNo,jdbcType=BIGINT}, #{thirdItemName,jdbcType=VARCHAR}, #{source},
            #{remark,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
            )
    </insert>

    <update id="updateById" parameterType="java.lang.Long" >
        update examination_man_base_third_item_rel
        <set >
            <if test="baseItemNo != null" >
                base_item_no = #{baseItemNo,jdbcType=BIGINT},
            </if>
            <if test="baseItemName != null" >
                base_item_name = #{baseItemName,jdbcType=VARCHAR},
            </if>
            <if test="thirdItemNo != null" >
                third_item_no = #{thirdItemNo,jdbcType=BIGINT},
            </if>
            <if test="thirdItemName != null" >
                third_item_name = #{thirdItemName,jdbcType=VARCHAR},
            </if>
            <if test="source != null" >
                source = #{source},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="yn != null" >
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null" >
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>