<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity" id="enterpriseInfoMap">
        <result property="id" column="id"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="serviceStatus" column="service_status"/>
        <result property="managerPin" column="manager_pin"/>
        <result property="accountNo" column="account_no"/>
        <result property="licenseNo" column="license_no"/>
        <result property="mobile" column="mobile"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="companyLogoUrl" column="logo_url"/>
        <result property="companyGroupName" column="company_group_name"/>
        <result property="subjectCompanyNo"  column="subject_company_no"/>
        <result property="subjectCompanyName" column="subject_company_name"/>
        <result property="upgradeSwitchStatus" column="upgrade_switch_status"/>
        <result property="companyDesc" column="company_desc"/>
        <result property="contractAmount" column="contract_amount"/>
        <result property="contractPeopleNumber" column="contract_people_number"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentChannel" column="payment_channel"/>
        <result property="paymentPercent" column="payment_percent"/>
        <result property="contractInformationUrl" column="contract_information_url"/>
        <result property="projectExecutive" column="project_executive"/>
        <result property="projectExecutiveDesc" column="project_executive_desc"/>
        <result property="customerNo" column="customer_no"/>
        <result property="customerSimpleName" column="customer_simple_name"/>
        <result property="customerOperator" column="customer_operator"/>
        <result property="salesManPhone" column="sales_man_phone"/>
        <result property="customerSource" column="customer_source"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="countyId" column="county_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityName" column="city_name"/>
        <result property="countyName" column="county_name"/>
        <result property="pluginDiscountsType" column="plugin_discounts_type"/>

    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            company_no,
            company_name,
            start_date,
            end_date,
            service_status,
            service_model,
            rel_order_status,
            manager_pin,
            company_address,
            company_contacter,
            company_contacter_phone,
            employee_login_account,
            employee_login_pwd,
            lng,
            lat,
            province_id,
            city_id,
            county_id,
            province_name,
            city_name,
            county_name,
            sales_man,
            account_no,
            license_no,
            mobile,
            relation_status,
            user_pin,
            yn,
            create_time,
            update_time,
            company_group_name,
            logo_url,
            subject_company_no,
            subject_company_name,
            upgrade_switch_status,
            company_desc,
            contract_amount,
            contract_people_number,
            payment_method,
            payment_channel,
            payment_percent,
            contract_information_url,
            project_executive,
            project_executive_desc,
            customer_no,
            customer_simple_name,
            customer_operator,
            sales_man_phone,
            customer_source,
            plugin_discounts_type
        </trim>
    </sql>
    <update id="deleteEnterprise">
        update examination_qx_man_company
        set yn = 0
        where company_no = #{companyNo}
        and yn = 1
    </update>

    <!-- 根据Id查询 -->
    <select id="selectEnterpriseInfoById" resultMap="enterpriseInfoMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryEnterpriseInfoList" resultMap="enterpriseInfoMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="companyName != null">
                and company_name like CONCAT(#{companyName,jdbcType=VARCHAR},"%")
            </if>
            <if test="startDate != null">
                and start_date = #{startDate,jdbcType=DATE}
            </if>
            <if test="endDate != null">
                and end_date = #{endDate,jdbcType=DATE}
            </if>
            <if test="serviceStatus != null">
                and service_status = #{serviceStatus,jdbcType=TINYINT}
            </if>
            <if test="serviceModel != null">
                and service_model = #{serviceModel,jdbcType=INTEGER}
            </if>
            <if test="relOrderStatus != null">
                and rel_order_status = #{relOrderStatus,jdbcType=INTEGER}
            </if>
            <if test="managerPin != null">
                and manager_pin = #{managerPin,jdbcType=VARCHAR}
            </if>
            <if test="customerOperator !=null">
                and customer_operator = #{customerOperator,jdbcType=VARCHAR}
            </if>
            <if test="customerSimpleName !=null and  customerSimpleName !=''">
                and customer_simple_name = #{customerSimpleName,jdbcType=VARCHAR}
            </if>
            <if test="projectExecutive !=null and  projectExecutive != ''">
                and project_executive = #{projectExecutive,jdbcType=VARCHAR}
            </if>
            <if test="customerNo !=null and customerNo != ''">
                and customer_no = #{customerNo,jdbcType=VARCHAR}
            </if>
            <if test='null != pluginDiscountsType'>
                and plugin_discounts_type = #{pluginDiscountsType}
            </if>
            and yn = 1
        </where>
        order by service_status asc, start_date desc
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryEnterpriseInfoListByOperators" resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            and company_no in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="serviceStatus != null">
                and service_status = #{serviceStatus,jdbcType=INTEGER}
            </if>
            <if test="companyName != null">
                and company_name like CONCAT(#{companyName,jdbcType=VARCHAR},"%")
            </if>
            and yn = 1
        </where>
        order by service_status asc, start_date desc
    </select>

    <!-- 插入实体 -->
    <insert id="insertEnterpriseInfo" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_qx_man_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            company_no,
            company_name,
            start_date,
            end_date,
            service_status,
            manager_pin,
            company_address,
            company_contacter,
            company_contacter_phone,
            employee_login_account,
            employee_login_pwd,
            lng,
            lat,
            province_id,
            city_id,
            county_id,
            province_name,
            city_name,
            county_name,
            sales_man,
            account_no,
            account_pwd,
            license_no,
            mobile,
            yn,
            create_time,
            update_time,
            company_group_name,
            logo_url,
            subject_company_no,
            subject_company_name,
            company_desc,
            contract_amount,
            contract_people_number,
            payment_method,
            payment_channel,
            payment_percent,
            contract_information_url,
            project_executive,
            project_executive_desc,
            customer_no,
            customer_simple_name,
            customer_operator,
            sales_man_phone,
            customer_source,
            <if test="pluginDiscountsType!=null">
                plugin_discounts_type,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{companyNo,jdbcType=BIGINT},
            #{companyName,jdbcType=VARCHAR},
            #{startDate,jdbcType=DATE},
            #{endDate,jdbcType=DATE},
            #{serviceStatus,jdbcType=TINYINT},
            #{managerPin,jdbcType=VARCHAR},
            #{companyAddress,jdbcType=VARCHAR},
            #{companyContacter,jdbcType=VARCHAR},
            #{companyContacterPhone,jdbcType=VARCHAR},
            #{employeeLoginAccount,jdbcType=INTEGER},
            #{employeeLoginPwd,jdbcType=INTEGER},
            #{lng,jdbcType=DECIMAL},
            #{lat,jdbcType=DECIMAL},
            #{provinceId,jdbcType=INTEGER},
            #{cityId,jdbcType=INTEGER},
            #{countyId,jdbcType=INTEGER},
            #{provinceName,jdbcType=VARCHAR},
            #{cityName,jdbcType=VARCHAR},
            #{countyName,jdbcType=VARCHAR},
            #{salesman,jdbcType=VARCHAR},
            #{accountNo,jdbcType=VARCHAR},
            #{accountPwd,jdbcType=VARCHAR},
            #{licenseNo,jdbcType=VARCHAR},
            #{mobile,jdbcType=VARCHAR},
            1,
            now(),
            now(),
            #{companyGroupName,jdbcType=VARCHAR},
            #{companyLogoUrl,jdbcType=VARCHAR},
            #{subjectCompanyNo,jdbcType=BIGINT},
            #{subjectCompanyName,jdbcType=VARCHAR},
            #{companyDesc,jdbcType=VARCHAR},
            #{contractAmount,jdbcType=BIGINT},
            #{contractPeopleNumber,jdbcType=INTEGER},
            #{paymentMethod,jdbcType=INTEGER},
            #{paymentChannel,jdbcType=INTEGER},
            #{paymentPercent,jdbcType=INTEGER},
            #{contractInformationUrl,jdbcType=VARCHAR},
            #{projectExecutive,jdbcType=VARCHAR},
            #{projectExecutiveDesc,jdbcType=VARCHAR},
            #{customerNo,jdbcType=BIGINT},
            #{customerSimpleName,jdbcType=VARCHAR},
            #{customerOperator,jdbcType=VARCHAR},
            #{salesManPhone},
            #{customerSource},
            <if test="pluginDiscountsType!=null">
                #{pluginDiscountsType},
            </if>
        </trim>
    </insert>

    <!-- 修改实体 -->
    <update id="updateEnterpriseInfo" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity">
        update examination_qx_man_company
        <set>
            <if test="startDate != null">
                start_Date = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                end_Date = #{endDate,jdbcType=DATE},
            </if>
            <if test="serviceStatus != null">
                service_status = #{serviceStatus,jdbcType=TINYINT},
            </if>
            <if test="companyAddress != null and companyAddress != ''">
                company_address = #{companyAddress,jdbcType=VARCHAR},
            </if>
            <if test="companyContacter != null and companyContacter != ''">
                company_contacter = #{companyContacter,jdbcType=VARCHAR},
            </if>
            <if test="companyContacterPhone != null and companyContacterPhone != ''">
                company_contacter_phone = #{companyContacterPhone,jdbcType=VARCHAR},
            </if>
            <if test="employeeLoginAccount != null">
                employee_login_account = #{employeeLoginAccount,jdbcType=INTEGER},
            </if>
            <if test="employeeLoginPwd != null">
                employee_login_pwd = #{employeeLoginPwd,jdbcType=INTEGER},
            </if>
            <if test="salesman != null and salesman != ''">
                sales_man = #{salesman,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DECIMAL},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DECIMAL},
            </if>
            <if test="provinceId != null and provinceId != ''">
                province_id = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null and cityId != ''">
                city_id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="countyId != null and countyId != ''">
                county_id = #{countyId,jdbcType=INTEGER},
            </if>
            <if test="provinceName != null and provinceName != ''">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="countyName != null and countyName != ''">
                county_name = #{countyName,jdbcType=VARCHAR},
            </if>
            <if test="companyName !=null and companyName !=''">
                company_name=#{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyLogoUrl !=null and companyLogoUrl !=''">
                logo_url=#{companyLogoUrl,jdbcType=VARCHAR},
            </if>
            <if test="companyGroupName !=null and companyGroupName !=''">
                company_group_name =#{companyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="subjectCompanyNo !=null">
                subject_company_no =#{subjectCompanyNo,jdbcType=BIGINT},
            </if>
            <if test="subjectCompanyName !=null and subjectCompanyName !=''">
                subject_company_name =#{subjectCompanyName,jdbcType=VARCHAR},
            </if>
            <if test="pluginDiscountsType !=null">
                plugin_discounts_type =#{pluginDiscountsType},
            </if>
            yn = 1
        </set>
        where company_no = #{companyNo,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!--未开始批量更新为服务中（先服后单模式）-->
    <update id="batchUpdateServiceStatusForService" parameterType="java.lang.String">
        update examination_qx_man_company
        <set>
            service_status = 1
        </set>
        where start_date = #{nowDate}
        and service_status = 0
        and service_model = 1
        and yn = 1
    </update>

    <!--通过当前时间与开始时间批量更新服务状态（先单后服模式）-->
    <update id="batchUpdateForServiceStatus" parameterType="java.lang.String">
        update examination_qx_man_company
        <set>
            service_status = 1
        </set>
        where start_date = #{nowDate}
        and service_status = 0
        and service_model = 0
        and rel_order_status = 1
        and yn = 1
    </update>

    <!--更新结束日期等于当天的记录，服务中的状态为已完成-->
    <update id="batchUpdateServiceStatusForFinished" parameterType="java.lang.String">
        update examination_qx_man_company
        <set>
            service_status = 2
        </set>
        where end_date = #{beforeDate}
        and service_status = 1
        and yn = 1
    </update>

    <!-- 删除实体 -->
    <update id="deleteEnterpriseInfoById" parameterType="LONG">
        update examination_qx_man_company
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="queryEnterpriseInfoListByServiceDate" resultMap="enterpriseInfoMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            <if test="startDate != null">
                and start_date >= #{startDate,jdbcType=DATE}
            </if>
            <if test="endDate != null">
                and end_date >= #{endDate,jdbcType=DATE}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 根据公司编号查询公司信息 -->
    <select id="selectEnterpriseInfoByCompanyNo"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            and company_no=#{companyNo}
            and yn = 1
        </where>
    </select>

    <!-- 根据公司名称查询公司信息 -->
    <select id="selectEnterpriseInfoByCompanyName"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            and company_name=#{companyName}
            and yn = 1
        </where>
    </select>

    <select id="selectPwdByAccountNo"
            resultType="java.lang.String">
        select
        account_pwd
        from examination_qx_man_company
        <where>
            account_no =#{accountNo}
            and yn = 1
        </where>
    </select>


    <!-- 查询大客户亲属套餐停启用状态 -->
    <select id="selectRelationStatus"
            resultType="java.lang.Integer">
        select
        relation_status
        from examination_qx_man_company
        <where>
            company_no =#{companyNo,jdbcType=BIGINT}
            and yn = 1
        </where>
    </select>

    <!-- 修改亲属套餐停启用状态 -->
    <update id="updateRelationStatus" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity">
        update examination_qx_man_company
        <set>
            <if test="relationStatus != null">
                relation_status = #{relationStatus,jdbcType=INTEGER}
            </if>
        </set>
        where company_no = #{companyNo,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 更新服务模式为先服后单 -->
    <update id="updateServiceModel">
        update examination_qx_man_company
        <set>
            service_model = 1
        </set>
        where company_no = #{companyNo,jdbcType=BIGINT}
        and service_model = 0
        and yn = 1
    </update>

    <!-- 更新项目经理 -->
    <update id="updateManageInfo">
        update examination_qx_man_company
        <set>
            <if test="managerPin != null and managerPin != ''">
                manager_pin = #{managerPin,jdbcType=VARCHAR}
            </if>
        </set>
        where company_no = #{companyNo,jdbcType=BIGINT}
        and yn = 1
    </update>

    <update id="updateServiceStatus">
        update examination_qx_man_company
        set service_status=#{serviceStatus}
        where company_no = #{companyNo}
        and yn = 1
    </update>

    <update id="updateRelOrderStatus">
        update examination_qx_man_company
        set rel_order_status=#{relOrderStatus}
        where company_no = #{companyNo}
        and yn = 1
    </update>

    <update id="updateEnterpriseUserPin">
        update examination_qx_man_company
        set user_pin=#{userPin}
        where company_no = #{companyNo}
        and yn = 1
    </update>
    <update id="updateCompanyUpgradeStatus">
        update examination_qx_man_company
        set upgrade_switch_status=#{upgradeSwitchStatus}
        where company_no = #{companyNo}
        and yn = 1
    </update>
    <update id="updateEnterprise">
        update examination_qx_man_company
        <set>
            <if test="startDate != null">
                start_Date = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                end_Date = #{endDate,jdbcType=DATE},
            </if>
            <if test="serviceStatus != null">
                service_status = #{serviceStatus,jdbcType=TINYINT},
            </if>
            <if test="companyAddress != null and companyAddress != ''">
                company_address = #{companyAddress,jdbcType=VARCHAR},
            </if>
            <if test="companyContacter != null and companyContacter != ''">
                company_contacter = #{companyContacter,jdbcType=VARCHAR},
            </if>
            <if test="companyContacterPhone != null and companyContacterPhone != ''">
                company_contacter_phone = #{companyContacterPhone,jdbcType=VARCHAR},
            </if>
            <if test="employeeLoginAccount != null">
                employee_login_account = #{employeeLoginAccount,jdbcType=INTEGER},
            </if>
            <if test="employeeLoginPwd != null">
                employee_login_pwd = #{employeeLoginPwd,jdbcType=INTEGER},
            </if>
            <if test="salesman != null and salesman != ''">
                sales_man = #{salesman,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DECIMAL},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DECIMAL},
            </if>
            <if test="provinceId != null and provinceId != ''">
                province_id = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null and cityId != ''">
                city_id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="countyId != null and countyId != ''">
                county_id = #{countyId,jdbcType=INTEGER},
            </if>
            <if test="provinceName != null and provinceName != ''">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="countyName != null and countyName != ''">
                county_name = #{countyName,jdbcType=VARCHAR},
            </if>
            <if test="companyName !=null and companyName !=''">
                company_name=#{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyLogoUrl !=null and companyLogoUrl !=''">
                logo_url=#{companyLogoUrl,jdbcType=VARCHAR},
            </if>
            <if test="companyGroupName !=null and companyGroupName !=''">
                company_group_name =#{companyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="subjectCompanyNo !=null">
                subject_company_no =#{subjectCompanyNo,jdbcType=BIGINT},
            </if>
            <if test="subjectCompanyName !=null and subjectCompanyName !=''">
                subject_company_name =#{subjectCompanyName,jdbcType=VARCHAR},
            </if>
            <if test="companyDesc !=null and companyDesc !=''">
                company_desc =#{companyDesc},
            </if>
            <if test="contractAmount !=null">
                contract_amount =#{contractAmount},
            </if>
            <if test="contractPeopleNumber !=null and contractPeopleNumber !=''">
                contract_people_number =#{contractPeopleNumber,jdbcType=VARCHAR},
            </if>
            <if test="paymentMethod !=null">
                payment_method =#{paymentMethod},
            </if>
            <if test="paymentChannel !=null">
                payment_channel =#{paymentChannel},
            </if>
            <if test="paymentPercent !=null">
                payment_percent =#{paymentPercent},
            </if>
            <if test="contractInformationUrl !=null and contractInformationUrl != ''">
                contract_information_url =#{contractInformationUrl},
            </if>
            <if test="projectExecutive !=null  and projectExecutive != ''">
                project_executive =#{projectExecutive},
            </if>
            <if test="projectExecutiveDesc !=null  and projectExecutiveDesc != ''">
                project_executive_desc =#{projectExecutiveDesc},
            </if>
            <if test="customerNo !=null">
                customer_no =#{customerNo},
            </if>
            <if test="customerSimpleName !=null and customerSimpleName!=''">
                customer_simple_name =#{customerSimpleName},
            </if>
            <if test="salesManPhone !=null and salesManPhone!=''">
                sales_man_phone =#{salesManPhone},
            </if>
            <if test="pluginDiscountsType !=null">
                plugin_discounts_type =#{pluginDiscountsType},
            </if>
        </set>
        where company_no = #{companyNo,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="selectEnterpriseInfoEntityByManagerPin"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            manager_pin =#{managerPin}
            and yn = 1
        </where>
    </select>
    <select id="queryCompanyNoAll" resultType="java.lang.Long">
        select distinct company_no from examination_qx_man_company where yn = 1
    </select>
    <select id="queryEnterpriseList"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            <if test="customerNo != null">
                and customer_no = #{customerNo,jdbcType=BIGINT}
            </if>
            <if test="customerSimpleName != null and customerSimpleName != '' ">
                and customer_simple_name like CONCAT(#{customerSimpleName,jdbcType=VARCHAR},"%")
            </if>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="companyName != null">
                and company_name like CONCAT(#{companyName,jdbcType=VARCHAR},"%")
            </if>
            <if test="serviceStatus != null">
                and service_status = #{serviceStatus,jdbcType=TINYINT}
            </if>
            <if test="serviceModel != null">
                and service_model = #{serviceModel,jdbcType=INTEGER}
            </if>
            <if test="managerPin != null">
                and manager_pin = #{managerPin,jdbcType=VARCHAR}
            </if>
            <if test="customerOperator !=null  and customerOperator !=''">
               and customer_operator=#{customerOperator,jdbcType=VARCHAR}
            </if>
            <if test="salesman != null and salesman != ''">
               and sales_man = #{salesman,jdbcType=VARCHAR}
            </if>
            <if test="projectExecutive !=null and projectExecutive !=''">
               and project_executive like CONCAT("%",#{projectExecutive},"%")
            </if>
            <if test="pluginDiscountsType !=null">
                and plugin_discounts_type =#{pluginDiscountsType}
            </if>
            and yn = 1
        </where>
    </select>
    <select id="queryEnterpriseExist" resultType="java.lang.Integer">
        select count(1) from examination_qx_man_company
        where customer_no = #{customerNo}
        and yn = 1
    </select>
    <select id="queryPassEnterprise"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            <if test="customerNo != null">
                and customer_no = #{customerNo,jdbcType=BIGINT}
            </if>
            <if test="customerSimpleName != null and customerSimpleName != '' ">
                and customer_simple_name like CONCAT(#{customerSimpleName,jdbcType=VARCHAR},"%")
            </if>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="companyName != null">
                and company_name like CONCAT(#{companyName,jdbcType=VARCHAR},"%")
            </if>
            <if test="serviceStatus != null">
                and service_status = #{serviceStatus,jdbcType=TINYINT}
            </if>
            <if test="serviceStatus == null">
                and service_status in (0,1,2,3)
            </if>
            <if test="serviceModel != null">
                and service_model = #{serviceModel,jdbcType=INTEGER}
            </if>
            <if test="managerPin != null">
                and manager_pin = #{managerPin,jdbcType=VARCHAR}
            </if>
            <if test="customerOperator !=null  and customerOperator !=''">
                and customer_operator=#{customerOperator,jdbcType=VARCHAR}
            </if>
            <if test="projectExecutive !=null and projectExecutive !=''">
                and project_executive like CONCAT("%",#{projectExecutive},"%")
            </if>
            <if test="pluginDiscountsType !=null">
                and plugin_discounts_type =#{pluginDiscountsType}
            </if>
            <if test="companyNos != null and companyNos.size() != 0">
                and company_no in
                <foreach collection="companyNos" item="companyNo" open="(" close=")" separator=",">
                    #{companyNo}
                </foreach>
            </if>
            and yn = 1
        </where>
    </select>
    <select id="queryOperatorEnterprise"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            <if test="customerNo != null">
                and customer_no = #{customerNo,jdbcType=BIGINT}
            </if>
            <if test="customerSimpleName != null and customerSimpleName != '' ">
                and customer_simple_name like CONCAT(#{customerSimpleName,jdbcType=VARCHAR},"%")
            </if>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="companyName != null">
                and company_name like CONCAT(#{companyName,jdbcType=VARCHAR},"%")
            </if>
            <if test="serviceStatus != null">
                and service_status = #{serviceStatus,jdbcType=TINYINT}
            </if>
            <if test="serviceStatus == null">
                and service_status in (0,1,2,3,5)
            </if>
            <if test="serviceModel != null">
                and service_model = #{serviceModel,jdbcType=INTEGER}
            </if>
            <if test="managerPin != null">
                and manager_pin = #{managerPin,jdbcType=VARCHAR}
            </if>
            <if test="customerOperator !=null  and customerOperator !=''">
                and customer_operator=#{customerOperator,jdbcType=VARCHAR}
            </if>
            <if test="salesman != null and salesman != ''">
                and sales_man = #{salesman,jdbcType=VARCHAR}
            </if>
            <if test="projectExecutive !=null and projectExecutive !=''">
                and project_executive like CONCAT("%",#{projectExecutive},"%")
            </if>
            <if test="pluginDiscountsType !=null">
                and plugin_discounts_type =#{pluginDiscountsType}
            </if>
            and yn = 1
        </where>
    </select>

    <select id="queryCompanyByCompanyNo"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        where company_no in
        <foreach collection="companyNos" item="companyNo" open="(" close=")" separator=",">
            #{companyNo}
        </foreach>
        and service_status in (0,1,2,3)
        and yn = 1 order by create_time desc
    </select>

    <select id="queryCompanyByCompanyNoWithoutStatus"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        where company_no in
        <foreach collection="companyNos" item="companyNo" open="(" close=")" separator=",">
            #{companyNo}
        </foreach>
<!--        加项包需求-只要企销体检中使用到了加项包，就不允许删除，不管什么状态都不允许删除-->
<!--        and service_status in (1,2)-->
        and yn = 1 order by create_time desc
    </select>

    <select id="queryPassPageEnterpriseByAuth"
            resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        <where>
            <if test="customerNo != null">
                and customer_no = #{customerNo,jdbcType=BIGINT}
            </if>
            <if test="customerSimpleName != null and customerSimpleName != '' ">
                and customer_simple_name like CONCAT(#{customerSimpleName,jdbcType=VARCHAR},"%")
            </if>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="companyName != null">
                and company_name like CONCAT(#{companyName,jdbcType=VARCHAR},"%")
            </if>
            <if test="serviceStatus != null">
                and service_status = #{serviceStatus,jdbcType=TINYINT}
            </if>
            <if test="serviceStatus == null">
                and service_status in (0,1,2,3)
            </if>
            <if test="serviceModel != null">
                and service_model = #{serviceModel,jdbcType=INTEGER}
            </if>
            <if test="managerPin != null">
                and manager_pin = #{managerPin,jdbcType=VARCHAR}
            </if>
            and (customer_operator=#{customerOperator,jdbcType=VARCHAR} or project_executive like CONCAT("%",#{projectExecutive},"%"))
            and yn = 1
        </where>
    </select>

    <update id="updateServiceStatusByCompanyNo">
        update  examination_qx_man_company
        set service_status=#{serviceStatus}
        where company_no=#{companyNo}
        and yn = 1
    </update>
    <update id="updateEnterpriseService">
        update  examination_qx_man_company
        <set>
            <if test="startDate != null">
                start_Date = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                end_Date = #{endDate,jdbcType=DATE},
            </if>
            <if test="serviceStatus != null">
                service_status = #{serviceStatus,jdbcType=TINYINT},
            </if>
            <if test="employeeLoginAccount != null">
                employee_login_account = #{employeeLoginAccount,jdbcType=INTEGER},
            </if>
            <if test="employeeLoginPwd != null">
                employee_login_pwd = #{employeeLoginPwd,jdbcType=INTEGER},
            </if>
            <if test="companyGroupName != null and companyGroupName != ''">
                company_group_name = #{companyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="pluginDiscountsType !=null">
                plugin_discounts_type =#{pluginDiscountsType},
            </if>
        </set>
        where company_no=#{companyNo}
        and yn = 1
    </update>
    <update id="updateEnterpriseCustomer">
        update  examination_qx_man_company
        <set>
            <if test="customerNo != null">
                customer_no = #{customerNo},
            </if>
            <if test="customerSimpleName != null">
                customer_simple_name = #{customerSimpleName},
            </if>
            <if test="customerOperator != null">
                customer_operator = #{customerOperator},
            </if>
            <if test="projectExecutive != null">
                project_executive = #{projectExecutive},
            </if>
            <if test="contractAmount !=null">
                contract_amount =#{contractAmount},
            </if>
            <if test="contractPeopleNumber !=null and contractPeopleNumber !=''">
                contract_people_number =#{contractPeopleNumber,jdbcType=VARCHAR},
            </if>
            <if test="paymentMethod !=null">
                payment_method =#{paymentMethod},
            </if>
            <if test="paymentChannel !=null">
                payment_channel =#{paymentChannel},
            </if>
            <if test="paymentPercent !=null">
                payment_percent =#{paymentPercent},
            </if>
            <if test="pluginDiscountsType !=null">
                plugin_discounts_type =#{pluginDiscountsType},
            </if>
        </set>
        where company_no=#{companyNo}
        and yn = 1
    </update>
    <update id="updateOperatorAndSalesman">
        update examination_qx_man_company set customer_operator = #{customerOperator},
        sales_man = #{customerSalesMan}
        where customer_no = #{customerNo}
    </update>

    <select id="queryCustomerNo" resultType="java.lang.Long">
        select customer_no
        from examination_qx_man_company
        where 
                customer_no in 
            <foreach collection="customerNos" item="customerNo" separator="," open="(" close=")">
                #{customerNo}
            </foreach>
            and yn = 1
    </select>

    <select id="selectAllEnterpriseInfo" resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
        where yn = 1
    </select>

    <select id="queryByCompanyNoAndServiceStatus" resultMap="enterpriseInfoMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company
                where
                company_no in
        <foreach collection="companyNos" item="companyNo" separator="," open="(" close=")">
            #{companyNo}
        </foreach>
        and
                service_status in
        <foreach collection="serviceStatus" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
        and yn = 1
    </select>
    <select id="existProjectExecutive" resultType="java.lang.Integer">
        select count(id)
        from examination_qx_man_company
        where
          yn = 1
          and project_executive like CONCAT('%',#{userPin},'%')
        limit 1
    </select>
</mapper>
