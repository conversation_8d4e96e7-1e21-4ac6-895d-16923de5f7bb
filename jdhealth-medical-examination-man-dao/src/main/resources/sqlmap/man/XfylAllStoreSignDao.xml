<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.XfylAllStoreSignDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.XfylAllStoreSignEntity">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="store_id" property="storeId" jdbcType="VARCHAR" />
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        <result column="store_status" property="storeStatus" jdbcType="INTEGER" />
        <result column="shop_id" property="shopId" jdbcType="VARCHAR" />
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
        <result column="vender_id" property="venderId" jdbcType="VARCHAR" />
        <result column="vender_name" property="venderName" jdbcType="VARCHAR" />
        <result column="vender_status" property="venderStatus" jdbcType="INTEGER" />
        <result column="channel_id" property="channelId" jdbcType="VARCHAR" />
        <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
        <result column="operation_type" property="operationType" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="yn" property="yn" jdbcType="INTEGER" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, store_id, store_name, store_status, shop_id, shop_name, vender_id, vender_name,
        vender_status,channel_id, channel_name, operation_type, remark, yn,
        create_user,create_time,update_user,update_time
    </sql>

    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.XfylAllStoreSignEntity" >
        insert into xfyl_all_store_sign (
        store_id, store_name, store_status, shop_id, shop_name,
        vender_id, vender_name,vender_status, channel_id,
        channel_name, operation_type, remark, yn,
        create_user,create_time,update_user,update_time
        ) values (
        #{storeId}, #{storeName},#{storeStatus}, #{shopId}, #{shopName},
        #{venderId}, #{venderName}, #{venderStatus}, #{channelId},
        #{channelName},#{operationType}, #{remark}, 1,
        #{createUser},now(),#{updateUser},now()
        )
    </insert>

    <update id="updateByStoreId"
            parameterType="com.jd.health.medical.examination.domain.XfylAllStoreSignEntity">
        UPDATE xfyl_all_store_sign
        <set>
            <if test="operationType != null" >
                operation_type = #{operationType},
            </if>
            <if test="remark != null and remark != ''" >
                remark = concat(ifnull(remark,''),#{remark}),
            </if>
            <if test="updateUser != null and updateUser != ''" >
                update_user = #{updateUser},
            </if>
            update_time = now()
        </set>
        WHERE store_id = #{storeId}
    </update>

    <select id="selectByStoreId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List" />
        FROM xfyl_all_store_sign
        WHERE store_id = #{storeId}
    </select>

    <delete id="deleteStoreSignByStoreId" parameterType="java.lang.String">
        DELETE
        FROM xfyl_all_store_sign
        <where>
            yn=1
        <if test="storeId != null and storeId != ''">
            AND store_id = #{storeId}
        </if>
        </where>
    </delete>

</mapper>