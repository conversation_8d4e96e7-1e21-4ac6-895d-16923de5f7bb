<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.ProviderDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="channel_no" property="channelNo" jdbcType="BIGINT" />
    <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
    <result column="provider_jd_no" property="providerJdNo" jdbcType="VARCHAR" />
    <result column="provider_jd_name" property="providerJdName" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="docking_type" property="dockingType" jdbcType="INTEGER" />
    <result column="business_license" property="businessLicense" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
    <result column="company_id" property="companyId" jdbcType="BIGINT" />
    <result column="business_type" property="businessType"/>
    <result column="vendor_type" property="vendorType"/>
    <result column="settlement_way" property="settlementWay"/>
  </resultMap>
  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
      id, channel_no, channel_name, provider_jd_no, provider_jd_name, create_user, create_time,
      update_user, update_time, status, docking_type, business_license, address, contact_name, contact_phone,
      company_id,business_type,vendor_type,settlement_way
    </trim>
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from examination_man_provider
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity" >
    insert into examination_man_provider (channel_no, channel_name,
      provider_jd_no, provider_jd_name,
      create_user, create_time, docking_type, business_license, address, contact_name, contact_phone,company_id,vendor_type,settlement_way)
    values (#{channelNo,jdbcType=BIGINT}, #{channelName,jdbcType=VARCHAR},
      #{providerJdNo,jdbcType=VARCHAR}, #{providerJdName,jdbcType=VARCHAR},
      #{createUser,jdbcType=VARCHAR}, now(), #{dockingType,jdbcType=INTEGER}, #{businessLicense,jdbcType=VARCHAR},
      #{address,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}, #{vendorType}, #{settlementWay})
  </insert>
  <update id="updateByChannelType" parameterType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity" >
    update examination_man_provider
    <set>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="providerJdNo != null">
        provider_jd_no = #{providerJdNo,jdbcType=VARCHAR},
      </if>
      <if test="providerJdName != null">
        provider_jd_name = #{providerJdName,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vendorType != null">
        vendor_type = #{vendorType},
      </if>
      <if test="settlementWay != null">
        settlement_way = #{settlementWay},
      </if>
      update_time = now(),
    </set>
    where channel_no = #{channelNo,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List"/>
    from examination_man_provider
    where id = #{id,jdbcType=BIGINT} and yn = 1
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List"/>
    from examination_man_provider where yn = 1 order by id desc
  </select>
  <select id="selectAllByStatus" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List"/>aiqi
    from examination_man_provider where yn = 1
    and status = #{status, jdbcType=INTEGER}
    order by id desc
  </select>
    <select id="selectProviderByChannelNo" resultType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_provider
      where channel_no=#{channelNo}
      and yn = 1
      limit 1
    </select>

  <select id="selectProviderByGoodsId" resultType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity">
    select e.channel_no, e.channel_name, e.provider_jd_no, e.provider_jd_name
    from examination_man_provider e join examination_man_third_goods s
    on e.channel_no=s.channel_no WHERE s.goods_id = #{goodsId} and e.yn = 1
  </select>

  <select id="selectProviderByPage" parameterType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"></include>
    from  examination_man_provider
    where
      yn = 1
      <if test="dockingType != null">
        and docking_type = #{dockingType,jdbcType=INTEGER}
      </if>
      <if test="providerJdNo != null and providerJdNo != ''">
        and provider_jd_no like CONCAT('%', #{providerJdNo}, '%')
      </if>
      <if test="channelNo != null">
        and channel_no = #{channelNo}
      </if>
      <if test="status != null and status != ''">
        and status = #{status}
      </if>
      <if test="channelName != null and channelName !=''">
        and channel_name like CONCAT('%', #{channelName}, '%')
      </if>
      <if test="businessType != null">
        and business_type = #{businessType}
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType,jdbcType=INTEGER}
      </if>
    order by create_time desc
  </select>

  <select id="selectProviderByChannelName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_man_provider
    where  channel_name = #{channelName}
    and yn = 1
    limit 1
  </select>
  <select id="selectAllProviderByDockingType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_provider
    where yn = 1
    <if test="dockingType != null">
      and docking_type = #{dockingType}
    </if>
  </select>
  <select id="getProviderByChannelNoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_provider
    where yn = 1
    and channel_no in
    <foreach collection="channelNoList" item="channelNo" open="(" close=")" separator=",">
      #{channelNo}
    </foreach>
  </select>

  <select id="selectProviderByJdName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_man_provider
    where  provider_jd_name = #{providerJdName}
    and yn = 1
    limit 1
  </select>

  <select id="selectProviderByBusinessLicense" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_man_provider
    where  business_license = #{businessLicense}
    and yn = 1
    limit 1
  </select>

  <select id="selectProviderJdNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_man_provider
    where  provider_jd_no = #{providerJdNo}
    and yn = 1
    limit 1
  </select>

  <insert id="insertAddProvider" parameterType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity" >
    insert into examination_man_provider (channel_no, channel_name,
                                          provider_jd_no, provider_jd_name,
                                          create_user, create_time, docking_type, business_license, address, contact_name, contact_phone,company_id,status,business_type,vendor_type,settlement_way)
    values (#{channelNo,jdbcType=BIGINT}, #{channelName,jdbcType=VARCHAR},
            #{providerJdNo,jdbcType=VARCHAR}, #{providerJdName,jdbcType=VARCHAR},
            #{createUser,jdbcType=VARCHAR}, now(), #{dockingType,jdbcType=INTEGER}, #{businessLicense,jdbcType=VARCHAR},
            #{address,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}, #{status, jdbcType=INTEGER},#{businessType},#{vendorType}, #{settlementWay})
  </insert>

  <select id="queryDockingTypeByChannelNo" resultType="java.lang.Integer">
    select docking_type
    from examination_man_provider
    <where>
      <if test="channelNo != null">
        and channel_no = #{channelNo}
      </if>
      and yn = 1
    </where>
    limit 1
  </select>

  <select id="getAllProviderByBusinessType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_provider
    where yn = 1
    <if test="businessType != null">
      and business_type = #{businessType}
    </if>
  </select>

  <select id="queryPageLikeName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_man_provider
    <where>
      <if test="providerJdName != null and providerJdName != ''">
        and provider_jd_name like CONCAT("%",#{providerJdName,jdbcType=VARCHAR},"%")
      </if>
      <if test="channelName != null and channelName != ''">
        and channel_name like CONCAT("%",#{channelName,jdbcType=VARCHAR},"%")
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="getProviderDetail"
          resultType="com.jd.health.medical.examination.domain.personal.entity.ProviderEntity">
    select
    <include refid="Base_Column_List"></include>
    from examination_man_provider
    <where>
      <if test="channelNo != null">
        and channel_no = #{channelNo}
      </if>
      <if test="providerJdNo != null">
        and provider_jd_no = #{providerJdNo}
      </if>
      <if test="vendorType != null">
        and vendor_type = #{vendorType}
      </if>
      and yn = 1
    </where>
  </select>
</mapper>