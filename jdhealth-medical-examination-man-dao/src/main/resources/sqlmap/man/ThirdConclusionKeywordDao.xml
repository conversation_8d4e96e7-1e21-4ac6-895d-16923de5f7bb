<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ThirdConclusionKeywordDao">


    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.report.entity.basic.ThirdConclusionKeyWordEntity">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="key_word_name" jdbcType="VARCHAR" property="thirdConclusionName"/>
        <result column="conclusion_no" jdbcType="BIGINT" property="conclusionNo"/>
        <result column="channel_no" jdbcType="BIGINT" property="channelNo"/>
        <result column="relevance_status" jdbcType="INTEGER" property="relevanceStatus"/>
        <result column="operation_user" jdbcType="VARCHAR" property="operationUser"/>
        <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="brand_id" jdbcType="BIGINT" property="brandId"/>
        <result column="third_conclusion_no" jdbcType="BIGINT" property="thirdConclusionNo"/>
        <result column="conclusion_description" jdbcType="VARCHAR" property="conclusionDescription"/>
        <result column="conclusion_suggest" jdbcType="VARCHAR" property="conclusionSuggest"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, conclusion_no, channel_no, key_word_name,
        relevance_status,operation_user, operation_time, brand_id, third_conclusion_no,
        conclusion_description, conclusion_suggest, source,
        yn, create_time, update_time
    </sql>

    <!-- 插入实体 -->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_conclusion_keyword
        (channel_no, key_word_name,
        relevance_status, brand_id,
        third_conclusion_no, conclusion_description,
        conclusion_suggest, source,
        create_user, update_time, yn)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.channelNo,jdbcType=BIGINT}, #{item.thirdConclusionName,jdbcType=VARCHAR},
                #{item.relevanceStatus,jdbcType=INTEGER}, #{item.brandId,jdbcType=BIGINT},
                #{item.thirdConclusionNo,jdbcType=BIGINT}, #{item.conclusionDescription,jdbcType=VARCHAR},
                #{item.conclusionSuggest,jdbcType=VARCHAR}, #{item.source,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                now(), 1
            </trim>
        </foreach>
    </insert>


    <update id="updateDynamic"
            parameterType="com.jd.health.medical.examination.domain.report.entity.basic.ThirdConclusionKeyWordEntity">
        update examination_man_conclusion_keyword
        <set>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="conclusionNo != null">
                conclusion_no = #{conclusionNo,jdbcType=BIGINT},
            </if>
            <if test="operationUser != null">
                operation_user = #{operationUser,jdbcType=VARCHAR},
            </if>
            <if test="operationTime != null">
                operation_time = #{operationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="conclusionDescription != null and conclusionDescription !=''">
                conclusion_description = #{conclusionDescription,jdbcType=VARCHAR},
            </if>
            <if test="conclusionSuggest != null and conclusionSuggest !=''">
                conclusion_suggest = #{conclusionSuggest,jdbcType=VARCHAR},
            </if>
            <if test="relevanceStatus != null">
                relevance_status = #{relevanceStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where third_conclusion_no = #{thirdConclusionNo,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="selectPageByConditions" parameterType="com.jd.health.medical.examination.export.param.report.QueryThirdConclusionPageParam"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_conclusion_keyword
        <where>
            <if test="brandId != null">
                and brand_id=#{brandId}
            </if>
            <if test="relevanceStatus != null">
                and relevance_status=#{relevanceStatus}
            </if>
            <if test="conclusionName != null and conclusionName !=''">
                and key_word_name like CONCAT('%',#{conclusionName,jdbcType=VARCHAR}, '%')
            </if>

            <if test="operationTimeStart != null and operationTimeEnd != null">
                and operation_time between #{operationTimeStart} and #{operationTimeEnd}
            </if>
            <if test="source != null">
                and source=#{source}
            </if>
            and yn = 1
        </where>
    </select>


    <select id="selectConclusionsByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_conclusion_keyword
        where brand_id=#{brandId}
        and key_word_name in
        <foreach collection="list" item="item"  open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="relevanceStatus != null">
            and relevance_status = #{relevanceStatus}
        </if>
        and yn=1

    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_conclusion_keyword
        where brand_id=#{brandId}
        and key_word_name = #{conclusionName}
        and yn=1

    </select>

    <select id="getKeywordByByConclusionNo" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_conclusion_keyword
        where `conclusion_no` = #{conclusionNo,jdbcType=BIGINT}
        and yn=1
    </select>

    <update id="initRelevanceStatus">
        update examination_man_conclusion_keyword
        set relevance_status = 1,
        operation_time = create_time
        where yn = 1
          and id between #{startId} and #{endId}
    </update>

    <update id="refreshOperationUser">
        update examination_man_conclusion_keyword
        set operation_user = #{conclusion.operationUser},
            operation_time = #{conclusion.operationTime},
            relevance_status = 1
        where yn = 1
        and key_word_name = #{conclusion.thirdConclusionName}
        and channel_no = #{conclusion.channelNo}
        and id between #{startId} and #{endId}

    </update>

    <update id="refreshBrandId">
        update examination_man_conclusion_keyword
        set brand_id = #{brandId}
        where channel_no = #{channelNo}
        and yn = 1
        and id between #{startId} and #{endId}
        and brand_id is null
    </update>

    <update id="refreshThirdConclusionNo">
        update examination_man_conclusion_keyword
        set third_conclusion_no = #{thirdConclusionNo}
        where id = #{id} and yn = 1 and third_conclusion_no = 0
    </update>

    <select id="selectRepetitionListByChannelNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_conclusion_keyword where key_word_name in(
                select  key_word_name from examination_man_conclusion_keyword
                where channel_no = #{channelNo,jdbcType=BIGINT} and yn = 1
                group by channel_no, key_word_name
                having count(*) != 1
                )
                and channel_no = #{channelNo, jdbcType=BIGINT}
                and yn = 1
    </select>

    <update id="removeBatch" parameterType="java.util.List">
        <if test="ids != null and ids.size() > 0">
            update examination_man_conclusion_keyword set yn = 0
            where id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>
    </update>

    <select id="selectRepetitionListByBrandId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_conclusion_keyword
            where key_word_name in (
            select key_word_name from examination_man_conclusion_keyword
            where brand_id = #{brandId} and yn = 1
            group by key_word_name
            having count(*) > 1
            )
            and brand_id = #{brandId}
            and yn = 1
            order by key_word_name, relevance_status desc
    </select>
    <!-- ===========================分割线========================== -->









    <update id="updateInvalidByConclusion">
          update examination_man_conclusion_keyword
        set yn=0
        where conclusion_no = #{conclusionNo,jdbcType=BIGINT}
             and yn=1
    </update>

   

</mapper>