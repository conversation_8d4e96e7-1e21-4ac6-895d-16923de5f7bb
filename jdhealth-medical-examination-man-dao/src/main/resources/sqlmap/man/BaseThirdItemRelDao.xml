<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.BaseThirdItemRelDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.BaseThirdItemRel">
    <!--@mbg.generated-->
    <!--@Table examination_man_base_third_item_rel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="base_item_no" jdbcType="BIGINT" property="baseItemNo" />
    <result column="base_item_name" jdbcType="VARCHAR" property="baseItemName" />
    <result column="third_item_no" jdbcType="BIGINT" property="thirdItemNo" />
    <result column="third_item_name" jdbcType="VARCHAR" property="thirdItemName" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, base_item_no, base_item_name, third_item_no, third_item_name, `source`, remark, 
    yn, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByParam" parameterType="com.jd.health.medical.examination.domain.BaseThirdItemRel" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_man_base_third_item_rel
    <where>
      <if test="baseItemNo != null">
        and base_item_no = #{baseItemNo,jdbcType=BIGINT}
      </if>
      <if test="baseItemName != null">
        and base_item_name = #{baseItemName,jdbcType=VARCHAR}
      </if>
      <if test="thirdItemNo != null">
        and third_item_no = #{thirdItemNo,jdbcType=BIGINT}
      </if>
      <if test="thirdItemName != null">
        and third_item_name = #{thirdItemName,jdbcType=VARCHAR}
      </if>
      <if test="source != null">
        and `source` = #{source,jdbcType=INTEGER}
      </if>
      and yn = 1
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from examination_man_base_third_item_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="deleteByBaseItemNo">
    update examination_man_base_third_item_rel
    set yn = 0
    where base_item_no = #{baseItemNo,jdbcType=BIGINT}
  </update>
  <update id="deleteByThirdItemNo">
    update examination_man_base_third_item_rel
    set yn = 0
    where third_item_no = #{thirdItemNo,jdbcType=BIGINT}
  </update>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.BaseThirdItemRel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_man_base_third_item_rel (base_item_no, base_item_name, third_item_no, 
      third_item_name, `source`, remark, 
      yn, create_user, create_time, 
      update_user, update_time)
    values (#{baseItemNo,jdbcType=BIGINT}, #{baseItemName,jdbcType=VARCHAR}, #{thirdItemNo,jdbcType=BIGINT}, 
      #{thirdItemName,jdbcType=VARCHAR}, #{source,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertBatch" parameterType="com.jd.health.medical.examination.domain.BaseThirdItemRel">
    insert into examination_man_base_third_item_rel (base_item_no, base_item_name, third_item_no,
    third_item_name, `source`, remark,
    yn, create_user, create_time,
    update_user, update_time)
    values
    <foreach collection="entities" item="entity" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{entity.baseItemNo,jdbcType=BIGINT}, #{entity.baseItemName,jdbcType=VARCHAR}, #{entity.thirdItemNo,jdbcType=BIGINT},
        #{entity.thirdItemName,jdbcType=VARCHAR}, #{entity.source,jdbcType=INTEGER}, #{entity.remark,jdbcType=VARCHAR},
        1, #{entity.createUser,jdbcType=VARCHAR}, now(),
        #{entity.updateUser,jdbcType=VARCHAR}, now()
      </trim>
    </foreach>
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.BaseThirdItemRel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_man_base_third_item_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="baseItemNo != null">
        base_item_no,
      </if>
      <if test="baseItemName != null">
        base_item_name,
      </if>
      <if test="thirdItemNo != null">
        third_item_no,
      </if>
      <if test="thirdItemName != null">
        third_item_name,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="baseItemNo != null">
        #{baseItemNo,jdbcType=BIGINT},
      </if>
      <if test="baseItemName != null">
        #{baseItemName,jdbcType=VARCHAR},
      </if>
      <if test="thirdItemNo != null">
        #{thirdItemNo,jdbcType=BIGINT},
      </if>
      <if test="thirdItemName != null">
        #{thirdItemName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.domain.BaseThirdItemRel">
    <!--@mbg.generated-->
    update examination_man_base_third_item_rel
    <set>
      <if test="baseItemNo != null">
        base_item_no = #{baseItemNo,jdbcType=BIGINT},
      </if>
      <if test="baseItemName != null">
        base_item_name = #{baseItemName,jdbcType=VARCHAR},
      </if>
      <if test="thirdItemNo != null">
        third_item_no = #{thirdItemNo,jdbcType=BIGINT},
      </if>
      <if test="thirdItemName != null">
        third_item_name = #{thirdItemName,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.BaseThirdItemRel">
    <!--@mbg.generated-->
    update examination_man_base_third_item_rel
    set base_item_no = #{baseItemNo,jdbcType=BIGINT},
      base_item_name = #{baseItemName,jdbcType=VARCHAR},
      third_item_no = #{thirdItemNo,jdbcType=BIGINT},
      third_item_name = #{thirdItemName,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>