<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManConclusionDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.conclusion.ExaminationManConclusion">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="conclusion_no" jdbcType="BIGINT" property="conclusionNo"/>
        <result column="conclusion_name" jdbcType="VARCHAR" property="conclusionName"/>
        <result column="group_first_level" jdbcType="BIGINT" property="examinationGroupI"/>
        <result column="group_second_level" jdbcType="BIGINT" property="examinationGroupIi"/>
        <result column="first_department_no" jdbcType="BIGINT" property="firstDepartmentNo" />
        <result column="second_department_no" jdbcType="BIGINT" property="secondDepartmentNo" />
        <result column="check_mean" jdbcType="VARCHAR" property="checkMean"/>
        <result column="check_suggest" jdbcType="VARCHAR" property="checkSuggest"/>
        <result column="operation_user" jdbcType="VARCHAR" property="operationUser"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    `id`, `conclusion_no`, `conclusion_name`, `group_first_level`, `group_second_level`,`first_department_no`,
    `second_department_no`,`check_mean`, `check_suggest`, `operation_user`, `yn`, `create_time`, `update_time`
  </sql>


    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationManConclusion">

    insert into examination_man_conclusion (`id`, `conclusion_no`, `conclusion_name`, 
     `group_first_level`, `group_second_level`, `first_department_no`, `second_department_no`,
      `check_mean`, `check_suggest`, `operation_user`, 
      `yn`, `create_time`, `update_time`
      )
    values (#{id,jdbcType=BIGINT}, #{conclusionNo,jdbcType=BIGINT}, #{conclusionName,jdbcType=VARCHAR}, 
     #{examinationGroupI,jdbcType=BIGINT}, #{examinationGroupIi,jdbcType=BIGINT},#{firstDepartmentNo,jdbcType=BIGINT},#{secondDepartmentNo,jdbcType=BIGINT},
      #{checkMean,jdbcType=VARCHAR}, #{checkSuggest,jdbcType=VARCHAR}, #{operationUser,jdbcType=VARCHAR}, 
      #{yn,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

    <select id="selectByConclusionNo" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from examination_man_conclusion
        where conclusion_no=#{conclusionNo,jdbcType=BIGINT}
        and yn=1
    </select>
    <select id="selectByConclusionNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from examination_man_conclusion
        where yn=1
        and conclusion_no in
        <foreach collection="conclusionNos" item="conclusionNo" open="(" close=")" separator=",">
            #{conclusionNo,jdbcType=BIGINT}
        </foreach>
    </select>
    <insert id="insertSelective"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationManConclusion">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>
            <if test="conclusionNo != null">
                `conclusion_no`,
            </if>
            <if test="conclusionName != null">
                `conclusion_name`,
            </if>
            <if test="examinationGroupI != null">
                `group_first_level`,
            </if>
            <if test="examinationGroupIi != null">
                `group_second_level`,
            </if>
            <if test="firstDepartmentNo != null">
                `first_department_no`,
            </if>
            <if test="secondDepartmentNo != null">
                `second_department_no`,
            </if>
            <if test="checkMean != null">
                `check_mean`,
            </if>
            <if test="checkSuggest != null">
                `check_suggest`,
            </if>
            <if test="operationUser != null">
                `operation_user`,
            </if>
            <if test="yn != null">
                `yn`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="conclusionNo != null">
                #{conclusionNo,jdbcType=BIGINT},
            </if>
            <if test="conclusionName != null">
                #{conclusionName,jdbcType=VARCHAR},
            </if>
            <if test="examinationGroupI != null">
                #{examinationGroupI,jdbcType=BIGINT},
            </if>
            <if test="examinationGroupIi != null">
                #{examinationGroupIi,jdbcType=BIGINT},
            </if>
            <if test="firstDepartmentNo != null">
                #{firstDepartmentNo,jdbcType=BIGINT},
            </if>
            <if test="secondDepartmentNo != null">
                #{secondDepartmentNo,jdbcType=BIGINT},
            </if>
            <if test="checkMean != null">
                #{checkMean,jdbcType=VARCHAR},
            </if>
            <if test="checkSuggest != null">
                #{checkSuggest,jdbcType=VARCHAR},
            </if>
            <if test="operationUser != null">
                #{operationUser,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByByConclusionNoSelective"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationManConclusion">
        update examination_man_conclusion
        <set>
            <if test="conclusionName != null">
                `conclusion_name` = #{conclusionName,jdbcType=VARCHAR},
            </if>
            <if test="examinationGroupI != null">
                `group_first_level` = #{examinationGroupI,jdbcType=BIGINT},
            </if>
            <if test="examinationGroupIi != null">
                `group_second_level` = #{examinationGroupIi,jdbcType=BIGINT},
            </if>
            <if test="firstDepartmentNo != null">
                `first_department_no` = #{firstDepartmentNo,jdbcType=BIGINT},
            </if>
            <if test="secondDepartmentNo != null">
                `second_department_no` = #{secondDepartmentNo,jdbcType=BIGINT},
            </if>
            <if test="checkMean != null">
                `check_mean` = #{checkMean,jdbcType=VARCHAR},
            </if>
            <if test="checkSuggest != null">
                `check_suggest` = #{checkSuggest,jdbcType=VARCHAR},
            </if>
            <if test="operationUser != null">
                `operation_user` = #{operationUser,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                `yn` = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where `conclusion_no` = #{conclusionNo,jdbcType=BIGINT}
        and yn=1
    </update>
    <update id="updateByConclusionNoAndChannel"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationManConclusion">

        update examination_man_conclusion
        set
        `conclusion_name` = #{conclusionName,jdbcType=VARCHAR},
        `group_first_level` = #{examinationGroupI,jdbcType=BIGINT},
        `group_second_level` = #{examinationGroupIi,jdbcType=BIGINT},
        `first_department_no` = #{firstDepartmentNo,jdbcType=BIGINT},
        `second_department_no` = #{secondDepartmentNo,jdbcType=BIGINT},
        `check_mean` = #{checkMean,jdbcType=VARCHAR},
        `check_suggest` = #{checkSuggest,jdbcType=VARCHAR},
        `operation_user` = #{operationUser,jdbcType=VARCHAR},
        `yn` = #{yn,jdbcType=TINYINT},
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
        `update_time` = #{updateTime,jdbcType=TIMESTAMP}
         where `conclusion_no` = #{conclusionNo,jdbcType=BIGINT} and yn=1
    </update>

    <select id="selectConclusionsByParam" parameterType="com.jd.health.medical.examination.domain.conclusion.ExaminationManConclusion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from examination_man_conclusion
        <where>
              yn=1
            <if test="examinationGroupI != null">
              and  group_first_level=#{examinationGroupI,jdbcType=BIGINT}
            </if>
            <if test="examinationGroupIi != null">
              and   group_second_level=#{examinationGroupIi,jdbcType=BIGINT}
            </if>
            <if test="conclusionName != null and conclusionName !=''">
                and   conclusion_name like concat ('%',#{conclusionName},'%')
            </if>
            <if test="firstDepartmentNo != null">
                and `first_department_no` = #{firstDepartmentNo,jdbcType=BIGINT}
            </if>
            <if test="secondDepartmentNo != null">
                and `second_department_no` = #{secondDepartmentNo,jdbcType=BIGINT}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectByConclusionName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from examination_man_conclusion
        where conclusion_name=#{conclusionName}
        and yn=1
        limit 1
    </select>
    <select id="listByNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from examination_man_conclusion
        where conclusion_name in
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name,jdbcType=VARCHAR}
        </foreach>
        and yn=1
    </select>
</mapper>