<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationReportFileDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.report.DO.ExaminationReportFileDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_pin" jdbcType="VARCHAR" property="userPin" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="jd_appointment_id" jdbcType="BIGINT" property="jdAppointmentId" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_pin, patient_id, jd_appointment_id, file_url, yn, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from examination_report_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from examination_report_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationReportFileDO">
    insert into examination_report_file (id, user_pin, patient_id,
      jd_appointment_id, file_url, yn,
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{userPin,jdbcType=VARCHAR}, #{patientId,jdbcType=BIGINT}, 
      #{jdAppointmentId,jdbcType=BIGINT}, #{fileUrl,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationReportFileDO">
    insert into examination_report_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userPin != null">
        user_pin,
      </if>
      <if test="patientId != null">
        patient_id,
      </if>
      <if test="jdAppointmentId != null">
        jd_appointment_id,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userPin != null">
        #{userPin,jdbcType=VARCHAR},
      </if>
      <if test="patientId != null">
        #{patientId,jdbcType=BIGINT},
      </if>
      <if test="jdAppointmentId != null">
        #{jdAppointmentId,jdbcType=BIGINT},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      1,now(),now()
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationReportFileDO">
    update examination_report_file
    <set>
      <if test="userPin != null">
        user_pin = #{userPin,jdbcType=VARCHAR},
      </if>
      <if test="patientId != null">
        patient_id = #{patientId,jdbcType=BIGINT},
      </if>
      <if test="jdAppointmentId != null">
        jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationReportFileDO">
    update examination_report_file
    set user_pin = #{userPin,jdbcType=VARCHAR},
      patient_id = #{patientId,jdbcType=BIGINT},
      jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert into examination_report_file(user_pin, patient_id, jd_appointment_id, file_url, yn, create_time, update_time)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.userPin}, #{entity.patientId}, #{entity.jdAppointmentId}, #{entity.fileUrl}, 1, now(), now())
    </foreach>
  </insert>

  <select id="selectAllByParam" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationReportFileDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from examination_report_file
    <where>
      yn = 1
      <if test="jdAppointmentId != null">
        and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
      </if>
    </where>
  </select>
</mapper>