<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.HealthWikiContentDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.wiki.HealthWikiContent">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_id" jdbcType="VARCHAR" property="contentId" />
    <result column="content_value" jdbcType="VARCHAR" property="contentValue" />
    <result column="content_type" jdbcType="INTEGER" property="contentType" />
    <result column="entry_id" jdbcType="VARCHAR" property="entryId" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="directory_id" jdbcType="VARCHAR" property="directoryId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="icon_link" jdbcType="VARCHAR" property="iconLink" />
    <result column="support_other" jdbcType="INTEGER" property="supportOther" />
    <result column="default_option" jdbcType="INTEGER" property="defaultOption" />
    <result column="input_maximum" jdbcType="INTEGER" property="inputMaximum" />
    <result column="input_default_text" jdbcType="VARCHAR" property="inputDefaultText" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, content_id, content_value, content_type, entry_id, attribute_id, directory_id, sort, icon_link,
    support_other, yn, create_user, create_time, update_user, update_time,default_option,input_maximum,input_default_text
  </sql>


  <insert id="insertHealthWikiContentList" parameterType="java.util.List">
    insert into health_wiki_content
        (content_id, content_value, content_type, attribute_id,sort, icon_link,support_other,
         default_option,input_maximum,input_default_text,create_user,update_user)
    values
    <foreach collection="contentList" item="content" separator=",">
      (#{content.contentId},#{content.contentValue},#{content.contentType},#{content.attributeId},
       #{content.sort},#{content.iconLink},#{content.supportOther},
       #{content.defaultOption},#{content.inputMaximum},#{content.inputDefaultText},
       #{content.createUser},#{content.updateUser})
    </foreach>
  </insert>


  <select id="selectByParam" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiContent" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_wiki_content
    <where>
      <if test="contentId != null and contentId != ''">
        and content_id = #{contentId,jdbcType=VARCHAR}
      </if>
      <if test="entryId != null and entryId != ''">
        and entry_id = #{entryId,jdbcType=VARCHAR}
      </if>
      <if test="attributeId != null and attributeId != ''">
        and attribute_id = #{attributeId,jdbcType=VARCHAR}
      </if>
      <if test="directoryId != null and directoryId != ''">
        and directory_id = #{directoryId,jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </select>
  <select id="queryHealthWikiContentByEntryIds"
          resultType="com.jd.health.medical.examination.domain.wiki.HealthWikiContent">
    select
    <include refid="Base_Column_List" />
    from health_wiki_content
    <where>
      entry_id in
      <foreach collection="entryIds" item="entryId" open="(" separator="," close=")">
        #{entryId}
      </foreach>
      and yn = 1
    </where>
  </select>


  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiContent" useGeneratedKeys="true">
    insert into health_wiki_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        content_id,
      </if>
      <if test="contentValue != null">
        content_value,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="entryId != null">
        entry_id,
      </if>
      <if test="attributeId != null">
        attribute_id,
      </if>
      <if test="directoryId != null">
        directory_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        #{contentId,jdbcType=VARCHAR},
      </if>
      <if test="contentValue != null">
        #{contentValue,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="entryId != null">
        #{entryId,jdbcType=VARCHAR},
      </if>
      <if test="attributeId != null">
        #{attributeId,jdbcType=VARCHAR},
      </if>
      <if test="directoryId != null">
        #{directoryId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateBySelective" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiContent">
    update health_wiki_content
    <set>
      <if test="contentValue != null">
        content_value = #{contentValue,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="entryId != null">
        entry_id = #{entryId,jdbcType=VARCHAR},
      </if>
      <if test="attributeId != null">
        attribute_id = #{attributeId,jdbcType=VARCHAR},
      </if>
      <if test="directoryId != null">
        directory_id = #{directoryId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where content_id = #{contentId,jdbcType=VARCHAR}
  </update>

  <update id="updateHealthWikiContentList" parameterType="java.util.List">
    <foreach collection="contentList" item="item" open="" close="" separator=";">
      update health_wiki_content
      <set>
        content_value = #{item.contentValue,jdbcType=VARCHAR},
        content_type = #{item.contentType,jdbcType=INTEGER},
        attribute_id = #{item.attributeId,jdbcType=VARCHAR},
        directory_id = #{item.directoryId,jdbcType=VARCHAR},
        sort = #{item.sort,jdbcType=INTEGER},
        default_option = #{item.defaultOption,jdbcType=INTEGER},
        input_maximum = #{item.inputMaximum,jdbcType=INTEGER},
        input_default_text = #{item.inputDefaultText,jdbcType=VARCHAR}
      </set>
      where content_id = #{item.contentId,jdbcType=VARCHAR}
      AND yn = 1
    </foreach>
  </update>

  <update id="deleteHealthWikiContentList" parameterType="java.util.List">
    <foreach collection="contentList" item="item" open="" close="" separator=";">
      update health_wiki_content
      <set>
        yn = 0
      </set>
      where content_id = #{item,jdbcType=VARCHAR}
      AND yn = 1
    </foreach>
  </update>


  <select id="queryHealthWikiContentByAttributeIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_wiki_content
    <where>
      attribute_id in
      <foreach collection="attributeIdList" item="attributeId" open="(" separator="," close=")">
        #{attributeId}
      </foreach>
      and yn = 1
      order by sort
    </where>
  </select>
</mapper>