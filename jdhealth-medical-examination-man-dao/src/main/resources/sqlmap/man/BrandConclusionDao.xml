<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.BrandConclusionDao">


    <select id="selectLinkedConclusions" resultMap="OldResultMap">
        select channel_no, trim(third_conclusion_name) as third_conclusion_name, operation_user, operation_time
        from report_pending_conclusion_record
        where yn=1 and relevance_status = 1 and channel_no = #{channelNo}
    </select>


    <!-- 历史SQL 如下================================-->
    <resultMap id="OldResultMap"
               type="com.jd.health.medical.examination.domain.conclusion.ThirdUnsolvedConclusionEntity">
<!--        <id column="id" jdbcType="BIGINT" property="id"/>-->
        <result column="third_conclusion_name" jdbcType="VARCHAR" property="thirdConclusionName"/>
<!--        <result column="conclusion_no" jdbcType="BIGINT" property="conclusionNo"/>-->
        <result column="channel_no" jdbcType="BIGINT" property="channelNo"/>
        <result column="relevance_status" jdbcType="INTEGER" property="relevanceStatus"/>
        <result column="operation_user" jdbcType="VARCHAR" property="operationUser"/>
        <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime"/>
<!--        <result column="yn" jdbcType="INTEGER" property="yn"/>-->
<!--        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>-->
<!--        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>-->
    </resultMap>


    <sql id="OLD_Column_List">
        id,  third_conclusion_name, conclusion_no,
        channel_no, relevance_status, operation_user, operation_time, yn, create_time, update_time
    </sql>


    <insert id="insert"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ThirdUnsolvedConclusionEntity">
  insert into report_pending_conclusion_record (
    third_conclusion_name, conclusion_no, channel_no,
    relevance_status, operation_user, operation_time,
    yn, create_time, update_time
    )
  values (#{thirdConclusionName,jdbcType=VARCHAR},
    #{conclusionNo,jdbcType=BIGINT}, #{channelNo,jdbcType=BIGINT},
    #{relevanceStatus,jdbcType=INTEGER}, #{operationUser,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP},
    #{yn,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
    )
</insert>




    <update id="updateDynamic"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ThirdUnsolvedConclusionEntity">
        update report_pending_conclusion_record
        <set>
            <if test="conclusionNo != null">
                conclusion_no = #{conclusionNo,jdbcType=BIGINT},
            </if>
            <if test="relevanceStatus != null">
                relevance_status = #{relevanceStatus,jdbcType=INTEGER},
            </if>
            <if test="operationUser != null">
                operation_user = #{operationUser,jdbcType=VARCHAR},
            </if>
            <if test="operationTime != null">
                operation_time = #{operationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where channel_no = #{channelNo,jdbcType=BIGINT} and yn=1 and
        third_conclusion_name = #{thirdConclusionName,jdbcType=VARCHAR}
    </update>


    <update id="update"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ThirdUnsolvedConclusionEntity">
  update report_pending_conclusion_record
  set
    third_conclusion_name = #{thirdConclusionName,jdbcType=VARCHAR},
    conclusion_no = #{conclusionNo,jdbcType=BIGINT},
    channel_no = #{channelNo,jdbcType=BIGINT},
    relevance_status = #{relevanceStatus,jdbcType=INTEGER},
    operation_user = #{operationUser,jdbcType=VARCHAR},
    operation_time = #{operationTime,jdbcType=TIMESTAMP},
    yn = #{yn,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
  where id = #{id,jdbcType=BIGINT}
  and yn=1
</update>



    <select id="selectByChannelAndThirdName" resultMap="OldResultMap">
        select
        <include refid="OLD_Column_List"/>
        from report_pending_conclusion_record
        where yn=1
        <if test="channelNo !=null">
            and channel_no=#{channelNo,jdbcType=BIGINT}
        </if>
        <if test="thirdConclusionName != null">
            and third_conclusion_name=#{thirdConclusionName,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectByChannelAndThirdNameList" resultMap="OldResultMap">
        select
        <include refid="OLD_Column_List"/>
        from report_pending_conclusion_record
        where yn=1
        and relevance_status = 0
        <if test="channelNo !=null">
            and channel_no=#{channelNo,jdbcType=BIGINT}
        </if>
        <if test="thirdConclusionNameList != null">
            and third_conclusion_name in
            <foreach collection="thirdConclusionNameList" item="thirdConclusionName" open="(" close=")" separator=",">
                #{thirdConclusionName,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>


    <select id="selectPendingConclusionList" resultMap="OldResultMap">
        select
        <include refid="OLD_Column_List"/>
        from report_pending_conclusion_record
        where yn=1
    </select>

</mapper>