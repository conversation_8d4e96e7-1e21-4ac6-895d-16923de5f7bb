<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.StructReportReviseRecordDao">
    <resultMap type="com.jd.health.medical.examination.domain.report.entity.basic.StructReportReviseRecordEntity" id="StructReportReviseRecordMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="structReportId" column="struct_report_id" jdbcType="VARCHAR"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="dataType" column="data_type" jdbcType="INTEGER"/>
        <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
        <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
        <result property="yn" column="yn" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, struct_report_id, brand_id, data_type, source_name, update_name, yn, create_user, create_time
    </sql>

    <!--通过实体作为筛选条件查询-->
    <select id="queryPage" resultMap="StructReportReviseRecordMap">
        select
        <include refid="Base_Column_List"/>
        from struct_report_revise_record
        <where>
            yn = 1
            <if test="brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
            <if test="createTimeStart != null">
                and create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                and #{createTimeEnd} >= create_time
            </if>
        </where>
        order by create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into struct_report_revise_record(struct_report_id, brand_id, data_type, source_name,
                                                           update_name,create_user, create_time)
        values (#{structReportId}, #{brandId}, #{dataType}, #{sourceName}, #{updateName}, #{createUser},
                #{createTime})
    </insert>

    <insert id="insertBatch">
        insert into struct_report_revise_record(struct_report_id, brand_id, data_type, source_name,
        update_name, create_user, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.structReportId}, #{entity.brandId}, #{entity.dataType}, #{entity.sourceName},
            #{entity.updateName}, #{entity.createUser}, #{entity.createTime})
        </foreach>
    </insert>




</mapper>

