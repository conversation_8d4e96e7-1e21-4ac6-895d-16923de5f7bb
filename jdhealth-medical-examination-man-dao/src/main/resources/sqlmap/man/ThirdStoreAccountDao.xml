<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.supplier.ThirdStoreAccountDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.supplier.ThirdStoreAccountEntity" id="thirdStoreAccountMap">
        <result property="id" column="id"/>
        <result property="channelNo" column="channel_no"/>
        <result property="storeId" column="store_id"/>
        <result property="accountNo" column="account_no"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            channel_no,
            store_id,
            account_no,
            yn,
            create_time,
            update_time
        </trim>
    </sql>

    <!-- 根据条件查询列表 -->
    <select id="queryStoreAccountList" parameterType="com.jd.health.medical.examination.domain.supplier.ThirdStoreAccountEntity" resultMap="thirdStoreAccountMap" >
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store_account
        <where>
            yn = 1
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="accountNo != null and accountNo != ''">
                and account_no = #{accountNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <!-- 根据条件查询列表 -->
    <select id="queryStoreAccountListNotStoreId" parameterType="com.jd.health.medical.examination.domain.supplier.ThirdStoreAccountEntity" resultMap="thirdStoreAccountMap" >
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store_account
        <where>
            yn = 1
            <if test="storeId != null and storeId != ''">
                and store_id != #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="accountNo != null and accountNo != ''">
                and account_no = #{accountNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 插入实体 -->
    <insert id="insertStoreAccount" parameterType="com.jd.health.medical.examination.domain.supplier.ThirdStoreAccountEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_third_store_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            channel_no,
            store_id,
            account_no,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{channelNo,jdbcType=VARCHAR},
            #{storeId,jdbcType=VARCHAR},
            #{accountNo,jdbcType=VARCHAR},
            1,
            now(),
            now(),
        </trim>
    </insert>
    <!-- 修改实体 -->
    <update id="updateStoreAccount" parameterType="com.jd.health.medical.examination.domain.supplier.ThirdStoreAccountEntity">
        update examination_man_third_store_account
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="storeId != null and storeId != ''">
                    store_id = #{storeId,jdbcType=VARCHAR}
                </if>
                <if test="channelNo != null and channelNo != ''">
                    channel_no = #{channelNo,jdbcType=VARCHAR}
                </if>
                <if test="accountNo != null and accountNo != ''">
                    account_no = #{accountNo,jdbcType=VARCHAR}
                </if>
            </trim>
        </set>
        where channel_no = #{channelNo,jdbcType=TINYINT}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and yn = 1
    </update>
    <!-- 逻辑删除实体 -->
    <update id="deleteStoreAccount" parameterType="com.jd.health.medical.examination.domain.supplier.ThirdStoreAccountEntity">
        update examination_man_third_store_account
        set yn = 0,update_time = now()
        where channel_no = #{channelNo,jdbcType=TINYINT}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and yn = 1
    </update>
    <!--批量插入-->
    <insert id="insertStoreAccountBatch" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="false">
        <!--@mbg.generated-->
        insert into examination_man_third_store_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            channel_no,
            store_id,
            account_no,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <foreach collection="accountList" item="item" separator=",">
            (#{item.channelNo,jdbcType=VARCHAR},
            #{item.storeId,jdbcType=VARCHAR},
            #{item.accountNo,jdbcType=VARCHAR},
            1,
            now(),
            now()
            )
        </foreach>
    </insert>
    <!--批量更新-->
    <update id="updateStoreAccountBatch" parameterType="java.util.List" >
        <foreach collection="accountList" item="item" index="index" open="" close="" separator=";">
            update examination_man_third_store_account
            <set>
                <trim prefix="" suffix="" suffixOverrides=",">
                    update_time = now(),
                    <if test="item.accountNo != null">
                        account_no = #{item.accountNo,jdbcType=TINYINT},
                    </if>
                </trim>
            </set>
            where channel_no = #{item.channelNo,jdbcType=TINYINT}
            and store_id = #{item.storeId,jdbcType=VARCHAR}
            and yn = 1
        </foreach>
    </update>

    <select id="queryStoreIdByAccountNoMap" resultMap="thirdStoreAccountMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"></include>
        from examination_man_third_store_account
        where yn = 1
        and account_no in <foreach collection="accountNoList" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>
</mapper>