<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManSkuVcRecordDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManSkuVcRecordEntity">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="apply_id" property="applyId" jdbcType="VARCHAR" />
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
        <result column="goods_id" property="goodsId" jdbcType="VARCHAR" />
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR" />
        <result column="sku_price" property="skuPrice" jdbcType="INTEGER" />
        <result column="source_type" property="sourceType" jdbcType="INTEGER" />
        <result column="sign_type" property="signType" jdbcType="INTEGER" />
        <result column="buyer_erp" property="buyerErp" jdbcType="VARCHAR" />
        <result column="buyer_name" property="buyerName" jdbcType="VARCHAR" />
        <result column="saler_erp" property="salerErp" jdbcType="VARCHAR" />
        <result column="saler_name" property="salerName" jdbcType="VARCHAR" />
        <result column="saler_audit_status" property="salerAuditStatus" jdbcType="INTEGER" />
        <result column="saler_audit_opinion" property="salerAuditOpinion" jdbcType="VARCHAR" />
        <result column="saler_audit_time" property="salerAuditTime" jdbcType="TIMESTAMP" />
        <result column="keeper_audit_status" property="keeperAuditStatus" jdbcType="INTEGER" />
        <result column="keeper_audit_opinion" property="keeperAuditOpinion" jdbcType="VARCHAR" />
        <result column="keeper_audit_time" property="keeperAuditTime" jdbcType="TIMESTAMP" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="valid_type" property="validType" jdbcType="INTEGER" />
        <result column="valid_value" property="validValue" jdbcType="BIGINT" />
        <result column="third_goods_extend" property="thirdGoodsExtend" jdbcType="VARCHAR" />
        <result column="yn" property="yn" jdbcType="INTEGER" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, apply_id, sku_no, sku_name, goods_id, goods_name, sku_price, source_type,
        sign_type,buyer_erp, buyer_name, saler_erp, saler_name, saler_audit_status, saler_audit_opinion,
        saler_audit_time, keeper_audit_status, keeper_audit_opinion, keeper_audit_time, remark,
        yn, create_user, create_time, update_user, update_time, third_goods_extend
       ,valid_type,valid_value
    </sql>

    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManSkuVcRecordEntity" >
        insert into examination_man_sku_vc_record (apply_id, sku_no,
          sku_name, goods_id, goods_name,
          sku_price, source_type, sign_type,
          buyer_erp, buyer_name, saler_erp,
          saler_name, saler_audit_status, saler_audit_opinion,
          saler_audit_time, keeper_audit_status, keeper_audit_opinion,
          keeper_audit_time, remark, yn,
          create_user, create_time, update_user,update_time,third_goods_extend
          ,valid_type,valid_value)
        values (#{applyId,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR},
          #{skuName,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
          #{skuPrice,jdbcType=INTEGER}, #{sourceType,jdbcType=INTEGER}, #{signType,jdbcType=INTEGER},
          #{buyerErp,jdbcType=VARCHAR}, #{buyerName,jdbcType=VARCHAR}, #{salerErp,jdbcType=VARCHAR},
          #{salerName,jdbcType=VARCHAR}, #{salerAuditStatus,jdbcType=INTEGER}, #{salerAuditOpinion,jdbcType=VARCHAR},
          #{salerAuditTime,jdbcType=TIMESTAMP}, #{keeperAuditStatus,jdbcType=INTEGER}, #{keeperAuditOpinion,jdbcType=VARCHAR},
          #{keeperAuditTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT},
          #{createUser,jdbcType=VARCHAR}, now(), #{updateUser,jdbcType=VARCHAR},
          now(), #{thirdGoodsExtend,jdbcType=VARCHAR},#{validType,jdbcType=INTEGER},#{validValue,jdbcType=BIGINT})
    </insert>

    <update id="updateByApplyId"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManSkuVcRecordEntity">
        UPDATE examination_man_sku_vc_record
        <set>
            <if test="skuNo != null" >
                sku_no = #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null" >
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null" >
                goods_id = #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null" >
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="skuPrice != null" >
                sku_price = #{skuPrice,jdbcType=INTEGER},
            </if>
            <if test="sourceType != null" >
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="signType != null" >
                sign_type = #{signType,jdbcType=INTEGER},
            </if>
            <if test="buyerErp != null" >
                buyer_erp = #{buyerErp,jdbcType=VARCHAR},
            </if>
            <if test="buyerName != null" >
                buyer_name = #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="salerErp != null" >
                saler_erp = #{salerErp,jdbcType=VARCHAR},
            </if>
            <if test="salerName != null" >
                saler_name = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="salerAuditStatus != null" >
                saler_audit_status = #{salerAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="salerAuditOpinion != null" >
                saler_audit_opinion = #{salerAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="salerAuditTime != null" >
                saler_audit_time = #{salerAuditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="keeperAuditStatus != null" >
                keeper_audit_status = #{keeperAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="keeperAuditOpinion != null" >
                keeper_audit_opinion = #{keeperAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="keeperAuditTime != null" >
                keeper_audit_time = #{keeperAuditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null" >
                remark = concat(ifnull(remark,''),#{remark}),
            </if>
            <if test="updateUser != null" >
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="yn != null" >
                yn = #{yn},
            </if>
            update_time = now()
        </set>
        WHERE apply_id = #{applyId} AND yn = 1
    </update>

    <update id="updateById" parameterType="java.lang.Long" >
        update examination_man_sku_vc_record
        <set>
            <if test="applyId != null" >
                apply_id = #{applyId,jdbcType=VARCHAR},
            </if>
            <if test="skuNo != null" >
                sku_no = #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null" >
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null" >
                goods_id = #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null" >
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="skuPrice != null" >
                sku_price = #{skuPrice,jdbcType=INTEGER},
            </if>
            <if test="sourceType != null" >
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="signType != null" >
                sign_type = #{signType,jdbcType=INTEGER},
            </if>
            <if test="buyerErp != null" >
                buyer_erp = #{buyerErp,jdbcType=VARCHAR},
            </if>
            <if test="buyerName != null" >
                buyer_name = #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="salerErp != null" >
                saler_erp = #{salerErp,jdbcType=VARCHAR},
            </if>
            <if test="salerName != null" >
                saler_name = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="salerAuditStatus != null" >
                saler_audit_status = #{salerAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="salerAuditOpinion != null" >
                saler_audit_opinion = #{salerAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="salerAuditTime != null" >
                saler_audit_time = #{salerAuditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="keeperAuditStatus != null" >
                keeper_audit_status = #{keeperAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="keeperAuditOpinion != null" >
                keeper_audit_opinion = #{keeperAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="keeperAuditTime != null" >
                keeper_audit_time = #{keeperAuditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="yn != null" >
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null" >
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id} and yn = 1
      </update>

    <select id="selectBySkuOrGoodsName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM examination_man_sku_vc_record
        <where>
            yn = 1
            <if test="skuNo != null and skuNo != ''">
                and sku_no = #{skuNo}
            </if>
            <if test="goodsName != null and goodsName != ''">
                and goods_name like CONCAT('%',#{goodsName},'%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectByApplyId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        SELECT
        <include refid="Base_Column_List" />
        FROM examination_man_sku_vc_record
        WHERE yn = 1
        AND apply_id = #{applyId}
    </select>
    <select id="exist" resultType="java.lang.Integer" parameterType="java.lang.String" >
        SELECT count(*)
        FROM examination_man_sku_vc_record
        where apply_id = #{applyId}
    </select>
    <select id="selectBySkuName" resultMap="BaseResultMap" parameterType="java.lang.String" >
        SELECT
        <include refid="Base_Column_List" />
        FROM examination_man_sku_vc_record
        WHERE yn = 1
        AND sku_name = #{skuName,jdbcType=VARCHAR}
    </select>

    <select id="selectCountById" parameterType="java.lang.Long" resultType="java.lang.Long" >
        select count(1) from examination_man_sku_vc_record
        where yn = 1
    </select>




</mapper>