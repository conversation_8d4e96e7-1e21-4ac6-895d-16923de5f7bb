<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ReportConclusionParseLogDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.conclusion.ReportConclusionParseLogEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="operator_type" jdbcType="TINYINT" property="operatorType"/>
        <result column="jd_appointment_id" jdbcType="BIGINT" property="jdAppointmentId"/>
        <result column="user_pin" jdbcType="VARCHAR" property="userPin"/>
        <result column="report_id" jdbcType="BIGINT" property="reportId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="msg" jdbcType="VARCHAR" property="msg"/>
        <result column="mq_body" jdbcType="LONGVARCHAR" property="mqBody"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        operator_type,
        jd_appointment_id,
        user_pin,
        report_id,
        `status`,
        msg,
        create_time,
        yn
    </sql>
    <sql id="All_Column_List">
        id,
        operator_type,
        jd_appointment_id,
        user_pin,
        report_id,
        `status`,
        msg,
        mq_body,
        create_time,
        yn
    </sql>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ReportConclusionParseLogEntity"
            useGeneratedKeys="true">
        insert into report_conclusion_parse_log (operator_type, jd_appointment_id, user_pin,
                                                 report_id, `status`, msg,
                                                 mq_body, create_time, yn)
        values (#{operatorType,jdbcType=TINYINT}, #{jdAppointmentId,jdbcType=BIGINT}, #{userPin,jdbcType=VARCHAR},
                #{reportId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{msg,jdbcType=VARCHAR},
                #{mqBody,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 1)
    </insert>

    <select id="selectBaseInfoList" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ReportConclusionParseLogEntity">
        select
        <include refid="Base_Column_List"/>
        from report_conclusion_parse_log
        <where>
            <if test="operatorType != null and operatorType != ''">
                and operator_type = #{operatorType}
            </if>
            <if test="jdAppointmentId != null and jdAppointmentId != ''">
                and jd_appointment_id = #{jdAppointmentId}
            </if>
            <if test="userPin != null and userPin != ''">
                and user_pin = #{userPin}
            </if>
            <if test="reportId != null and reportId != ''">
                and report_id = #{reportId}
            </if>
            <if test="status != null and status != ''">
                and `status` = #{status}
            </if>
            <if test="msg != null and msg != ''">
                and msg = #{msg}
            </if>
            and yn = 1
        </where>
    </select>

    <select id="selectAllInfoList" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.conclusion.ReportConclusionParseLogEntity">
        select
        <include refid="All_Column_List"/>
        from report_conclusion_parse_log
        <where>
            <if test="operatorType != null and operatorType != ''">
                and operator_type = #{operatorType}
            </if>
            <if test="jdAppointmentId != null and jdAppointmentId != ''">
                and jd_appointment_id = #{jdAppointmentId}
            </if>
            <if test="userPin != null and userPin != ''">
                and user_pin = #{userPin}
            </if>
            <if test="reportId != null and reportId != ''">
                and report_id = #{reportId}
            </if>
            <if test="status != null and status != ''">
                and `status` = #{status}
            </if>
            <if test="msg != null and msg != ''">
                and msg = #{msg}
            </if>
            and yn = 1
        </where>
    </select>
</mapper>