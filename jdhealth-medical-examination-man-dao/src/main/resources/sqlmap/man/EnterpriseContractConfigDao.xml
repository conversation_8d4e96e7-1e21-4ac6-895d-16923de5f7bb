<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.EnterpriseContractConfigDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseContractConfigEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="company_no" property="companyNo" jdbcType="BIGINT" />
    <result column="contract_type" property="contractType" jdbcType="INTEGER" />
    <result column="contract_name" property="contractName" jdbcType="VARCHAR" />
    <result column="contract_desc" property="contractDesc" jdbcType="VARCHAR" />
    <result column="contract_detail_desc" property="contractDetailDesc" jdbcType="VARCHAR" />
    <result column="contract_url" property="contractUrl" jdbcType="VARCHAR" />
    <result column="contract_status" property="contractStatus" jdbcType="INTEGER" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!--添加项目履约信息-->
  <insert id="insertEnterpriseContract">
    insert into examination_qx_man_company_contract_config (company_no, contract_type, contract_name, contract_desc, contract_detail_desc,
    contract_url,contract_status, yn, create_time, update_time)
    values (#{companyNo},#{contractType},#{contractName},#{contractDesc},#{contractDetailDesc},#{contractUrl},#{contractStatus},1,now(),now())
  </insert>

  <!--更新项目履约信息-->
  <update id="updateEnterpriseContract">
    update examination_qx_man_company_contract_config
    <set>
      <if test="contractDesc != null">
         contract_desc = #{contractDesc},
      </if>
      <if test="contractDetailDesc != null">
         contract_detail_desc = #{contractDetailDesc},
      </if>
      <if test="contractUrl != null">
         contract_url = #{contractUrl},
      </if>
      <if test="contractStatus != null">
         contract_status = #{contractStatus},
      </if>
    </set>
    where company_no = #{companyNo}
    and contract_type = #{contractType}
    and yn = 1
  </update>

  <select id="selectEnterpriseContract" resultMap="BaseResultMap" >
    select id, company_no, contract_type, contract_name, contract_desc, contract_detail_desc, 
    contract_url,contract_status, yn, create_time, update_time
    from examination_qx_man_company_contract_config
    <where>
      <if test="companyNo != null">
        and company_no = #{companyNo}
      </if>
      <if test="contractType != null">
        and contract_type = #{contractType}
      </if>
      and yn = 1
    </where>
  </select>
</mapper>