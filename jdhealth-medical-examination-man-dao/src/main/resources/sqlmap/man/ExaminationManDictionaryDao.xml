<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.dictionary.ExaminationManDictionaryDao">

    <resultMap id="ValueResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManDictionaryEntity">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="index_code" property="indexCode" jdbcType="VARCHAR" />
        <result column="value_code" property="valueCode" jdbcType="VARCHAR" />
        <result column="value_name" property="valueName" jdbcType="VARCHAR" />
        <result column="value_desc" property="valueDesc" jdbcType="VARCHAR" />
        <result column="seq" property="seq" jdbcType="INTEGER" />
        <result column="yn" property="yn" jdbcType="TINYINT" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>


    <sql id="Value_Column_List" >
        id, index_code, value_code, value_name, value_desc, seq, yn, create_user, create_time,
        update_user, update_time
    </sql>

    <sql id="queryValue">
        <where>
            yn=1
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="indexCode != null">
                and index_code = #{indexCode,jdbcType=VARCHAR}
            </if>
            <if test="valueCode != null">
                and value_code like CONCAT("%",#{valueCode,jdbcType=VARCHAR},"%")
            </if>
            <if test="valueName != null">
                and value_name like CONCAT("%",#{valueName,jdbcType=VARCHAR},"%")
            </if>
            <if test="valueDesc != null">
                and value_desc like CONCAT("%",#{valueDesc,jdbcType=VARCHAR},"%")
            </if>
            <if test="seq != null">
                and seq = #{seq,jdbcType=INTEGER}
            </if>
        </where>
    </sql>

    <insert id="insertValue" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManDictionaryEntity" >
        INSERT INTO examination_man_dictionary_value (index_code, value_code,
          value_name, value_desc, seq,
          yn, create_user, create_time,
          update_user, update_time)
        VALUES (#{indexCode,jdbcType=VARCHAR}, #{valueCode,jdbcType=VARCHAR},
          #{valueName,jdbcType=VARCHAR}, #{valueDesc,jdbcType=VARCHAR}, #{seq,jdbcType=INTEGER},
          #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
          #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateValueByIndexCode" parameterType="java.lang.Long" >
        UPDATE examination_man_dictionary_value
        <set>
            <if test="valueName != null" >
                value_name = #{valueName,jdbcType=VARCHAR},
            </if>
            <if test="valueDesc != null" >
                value_desc = #{valueDesc,jdbcType=VARCHAR},
            </if>
            <if test="seq != null" >
                seq = #{seq,jdbcType=INTEGER},
            </if>
            <if test="yn != null" >
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="createUser != null" >
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null" >
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            update_time = now()
        </set>
        WHERE index_code = #{indexCode} AND yn = 1
    </update>

    <update id="deleteDictionaryValueById" >
        UPDATE examination_man_dictionary_value
        SET yn = 0, update_time = now()
        WHERE id = #{id,jdbcType=BIGINT} AND yn = 1
    </update>

    <update id="deleteDictionaryValueByIndexCode" >
        UPDATE examination_man_dictionary_value
        SET
        yn = 0,
        update_time = now()
        WHERE index_code = #{indexCode,jdbcType=VARCHAR} AND yn = 1
    </update>

    <select id="queryDictionaryValue" resultMap="ValueResultMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManDictionaryEntity">
        SELECT
        <include refid="Value_Column_List"/>
        FROM examination_man_dictionary_value
        <include refid="queryValue"/>
        ORDER BY create_time DESC
    </select>

    <select id="queryDictValueByIndexCode" resultMap="ValueResultMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationManDictionaryEntity">
        SELECT
        <include refid="Value_Column_List"/>
        FROM examination_man_dictionary_value
        WHERE index_code = #{indexCode,jdbcType=VARCHAR} AND yn = 1
    </select>
</mapper>