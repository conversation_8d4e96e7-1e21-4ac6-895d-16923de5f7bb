<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.BillRecordDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.personal.entity.BillRecordEntity" id="billRecordMap">
        <result property="id" column="id"/>
        <result property="billId" column="bill_id"/>
        <result property="billDate" column="bill_date"/>
        <result property="approvalNo" column="approval_no"/>
        <result property="finishAllNum" column="finish_all_num"/>
        <result property="finishCheckNum" column="finish_check_num"/>
        <result property="confirmCheckNum" column="confirm_check_num"/>
        <result property="storeId" column="store_id"/>
        <result property="storeName" column="store_name"/>
        <result property="channelNo" column="channel_no"/>
        <result property="channelName" column="channel_name"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="approvalOpinions" column="approval_opinions"/>
        <result property="applicationReason" column="application_reason"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            bill_id,bill_date,approval_no,finish_all_num,finish_check_num,confirm_check_num,
            store_id,store_name,channel_no,channel_name,approval_status,approval_opinions,application_reason,
            yn,create_time,update_time
        </trim>
    </sql>

    <!-- 根据条件查询列表 -->
    <select id="queryBillRecordList" resultMap="billRecordMap"
            parameterType="com.jd.health.medical.examination.domain.bo.BillRecordBaseBO">
        select
        <include refid="Base_Column_List"/>
        from xfyl_service_bill_record
        <where>
            <if test="null != startDate and startDate !=''">
                and bill_date &gt;= #{startDate}
            </if>
            <if test="null != endDate and endDate !=''">
                and bill_date &lt;= #{endDate}
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <if test="storeId != null and storeId !=''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null and channelNo !=''">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 保存数据 -->
    <insert id="insertBillRecord" parameterType="com.jd.health.medical.examination.domain.personal.entity.BillRecordEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into xfyl_service_bill_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            bill_id,bill_date,finish_all_num,finish_check_num,
            store_id,store_name,channel_no,channel_name
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{billId,jdbcType=BIGINT},#{billDate,jdbcType=DATE},#{finishAllNum,jdbcType=INTEGER},#{finishCheckNum,jdbcType=INTEGER},
            #{storeId,jdbcType=VARCHAR},#{storeName,jdbcType=VARCHAR},#{channelNo,jdbcType=BIGINT},#{channelName,jdbcType=VARCHAR}
        </trim>
    </insert>

    <!-- 批量保存数据 -->
    <insert id="insertBillRecordList">
        insert into xfyl_service_bill_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            bill_id,bill_date,finish_all_num,finish_check_num,
            store_id,store_name,channel_no,channel_name
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.billId,jdbcType=BIGINT},#{item.billDate,jdbcType=DATE},
                #{item.finishAllNum,jdbcType=INTEGER},#{item.finishCheckNum,jdbcType=INTEGER},
                #{item.storeId,jdbcType=VARCHAR},#{item.storeName,jdbcType=VARCHAR},#{item.channelNo,jdbcType=BIGINT},#{item.channelName,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <!-- 根据条件查询列表 -->
    <select id="queryBillRecordDetail" resultMap="billRecordMap"
            parameterType="com.jd.health.medical.examination.domain.bo.BillRecordBaseBO">
        select
        <include refid="Base_Column_List"/>
        from xfyl_service_bill_record
        <where>
            bill_id = #{billId}
            <if test="storeId != null and storeId!=''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null and channelNo!=''">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 更新 -->
    <update id="updateBillRecord"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.BillRecordEntity">
        update xfyl_service_bill_record
        <set>
            <if test="approvalNo != null and approvalNo != ''">
                approval_no = #{approvalNo},
            </if>
            <if test="approvalStatus != null">
                approval_status = #{approvalStatus},
            </if>
            <if test="confirmCheckNum != null">
                confirm_check_num = #{confirmCheckNum},
            </if>
            <if test="approvalOpinions != null and approvalOpinions != ''">
                approval_opinions = #{approvalOpinions},
            </if>
        </set>
        <where>
            bill_id = #{billId}
            <if test="storeId != null and storeId!=''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null and channelNo!=''">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            and yn = 1
        </where>
    </update>

</mapper>