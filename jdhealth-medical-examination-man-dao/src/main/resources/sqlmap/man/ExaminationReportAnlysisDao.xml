<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationReportAnalysisDao">

    <!-- 数据库返回字段 -->
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationReportAnalysisEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="business_id" jdbcType="BIGINT" property="businessId"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="report_id" jdbcType="VARCHAR" property="reportId"/>
        <result column="report_url" jdbcType="VARCHAR" property="reportUrl"/>
        <result column="report_info" jdbcType="VARCHAR" property="reportInfo"/>
        <result column="correct_report_info" jdbcType="VARCHAR" property="correctReportInfo"/>
        <result column="file_type" jdbcType="TINYINT" property="fileType"/>
        <result column="total_index_num" jdbcType="DECIMAL" property="totalIndexNum"/>
        <result column="correct_num" jdbcType="DECIMAL" property="correctNum"/>
        <result column="error_num" jdbcType="DECIMAL" property="errorNum"/>
        <result column="add_num" jdbcType="DECIMAL" property="addNum"/>
        <result column="detection_rate" jdbcType="DECIMAL" property="detectionRate"/>
        <result column="accuracy_rate" jdbcType="DECIMAL" property="accuracyRate"/>
        <result column="last_flag" jdbcType="TINYINT" property="lastFlag"/>
        <result column="report_analysis_time" jdbcType="TIMESTAMP" property="reportAnalysisTime"/>
        <result column="check_status" jdbcType="TINYINT" property="checkStatus"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
    </resultMap>

    <!-- 通用字段 -->
    <sql id="Base_Column_List">
       `id`,`business_id`,`task_id`,`report_id`,`report_url`,
       `report_info`,`correct_report_info`,`file_type`, `total_index_num`,`correct_num`,
       `error_num`, `add_num`,`detection_rate`, `accuracy_rate`, `last_flag`,
       `report_analysis_time`, `check_status`,`create_user`, `update_user`, `create_time`,
       `update_time`, `yn`
    </sql>

    <!-- 保存报告-->
    <insert id="saveReportAnalysis">
        insert into
         examination_report_analysis_data (
             `id`,
             `business_id`,
             `task_id`,
             `report_id`,
             `report_url`,
             `file_type`,
             `report_info`,
             `correct_report_info`,
             `total_index_num`,
             `correct_num`,
             `error_num`,
             `add_num`,
             `detection_rate`,
             `accuracy_rate`,
             `last_flag`,
             `report_analysis_time`,
             `check_status`,
             `create_user`,
             `update_user`,
             `create_time`,
             `update_time`,
             `yn`
           )
        values(
         #{id,jdbcType=BIGINT},
         #{businessId,jdbcType=BIGINT},
         #{taskId,jdbcType=VARCHAR},
         #{reportId,jdbcType=VARCHAR},
         #{reportUrl,jdbcType=VARCHAR},
         #{fileType,jdbcType=TINYINT},
         #{reportInfo,jdbcType=VARCHAR},
         #{correctReportInfo,jdbcType=VARCHAR},
         #{totalIndexNum,jdbcType=DECIMAL},
         #{correctNum,jdbcType=DECIMAL},
         #{errorNum,jdbcType=DECIMAL},
         #{addNum,jdbcType=DECIMAL},
         #{detectionRate,jdbcType=DECIMAL},
         #{accuracyRate,jdbcType=DECIMAL},
         #{lastFlag,jdbcType=TINYINT},
         #{reportAnalysisTime,jdbcType=TIMESTAMP},
         #{checkStatus,jdbcType=TINYINT},
         #{createUser,jdbcType=VARCHAR},
         #{updateUser,jdbcType=VARCHAR},
         now(),
         now(),
         #{yn,jdbcType=TINYINT}
         )
  </insert>

    <!-- 查询解析报告信息 -->
    <select id="queryReportAnalysisList"
            resultType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationReportAnalysisEntity">
        select
        <include refid="Base_Column_List"/>
        from
        examination_report_analysis_data
        <where>
            <if test="taskId !=null and taskId!=''">
                and task_id=#{taskId,jdbcType=VARCHAR}
            </if>
            <if test="reportId !=null and reportId!=''">
                and report_id=#{reportId,jdbcType=VARCHAR}
            </if>
            <if test="true">
                and yn=1
            </if>
        </where>
    </select>

    <!-- 获取最新报告-->
    <select id="queryLastReportAnalysis"
            resultType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationReportAnalysisEntity">
        select
        <include refid="Base_Column_List"/>
        from
        examination_report_analysis_data
        <where>
            report_id=#{reportId,jdbcType=VARCHAR}
            and last_flag = #{lastFlag,jdbcType=TINYINT}
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId,jdbcType=VARCHAR}
            </if>
            and yn = 1 order by update_time desc limit 1
        </where>
    </select>

    <!-- 更新报告原来的last_flag-->
    <update id="updateReportAnalysis" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationReportAnalysisEntity">
        update examination_report_analysis_data set last_flag=#{lastFlag,jdbcType=TINYINT},update_time=now()
        where
        task_id = #{taskId,jdbcType=VARCHAR}
        and report_id=#{reportId,jdbcType=VARCHAR}
        and business_id!=#{businessId,jdbcType=BIGINT}
    </update>


</mapper>