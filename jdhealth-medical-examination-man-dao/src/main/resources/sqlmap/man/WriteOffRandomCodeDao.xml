<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.WriteOffRadomCodeDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.third.WriteOffRandomCodeEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="jd_appointment_id" property="jdAppointmentId" jdbcType="BIGINT" />
    <result column="operator_pin" property="operatorPin" jdbcType="VARCHAR" />
    <result column="user_name" property="userName" jdbcType="VARCHAR"/>
    <result column="user_phone" property="userPhone" jdbcType="VARCHAR"/>
    <result column="user_credential_no" property="userCredentialNo" jdbcType="VARCHAR"/>
    <result column="write_date" property="writeDate" jdbcType="TIMESTAMP" />
    <result column="store_name" property="storeName" jdbcType="VARCHAR" />
    <result column="appointment_date" property="appointmentDate" jdbcType="VARCHAR" />
    <result column="card_last_six" property="cardLastSix" jdbcType="VARCHAR" />
    <result column="random_code" property="randomCode" jdbcType="INTEGER" />
    <result column="write_off" property="writeOff" jdbcType="INTEGER" />
    <result column="finish_status" property="finishStatus" jdbcType="INTEGER" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.third.WriteOffRandomCodeEntity" >
    insert into examination_write_off_random_record ( jd_appointment_id,store_name,appointment_date,
             random_code,user_name,user_phone,user_credential_no,card_last_six)
    values (#{jdAppointmentId,jdbcType=BIGINT},#{storeName,jdbcType=VARCHAR}, #{appointmentDate,jdbcType=VARCHAR},
            #{randomCode,jdbcType=VARCHAR},#{userName,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR},
            #{userCredentialNo,jdbcType=VARCHAR}, #{cardLastSix,jdbcType=VARCHAR})
  </insert>

  <resultMap id="SimpleMap" type="com.jd.health.medical.examination.domain.third.WriteOffRandomCodeEntity" >
    <result column="jd_appointment_id" property="jdAppointmentId" jdbcType="BIGINT" />
    <result column="user_name" property="userName" jdbcType="VARCHAR"/>
    <result column="user_phone" property="userPhone" jdbcType="VARCHAR"/>
    <result column="user_credential_no" property="userCredentialNo" jdbcType="VARCHAR"/>
    <result column="write_date" property="writeDate" jdbcType="TIMESTAMP" />
    <result column="store_name" property="storeName" jdbcType="VARCHAR" />
    <result column="appointment_date" property="appointmentDate" jdbcType="VARCHAR" />
    <result column="write_off" property="writeOff" jdbcType="INTEGER" />
    <result column="finish_status" property="finishStatus" jdbcType="INTEGER" />
  </resultMap>


  <update id="updateOperatorStatus" parameterType="com.jd.health.medical.examination.domain.third.WriteOffRandomCodeEntity" >
    update examination_write_off_random_record
    set operator_pin = #{operatorPin,jdbcType=VARCHAR},
      write_off = 1,
      write_date = CURRENT_TIMESTAMP ,
      finish_status = finish_status + 1
    where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
    and yn = 1 and write_off = 0
  </update>

  <select id="queryStatusByUserPin" resultMap="SimpleMap" parameterType="java.lang.String" >
    select user_name,user_credential_no,write_date
    from examination_write_off_random_record
    where operator_pin = #{operatorPin,jdbcType=VARCHAR}
    and yn = 1
  </select>

  <select id="queryStatusByCardAndRandomCode" resultType="java.lang.Integer" parameterType="com.jd.health.medical.examination.domain.third.WriteOffRandomCodeEntity" >
    select finish_status
    from examination_write_off_random_record
    where card_last_six = #{entity.cardLastSix,jdbcType=BIGINT}
    and random_code = #{entity.randomCode,jdbcType=VARCHAR}
    and yn = 1 and write_off = 0
  </select>

  <select id="queryEntityCardAndRandomCode" resultMap="SimpleMap" parameterType="com.jd.health.medical.examination.domain.third.WriteOffRandomCodeEntity" >
    select jd_appointment_id,user_name,user_phone,store_name,write_date,write_off
    from examination_write_off_random_record
    where card_last_six = #{entity.cardLastSix,jdbcType=BIGINT}
    and random_code = #{entity.randomCode,jdbcType=VARCHAR}
    and yn = 1
  </select>

</mapper>