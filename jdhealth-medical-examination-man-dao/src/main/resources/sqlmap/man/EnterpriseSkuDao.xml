<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuEntity" id="enterpriseSkuMap">
        <result property="id" column="id"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="skuAlias" column="sku_alias"/>
        <result property="groupNo" column="group_no"/>
        <result property="skuCompanyPrice" column="sku_company_price"/>
        <result property="skuPersonPrice" column="sku_person_price"/>
        <result property="skuUpgradeCount" column="sku_upgrade_count"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="yn" column="yn"/>
        <result property="pluginDiscountsType" column="plugin_discounts_type"/>
        <result property="pluginDiscountsValue" column="plugin_discounts_value"/>
        <result property="examinationNotes" column="examination_notes"/>
        <association property="group" javaType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity" columnPrefix="g_">
            <id column="id" property="id" jdbcType="BIGINT"/>
            <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
            <result column="group_no" property="groupNo" jdbcType="BIGINT"/>
            <result column="group_desc" property="groupDesc" jdbcType="VARCHAR"/>
            <result column="group_suitable" property="groupSuitable" jdbcType="TINYINT"/>
            <result column="group_suit_man" property="groupSuitMan" jdbcType="VARCHAR"/>
            <result column="group_item_num" property="groupItemNum" jdbcType="INTEGER"/>
            <result column="age_upper" property="ageUpper" jdbcType="INTEGER"/>
            <result column="age_floor" property="ageFloor" jdbcType="INTEGER"/>
            <result column="yn" property="yn" jdbcType="INTEGER"/>
        </association>
    </resultMap>

    <!-- skuInfo -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.bo.EnterpriseSkuBO" id="SkuInfoMap">
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="groupName" column="group_name"/>
        <result property="groupNo" column="group_no"/>
        <result property="groupSuitable" column="sku_suitable"/>
        <result property="skuCompanyPrice" column="sku_company_price"/>
        <result property="skuPersonPrice" column="sku_price"/>
        <result property="skuUpgradeCount" column="sku_upgrade_count"/>
        <result property="pluginDiscountsType" column="plugin_discounts_type"/>
        <result property="pluginDiscountsValue" column="plugin_discounts_value"/>
        <result property="groupYn" column="group_yn"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            company_no,
            company_name,
            sku_no,
            sku_name,
            group_no,
            sku_company_price,
            sku_person_price,
            create_user,
            create_time,
            update_user,
            update_time,
            sku_upgrade_count,
            yn,
            plugin_discounts_type,
            plugin_discounts_value,
            examination_notes,
        </trim>
    </sql>
    <insert id="insertEnterpriseSku">
        insert into examination_qx_man_sku(
            company_no,
            company_name,
            sku_no,
            sku_name,
            <if test="skuAlias!=null and skuAlias!=''">
                sku_alias,
            </if>
            group_no,
            sku_company_price,
            sku_person_price,
            create_user,
            plugin_discounts_type,
            plugin_discounts_value,
            examination_notes
        )
        values (
            #{companyNo},
            #{companyName},
            #{skuNo},
            #{skuName},
            <if test="skuAlias!=null and skuAlias!=''">
                #{skuAlias},
            </if>
            #{groupNo},
            #{skuCompanyPrice},
            #{skuPersonPrice},
            #{createUser},
            #{pluginDiscountsType},
            #{pluginDiscountsValue},
            #{examinationNotes,jdbcType=VARCHAR}
        )

    </insert>
    <insert id="addSkuUpgradeCount">
         update examination_qx_man_sku
        set sku_upgrade_count = sku_upgrade_count+1
        where company_no=#{companyNo}
        and sku_no=#{skuNo}
        and yn = 1
    </insert>
    <update id="deleteEnterpriseSkuBySkuNoAndCompanyNo">
        update examination_qx_man_sku
        set yn = 0,
        update_user=#{updateUser}
        where sku_no=#{skuNo} and company_no=#{companyNo}
        and yn = 1
    </update>


    <!-- 根据条件查询列表 -->
    <select id="queryEnterpriseSkuList" resultMap="enterpriseSkuMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku
        <where>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <!--<if test="companyName != null and companyName=''">
                and company_name = #{companyName,jdbcType=VARCHAR}
            </if>
            <if test="skuNo != null and skuNo!=''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="skuName != null and skuName!=''">
                and sku_name = #{skuName,jdbcType=VARCHAR}
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="skuCompanyPrice != null">
                and sku_company_price = #{skuCompanyPrice,jdbcType=INT}
            </if>
            <if test="skuPersonPrice != null">
                and sku_person_price = #{skuPersonPrice,jdbcType=INT}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>-->
            and yn = 1
        </where>
    </select>

    <select id="querySkuInfoPage" resultMap="SkuInfoMap">
        SELECT
            sku_no,
            sku_name,
            s.group_no,
            g.group_name,
            sku_suitable,
            sku_price
            g.yn as group_yn
        FROM
            examination_man_sku s
        LEFT JOIN examination_man_group g ON s.group_no=g.group_no
        <where>
            <if test="groupSuitable != null">
                and sku_suitable LIKE CONCAT('%',#{groupSuitable}, '%')
            </if>
            <if test="groupSuitable != null and groupSuitable=='13'">
                or sku_suitable LIKE CONCAT('%','123', '%')
            </if>
            <if test="skuNo != null and skuNo!=''">
                and sku_no = #{skuNo,jdbcType=VARCHAR}
            </if>
            <if test="skuName != null and skuName!=''">
                and sku_name LIKE CONCAT('%',#{skuName}, '%')
            </if>
            <if test="groupName != null and groupName!=''">
                and g.group_name LIKE CONCAT('%',#{groupName}, '%')
            </if>
            <if test="groupNo != null">
                and s.group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            and sku_union_type = 1
            and s.yn = 1 and sku_vindicate_status = 1 and s.sku_status != 3
        </where>
    </select>

    <select id="selectEnterpriseSkuBySkuNoAndCompanyNo"
            resultMap="enterpriseSkuMap">
        SELECT
            s.id,
            company_no,
            company_name,
            sku_no,
            sku_name,
            sku_alias,
            s.group_no,
            sku_company_price,
            sku_person_price,
            s.plugin_discounts_type,
            s.plugin_discounts_value,
            s.examination_notes,
            g.id as g_id,
            g.group_name as g_group_name,
            g.group_no as g_group_no,
            g.group_desc as g_group_desc,
            g.group_suitable as g_group_suitable,
            g.group_suit_man as g_group_suit_man,
            g.group_item_num as g_group_item_num,
            g.age_upper as g_age_upper,
            g.age_floor as g_age_floor,
            g.yn as g_yn
        FROM
            examination_qx_man_sku s
        LEFT JOIN examination_man_group g ON s.group_no = g.group_no
        WHERE company_no=#{companyNo}
        and sku_no=#{skuNo}
        and s.yn = 1
    </select>
    <select id="selectEnterpriseSkuByCompanyNoAndSkuName"
            resultMap="enterpriseSkuMap">
        SELECT
            s.id,
            company_no,
            company_name,
            sku_no,
            sku_name,
            s.sku_alias,
            s.group_no,
            sku_company_price,
            sku_person_price,
            s.plugin_discounts_type,
            s.plugin_discounts_value,
            g.id as g_id,
            g.group_name as g_group_name,
            g.group_no as g_group_no,
            g.group_desc as g_group_desc,
            g.group_suitable as g_group_suitable,
            g.group_suit_man as g_group_suit_man,
            g.group_item_num as g_group_item_num,
            g.age_upper as g_age_upper,
            g.age_floor as g_age_floor,
            g.yn as g_yn
        FROM
            examination_qx_man_sku s
        LEFT JOIN examination_man_group g ON s.group_no = g.group_no
        <where>
            company_no=#{companyNo}
            <if test="skuName!=null and skuName!=''">
                and s.sku_name LIKE CONCAT('%',#{skuName}, '%')
            </if>
            and s.yn = 1
        </where>
    </select>

    <select id="pageSkuByCompanyNo"
            resultMap="enterpriseSkuMap">
        SELECT
            id,
            company_no,
            company_name,
            sku_no,
            sku_name,
            sku_alias,
            group_no,
            sku_company_price,
            sku_person_price,
            sku_upgrade_count,
            examination_notes
        FROM
            examination_qx_man_sku s
        where yn = 1
        and company_no=#{companyNo}
        <if test="skuNo != null and skuNo != ''">
            and sku_no = #{skuNo,jdbcType=VARCHAR}
        </if>
        <if test="skuName != null and skuName != ''">
            and sku_name LIKE CONCAT('%',#{skuName}, '%')
        </if>
        <if test="skuAlias != null and skuAlias != ''">
            and sku_alias LIKE CONCAT('%',#{skuAlias}, '%')
        </if>
    </select>

    <update id="updateEnterpriseSkuCompanyPrice">
        update examination_qx_man_sku
        <set>
            sku_company_price = #{skuCompanyPrice},
            update_user = #{updateUser}
        </set>
        where company_no = #{companyNo}
        and sku_no = #{skuNo}
        and yn = 1
    </update>
    <update id="updateEnterpriseSkuExaminationNote">
        update examination_qx_man_sku
        <set>
            examination_notes = #{examinationNotes,jdbcType=VARCHAR},
            update_user = #{updateUser}
        </set>
        where company_no = #{companyNo}
        and sku_no = #{skuNo}
        and yn = 1
    </update>
    <update id="subSkuUpgradeCount">
        update examination_qx_man_sku
        set sku_upgrade_count = sku_upgrade_count-1
        where company_no=#{companyNo}
        and sku_no=#{skuNo}
        and yn = 1
    </update>
    <update id="updateSkuAlias">
        update examination_qx_man_sku
        set sku_alias = #{skuAlias}
        where company_no=#{companyNo}
        and sku_no = #{skuNo}
        and yn = 1
    </update>

    <!-- 根据companyNo批量获取企销sku信息 -->
    <select id="querySkuNoByCompanyNoList" resultMap="enterpriseSkuMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku
        where
        yn = 1
        and company_no in
        <foreach collection="list" item="companyNo" open="(" close=")" separator=",">
            #{companyNo}
        </foreach>
    </select>
    <select id="querySkuBaseInfo"
            resultMap="enterpriseSkuMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku
        where company_no = #{companyNo} and
        sku_no = #{skuNo} and
        yn = 1
    </select>

    <select id="updateEnterpriseSkuByBaseSkuInfo" resultType="java.lang.Integer">
        update examination_qx_man_sku
        <set>
            <if test="skuName != null and skuName != ''">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                group_no = #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="skuPersonPrice != null">
                sku_person_price = #{skuPersonPrice},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where sku_no = #{skuNo}
        and yn = 1
    </select>

    <update id="removeEnterpriseSkuNo">
        update examination_qx_man_sku
        set yn = 0
        where sku_no = #{skuNo}
          and yn = 1
    </update>

    <select id="queryCompanyNoInUsedBySkuNo" resultType="java.lang.Long">
        select distinct company_no
        from examination_qx_man_sku
        where sku_no = #{skuNo}
          and yn = 1
    </select>

    <update id="updateSkuPluginInfo">
        update examination_qx_man_sku
        set
            plugin_discounts_type = #{pluginDiscountsType},
            plugin_discounts_value = #{pluginDiscountsValue}
        where company_no=#{companyNo}
          and sku_no=#{skuNo}
          and yn = 1
    </update>
</mapper>