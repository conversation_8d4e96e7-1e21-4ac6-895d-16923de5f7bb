<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.UnresolvedReportRecordDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.conclusion.UnresolvedReportRecordEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="jd_appointment_id" jdbcType="BIGINT" property="jdAppointmentId"/>
        <result column="report_result" jdbcType="LONGVARCHAR" property="reportResult"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        jd_appointment_id,
        report_result,
        create_time,
        update_time,
        yn
    </sql>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.conclusion.UnresolvedReportRecordEntity"
            useGeneratedKeys="true">
        insert into unresolved_report_record (jd_appointment_id,
                                              report_result, create_time, update_time, yn)
        values (#{jdAppointmentId,jdbcType=BIGINT},
                #{reportResult,jdbcType=LONGVARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, 1)
    </insert>

    <update id="update"
            parameterType="com.jd.health.medical.examination.domain.conclusion.UnresolvedReportRecordEntity">
        update unresolved_report_record
        <set>
            <if test="reportResult != null and reportResult != ''">
                report_result = #{reportResult,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
          and yn = 1
    </update>

    <select id="select" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.conclusion.UnresolvedReportRecordEntity">
        select
        <include refid="Base_Column_List"/>
        from unresolved_report_record
        <where>
            <if test="jdAppointmentId != null and jdAppointmentId != ''">
                and jd_appointment_id = #{jdAppointmentId}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time > #{createTime}
            </if>
            and yn = 1
        </where>
    </select>

    <update id="deleteByJdIds" parameterType="java.util.List">
        update unresolved_report_record
        set yn=0
                where yn = 1
                  and jd_appointment_id in
        <foreach collection="list" item="jdAppointmentId" open="(" close=")" separator=",">
            #{jdAppointmentId}
        </foreach>
    </update>

    <select id="listConclusions" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from unresolved_report_record
        where yn = 1
    </select>

</mapper>