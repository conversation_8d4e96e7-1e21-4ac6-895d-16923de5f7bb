<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManGroupDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="group_no" property="groupNo" jdbcType="BIGINT"/>
        <result column="group_desc" property="groupDesc" jdbcType="VARCHAR"/>
        <result column="group_suitable" property="groupSuitable" jdbcType="TINYINT"/>
        <result column="group_suit_man" property="groupSuitMan" jdbcType="VARCHAR"/>
        <result column="group_item_num" property="groupItemNum" jdbcType="INTEGER"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="age_upper" property="ageUpper" jdbcType="INTEGER"/>
        <result column="age_floor" property="ageFloor" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
  id, group_name, group_no, group_desc, group_suitable, group_suit_man, group_item_num, yn,
  create_user, create_time, update_user, update_time,age_upper,age_floor
</sql>

    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
    insert into examination_man_group (id, group_name, group_no,
      group_desc, group_suitable, group_suit_man,
      group_item_num, yn, create_user,
      create_time, update_user, update_time,age_upper,age_floor
      )
    values (#{id,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, #{groupNo,jdbcType=BIGINT},
      #{groupDesc,jdbcType=VARCHAR}, #{groupSuitable,jdbcType=TINYINT}, #{groupSuitMan,jdbcType=VARCHAR},
      #{groupItemNum,jdbcType=INTEGER}, #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{ageUpper,jdbcType=INTEGER}, #{ageFloor,jdbcType=INTEGER}
      )
  </insert>
    <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
    update examination_man_group
    set group_name = #{groupName,jdbcType=VARCHAR},
      group_no = #{groupNo,jdbcType=BIGINT},
      group_desc = #{groupDesc,jdbcType=VARCHAR},
      group_suitable = #{groupSuitable,jdbcType=TINYINT},
      group_suit_man = #{groupSuitMan,jdbcType=VARCHAR},
      group_item_num = #{groupItemNum,jdbcType=INTEGER},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select id, group_name, group_no, group_desc, group_suitable, group_suit_man, group_item_num,
    yn, create_user, create_time, update_user, update_time,age_upper,age_floor
    from examination_man_group
    where id = #{id,jdbcType=BIGINT}
  </select>

    <select id="selectOne" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
        select id, group_name, group_no, group_desc, group_suitable, group_suit_man, group_item_num,
        yn, create_user, create_time, update_user, update_time,age_upper,age_floor
        from examination_man_group

        <where>yn=1
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="groupName != null and groupName != ''">
                and group_name = #{groupName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="groupDesc != null and groupDesc != ''">
                and group_desc = #{groupDesc,jdbcType=VARCHAR}
            </if>
            <if test="groupSuitable != null">
                and group_suitable = #{groupSuitable,jdbcType=INTEGER}
            </if>
            <if test="groupSuitMan != null and groupSuitMan != ''">
                and group_suit_man = #{groupSuitMan,jdbcType=VARCHAR}
            </if>
            <if test="groupItemNum != null">
                and group_item_num = #{groupItemNum,jdbcType=INTEGER}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
        </where>
        limit 1
    </select>
    <select id="selectManGroupByGroupNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
    select id, group_name, group_no, group_desc, group_suitable, group_suit_man, group_item_num,age_upper,age_floor
    from examination_man_group
    where group_no=#{groupNo}
        and yn = 1
    </select>
    <select id="selectManGroupByGroupName" resultMap="BaseResultMap">
        select id, group_name, group_no, group_desc, group_suitable, group_suit_man, group_item_num,
        yn, create_user, create_time, update_user, update_time,age_upper,age_floor
        from examination_man_group
        <where>
            <if test="groupName != null and groupName != ''">
                and group_name LIKE CONCAT('%',#{groupName}, '%')
            </if>
            and yn = 1
        </where>
    </select>

    <insert id="insertDynamic" parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
        insert into examination_man_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="groupNo != null">
                group_no,
            </if>
            <if test="groupDesc != null">
                group_desc,
            </if>
            <if test="groupSuitable != null">
                group_suitable,
            </if>
            <if test="groupSuitMan != null">
                group_suit_man,
            </if>
            <if test="groupItemNum != null">
                group_item_num,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="ageFloor != null">
                age_floor,
            </if>
            <if test="ageUpper != null">
                age_upper,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupNo != null">
                #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="groupDesc != null">
                #{groupDesc,jdbcType=VARCHAR},
            </if>
            <if test="groupSuitable != null">
                #{groupSuitable,jdbcType=INTEGER},
            </if>
            <if test="groupSuitMan != null">
                #{groupSuitMan,jdbcType=VARCHAR},
            </if>
            <if test="groupItemNum != null">
                #{groupItemNum,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="ageFloor != null">
                #{ageFloor,jdbcType=INTEGER},
            </if>
            <if test="ageUpper != null">
                #{ageUpper,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <update id="updateDynamic" parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity">
        update examination_man_group
        <set>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupDesc != null">
                group_desc = #{groupDesc,jdbcType=VARCHAR},
            </if>
            <if test="groupSuitable != null">
                group_suitable = #{groupSuitable,jdbcType=INTEGER},
            </if>
            <if test="groupSuitMan != null">
                group_suit_man = #{groupSuitMan,jdbcType=VARCHAR},
            </if>
            <if test="groupItemNum != null">
                group_item_num = #{groupItemNum,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="ageUpper != null">
                age_upper = #{ageUpper,jdbcType=INTEGER},
            </if>
            <if test="ageFloor != null">
                age_floor = #{ageFloor,jdbcType=INTEGER},
            </if>
        </set>
        where  group_no = #{groupNo,jdbcType=BIGINT}
    </update>


    <select id="findPageWithResult" parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity"
            resultMap="BaseResultMap">
        select id, group_name, group_no, group_desc, group_suitable, group_suit_man, group_item_num,
        yn, create_user, create_time, update_user, update_time,age_upper,age_floor
        from examination_man_group
        <where>yn=1
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="groupName != null and groupName != ''">
                and group_name like CONCAT ('%',#{groupName,jdbcType=VARCHAR},'%')
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="groupDesc != null and groupDesc != ''">
                and group_desc = #{groupDesc,jdbcType=VARCHAR}
            </if>
            <if test="groupSuitable != null">
                and group_suitable  like CONCAT('%',#{groupSuitable,jdbcType=INTEGER},'%')
            </if>
            <if test="groupSuitMan != null and groupSuitMan != ''">
                and group_suit_man =#{groupSuitMan,jdbcType=VARCHAR}
            </if>
            <if test="groupItemNum != null">
                and group_item_num = #{groupItemNum,jdbcType=INTEGER}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>

    <!-- 批量查询groupName -->
    <select id="selectGroupNos"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity"
            parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group
        where
        yn = 1
        and group_no in
        <foreach collection="groupNoList" item="groupNo" open="(" close=")" separator=",">
            #{groupNo,jdbcType=BIGINT}
        </foreach>
        order by id desc
    </select>

    <select id="selectExaminationGroupListByComposeId"
            resultMap="BaseResultMap">
        select group_no,group_name from examination_man_group
        where group_no in (select group_no from examination_man_goods_compose where compose_id = #{composeId} and yn = 1)
        and yn = 1
    </select>

    <select id="selectAll"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group
        where yn = 1
    </select>


    <select id="selectListByGroupNos"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group
        where group_no in
        <foreach collection="groupNos" item="groupNo" open="(" close=")" separator=",">
            #{groupNo,jdbcType=BIGINT}
        </foreach>
        and yn = 1
    </select>

    <select id="selectGroupNosByName" resultType="java.lang.Long">
        select
        group_no
        from examination_man_group
        where
        group_name LIKE CONCAT('%',#{groupName}, '%')
        and yn = 1
    </select>

    <!-- 批量查询groupName -->
    <select id="selectByGroupNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity"
            parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group
        where
        yn = 1
        and group_no = #{groupNo,jdbcType=BIGINT}
    </select>

</mapper>