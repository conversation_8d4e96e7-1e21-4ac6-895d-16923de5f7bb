<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ItemRelDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ItemRelEntity">
        <!--@mbg.generated-->
        <!--@Table examination_man_item_rel-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="parent_item_no" jdbcType="BIGINT" property="parentItemNo"/>
        <result column="item_no" jdbcType="BIGINT" property="itemNo"/>
        <result column="parent_item_level" jdbcType="INTEGER" property="parentItemLevel"/>
        <result column="item_level" jdbcType="INTEGER" property="itemLevel"/>
        <result column="item_weight" jdbcType="INTEGER" property="itemWeight"/>
        <result column="operation_user" jdbcType="VARCHAR" property="operationUser"/>
        <result column="yn" jdbcType="TINYINT" property="yn"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, parent_item_no, item_no, parent_item_level, item_level, item_weight, operation_user,
        yn, create_time, update_time
    </sql>

    <insert id="insertRel" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ItemRelEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into examination_man_item_rel (parent_item_no, item_no, parent_item_level,
        item_level, item_weight, operation_user, is_new)
        values (#{parentItemNo,jdbcType=BIGINT}, #{itemNo,jdbcType=BIGINT}, #{parentItemLevel,jdbcType=INTEGER},
        #{itemLevel,jdbcType=INTEGER}, #{itemWeight,jdbcType=INTEGER}, #{operationUser,jdbcType=VARCHAR},
        #{isNew,jdbcType=INTEGER})
    </insert>

    <update id="updateIgnoreNull"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ItemRelEntity">
        update examination_man_item_rel
        <set>
            <if test="parentItemNo != null">
                parent_item_no = #{parentItemNo,jdbcType=BIGINT},
            </if>
            <if test="itemWeight != null">
                item_weight = #{itemWeight,jdbcType=INTEGER},
            </if>
            <if test="operationUser != null and operationUser !=''">
                operation_user = #{operationUser,jdbcType=VARCHAR},
            </if>
        </set>
        where item_no = #{itemNo}
    </update>

    <select id="queryByItemNo" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        where item_no = #{itemNo,jdbcType=BIGINT}
        and yn = 1
        order by item_weight asc
    </select>
    <select id="queryByLevel"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        where item_level = #{itemLevel,jdbcType=BIGINT}
        and yn = 1
        order by item_weight asc
    </select>

    <select id="queryListByParentItemNo"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        where parent_item_no = #{parentItemNo,jdbcType=BIGINT}
        and yn = 1
        order by item_weight
    </select>

    <select id="queryListByItemNos" resultMap="BaseResultMap">
        <if test="itemNos != null and itemNos.size() >0">
            select
            <include refid="Base_Column_List"/>
            from examination_man_item_rel
            where item_no in
            <foreach collection="itemNos" item="itemNo" open="(" separator="," close=")">
                #{itemNo}
            </foreach>
            <if test="itemLevel != null">
                and item_level = #{itemLevel}
            </if>
            and yn = 1
        </if>
    </select>

    <select id="queryLowerLevelRelByItemNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        where item_no = #{itemNo}
        and item_level= #{itemLevel}
        and yn = 1
        and item_level in(1,2,3)
    </select>

    <select id="queryPageNewThreeLevelItem" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        <where>
            <if test="itemNos != null and itemNos.size() > 0">
                and item_no in
                <foreach collection="itemNos" item="itemNo" open="(" separator="," close=")">
                    #{itemNo}
                </foreach>
            </if>
            <if test="parentItemNos != null and parentItemNos.size() > 0">
                and parent_item_no in
                <foreach collection="parentItemNos" item="parentItemNo" open="(" separator="," close=")">
                    #{parentItemNo}
                </foreach>
            </if>
            and item_level = 3
            and is_new = 1
            and yn = 1
        </where>
        order by item_weight asc
    </select>

    <select id="queryListNewByNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        <where>
            <if test="itemNos != null and itemNos.size() > 0">
                and item_no in
                <foreach collection="itemNos" item="itemNo" open="(" separator="," close=")">
                    #{itemNo}
                </foreach>
            </if>
            <if test="parentItemNos != null and parentItemNos.size() > 0">
                and parent_item_no in
                <foreach collection="parentItemNos" item="parentItemNo" open="(" separator="," close=")">
                    #{parentItemNo}
                </foreach>
            </if>
            <if test="isNew != null">
                and is_new = #{isNew,jdbcType=INTEGER}
            </if>
            and yn = 1
        </where>
        order by item_weight asc
    </select>
    <select id="queryListByParam" resultMap="BaseResultMap" parameterType="com.jd.health.medical.examination.domain.bo.BaseItemRelBO">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        <where>
            <if test="itemNos != null and itemNos.size() > 0">
                and item_no in
                <foreach collection="itemNos" item="itemNo" open="(" separator="," close=")">
                    #{itemNo}
                </foreach>
            </if>
            <if test="parentItemNos != null and parentItemNos.size() > 0">
                and parent_item_no in
                <foreach collection="parentItemNos" item="parentItemNo" open="(" separator="," close=")">
                    #{parentItemNo}
                </foreach>
            </if>
            <if test="isNew != null">
                and is_new = #{isNew,jdbcType=INTEGER}
            </if>
            and yn = 1
        </where>
        order by item_weight asc
    </select>


    <insert id="batchInsertRelItem" parameterType="java.util.List" keyProperty="id">
        insert into examination_man_item_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            parent_item_no,
            item_no,
            parent_item_level,
            item_level,
            item_weight,
            operation_user
        </trim>
        values
        <foreach collection="list" index="index" item="rel" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{rel.parentItemNo,jdbcType=BIGINT},
                #{rel.itemNo,jdbcType=BIGINT},
                #{rel.parentItemLevel,jdbcType=INTEGER},
                #{rel.itemLevel,jdbcType=INTEGER},
                #{rel.itemWeight,jdbcType=INTEGER},
                #{rel.operationUser,jdbcType=VARCHAR},
            </trim>
        </foreach>
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_rel
        where yn = 1
        <if test="isNew != null">
            and is_new = #{isNew}
        </if>
        <if test="itemLevelList != null and itemLevelList.size() != 0">
            and item_level in
            <foreach collection="itemLevelList" index="index" item="itemLevel" open="(" separator="," close=")">
                #{itemLevel}
            </foreach>
        </if>
        order by item_weight
    </select>

    <update id="batchDeleteByParentNoAndChildrenNo">
        update examination_man_item_rel
        set yn=0,
        operation_user = #{updateUser,jdbcType=VARCHAR}
        <where>
            yn=1
            and parent_item_no = #{parentItemNo,jdbcType=BIGINT}
            and parent_item_level = #{parentItemLevel,jdbcType=INTEGER}
            and item_no in
            <foreach collection="childrenList" index="index" item="child" open="(" separator="," close=")">
                #{child,jdbcType=BIGINT}
            </foreach>
        </where>
    </update>

    <select id="queryMaxWeightByParentNo" resultType="java.lang.Integer">
        select max(item_weight)
        from examination_man_item_rel
        where parent_item_no = #{parentItemNo,jdbcType=BIGINT}
        and yn=1
    </select>

    <select id="queryMaxWeightByChildrenNo" resultType="java.lang.Integer">
        select max(item_weight)
        from examination_man_item_rel
        where item_no in
        <foreach collection="childrenList" index="index" item="child" open="(" separator="," close=")">
            #{child,jdbcType=BIGINT}
        </foreach>
        and yn=1
    </select>
</mapper>