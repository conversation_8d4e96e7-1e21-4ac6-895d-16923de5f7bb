<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdCallBackDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.third.ThirdCallBackEntity" id="thirdCallBackMap">
        <result property="id" column="id"/>
        <result property="resultType" column="result_type"/>
        <result property="resultCode" column="result_code"/>
        <result property="resultDate" column="result_date"/>
        <result property="resultMsg" column="result_msg"/>
        <result property="jdAppointmentId" column="jd_appointment_id"/>
        <result property="appointmentNo" column="appointment_no"/>
        <result property="channelNo" column="channel_no"/>
        <result property="reportId" column="report_id"/>
        <result property="operatorResult" column="operator_result"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            result_type,
            result_code,
            result_date,
            result_msg,
            jd_appointment_id,
            appointment_no,
            channel_no,
            report_id,
            operator_result,
            yn,
            create_time,
            update_time,
        </trim>
    </sql>


    <!-- 根据条件查询列表 -->
    <select id="queryThirdCallBackList" resultMap="thirdCallBackMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdCallBackEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_callback
        <where>
            <if test="appointmentNo != null">
                and appointment_no = #{appointmentNo,jdbcType=VARCHAR}
            </if>
            <if test="jdAppointmentId != null">
                and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="operatorResult != null">
                and operator_result = #{operatorResult,jdbcType=INTEGER}
            </if>
            <if test="resultType != null">
                and result_type = #{resultType,jdbcType=INTEGER}
            </if>
            <if test="resultDate != null">
                and result_date >= #{resultDate,jdbcType=TIMESTAMP}
            </if>
            <if test="startTime != null">
                and create_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[ <= #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
            and yn = 1
        </where>
    </select>

    <!-- 插入实体 -->
    <insert id="insertThirdCallBack" parameterType="com.jd.health.medical.examination.domain.third.ThirdCallBackEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_third_callback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            result_type,
            result_code,
            result_date,
            result_msg,
            jd_appointment_id,
            appointment_no,
            channel_no,
            report_id,
            operator_result,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{resultType,jdbcType=INTEGER},
            #{resultCode,jdbcType=VARCHAR},
            #{resultDate,jdbcType=TIMESTAMP},
            #{resultMsg,jdbcType=VARCHAR},
            #{jdAppointmentId,jdbcType=BIGINT},
            #{appointmentNo,jdbcType=VARCHAR},
            #{channelNo,jdbcType=BIGINT},
            #{reportId,jdbcType=VARCHAR},
            #{operatorResult,jdbcType=VARCHAR},
            1,
            now(),
            now(),
        </trim>
    </insert>

    <!--更新回调记录状态-->
    <update id="updateThirdCallBackStatus" parameterType="com.jd.health.medical.examination.domain.third.ThirdCallBackEntity" >
        update examination_man_third_callback
        <set>
            operator_result = #{operatorResult, jdbcType = INTEGER}
        </set>
        WHERE jd_appointment_id = #{jdAppointmentId, jdbcType = BIGINT}
        AND result_type = #{resultType, jdbcType = INTEGER}
        AND yn=1
    </update>

</mapper>