<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.EmployeeImportRecordDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.employeeImport.ImportRecordInfo">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="company_no" jdbcType="BIGINT" property="companyNo" />
    <result column="operation_type" jdbcType="TINYINT" property="operationType" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="failed_count" jdbcType="INTEGER" property="failedCount" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="import_status" jdbcType="INTEGER" property="importStatus" />
    <result column="appointment_handled_stage" jdbcType="INTEGER" property="appointmentHandledStage" />
    <result column="origin_file_name" jdbcType="VARCHAR" property="originFileName" />
    <result column="origin_file_url" jdbcType="VARCHAR" property="originFileUrl" />
    <result column="result_file_url" jdbcType="VARCHAR" property="resultFileUrl" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="fail_msg" jdbcType="VARCHAR" property="failMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, record_id, company_no, operator_id, operation_type,import_status,appointment_handled_stage,
    create_time, success_count, failed_count, total_count, origin_file_name, origin_file_url,
    result_file_url, update_time, fail_msg
  </sql>

    <insert id="addImportRecord" parameterType="com.jd.health.medical.examination.domain.employeeImport.ImportRecordInfo"
            useGeneratedKeys="true" keyProperty="id">
    insert into examination_man_import_record(
      record_id,
      company_no,
      operator_id,
      operation_type,
      origin_file_name,
      origin_file_url,
      import_status,
      appointment_handled_stage,
      total_count,
      success_count,
      failed_count,
      result_file_url,
      fail_msg
    )values(
      #{recordId},
      #{companyNo},
      #{operatorId},
      #{operationType},
      #{originFileName},
      #{originFileUrl},
      #{importStatus,jdbcType=INTEGER},
      #{appointmentHandledStage,jdbcType=INTEGER},
      #{totalCount,jdbcType=INTEGER},
      #{successCount,jdbcType=INTEGER},
      #{failedCount,jdbcType=INTEGER},
      #{resultFileUrl,jdbcType=VARCHAR},
      #{failMsg,jdbcType=VARCHAR}
    )
  </insert>

  <!--更新导入记录-->
  <update id="updateImportRecord" parameterType="com.jd.health.medical.examination.domain.employeeImport.ImportRecordInfo">
      update examination_man_import_record
      <set>
        <if test="totalCount != null">
          total_count = #{totalCount,jdbcType=INTEGER},
        </if>
        <if test="successCount != null">
          success_count = #{successCount,jdbcType=INTEGER},
        </if>
        <if test="failedCount != null">
          failed_count = #{failedCount,jdbcType=INTEGER},
        </if>
        <if test="resultFileUrl != null and resultFileUrl != ''">
          result_file_url = #{resultFileUrl,jdbcType=VARCHAR},
        </if>
        <if test="originFileUrl != null and originFileUrl != ''">
          origin_file_url = #{originFileUrl,jdbcType=VARCHAR},
        </if>
        update_time = now(),
        <if test="importStatus != null">
          import_status = #{importStatus,jdbcType=INTEGER},
        </if>
        <if test="appointmentHandledStage != null">
          appointment_handled_stage = #{appointmentHandledStage,jdbcType=INTEGER},
        </if>
        <if test="failMsg != null and failMsg != ''">
          fail_msg = #{failMsg,jdbcType=VARCHAR},
        </if>
      </set>
      where record_id = #{recordId,jdbcType=BIGINT}
      <if test="companyNo != null">
        and company_no = #{companyNo,jdbcType=BIGINT}
      </if>
      and yn = 1
  </update>

  <!--查询记录详情-->
    <select id="queryRecordInfo" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from examination_man_import_record
      where yn = 1
      and record_id = #{recordId}
   </select>

  <!--查询记录总数-->
  <select id="getCountByPage" resultType="java.lang.Integer">
    select count(1)
    from examination_man_import_record
    where yn = 1
    and company_no = #{companyNo}
  </select>

  <!--查询记录列表  分页可用-->
  <select id="selectByPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    examination_man_import_record
    where yn = 1
    and company_no=#{companyNo,jdbcType=BIGINT}
    <if test="operationTypes != null and operationTypes.size() != 0">
      and operation_type in
      <foreach collection="operationTypes" index="index" item="item" open="(" separator="," close=")">
                   #{item}
      </foreach>
    </if>
    ORDER BY
    create_time
    DESC
  </select>

    <!--查询最近三天导出记录详情-->
    <select id="queryRecordInfoByOperator" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from
      examination_man_import_record
      where yn = 1
      and operation_type = #{operationType}
      <if test="createTime != null">
        and create_time > #{createTime}
      </if>
      <if test="operatorId != null">
        and operator_id = #{operatorId}
      </if>
    </select>

  <select id="queryImportRecord" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from
    examination_man_import_record
    <where>
      <if test="recordId != null">
        record_id = #{recordId}
      </if>
      <if test="operatorId != null and operatorId != ''">
        and operator_id = #{operatorId}
      </if>
      <if test="operationType != null">
        and operation_type = #{operationType}
      </if>
      <if test="importStatus != null">
        and import_status = #{importStatus}
      </if>
      <if test="companyNo != null">
        and company_no = #{companyNo}
      </if>
      and yn = 1
    </where>
  </select>

</mapper>