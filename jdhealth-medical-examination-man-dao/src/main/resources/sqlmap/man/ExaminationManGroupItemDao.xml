<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManGroupItemDao">

    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="group_no" jdbcType="BIGINT" property="groupNo"/>
        <result column="item_no" jdbcType="BIGINT" property="itemNo"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="important" jdbcType="INTEGER" property="important"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="relation_no" jdbcType="VARCHAR" property="relationNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        group_no,
        item_no,
        item_name,
        important,
        yn,
        create_user,
        create_time,
        update_user,
        update_time,
        relation_no
    </sql>

    <insert id="insertDynamic"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        insert into examination_man_group_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="groupNo != null">
                group_no,
            </if>
            <if test="itemNo != null">
                item_no,
            </if>
            <if test="itemName != null">
                item_name,
            </if>
            <if test="important != null">
                important,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="relationNo != null">
                relation_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="groupNo != null">
                #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="itemNo != null">
                #{itemNo,jdbcType=BIGINT},
            </if>
            <if test="itemName != null">
                #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="important != null">
                #{important,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="relationNo != null">
                #{relationNo,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>


    <update id="updateDynamic"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        update examination_man_group_item
        <set>
            <if test="important != null">
                important = #{important,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="itemName != null">
                item_name=#{itemName,jdbcType=VARCHAR},
            </if>
            <if test="relationNo != null">
                relation_no = #{relationNo,jdbcType=VARCHAR},
            </if>
        </set>
        where
        group_no = #{groupNo,jdbcType=BIGINT}
        and item_no = #{itemNo,jdbcType=BIGINT}
        and yn=1
    </update>

    <select id="selectByGroupNoAndItemNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where group_no = #{groupNo,jdbcType=BIGINT}
        and item_no = #{itemNo} and yn=1 limit 1
    </select>


    <select id="findPageWithResult"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        <where>
            yn=1
            <if test="groupIds != null and groupIds.size() > 0">
                and group_no in
                <foreach item="groupNo" collection="groupIds" separator="," open="(" close=")" index="">
                    #{groupNo, jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="groupNo != null">
                and group_no = #{groupNo,jdbcType=BIGINT}
            </if>
            <if test="itemNo != null">
                and item_no = #{itemNo,jdbcType=BIGINT}
            </if>
            <if test="important != null">
                and important = #{important,jdbcType=INTEGER}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="itemName != null">
                and item_name=#{itemName,jdbcType=VARCHAR}
            </if>
            <if test="relationNo != null">
                and relation_no=#{relationNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByGroupNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where group_no=#{groupNo} and yn = 1
    </select>

    <select id="selectByGroupNos"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where
            group_no in
            <foreach collection="groupNos" item="groupNo" open="(" separator="," close=")">
                #{groupNo}
            </foreach>
        and yn = 1
    </select>

    <select id="selectImportItemByGroupNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where group_no=#{groupNo} and important = 1 and yn = 1
    </select>

    <!--查询出体检项目组中的体检项目编号和名称-->
    <select id="selectItemByGroupNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where yn = 1
        <if test="groupNo != null">
            and group_no = #{groupNo,jdbcType=BIGINT}
        </if>
    </select>

    <update id="deleteGroupItemByGroupNoWithoutItemNo" parameterType="java.lang.Long">
        update examination_man_group_item
        set yn=0
        where group_no = #{groupNo,jdbcType=BIGINT}
        <if test="itemNo !=null">
            and item_no not in
            <foreach item="item" collection="itemNo" separator="," open="(" close=")" index="">
                #{item, jdbcType=BIGINT}
            </foreach>
        </if>
    </update>

    <update id="deleteGroupItemByGroupNoAndItemNo">
        update examination_man_group_item
        set yn=0
        where group_no = #{groupNo,jdbcType=BIGINT}
        <if test="itemNo !=null">
            and item_no in
            <foreach item="item" collection="itemNo" separator="," open="(" close=")" index="">
                #{item, jdbcType=BIGINT}
            </foreach>
        </if>
    </update>

    <select id="selectImportItemByGroupNos"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        <where>
            <if test="groupNos != null">
                and group_no in
                <foreach item="groupNo" collection="groupNos" separator="," open="(" close=")" index="">
                    #{groupNo, jdbcType=BIGINT}
                </foreach>
            </if>
            and important = 1
            and yn = 1
        </where>
    </select>

    <select id="selectItemByRelationNos"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where relation_no in
        <foreach collection="relationNos" item="relationNo" open="(" close=")" separator=",">
            #{relationNo}
        </foreach>
        and yn = 1
    </select>
    <select id="listByRelationNo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where relation_no = #{relationNo,jdbcType=VARCHAR}
        and yn = 1
    </select>

    <insert id="addGroupItemBatch">
        insert into examination_man_group_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
                item_no,
                item_name,
                create_user,
                create_time,
                relation_no,
        </trim>
        values
        <foreach collection="itemList" item="entity" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                    #{entity.itemNo,jdbcType=BIGINT},
                    #{entity.itemName,jdbcType=VARCHAR},
                    #{entity.createUser,jdbcType=VARCHAR},
                    now(),
                    #{entity.relationNo,jdbcType=TIMESTAMP},
            </trim>
        </foreach>
    </insert>
    <update id="deleteGroupItemBatch">
    update  examination_man_group_item
    set yn = 0, update_time = now(), update_user = #{updateUser}
    where relation_no = #{relationNo}
    and yn = 1
  </update>

    <select id="selectCountByRelationNo" resultType="java.lang.Integer">
        select count(*)
        from examination_man_group_item
        where  relation_no = #{relationNo}
        and yn = 1
    </select>

    <select id="selectGroupItemRelationNo" resultType="com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_group_item
        where  relation_no is not null
          and yn = 1
    </select>
</mapper>