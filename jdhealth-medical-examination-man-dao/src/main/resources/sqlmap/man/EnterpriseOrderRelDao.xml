<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseOrderRelDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOrderRelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_no" property="companyNo" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_rel_time" property="orderRelTime" jdbcType="TIMESTAMP"/>
        <result column="order_price" property="orderPrice" jdbcType="INTEGER"/>
        <result column="remnant_sum" property="remnantSum" jdbcType="INTEGER"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="insertOrderRel">
    insert into examination_qx_man_company_order_rel(company_no, order_id, order_rel_time, order_price, create_time, create_user)
    values (#{companyNo},#{orderId},now(),#{orderPrice},now(),#{createUser})
  </insert>
    <select id="selectAll" resultMap="BaseResultMap">
    select id, company_no, order_id, order_rel_time, order_price, yn, create_time, create_user, 
    update_time, update_user
    from examination_qx_man_company_order_rel
  </select>
    <select id="selectOrderRelByOrderId"
            resultMap="BaseResultMap">
      select id, company_no, order_id, order_rel_time, order_price
      from examination_qx_man_company_order_rel
      where order_id=#{orderId}
      and yn = 1
      limit 1
    </select>
    <select id="selectOrderRelByCompanyNo"
            resultMap="BaseResultMap">
      select company_no, order_id, order_rel_time, order_price
      from examination_qx_man_company_order_rel
      where company_no=#{companyNo}
      and yn = 1
      limit 1
    </select>
    <select id="selectFirstOrderRelByCompanyNo"
            resultMap="BaseResultMap">
      select id, company_no, order_id, order_rel_time, order_price, yn, create_time, create_user,
    update_time, update_user,remnant_sum
      from examination_qx_man_company_order_rel
      where company_no=#{companyNo}
      and yn = 1
      order by order_rel_time asc
      limit 1
    </select>
    <update id="updateRemnantSum">
        update examination_qx_man_company_order_rel
        set
        <if test="operation !=null and operation==2">
            remnant_sum=remnant_sum-#{sum}
        </if>
        <if test="operation ==null or operation==1">
            remnant_sum=order_price-#{sum}
        </if>
        where company_no=#{companyNo}
        and order_id=#{orderId}
        <if test="operation !=null and operation==2">
           and remnant_sum >=  #{sum}
        </if>
        <if test="operation ==null or operation==1">
           and  order_price >=  #{sum}
        </if>
    </update>
</mapper>