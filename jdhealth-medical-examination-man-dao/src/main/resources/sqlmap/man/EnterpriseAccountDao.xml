<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseAccountDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity" id="enterpriseAccountMap">
        <result property="id" column="id"/>
        <result property="customerNo" column="customer_no"/>
        <result property="customerSimpleName" column="customer_simple_name"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="accountNo" column="account_no"/>
        <result property="accountPwd" column="account_pwd"/>
        <result property="accountPhone" column="account_phone"/>
        <result property="expireDate" column="expire_date"/>
        <result property="accountStatus" column="account_status"/>
        <result property="createUser" column="create_user"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isMain" column="is_main"/>
        <result property="accountEmail" column="account_email"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            customer_no,
            customer_simple_name,
            company_no,
            company_name,
            account_no,
            account_pwd,
            account_phone,
            expire_date,
            account_status,
            create_user,
            yn,
            create_time,
            update_time,
            is_main,
            account_email,
        </trim>
    </sql>

    <!-- 根据Id查询 -->
    <select id="selectEnterpriseAccountById" resultMap="enterpriseAccountMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_account
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryEnterpriseAccountList" resultMap="enterpriseAccountMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_account
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="companyName != null">
                and company_name = #{companyName,jdbcType=VARCHAR}
            </if>
            <if test="accountNo != null">
                and account_no = #{accountNo,jdbcType=VARCHAR}
            </if>
            <if test="accountPwd != null">
                and account_pwd = #{accountPwd,jdbcType=VARCHAR}
            </if>
            <if test="accountPhone != null">
                and account_phone = #{accountPhone,jdbcType=CHAR}
            </if>
            <if test="expireDate != null">
                and expire_date = #{expireDate,jdbcType=DATETIME}
            </if>
            <if test="accountStatus != null">
                and account_status = #{accountStatus,jdbcType=TINYINT}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test = "isMain != null">
                and is_main = #{isMain,jdbcType=TINYINT}
            </if>
            and yn = 1
        </where>
    </select>
    <select id="queryCompanyNoByPin" resultType="java.lang.Long">
        select company_no from examination_qx_man_account
        where account_no=#{accountNo}
        and yn = 1
    </select>

    <select id="queryAccountInfoByPin"  resultMap="enterpriseAccountMap">
        select
         <include refid="Base_Column_List"/>
         from examination_qx_man_account
        where account_no=#{accountNo}
        and yn = 1
    </select>

    <!-- 插入实体 -->
    <insert id="insertEnterpriseAccount" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_qx_man_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            customer_no,
            customer_simple_name,
            company_no,
            company_name,
            account_no,
            account_pwd,
            account_phone,
            expire_date,
            account_status,
            is_main,
            create_user,
            yn,
            create_time,
            update_time,
            account_email,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{customerNo,jdbcType=BIGINT},
            #{customerSimpleName,jdbcType=VARCHAR},
            #{companyNo,jdbcType=BIGINT},
            #{companyName,jdbcType=VARCHAR},
            #{accountNo,jdbcType=VARCHAR},
            #{accountPwd,jdbcType=VARCHAR},
            #{accountPhone,jdbcType=CHAR},
            #{expireDate,jdbcType=DATE},
            #{accountStatus,jdbcType=TINYINT},
            #{isMain,jdbcType=TINYINT},
            #{createUser,jdbcType=VARCHAR},
            1,
            now(),
            now(),
            #{accountEmail,jdbcType=VARCHAR},
        </trim>
    </insert>

    <!-- 修改实体 -->
    <update id="updateEnterpriseAccount"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity">
        update examination_qx_man_account
        <set>
            yn = 1
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 删除实体 -->
    <update id="deleteEnterpriseAccountById" parameterType="LONG">
        update examination_qx_man_account
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="queryHrPhoneByCompanyNo" resultType="java.lang.String"
            parameterType="java.lang.Long">
        select
          account_phone
        from examination_qx_man_account
        <where>
            and yn = 1
            and account_status = 1
            and is_main = 0
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectPwdByAccountNo" resultType="java.lang.String">
        select
        account_pwd
        from examination_qx_man_account
        <where>
            and yn = 1
            and account_no = #{accountNo}
        </where>
    </select>

    <!-- 根据accountNo更新账号状态 -->
    <update id="updateUserStatusByAccountNo"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity">
        update examination_qx_man_account
        <set>
            account_status = #{accountStatus,jdbcType=INTEGER}
        </set>
        where account_no = #{accountNo,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 根据accountNo更新账号状态 -->
    <update id="updateAccountByAccountNo"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity">
        update examination_qx_man_account
        <set>
            <if test="accountEmail != null and accountEmail != ''">
                account_email = #{accountEmail,jdbcType=VARCHAR},
            </if>
            <if test = "accountStatus != null">
                account_status = #{accountStatus,jdbcType=INTEGER},
            </if>
        </set>
        where account_no = #{accountNo,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <select id="queryLeaderAccountByProvider" resultMap="enterpriseAccountMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_account
        where company_no =#{companyNo}
        and is_main = 1
        and yn = 1
    </select>
    <select id="queryAccountNoAvailable" resultMap="enterpriseAccountMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_account
        where
        yn = 1
        and is_main = 0
        and company_no = #{channelNo,jdbcType=VARCHAR}
        <if test="accountNo != null and accountNo != ''">
            and account_no like CONCAT(#{accountNo,jdbcType=VARCHAR},"%")
        </if>
        <if test="accountNoList != null and accountNoList.size() > 0">
            and account_no NOT IN
            <foreach collection="accountNoList" item="item" open="("  separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryAccountByAccountNos" resultMap="enterpriseAccountMap">
        <if test="accountNos != null and accountNos.size() > 0">
            select
            <include refid="Base_Column_List"/>
            from examination_qx_man_account
            where
            yn = 1
            and is_main = 0
            and account_status = 1
            and account_no IN
            <foreach collection="accountNos" item="item" open="("  separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>