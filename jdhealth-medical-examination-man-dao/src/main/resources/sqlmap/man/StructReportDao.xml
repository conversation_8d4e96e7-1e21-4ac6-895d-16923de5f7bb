<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.StructReportDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.StructReportEntity" id="structReportMap">
        <result property="id" column="id"/>
        <result property="channelNo" column="channel_no"/>
        <result property="jdAppointmentId" column="jd_appointment_id"/>
        <result property="structReportStr" column="struct_report_str"/>
        <result property="extendInfo" column="extend_info"/>
        <result property="reportOss" column="report_oss"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="reportSource" column="report_source"/>
        <result property="sourceOss" column="source_oss"/>
        <result property="snapshotOss" column="snapshot_oss"/>
        <result property="structReportOss" column="struct_report_oss"/>
        <result property="version" column="version"/>
        <result property="structReportId" column="struct_report_id"/>
        <result property="jdStructReportOss" column="jd_struct_report_oss"/>
        <result property="reportId" column="report_id"/>
        <result property="reportType" column="report_type"/>
        <result property="reportLabel" column="report_label"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            channel_no,
            jd_appointment_id,
            struct_report_str,
            extend_info,
            report_oss,
            yn,
            create_time,
            update_time,
            create_user,
            report_source,
            source_oss,
            snapshot_oss,
            struct_report_oss,
            version,
            struct_report_id,
            jd_struct_report_oss,
            report_id,
            report_type,
            report_label,
            business_type,
            extend_id
        </trim>
    </sql>

    <!-- 查询结构化报告列表 -->
    <select id="getStructReportList" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where yn = 1
        <if test="channelNo != null">
            and channel_no = #{channelNo,jdbcType=BIGINT}
        </if>
        <if test="jdAppointmentId != null">
            and jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        </if>
        <if test="structReportId != null and structReportId != ''">
            and struct_report_id = #{structReportId,jdbcType=VARCHAR}
        </if>
        <if test="reportSource != null">
            and report_source = #{reportSource,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 插入实体 -->
    <insert id="insertStructReport" parameterType="com.jd.health.medical.examination.domain.StructReportEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_struct_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            channel_no,
            jd_appointment_id,
            struct_report_str,
            extend_info,
            report_oss,
            yn,
            create_time,
            update_time,
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="sourceOss != null and sourceOss != ''">
                source_oss,
            </if>
            <if test="snapshotOss != null and snapshotOss != ''">
                snapshot_oss,
            </if>
            <if test="structReportOss != null and structReportOss != ''">
                struct_report_oss,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="structReportId != null and structReportId != ''">
                struct_report_id,
            </if>
            <if test="reportSource != null">
                report_source,
            </if>
            <if test="jdStructReportOss != null and jdStructReportOss != '' != null">
                jd_struct_report_oss,
            </if>
            <if test="reportId != null and reportId != '' != null">
                report_id,
            </if>
            report_type,
            report_label,
            business_type,
            extend_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{channelNo,jdbcType=BIGINT},
            #{jdAppointmentId,jdbcType=BIGINT},
            #{structReportStr,jdbcType=LONGVARCHAR},
            #{extendInfo,jdbcType=VARCHAR},
            #{reportOss,jdbcType=VARCHAR},
            1,
            now(),
            now(),
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="sourceOss != null and sourceOss != ''">
                #{sourceOss,jdbcType=VARCHAR},
            </if>
            <if test="snapshotOss != null and snapshotOss != ''">
                #{snapshotOss,jdbcType=VARCHAR},
            </if>
            <if test="structReportOss != null and structReportOss != ''">
                #{structReportOss,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="structReportId != null and structReportId != ''">
                #{structReportId,jdbcType=VARCHAR},
            </if>
            <if test="reportSource != null">
                #{reportSource},
            </if>
            <if test="jdStructReportOss != null and jdStructReportOss != '' != null">
                #{jdStructReportOss,jdbcType=VARCHAR},
            </if>
            <if test="reportId != null and reportId != '' != null">
                #{reportId,jdbcType=VARCHAR},
            </if>
            #{reportType,jdbcType=TINYINT},
            #{reportLabel,jdbcType=TINYINT},
            #{businessType,jdbcType=INTEGER},
            #{extendId,jdbcType=VARCHAR}
        </trim>
    </insert>

    <update id="updateStructReport" parameterType="com.jd.health.medical.examination.domain.StructReportEntity">
        update examination_struct_report
        <set>
            <if test="structReportStr != null">
                struct_report_str = #{structReportStr,jdbcType=LONGVARCHAR}
            </if>
            <if test="jdStructReportOss != null and jdStructReportOss != ''">
                jd_struct_report_oss = #{jdStructReportOss,jdbcType=LONGVARCHAR}
            </if>
        </set>
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and struct_report_id = #{structReportId,jdbcType=VARCHAR}
    </update>

    <update id="updateStructReportOssByStructId">
        update examination_struct_report
        <set>
            <if test="structReportOss != null">
                struct_report_oss = #{structReportOss,jdbcType=VARCHAR}
            </if>
        </set>
        where struct_report_id = #{structReportId,jdbcType=BIGINT}
          and yn = 1
    </update>

    <update id="updateStructReportSnapshotOssByStructId">
        update examination_struct_report
        <set>
            <if test="snapshotOss != null and snapshotOss != ''">
                snapshot_oss = #{snapshotOss,jdbcType=VARCHAR}
            </if>
        </set>
        where struct_report_id = #{structReportId,jdbcType=BIGINT}
        and yn = 1
    </update>

    <select id="getMaxVersionByJdId" resultType="java.lang.Integer">
        select version
        from examination_struct_report
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
          and yn = 1 order by version desc limit 1
    </select>

    <select id="getStructReportInfoByStructId" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where struct_report_id = #{structReportId,jdbcType=VARCHAR}
        and yn = 1 order by version desc limit 1
    </select>

    <select id="getStructReportInfoByJdAppointmentId" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
        and yn = 1 order by version desc limit 1
    </select>

    <select id="getStructReportInfoByExtendId" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where extend_id = #{extendId,jdbcType=VARCHAR}
        and yn = 1 order by version desc limit 1
    </select>
    <select id="getStructReportInfoByReportId" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where report_id = #{reportId,jdbcType=VARCHAR}
        and yn = 1 order by version desc limit 1
    </select>
    <select id="getStructReportInfoByStructIds" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where struct_report_id in
        <foreach collection="structReportIds" item="structId" open="(" separator="," close=")">
            #{structId}
        </foreach>
        and yn = 1
    </select>

    <select id="getMaxVerStructReportByNo" resultMap="structReportMap">
        select
        <include refid="Base_Column_List"/>
        from examination_struct_report
        where jd_appointment_id = #{jdAppointmentId,jdbcType=BIGINT}
          and yn = 1 order by version, update_time desc limit 1
    </select>
</mapper>