<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseInfoConfigItemDao">

    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigItemEntity">
        <result column="id" property="id"/>
        <result column="config_item_id" property="configItemId"/>
        <result column="company_no" property="companyNo"/>
        <result column="service_name" property="serviceName"/>
        <result column="service_desc" property="serviceDesc"/>
        <result column="service_link" property="serviceLink"/>
        <result column="service_equity_type" property="serviceEquityType"/>
        <result column="avail_crowd" property="availCrowd"/>
        <result column="equity_no_status" property="equityNoStatus"/>
        <result column="equity_tips" property="equityTips"/>
        <result column="equity_examination_relation" property="equityExaminationRelation"/>
        <result column="logo_color_url" property="logoColorUrl"/>
        <result column="logo_grey_url" property="logoGreyUrl"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="yn" property="yn"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,
        config_item_id,
        company_no,
        service_name,
        service_desc,
        service_link,
        service_equity_type,
        avail_crowd,
        equity_no_status,
        equity_tips,
        equity_examination_relation,
        logo_color_url,
        logo_grey_url,
        sort,
        create_time,
        update_time,
        yn,
        contract_types
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigItemEntity">
        INSERT INTO examination_qx_man_company_config_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test='null != configItemId'>
                config_item_id,
            </if>
            <if test='null != companyNo'>
                company_no,
            </if>
            <if test='null != serviceName'>
                service_name,
            </if>
            <if test='null != serviceDesc'>
                service_desc,
            </if>
            <if test='null != serviceLink'>
                service_link,
            </if>
            <if test='null != serviceEquityType'>
                service_equity_type,
            </if>
            <if test='null != availCrowd'>
                avail_crowd,
            </if>
            <if test='null != equityNoStatus'>
                equity_no_status,
            </if>
            <if test='null != equityTips'>
                equity_tips,
            </if>
            <if test='null != equityExaminationRelation'>
                equity_examination_relation,
            </if>
            <if test='null != logoColorUrl'>
                logo_color_url,
            </if>
            <if test='null != logoGreyUrl'>
                logo_grey_url,
            </if>
            <if test='null != sort'>
                sort,
            </if>
            <if test='null != createTime'>
                create_time,
            </if>
            <if test='null != updateTime'>
                update_time,
            </if>
            <if test='null != yn'>
                yn
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test='null != configItemId'>
                #{configItemId},
            </if>
            <if test='null != companyNo'>
                #{companyNo},
            </if>
            <if test='null != serviceName'>
                #{serviceName},
            </if>
            <if test='null != serviceDesc'>
                #{serviceDesc},
            </if>
            <if test='null != serviceLink'>
                #{serviceLink},
            </if>
            <if test='null != serviceEquityType'>
                #{serviceEquityType},
            </if>
            <if test='null != availCrowd'>
                #{availCrowd},
            </if>
            <if test='null != equityNoStatus'>
                #{equityNoStatus},
            </if>
            <if test='null != equityTips'>
                #{equityTips},
            </if>
            <if test='null != equityExaminationRelation'>
                #{equityExaminationRelation},
            </if>
            <if test='null != logoColorUrl'>
                #{logoColorUrl},
            </if>
            <if test='null != logoGreyUrl'>
                #{logoGreyUrl},
            </if>
            <if test='null != sort'>
                #{sort},
            </if>
            <if test='null != createTime'>
                #{createTime},
            </if>
            <if test='null != updateTime'>
                #{updateTime},
            </if>
            <if test='null != yn'>
                #{yn}
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE FROM examination_qx_man_company_config_item
        WHERE company_no = #{companyNo}
    </delete>

    <update id="update"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigItemEntity">
        UPDATE examination_qx_man_company_config_item
        <set>
            service_name = #{serviceName},
            service_desc = #{serviceDesc},
            service_link = #{serviceLink},
            service_equity_type = #{serviceEquityType},
            avail_crowd = #{availCrowd},
            equity_no_status = #{equityNoStatus},
            equity_tips = #{equityTips},
            equity_examination_relation = #{equityExaminationRelation},
            contract_types = #{contractTypes},
        </set>
        WHERE config_item_id = #{configItemId}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM examination_qx_man_company_config_item
        WHERE config_item_id = #{configItemId}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM examination_qx_man_company_config_item
        WHERE company_no = #{companyNo}
    </select>
    <select id="queryCompanyNoAll" resultType="java.lang.Long">
        select distinct company_no from examination_qx_man_company_config_item
    </select>


</mapper>