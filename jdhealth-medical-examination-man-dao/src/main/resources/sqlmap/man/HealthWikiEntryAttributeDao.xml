<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.HealthWikiEntryAttributeDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.wiki.HealthWikiEntryAttribute">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="attribute_name" jdbcType="VARCHAR" property="attributeName" />
    <result column="attribute_type" jdbcType="INTEGER" property="attributeType" />

    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="parent_attribute_id" jdbcType="VARCHAR" property="parentAttributeId" />
    <result column="parent_content_id" jdbcType="VARCHAR" property="parentContentId" />
    <result column="attribute_level" jdbcType="INTEGER" property="attributeLevel" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="input_type" jdbcType="INTEGER" property="inputType" />
    <result column="must" jdbcType="INTEGER" property="must" />
    <result column="multi" jdbcType="INTEGER" property="multi" />
    <result column="input_limited_words" jdbcType="INTEGER" property="inputLimitedWords" />
    <result column="number_maximum" jdbcType="DECIMAL" property="numberMaximum" />
    <result column="number_minimum" jdbcType="DECIMAL" property="numberMinimum" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="c_show" jdbcType="INTEGER" property="cShow" />
    <result column="aline_type" jdbcType="INTEGER" property="alineType" />
    <result column="icon_link" jdbcType="VARCHAR" property="iconLink" />
    <result column="show_attr_name" jdbcType="INTEGER" property="showAttrName" />
    <result column="replace_parent_value" jdbcType="INTEGER" property="replaceParentValue" />
    <result column="event_set" jdbcType="INTEGER" property="eventSet" />
    <result column="tip" jdbcType="VARCHAR" property="tip" />
    <result column="decimal_point" jdbcType="INTEGER" property="decimalPoint" />

    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    attribute_id, attribute_name, attribute_type, category_id, parent_attribute_id, parent_content_id, attribute_level,
    memo, input_type, must, multi, input_limited_words, number_maximum, number_minimum, unit, sort, c_show, aline_type,
    icon_link, show_attr_name, replace_parent_value, enable, event_set,tip,decimal_point, yn, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByParam" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntryAttribute" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_wiki_entry_attribute
    <where>
      <if test="attributeId != null and attributeId != ''">
        and attribute_id = #{attributeId,jdbcType=VARCHAR}
      </if>
      <if test="attributeName != null and attributeName != ''">
        and attribute_name = #{attributeName,jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntryAttribute" useGeneratedKeys="true">
    insert into health_wiki_entry_attribute
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="attributeId != null">
        attribute_id,
      </if>
      <if test="attributeName != null">
        attribute_name,
      </if>
      <if test="attributeType != null">
        attribute_type,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="attributeId != null">
        #{attributeId,jdbcType=VARCHAR},
      </if>
      <if test="attributeName != null">
        #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="attributeType != null">
        #{attributeType,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="insertAttributeList" parameterType="java.util.List">
    insert into health_wiki_entry_attribute
    (attribute_id, attribute_name, attribute_type, category_id, parent_attribute_id, parent_content_id, attribute_level,
    memo, input_type, must, multi, input_limited_words, number_maximum, number_minimum, unit, sort, c_show, aline_type,
    icon_link, show_attr_name, replace_parent_value, event_set,tip,decimal_point,create_user,update_user)
    values
    <foreach collection="attributeList" item="attribute" separator=",">
      (#{attribute.attributeId},#{attribute.attributeName},#{attribute.attributeType},#{attribute.categoryId},
      #{attribute.parentAttributeId},#{attribute.parentContentId},#{attribute.attributeLevel},
      #{attribute.memo},#{attribute.inputType},#{attribute.must},
      #{attribute.multi},#{attribute.inputLimitedWords},#{attribute.numberMaximum},
      #{attribute.numberMinimum},#{attribute.unit},#{attribute.sort},
      #{attribute.cShow},#{attribute.alineType},#{attribute.iconLink},
      #{attribute.showAttrName},#{attribute.replaceParentValue},#{attribute.eventSet},
      #{attribute.tip},#{attribute.decimalPoint},#{attribute.createUser},#{attribute.updateUser})
    </foreach>
  </insert>

  <update id="updateAttributeList" parameterType="java.util.List">
    <foreach collection="attributeList" item="item" open="" close="" separator=";">
      update health_wiki_entry_attribute
      <set>
        <if test="item.enable != null">
          enable = #{item.enable,jdbcType=VARCHAR},
        </if>
        <if test="item.attributeType != null">
          attribute_type = #{item.attributeType,jdbcType=INTEGER},
        </if>
        <if test="item.inputType != null">
          input_type = #{item.inputType,jdbcType=INTEGER},
        </if>
        <if test="item.attributeName != null and item.attributeName != ''">
          attribute_name = #{item.attributeName,jdbcType=VARCHAR},
        </if>
        <if test="item.tip != null and item.tip != ''">
          tip = #{item.tip,jdbcType=VARCHAR},
        </if>
        <if test="item.must != null">
          must = #{item.must,jdbcType=INTEGER},
        </if>
        <if test="item.cShow != null">
          c_show = #{item.cShow,jdbcType=INTEGER},
        </if>
        <if test="item.alineType != null">
          aline_type = #{item.alineType,jdbcType=INTEGER},
        </if>
        <if test="item.unit != null">
          unit = #{item.unit,jdbcType=VARCHAR},
        </if>
        <if test="item.multi != null">
          multi = #{item.multi,jdbcType=INTEGER},
        </if>
        input_limited_words = #{item.inputLimitedWords,jdbcType=INTEGER},
        number_maximum = #{item.numberMaximum,jdbcType=DECIMAL},
        number_minimum = #{item.numberMinimum,jdbcType=DECIMAL},
        icon_link = #{item.iconLink,jdbcType=VARCHAR},
        show_attr_name = #{item.showAttrName,jdbcType=INTEGER},
        replace_parent_value = #{item.replaceParentValue,jdbcType=INTEGER},
        event_set = #{item.eventSet,jdbcType=INTEGER},
        decimal_point = #{item.decimalPoint,jdbcType=INTEGER},
        memo = #{item.memo,jdbcType=VARCHAR}
      </set>
      where attribute_id = #{item.attributeId,jdbcType=VARCHAR}
      AND yn = 1
    </foreach>
  </update>

  <update id="deleteAttributeList" parameterType="java.util.List">
    <foreach collection="attributeList" item="item" open="" close="" separator=";">
      update health_wiki_entry_attribute
      <set>
         yn = 0
      </set>
      where attribute_id = #{item,jdbcType=VARCHAR}
      AND yn = 1
    </foreach>
  </update>

  <update id="updateEntryAttribute" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntryAttribute">
    update health_wiki_entry_attribute
    <set>
      <if test="enable != null">
        enable = #{enable,jdbcType=VARCHAR},
      </if>
      <if test="attributeType != null">
        attribute_type = #{attributeType,jdbcType=INTEGER},
      </if>
      <if test="inputType != null">
        input_type = #{inputType,jdbcType=INTEGER},
      </if>
      <if test="attributeName != null and attributeName != ''">
        attribute_name = #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="tip != null and tip != ''">
        tip = #{tip,jdbcType=VARCHAR},
      </if>
      <if test="must != null">
        must = #{must,jdbcType=INTEGER},
      </if>
      <if test="cShow != null">
        c_show = #{cShow,jdbcType=INTEGER},
      </if>
      multi = #{multi,jdbcType=INTEGER},
      input_limited_words = #{inputLimitedWords,jdbcType=INTEGER},
      number_maximum = #{numberMaximum,jdbcType=DECIMAL},
      number_minimum = #{numberMinimum,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR},
      aline_type = #{alineType,jdbcType=INTEGER},
      icon_link = #{iconLink,jdbcType=VARCHAR},
      show_attr_name = #{showAttrName,jdbcType=INTEGER},
      replace_parent_value = #{replaceParentValue,jdbcType=INTEGER},
      event_set = #{eventSet,jdbcType=INTEGER},
      decimal_point = #{decimalPoint,jdbcType=INTEGER}
    </set>
    where attribute_id = #{attributeId,jdbcType=VARCHAR}
    and yn = 1
  </update>

  <update id="updateAttributeEnable" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntryAttribute">
    update health_wiki_entry_attribute
    <set>
      <if test="enable != null">
        enable = #{enable,jdbcType=VARCHAR},
      </if>
    </set>
    where attribute_id = #{attributeId,jdbcType=VARCHAR}
    and yn = 1
  </update>

  <!-- 批量更新属性排序 -->
  <update id="updateEntryAttributeSort" parameterType="java.util.List" >
    <foreach collection="attributeList" item="item" open="" close="" separator=";">
      update health_wiki_entry_attribute
      <set>
        sort = #{item.sort}
      </set>
      where attribute_id = #{item.attributeId,jdbcType=VARCHAR}
      AND yn = 1
    </foreach>
  </update>

  <select id="queryManCategoryAttrList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_wiki_entry_attribute
    <where>
      <if test="categoryIdList != null and categoryIdList.size() != 0">
        category_id in
        <foreach collection="categoryIdList" item="categoryId" open="(" close=")" separator=",">
          #{categoryId}
        </foreach>
      </if>
      <if test="attributeId != null and attributeId != ''">
        and attribute_id = #{attributeId, jdbcType=VARCHAR}
      </if>
      <if test="attributeName != null and attributeName != ''">
        and attribute_name = #{attributeName,jdbcType=VARCHAR}
      </if>
      <if test="parentAttrId != null and parentAttrId != ''">
        and parent_attribute_id = #{parentAttrId, jdbcType=VARCHAR}
      </if>
      <if test="parentAttrContentIds != null and parentAttrContentIds.size() != 0">
        and parent_content_id in
        <foreach collection="parentAttrContentIds" index="index" item="parentAttrContentId" open="(" separator="," close=")">
          #{parentAttrContentId}
        </foreach>
      </if>
      <if test="attributeLevel != null and attributeLevel != ''">
        and attribute_level = #{attributeLevel,jdbcType=VARCHAR}
      </if>
      and enable = 1
      and yn = 1
      order by sort
    </where>
  </select>

  <!--查询三级类目下父属性信息列表 -->
  <select id="queryParentCategoryAttrList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_wiki_entry_attribute
    <where>
      <if test="thirdCategoryId != null and thirdCategoryId != ''">
        category_id = #{thirdCategoryId}
      </if>
      <if test="attributeName != null and attributeName != ''">
        and attribute_name like CONCAT('%',#{attributeName,jdbcType=VARCHAR},'%')
      </if>
      <if test="attributeLevel != null and attributeLevel != ''">
        and attribute_level = #{attributeLevel,jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="queryCategoryAttrInfoMaxSort" resultType="java.lang.Integer">
    select
    MAX(sort)
    from health_wiki_entry_attribute
    <where>
      category_id in
      <foreach collection="categoryIdList" item="categoryId" open="(" close=")" separator=",">
        #{categoryId}
      </foreach>
      and attribute_level = 1
      and yn = 1
    </where>
  </select>

  <!--查询三级类目下父属性信息列表 -->
  <select id="queryCategoryAttrInfoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_wiki_entry_attribute
    <where>
      category_id in
      <foreach collection="categoryIdList" item="categoryId" open="(" close=")" separator=",">
         #{categoryId}
      </foreach>
      <if test="attributeName != null and attributeName != ''">
        and attribute_name = #{attributeName,jdbcType=VARCHAR}
      </if>
      and attribute_level = 1
      and yn = 1
    </where>
  </select>
</mapper>