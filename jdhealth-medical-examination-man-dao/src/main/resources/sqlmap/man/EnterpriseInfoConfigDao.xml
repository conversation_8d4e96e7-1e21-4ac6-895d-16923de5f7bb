<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseInfoConfigDao">

    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigEntity">
        <result column="id" property="id"/>
        <result column="company_no" property="companyNo"/>
        <result column="home_top_url" property="homeTopUrl"/>
        <result column="questionnaire" property="questionnaire"/>
        <result column="watermark_status" property="watermarkStatus"/>
        <result column="drainage_status" property="drainageStatus"/>
        <result column="shop_logo_status" property="shopLogoStatus"/>
        <result column="employee_notice_status" property="employeeNoticeStatus"/>
        <result column="region_filter_status" property="regionFilterStatus"/>
        <result column="report_explain_status" property="reportExplainStatus"/>
        <result column="report_explain_config" property="reportExplainConfig"/>
        <result column="report_cover_id" property="reportCoverId"/>
        <result column="report_cover_name" property="reportCoverName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="yn" property="yn"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        company_no,
        home_top_url,
        questionnaire,
        watermark_status,
        drainage_status,
        shop_logo_status,
        employee_notice_status,
        region_filter_status,
        report_explain_status,
        report_explain_config,
        report_cover_id,report_cover_name,
        create_time,
        update_time,
        yn
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigEntity">
        INSERT INTO examination_qx_man_company_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test='null != companyNo'>
                company_no,
            </if>
            <if test='null != homeTopUrl'>
                home_top_url,
            </if>
            <if test='null != questionnaire'>
                questionnaire,
            </if>
            <if test='null != reportExplainConfig'>
                report_explain_config,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test='null != companyNo'>
                #{companyNo},
            </if>
            <if test='null != homeTopUrl'>
                #{homeTopUrl},
            </if>
            <if test='null != questionnaire'>
                #{questionnaire},
            </if>
            <if test='null != reportExplainConfig'>
                #{reportExplainConfig},
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE FROM examination_qx_man_company_config
        WHERE company_no = #{companyNo} and yn = 1
    </delete>

    <update id="updateHomeTopUrlConfig"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigEntity">
        UPDATE examination_qx_man_company_config
        <set>
            <if test='null != homeTopUrl'>home_top_url = #{homeTopUrl},</if>
        </set>
        WHERE company_no = #{companyNo}
        and yn = 1
    </update>

    <update id="updateQuestionnaireConfig"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigEntity">
        UPDATE examination_qx_man_company_config
        <set>
            questionnaire = #{questionnaire}
        </set>
        WHERE company_no = #{companyNo} and yn = 1
    </update>

    <update id="updateConfigStatus">
        UPDATE examination_qx_man_company_config
        <set>
            <if test='null != watermarkStatus'>
                watermark_status = #{watermarkStatus},
            </if>
            <if test='null != drainageStatus'>
                drainage_status = #{drainageStatus},
            </if>
            <if test='null != shopLogoStatus'>
                shop_logo_status = #{shopLogoStatus},
            </if>
            <if test='null != regionFilterStatus'>
                region_filter_status = #{regionFilterStatus},
            </if>
            <if test='null != employeeNoticeStatus'>
                employee_notice_status = #{employeeNoticeStatus},
            </if>
            <if test='null != reportExplainStatus'>
                report_explain_status = #{reportExplainStatus},
            </if>
            <if test="reportExplainConfig !=null and reportExplainConfig !=''">
                report_explain_config =#{reportExplainConfig},
            </if>
            <if test='null != reportCoverId'>
                report_cover_id = #{reportCoverId},
            </if>
            <if test="reportCoverName !=null and reportCoverName !=''">
                report_cover_name =#{reportCoverName},
            </if>

        </set>
        WHERE company_no = #{companyNo} and yn = 1
    </update>


    <select id="getConfigByCompanyNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM examination_qx_man_company_config
        WHERE company_no = #{companyNo} and yn = 1
    </select>
    <select id="queryCompanyNoAll" resultType="java.lang.Long">
        select distinct  company_no from examination_qx_man_company_config
    </select>

</mapper>