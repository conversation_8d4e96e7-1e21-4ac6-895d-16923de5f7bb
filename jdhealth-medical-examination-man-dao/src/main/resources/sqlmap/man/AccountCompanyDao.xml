<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.AccountCompanyDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.AccountCompanyEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_no" property="accountNo" jdbcType="VARCHAR" />
    <result column="company_no" property="companyNo" jdbcType="BIGINT" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="operate_account" property="operateAccount" jdbcType="INTEGER" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    id, account_no, company_no,company_name,operate_account, yn, create_time, update_time
  </sql>
  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.AccountCompanyEntity" >
    insert into examination_qx_man_account_company (id, account_no, company_no, company_name,operate_account,
      yn, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{accountNo,jdbcType=VARCHAR}, #{companyNo,jdbcType=BIGINT},  #{companyName,jdbcType=VARCHAR},#{operateAccount,jdbcType=INTEGER},
      #{yn,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="batchInsert">
    insert into examination_qx_man_account_company (id, account_no, company_no, company_name,operate_account,
      yn, create_time, update_time
      )
    values
    <foreach collection="accountCompanys" index="index" item="item" open="" separator="," close="">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT}, #{item.accountNo,jdbcType=VARCHAR}, #{item.companyNo,jdbcType=BIGINT},  #{item.companyName,jdbcType=VARCHAR},#{item.operateAccount,jdbcType=INTEGER},
        #{item.yn,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
      </trim>
    </foreach>
  </insert>

  <select id="selectAll" resultMap="BaseResultMap" >
    select id, account_no, company_no,account_no, yn, create_time, update_time
    from examination_qx_man_account_company
  </select>
    <select id="queryCompanyNoByAccount" resultType="java.lang.Long">
      select company_no from examination_qx_man_account_company
      where account_no = #{pin}
      and yn = 1
    </select>
    <select id="queryCompanyByAccount"
            resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/> from examination_qx_man_account_company
      where account_no = #{accountNo}
      and yn = 1
    </select>
    <select id="queryAccountCompany"
            resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/> from examination_qx_man_account_company
      where account_no = #{accountNo}
      and company_no = #{companyNo}
      and yn = 1
    </select>
    <select id="queryAccountByCompanyNos"
            resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/> from examination_qx_man_account_company
      where
      company_no in
      <foreach collection="companyNos" item="companyNo" open="(" close=")" separator=",">
        #{companyNo}
      </foreach>
      and yn = 1
      and operate_account = 1
    </select>
    <select id="queryAccountCompanyByCompanyNo"
            resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/> from examination_qx_man_account_company
      where
      company_no = #{companyNo}
      and account_no = #{accountNo}
      and yn = 1
    </select>
</mapper>