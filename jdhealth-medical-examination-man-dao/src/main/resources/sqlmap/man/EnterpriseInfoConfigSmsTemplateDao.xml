<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseInfoConfigSmsTemplateDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity">
    <id column="id" property="id" />
    <result column="company_no" property="companyNo" />
    <result column="sms_account" property="smsAccount" />
    <result column="template_id"  property="templateId" />
    <result column="create_user"  property="createUser" />
    <result column="create_time"  property="createTime" />
    <result column="yn"  property="yn" />
  </resultMap>
  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
        id, company_no, sms_account, template_id, create_user, create_time, yn
    </trim>
  </sql>

  <select id="selectByCondition"
          resultType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity">
    select
    <include refid="Base_Column_List" />
    from examination_qx_man_company_config_sms
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="companyNo != null">
        and company_no = #{companyNo}
      </if>
      <if test="smsAccount != null and smsAccount != ''">
        and sms_account = #{smsAccount}
      </if>
      <if test="templateId != null and templateId != ''">
        and template_id = #{templateId}
      </if>
      <if test="createUser != null  and createUser != ''">
        and create_user = #{createUser}
      </if>
      <if test="createTime !=null">
        and create_time = #{createTime}
      </if>
      and yn = 1
    </where>
    order by create_time desc
    limit  50
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity" useGeneratedKeys="true">
    insert into examination_qx_man_company_config_sms (company_no, sms_account, template_id, 
      create_user, create_time, yn
      )
    values (#{companyNo,jdbcType=BIGINT}, #{smsAccount,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 1
      )
  </insert>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity" useGeneratedKeys="true">
    insert into examination_qx_man_company_config_sms
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        company_no,
      </if>
      <if test="smsAccount != null">
        sms_account,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="yn != null">
        yn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        #{companyNo,jdbcType=BIGINT},
      </if>
      <if test="smsAccount != null">
        #{smsAccount,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity">
    update examination_qx_man_company_config_sms
    <set>
      <if test="companyNo != null">
        company_no = #{companyNo,jdbcType=BIGINT},
      </if>
      <if test="smsAccount != null">
        sms_account = #{smsAccount,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity">
    update examination_qx_man_company_config_sms
    set company_no = #{companyNo,jdbcType=BIGINT},
      sms_account = #{smsAccount,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      yn = #{yn,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="deleteByConfigSmsTemplate" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity">
    update examination_qx_man_company_config_sms
    set yn = 0
    where company_no = #{companyNo,jdbcType=BIGINT}
      and sms_account = #{smsAccount,jdbcType=VARCHAR}
      and template_id = #{templateId,jdbcType=VARCHAR}
      and yn = 1
  </update>

  <update id="deleteCompanySmsConfigByAccountAndTemplateId">
    update examination_qx_man_company_config_sms
    set yn = 0
    where sms_account = #{smsAccount,jdbcType=VARCHAR}
      and template_id = #{templateId,jdbcType=VARCHAR}
  </update>

  <select id="selectByConfigSmsTemplate" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoConfigSmsTemplateEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_qx_man_company_config_sms
    where company_no = #{companyNo,jdbcType=BIGINT}
    and sms_account = #{smsAccount,jdbcType=VARCHAR}
    and template_id = #{templateId,jdbcType=VARCHAR}
    and yn = 1
    limit 1
  </select>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into examination_qx_man_company_config_sms (company_no, sms_account, template_id,
                                                       create_user, create_time, yn
    )
    values
           <foreach collection="projectTemplateEntity" item="item" open="" separator="," close="">
             (#{item.companyNo,jdbcType=BIGINT}, #{item.smsAccount,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR},
             #{item.createUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 1
             )
           </foreach>

  </insert>

  <select id="selectByCompanyNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from examination_qx_man_company_config_sms
    where company_no = #{companyNo,jdbcType=BIGINT}
    and yn = 1
  </select>

  <select id="selectAllCompanyNo" resultType="java.lang.Long">
    select company_no
    from examination_qx_man_company_config_sms
    where and yn = 1
  </select>
</mapper>