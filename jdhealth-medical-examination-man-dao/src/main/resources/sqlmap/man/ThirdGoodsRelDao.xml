<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ThirdGoodsRelDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.third.ThirdGoodsRelEntity">
    <!--@mbg.generated-->
    <!--@Table examination_man_third_goods_rel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="channel_no" jdbcType="BIGINT" property="channelNo" />
    <result column="relation_goods_id" jdbcType="VARCHAR" property="relationGoodsId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="yn" jdbcType="INTEGER" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, goods_id, channel_no, relation_goods_id, `status`, yn, create_time, update_time
  </sql>
  <select id="selectThirdGoodsRel" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsRelEntity" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from examination_man_third_goods_rel
    where goods_id = #{goodsId,jdbcType=VARCHAR}
    and channel_no = #{channelNo,jdbcType=BIGINT}
    and relation_goods_id = #{relationGoodsId,jdbcType=VARCHAR}
    and yn = 1
  </select>
  <select id="selectThirdGoodsRelList" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsRelEntity" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_man_third_goods_rel
    where yn = 1
      <if test="goodsId != null">
        and goods_id = #{goodsId,jdbcType=VARCHAR}
      </if>
      <if test="channelNo != null">
        and channel_no = #{channelNo,jdbcType=BIGINT}
      </if>
      <if test="relationGoodsId != null and relationGoodsId != ''">
        and relation_goods_id = #{relationGoodsId,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and status = #{status,jdbcType=INTEGER}
      </if>
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsRelEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_man_third_goods_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="channelNo != null">
        channel_no,
      </if>
      <if test="relationGoodsId != null">
        relation_goods_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="channelNo != null">
        #{channelNo,jdbcType=BIGINT},
      </if>
      <if test="relationGoodsId != null">
        #{relationGoodsId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>


  <insert id="batchInsert" keyColumn="id" keyProperty="id">
    insert into examination_man_third_goods_rel (goods_id, channel_no, relation_goods_id,status )
    values
    <foreach collection="records" index="index" item="record" open="" separator="," close="">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{record.goodsId,jdbcType=VARCHAR}, #{record.channelNo,jdbcType=BIGINT},
        #{record.relationGoodsId,jdbcType=VARCHAR}, #{record.status,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>
  <update id="updateBySelective" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsRelEntity">
    <!--@mbg.generated-->
    update examination_man_third_goods_rel
    <set>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="channelNo != null">
        channel_no = #{channelNo,jdbcType=BIGINT},
      </if>
      <if test="relationGoodsId != null">
        relation_goods_id = #{relationGoodsId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=INTEGER},
      </if>
    </set>
    where goods_id = #{goodsId,jdbcType=VARCHAR}
    and channel_no = #{channelNo,jdbcType=BIGINT}
    and relation_goods_id = #{relationGoodsId,jdbcType=VARCHAR}
    and yn = 1
  </update>

  <update id="offByGoodsId">
    <!--@mbg.generated-->
    update examination_man_third_goods_rel
    set status = 2
    where channel_no = #{channelNo,jdbcType=BIGINT}
    and yn = 1
    and goods_id = #{goodsId,jdbcType=VARCHAR}
    and status = #{status,jdbcType=INTEGER}
    and relation_goods_id in
    <foreach collection="relationIds" item="relationId" open="(" separator="," close=")">
      #{relationId,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="changeStatusByRelationId">
    <!--@mbg.generated-->
    update examination_man_third_goods_rel
    set status = #{targetStatus,jdbcType=INTEGER}
    where channel_no = #{channelNo,jdbcType=BIGINT}
    and yn = 1
    and relation_goods_id = #{relationId,jdbcType=VARCHAR}
    and status = #{sourceStatus,jdbcType=INTEGER}
    and goods_id in
    <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
      #{goodsId,jdbcType=VARCHAR}
    </foreach>
  </update>


  <update id="changeStatusByGoodsId">
    <!--@mbg.generated-->
    update examination_man_third_goods_rel
    set status = #{targetStatus,jdbcType=INTEGER}
    where channel_no = #{channelNo,jdbcType=BIGINT}
    and yn = 1
    and goods_id = #{goodsId,jdbcType=VARCHAR}
    and status = #{sourceStatus,jdbcType=INTEGER}
    and relation_goods_id in
    <foreach collection="relationIds" item="relationId" open="(" separator="," close=")">
      #{relationId,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from examination_man_third_goods_rel
    <where>
      <if test="goodsId != null and goodsId != ''">
        and goods_id = #{goodsId,jdbcType=VARCHAR}
      </if>
      <if test="channelNo != null">
        and channel_no = #{channelNo,jdbcType=BIGINT}
      </if>
      <if test="relationGoodsId != null">
        and relation_goods_id = #{relationGoodsId,jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="selectListByRelationIds" resultMap="BaseResultMap">

      select
      <include refid="Base_Column_List" />
      from examination_man_third_goods_rel
      where relation_goods_id in
        <foreach collection="relationIds" item="relationId" open="(" separator="," close=")">
            #{relationId}
        </foreach>
      and channel_no = #{channelNo}
      and status = 1
      and yn = 1
  </select>

  <select id="listByIdStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from examination_man_third_goods_rel
    where yn =1
    <if test="status != null">
      and status = #{status,jdbcType=INTEGER}
    </if>
    and (channel_no, goods_id, relation_goods_id) in
    <foreach collection="relationIds" item="item" open="(" separator="," close=")">
      (#{item.channelNo,jdbcType=BIGINT}, #{item.goodsId}, #{item.relationGoodsId,jdbcType=VARCHAR})
    </foreach>
  </select>


</mapper>