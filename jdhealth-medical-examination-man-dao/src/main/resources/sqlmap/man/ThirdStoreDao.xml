<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdStoreDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.third.ThirdStoreEntity" id="thirdStoreMap">
        <result property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="storeName" column="store_name"/>
        <result property="channelNo" column="channel_no"/>
        <result property="storeAddr" column="store_addr"/>
        <result property="storePhone" column="store_phone"/>
        <result property="storeType" column="store_type"/>
        <result property="storeLevel" column="store_level"/>
        <result property="storeHours" column="store_hours"/>
        <result property="storeRemark" column="store_remark"/>
        <result property="provinceId" column="province_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="countyId" column="county_id"/>
        <result property="countyName" column="county_name"/>
        <result property="reportSupport" column="report_support"/>
        <result property="supportWriteOff" column="support_write_off"/>
        <result property="storeStatus" column="store_status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="yn" column="yn"/>
        <result column="brand_id" property="brandId"/>
        <result column="brand_name" property="brandName"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="preAppointmentDay" column="pre_appointment_day"/>
        <result property="lowestLimit" column="lowest_limit"/>
        <result property="approvalStatus" column="approval_status"/>
        <result property="autoReceiveConfirm" column="auto_receive_confirm"/>
        <result property="autoCancelConfirm" column="auto_cancel_confirm"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="businessType" column="business_type"/>
        <result property="scheduleType" column="schedule_type"/>
        <result property="topStatus" column="top_status"/>
        <result property="topText" column="top_text"/>
        <result property="signHoursStart" column="sign_hours_start"/>
        <result property="signHoursEnd" column="sign_hours_end"/>
        <result property="selfStore" column="self_store"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            store_id,
            store_name,
            channel_no,
            store_addr,
            store_phone,
            store_type,
            store_level,
            store_hours,
            store_remark,
            province_id,
            province_name,
            city_id,
            city_name,
            county_id,
            county_name,
            report_support,
            support_write_off,
            store_status,
            lng,
            lat,
            yn,
            brand_id,
            brand_name,
            create_time,
            update_time,
            pre_appointment_day,
            lowest_limit,
            approval_status,
            auto_receive_confirm,
            auto_cancel_confirm,
            icon_url,
            business_type,
            schedule_type,
            top_status,
            top_text,
            sign_hours_start,
            sign_hours_end,
            self_store
        </trim>
    </sql>

    <!-- 根据查询 -->
    <select id="selectThirdStore" resultMap="thirdStoreMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        where store_id = #{storeId,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryThirdStoreList" resultMap="thirdStoreMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        <where>
            yn=1
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="storeName != null and storeName != ''">
                and store_name LIKE CONCAT('%',#{storeName}, '%')
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="storeAddr != null and storeAddr != ''">
                and store_addr = #{storeAddr,jdbcType=VARCHAR}
            </if>
            <if test="storePhone != null and storePhone != ''">
                and store_phone = #{storePhone,jdbcType=VARCHAR}
            </if>
            <if test="storeType != null">
                and store_type = #{storeType,jdbcType=INTEGER}
            </if>
            <if test="storeLevel != null">
                and store_level = #{storeLevel,jdbcType=INTEGER}
            </if>
            <if test="provinceId != null">
                and province_id = #{provinceId,jdbcType=INTEGER}
            </if>
            <if test="cityId != null">
                and city_id = #{cityId,jdbcType=INTEGER}
            </if>
            <if test="countyId != null">
                and county_id = #{countyId,jdbcType=INTEGER}
            </if>
            <if test="brandId != null">
                and brand_id = #{brandId,jdbcType=INTEGER}
            </if>
            <if test="reportSupport !=null">
                and report_support = #{reportSupport,jdbcType=INTEGER}
            </if>
            <if test="supportWriteOff !=null">
                and support_write_off = #{supportWriteOff,jdbcType=INTEGER}
            </if>
            <if test="provinceName != null and provinceName != ''">
                and province_name = #{provinceName,jdbcType=VARCHAR}
            </if>
            <if test="cityName != null and cityName != ''">
                and city_name = #{cityName,jdbcType=VARCHAR}
            </if>
            <if test="countyName != null and countyName != ''">
                and county_name = #{countyName,jdbcType=VARCHAR}
            </if>
            <if test="storeStatus != null">
                and store_status = #{storeStatus,jdbcType=INTEGER}
            </if>
            <if test="lng != null">
                and lng = #{lng,jdbcType=DOUBLE}
            </if>
            <if test="lat != null">
                and lat = #{lat,jdbcType=DOUBLE}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="scheduleType != null">
                and schedule_type = #{scheduleType}
            </if>
        </where>
    </select>
    <select id="selectThirdStoreByIds" resultMap="thirdStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        where store_id in
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and yn = 1
    </select>


    <!-- 插入实体 -->
    <insert id="insertThirdStore" parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_third_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            store_id,
            store_name,
            channel_no,
            store_addr,
            store_phone,
            store_type,
            store_level,
            store_hours,
            report_support,
            province_name,
            city_name,
            county_name,
            province_id,
            city_id,
            county_id,
            store_status,
            lng,
            lat,
            yn,
            create_time,
            update_time,
            pre_appointment_day,
            lowest_limit,
            approval_status,
            auto_receive_confirm,
            auto_cancel_confirm,
            icon_url,
            store_remark,
            self_store,
            <if test="supportWriteOff != null">
                support_write_off,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="scheduleType != null">
                schedule_type,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="brandName != null">
                brand_name,
            </if>
            <if test="topStatus != null">
                top_status,
            </if>
            <if test="topText != null and topText != ''">
                top_text
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{storeId,jdbcType=VARCHAR},
            #{storeName,jdbcType=VARCHAR},
            #{channelNo,jdbcType=BIGINT},
            #{storeAddr,jdbcType=VARCHAR},
            #{storePhone,jdbcType=VARCHAR},
            #{storeType,jdbcType=INTEGER},
            #{storeLevel,jdbcType=INTEGER},
            #{storeHours,jdbcType=VARCHAR},
            #{reportSupport,jdbcType=INTEGER},
            #{provinceName,jdbcType=VARCHAR},
            #{cityName,jdbcType=VARCHAR},
            #{countyName,jdbcType=VARCHAR},
            #{provinceId,jdbcType=INTEGER},
            #{cityId,jdbcType=INTEGER},
            #{countyId,jdbcType=INTEGER},
            #{storeStatus,jdbcType=INTEGER},
            #{lng,jdbcType=DOUBLE},
            #{lat,jdbcType=DOUBLE},
            1,
            now(),
            now(),
            #{preAppointmentDay,jdbcType=INTEGER},
            #{lowestLimit,jdbcType=INTEGER},
            #{approvalStatus,jdbcType=TINYINT},
            #{autoReceiveConfirm,jdbcType=TINYINT},
            #{autoCancelConfirm,jdbcType=TINYINT},
            #{iconUrl,jdbcType=VARCHAR},
            #{storeRemark,jdbcType=VARCHAR},
            #{selfStore,jdbcType=INTEGER},
            <if test="supportWriteOff != null">
                #{supportWriteOff,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                #{businessType},
            </if>
            <if test="scheduleType != null">
                #{scheduleType},
            </if>
            <if test="brandId != null">
                 #{brandId},
            </if>
            <if test="brandName != null">
                #{brandName},
            </if>
            <if test="topStatus != null">
                #{topStatus,jdbcType=INTEGER},
            </if>
            <if test="topText != null and topText != ''">
                #{topText,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>
    <!-- 修改实体 -->
    <update id="updateThirdStore" parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        update examination_man_third_store
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="storeName != null and storeName != ''">
                    store_name = #{storeName,jdbcType=VARCHAR},
                </if>
                <if test="storeAddr != null and storeAddr != ''">
                    store_addr = #{storeAddr,jdbcType=VARCHAR},
                </if>
                <if test="storePhone != null and storePhone != ''">
                    store_phone = #{storePhone,jdbcType=VARCHAR},
                </if>
                <if test="storeType != null">
                    store_type = #{storeType,jdbcType=INTEGER},
                </if>
                <if test="storeLevel != null">
                    store_level = #{storeLevel,jdbcType=INTEGER},
                </if>
                <if test="storeHours != null and storeHours != ''">
                    store_hours = #{storeHours},
                </if>
                <if test="reportSupport !=null">
                    report_support = #{reportSupport,jdbcType=INTEGER},
                </if>
                <if test="provinceId != null">
                    province_id = #{provinceId,jdbcType=INTEGER},
                </if>
                <if test="provinceName != null and provinceName != ''">
                    province_name = #{provinceName,jdbcType=VARCHAR},
                </if>
                <if test="cityId != null">
                    city_id = #{cityId,jdbcType=INTEGER},
                </if>
                <if test="cityName != null and cityName != ''">
                    city_name = #{cityName,jdbcType=VARCHAR},
                </if>
                <if test="countyId != null">
                    county_id = #{countyId,jdbcType=INTEGER},
                </if>
                <if test="countyName != null and countyName != ''">
                    county_name = #{countyName,jdbcType=VARCHAR},
                </if>
                <if test="storeStatus != null">
                    store_status = #{storeStatus,jdbcType=INTEGER},
                </if>
                <if test="lng != null">
                    lng = #{lng,jdbcType=DOUBLE},
                </if>
                <if test="lat != null">
                    lat = #{lat,jdbcType=DOUBLE},
                </if>
                <if test="preAppointmentDay != null">
                    pre_appointment_day = #{preAppointmentDay,jdbcType=INTEGER},
                </if>
                <if test="lowestLimit != null">
                    lowest_limit = #{lowestLimit,jdbcType=INTEGER},
                </if>
                <if test="approvalStatus != null">
                    approval_status = #{approvalStatus,jdbcType=TINYINT},
                </if>
                <if test="autoReceiveConfirm != null">
                    auto_receive_confirm = #{autoReceiveConfirm,jdbcType=TINYINT},
                </if>
                <if test="autoCancelConfirm != null">
                    auto_cancel_confirm = #{autoCancelConfirm,jdbcType=TINYINT},
                </if>
                <if test="iconUrl != null and iconUrl != ''">
                    icon_url = #{iconUrl,jdbcType=VARCHAR},
                </if>
                <if test="supportWriteOff != null">
                    support_write_off = #{supportWriteOff,jdbcType=TINYINT},
                </if>
                <if test="businessType != null">
                    business_type = #{businessType},
                </if>
                <if test="scheduleType != null">
                    schedule_type = #{scheduleType},
                </if>
                <if test="storeRemark != null">
                    store_remark = #{storeRemark,jdbcType=VARCHAR},
                </if>
                <if test="selfStore != null">
                    self_store = #{selfStore},
                </if>
            </trim>
        </set>
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 更新创建时间戳 -->
    <update id="updateThirdStoreUpdateTimeMillis" parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        update examination_man_third_store
        set update_time = #{createTime,jdbcType=TIMESTAMP}
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!-- 更新门店下架 -->
    <update id="updateThirdStoreStatus" parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        update examination_man_third_store
        set store_status = #{storeStatus,jdbcType=INTEGER}
        where channel_no = #{channelNo,jdbcType=BIGINT}
          and store_id = #{storeId,jdbcType=VARCHAR}
    </update>

    <update id="updateSupportWriteOff">
        update examination_man_third_store
        set support_write_off=#{supportWriteOff},
            update_time=now()
        where channel_no = #{channelNo}
          and store_id = #{storeId}
    </update>

    <select id="queryStoreInfoByStoreId" resultMap="thirdStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        where store_id = #{storeId}
        AND channel_no = #{channelNo}
        AND yn = 1
        limit 1
    </select>


    <select id="selectThirdStoreByChannelNo"
            resultMap="thirdStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        where channel_no = #{channelNo}
        and yn = 1
        limit #{pageOffset},#{pageSize}
    </select>
    <!-- 插入实体 -->
    <insert id="insertThirdStoreBatch" parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        insert into examination_man_third_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            store_id,
            store_name,
            channel_no,
            store_addr,
            store_phone,
            store_type,
            store_level,
            store_hours,
            report_support,
            province_name,
            city_name,
            county_name,
            province_id,
            city_id,
            county_id,
            store_status,
            lng,
            lat,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <foreach collection="thirdStores" item="thirdStore" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{thirdStore.storeId,jdbcType=VARCHAR},
                #{thirdStore.storeName,jdbcType=VARCHAR},
                #{thirdStore.channelNo,jdbcType=BIGINT},
                #{thirdStore.storeAddr,jdbcType=VARCHAR},
                #{thirdStore.storePhone,jdbcType=VARCHAR},
                #{thirdStore.storeType,jdbcType=INTEGER},
                #{thirdStore.storeLevel,jdbcType=INTEGER},
                "08:00-09:00",
                #{thirdStore.reportSupport,jdbcType=INTEGER},
                #{thirdStore.provinceName,jdbcType=VARCHAR},
                #{thirdStore.cityName,jdbcType=VARCHAR},
                #{thirdStore.countyName,jdbcType=VARCHAR},
                #{thirdStore.provinceId,jdbcType=INTEGER},
                #{thirdStore.cityId,jdbcType=INTEGER},
                #{thirdStore.countyId,jdbcType=INTEGER},
                #{thirdStore.storeStatus,jdbcType=INTEGER},
                #{thirdStore.lng,jdbcType=DOUBLE},
                #{thirdStore.lat,jdbcType=DOUBLE},
                1,
                now(),
                now(),
            </trim>
        </foreach>
    </insert>

    <!-- 批量更新门店三级地址 -->
    <update id="updateThirdStoreAddressId">
        update
        examination_man_third_store
        <set>
            province_id= #{provinceId},
            province_name= #{provinceName},
            city_id = #{cityId},
            city_name = #{cityName},
            county_id = #{countyId},
            county_name = #{countyName},
            <if test="lng!=null and lng!=0">
                lng = #{lng},
            </if>
            <if test="lat!=null and lat!=0">
                lat =#{lat}
            </if>
        </set>
        where
        channel_no = #{channelNo}
        and store_id= #{storeId}
        and yn = 1
    </update>

    <update id="batchUpdateBrandId">
        update examination_man_third_store
        set brand_id    = #{brandId},
            brand_name=#{brandName},
            update_time = now()
        where store_id = #{storeId}
          and channel_no = #{channelNo}
    </update>
    <update id="updateSupportWriteOffAndBrandId">
        update examination_man_third_store
        <set>
            brand_id          = #{brandId},
            brand_name        = #{brandName},
            support_write_off = #{supportWriteOff},
            lng               = #{lng},
            lat               = #{lat},
            store_remark      = #{storeRemark},
            top_status        = #{topStatus,jdbcType=INTEGER},
            top_text          = #{topText,jdbcType=VARCHAR},
            sign_hours_start  = #{signHoursStart},
            sign_hours_end    = #{signHoursEnd},
            <if test="selfStore!=null">
                self_store = #{selfStore},
            </if>
        </set>
        where store_id = #{storeId}
          and channel_no = #{channelNo}
          and yn = 1
    </update>

    <select id="queryStoreListByStoreIdList" resultMap="thirdStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        where store_id IN
        <foreach collection="storeIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND channel_no = #{channelNo}
        AND yn = 1
    </select>

    <select id="queryStoreByStoreIdsMap" resultMap="thirdStoreMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store
        where store_id IN
        <foreach collection="storeIdList" item="item" open="(" separator="," close=")"> #{item} </foreach>
        AND channel_no = #{channelNo}
        <if test="status != null">
            and store_status = #{status,jdbcType=INTEGER}
        </if>
        AND yn = 1
    </select>

    <select id="selectThirdStoreByCondition" resultMap="thirdStoreMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        examination_man_third_store
        WHERE
        yn = 1
        <if test="storeName != null and storeName != ''">
            and store_name like concat('%', #{storeName},'%')
        </if>
        <if test="approvalStatus != null and approvalStatus != ''">
            and approval_status = #{approvalStatus}
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="channelNoList != null and channelNoList.size() >0 ">
            and channel_no in
            <foreach collection="channelNoList" item="channelNo" open="(" separator="," close=")">
                #{channelNo}
            </foreach>
        </if>
    </select>
    <select id="queryExaminationThirdStore" parameterType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity" resultType="com.jd.health.medical.examination.domain.third.ThirdStoreEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM examination_man_third_store
        <where>
            <if test="thirdStore.storeId != null and thirdStore.storeId !='' ">
                AND  store_id = #{thirdStore.storeId,jdbcType=VARCHAR}
            </if>
            <if test="thirdStore.channelNo != null ">
                AND  channel_no = #{thirdStore.channelNo,jdbcType=BIGINT}
            </if>
            AND yn = 1
        </where>
        limit 1
    </select>
    <select id="selectThirdStoreIncludeBrands" resultMap="thirdStoreMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        examination_man_third_store
        WHERE
        yn = 1
        and  brand_id is not null
    </select>

    <update id="updateThirdStoreList" parameterType="java.util.List">
        <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
            UPDATE examination_man_third_store
            <set>
                <if test="bean.lat !=null">
                    lat=#{bean.lat},
                </if>
                <if test="bean.lng !=null">
                    lng=#{bean.lng}
                </if>
            </set>
            <where>
                 store_id = #{bean.storeId}
                and channel_no = #{bean.channelNo}
                and yn = 1
            </where>

        </foreach>
    </update>

    <select id="listByIdStatus" resultMap="thirdStoreMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        examination_man_third_store
        WHERE yn = 1
        and channel_no = #{channelNo,jdbcType=BIGINT}
        and store_status = #{storeStatus,jdbcType=INTEGER}
        and store_id in
        <foreach collection="storeIdList" item="storeId" open="(" separator="," close=")">
            #{storeId,jdbcType=VARCHAR}
        </foreach>
    </select>


</mapper>