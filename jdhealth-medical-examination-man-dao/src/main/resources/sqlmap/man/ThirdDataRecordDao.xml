<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdDataRecordDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.third.ThirdDataRecordEntity">
        <!--@mbg.generated-->
        <!--@Table examination_man_third_data_record-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="operate_type" jdbcType="INTEGER" property="operateType"/>
        <result column="channel_type" jdbcType="BIGINT" property="channelType"/>
        <result column="goods_id" jdbcType="VARCHAR" property="goodsId"/>
        <result column="store_id" jdbcType="VARCHAR" property="storeId"/>
        <result column="confirm" jdbcType="INTEGER" property="confirm"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, data_type, operate_type, channel_type, goods_id, store_id, confirm, yn, create_user,
        update_user, create_time, update_time
    </sql>

    <!-- 根据id查询数据 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_data_record
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 根据id删除数据 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from examination_man_third_data_record
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 插入实体 -->
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdDataRecordEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into examination_man_third_data_record (data_type, operate_type, channel_type,
        goods_id, store_id, confirm,
        yn, create_user, update_user,
        create_time, update_time)
        values (#{dataType,jdbcType=TINYINT}, #{operateType,jdbcType=TINYINT}, #{channelType,jdbcType=BIGINT},
        #{goodsId,jdbcType=VARCHAR}, #{storeId,jdbcType=VARCHAR}, #{confirm,jdbcType=TINYINT},
        1, #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 插入实体中不为空的字段 -->
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdDataRecordEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into examination_man_third_data_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataType != null">
                data_type,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="channelType != null">
                channel_type,
            </if>
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="confirm != null">
                confirm,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataType != null">
                #{dataType,jdbcType=INTEGER},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=INTEGER},
            </if>
            <if test="channelType != null">
                #{channelType,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=VARCHAR},
            </if>
            <if test="confirm != null">
                #{confirm,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 通过id更新实体中不为空的字段 -->
    <update id="updateByPrimaryKeySelective"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdDataRecordEntity">
        <!--@mbg.generated-->
        update examination_man_third_data_record
        <set>
            <if test="dataType != null">
                data_type = #{dataType,jdbcType=INTEGER},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=INTEGER},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=VARCHAR},
            </if>
            <if test="confirm != null">
                confirm = #{confirm,jdbcType=INTEGER},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 更新实体 -->
    <update id="updateByPrimaryKey"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdDataRecordEntity">
        <!--@mbg.generated-->
        update examination_man_third_data_record
        set data_type = #{dataType,jdbcType=INTEGER},
        operate_type = #{operateType,jdbcType=INTEGER},
        channel_type = #{channelType,jdbcType=BIGINT},
        goods_id = #{goodsId,jdbcType=VARCHAR},
        store_id = #{storeId,jdbcType=VARCHAR},
        confirm = #{confirm,jdbcType=INTEGER},
        yn = #{yn,jdbcType=INTEGER},
        create_user = #{createUser,jdbcType=VARCHAR},
        update_user = #{updateUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>