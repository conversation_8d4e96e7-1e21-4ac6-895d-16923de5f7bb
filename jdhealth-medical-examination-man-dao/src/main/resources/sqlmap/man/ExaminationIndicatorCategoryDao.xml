<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationIndicatorCategoryDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO">
    <!--@mbg.generated-->
    <!--@Table examination_indicator_category-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="category_level" jdbcType="INTEGER" property="categoryLevel" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, category_code, category_name, parent_code, category_level, yn, create_user, create_time, 
    update_user, update_time
  </sql>
  <select id="selectList" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from examination_indicator_category
    <where>
      <if test="categoryCode != null and categoryCode != ''">
        and category_code = #{categoryCode,jdbcType=VARCHAR}
      </if>
      <if test="categoryLevel != null">
        and category_level = #{categoryLevel,jdbcType=INTEGER}
      </if>
      and yn = 1
    </where>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from examination_indicator_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from examination_indicator_category
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_indicator_category (category_code, category_name, parent_code, 
      category_level, yn, create_user, 
      create_time, update_user, update_time
      )
    values (#{categoryCode,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, #{parentCode,jdbcType=VARCHAR}, 
      #{categoryLevel,jdbcType=INTEGER}, #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_indicator_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="parentCode != null">
        parent_code,
      </if>
      <if test="categoryLevel != null">
        category_level,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel != null">
        #{categoryLevel,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO">
    <!--@mbg.generated-->
    update examination_indicator_category
    <set>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        parent_code = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryLevel != null">
        category_level = #{categoryLevel,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO">
    <!--@mbg.generated-->
    update examination_indicator_category
    set category_code = #{categoryCode,jdbcType=VARCHAR},
      category_name = #{categoryName,jdbcType=VARCHAR},
      parent_code = #{parentCode,jdbcType=VARCHAR},
      category_level = #{categoryLevel,jdbcType=INTEGER},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="deleteAll">
    update examination_indicator_category
    set yn = 0
    where 1=1
  </update>
  <select id="selectByParam" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_indicator_category
    where yn = 1 and category_code = #{categoryCode,jdbcType=VARCHAR}
  </select>
  <select id="selectListByParam" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorCategoryDO" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_indicator_category
    where yn = 1
    <if test="categoryLevel != null and categoryLevel != ''">
      and category_level = #{categoryLevel,jdbcType=INTEGER}
    </if>
    <if test="parentCode != null and parentCode != ''">
      and parent_code = #{parentCode,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>