<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.doctor.DoctorStoreRelDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.doctor.DoctorStoreRelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="doctor_type" property="doctorType" jdbcType="TINYINT"/>
        <result column="doctor_id" property="doctorId" jdbcType="VARCHAR"/>
        <result column="doctor_name" property="doctorName" jdbcType="VARCHAR"/>
        <result column="store_id" property="storeId" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="approval_status" property="approvalStatus" jdbcType="TINYINT"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="insert">
        insert into manage_doctor_store_rel (doctor_type, doctor_id, doctor_name, store_id, store_name, approval_status,
        yn, create_time, create_user, update_time, update_user
        )
        values (#{doctorType}, #{doctorId}, #{doctorName}, #{storeId}, #{storeName}, #{approvalStatus}, 1, now(),
        #{createUser}, now(), #{createUser}
        )
    </insert>
    <update id="update">
        update manage_doctor_store_rel
        <set>
            <if test="doctorType != null">
                doctor_type = #{doctorType},
            </if>
            <if test="doctorName != null and doctorName != ''">
                doctor_name = #{doctorName},
            </if>
            <if test="storeName != null and storeName != ''">
                store_name = #{storeName},
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                approval_status = #{approvalStatus},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
        </set>
        where doctor_id = #{doctorId} and store_id = #{storeId}
    </update>
    <select id="query" resultMap="BaseResultMap">
        select id, doctor_type, doctor_id, doctor_name, store_id, store_name, approval_status, yn, create_time,
        create_user, update_time, update_user
        from manage_doctor_store_rel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="doctorType != null">
                and doctor_type = #{doctorType}
            </if>
            <if test="doctorId != null">
                and doctor_id = #{doctorId}
            </if>
            <if test="doctorName != null and doctorName != ''">
                and doctor_name like CONCAT("%",#{doctorName},"%")
            </if>
            <if test="storeId != null">
                and store_id = #{storeId}
            </if>
            <if test="storeName != null and storeName != ''">
                and store_name like CONCAT("%",#{storeName},"%")
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <choose>
                <when test="yn != null">
                    and yn = #{yn}
                </when>
                <otherwise>
                    and yn = 1
                </otherwise>
            </choose>
        </where>
        order by create_time desc
    </select>
    <select id="get" resultMap="BaseResultMap">
        select id, doctor_type, doctor_id, doctor_name, store_id, store_name, approval_status, yn, create_time,
        create_user, update_time, update_user
        from manage_doctor_store_rel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="doctorType != null">
                and doctor_type = #{doctorType}
            </if>
            <if test="doctorId != null and doctorId != ''">
                and doctor_id = #{doctorId}
            </if>
            <if test="doctorName != null and doctorName != ''">
                and doctor_name like CONCAT("%",#{doctorName},"%")
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId}
            </if>
            <if test="storeName != null and storeName != ''">
                and store_name like CONCAT("%",#{storeName},"%")
            </if>
            <if test="approvalStatus != null">
                and approval_status = #{approvalStatus}
            </if>
            <choose>
                <when test="yn != null">
                    and yn = #{yn}
                </when>
                <otherwise>
                    and yn = 1
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>