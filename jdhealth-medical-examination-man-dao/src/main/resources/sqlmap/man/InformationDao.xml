<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.jd.health.medical.examination.dao.InformationDao" >

  <resultMap id="CompositionMap" type="com.jd.health.medical.examination.domain.personal.entity.InformationEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="pkg_id" property="pkgId" jdbcType="BIGINT" />
    <result column="pkg_name" property="pkgName" jdbcType="VARCHAR" />
    <result column="pkg_item_id" property="pkgItemId" jdbcType="VARCHAR" />
    <result column="pkg_item_name" property="pkgItemName" jdbcType="VARCHAR" />
    <result column="pkg_desc" property="pkgDesc" jdbcType="VARCHAR" />
    <result column="pkg_content" property="pkgContent" jdbcType="VARCHAR" />
    <result column="pkg_remark" property="pkgRemark" jdbcType="VARCHAR" />
    <result column="pkg_sell_type" property="pkgSellType" jdbcType="INTEGER" />
    <result column="pkg_valid_value" property="pkgValidValue" jdbcType="VARCHAR" />
    <result column="pkg_valid_type" property="pkgValidType" jdbcType="INTEGER" />
    <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
    <result column="sku_price" property="skuPrice" jdbcType="BIGINT" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="sku_status" property="skuStatus" jdbcType="VARCHAR" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
      id,
      pkg_id,
      pkg_name,
      pkg_item_id,
      pkg_item_name,
      pkg_desc,
      pkg_content,
      pkg_remark,
      pkg_sell_type,
      pkg_valid_value,
      pkg_valid_type,
      sku_no,
      sku_price,
      sku_name,
      sku_status,
      create_user,
      yn,
      create_time,
      update_time,
    </trim>
  </sql>

  <select id="selectAll" resultMap="CompositionMap"
          parameterType="com.jd.health.medical.examination.domain.personal.entity.InformationEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_equity_package
      <where>
          <if test="pkgId != null">
              and pkg_id = #{pkgId,jdbcType=BIGINT}
          </if>
          <if test="pkgName != null and pkgName != ''">
            and pkg_name like CONCAT(#{pkgName,jdbcType=VARCHAR}, '%')
          </if>
          <if test="pkgSellType != null">
            and pkg_sell_type = #{pkgSellType,jdbcType=INTEGER}
          </if>
          <if test="pkgRemark != null and pkgRemark != ''">
            and pkg_remark like CONCAT(#{pkgRemark,jdbcType=VARCHAR}, '%')
          </if>
          <if test="skuNo != null and skuNo != ''">
            and sku_no=#{skuNo}
          </if>
          <if test="skuName != null and skuName != ''">
            and sku_name=#{skuName}
          </if>
          <if test="pkgItemId != null and pkgItemId != ''">
            and pkg_item_id=#{pkgItemId}
          </if>
          <if test="pkgItemName != null and pkgItemName != ''">
            and pkg_item_name like CONCAT(#{pkgItemName,jdbcType=VARCHAR}, '%')
          </if>
          and yn = 1
      </where>
  </select>

  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.personal.entity.InformationEntity" >
    insert into examination_man_equity_package (pkg_id,pkg_name,pkg_desc,pkg_sell_type,sku_no,sku_name,sku_price,sku_status,pkg_content,pkg_valid_type,pkg_valid_value,pkg_remark,create_user,yn,create_time,update_time,pkg_item_id,
      pkg_item_name)
    values (#{pkgId},#{pkgName,jdbcType=VARCHAR},
      #{pkgDesc,jdbcType=VARCHAR},#{pkgSellType,jdbcType=INTEGER},#{skuNo,jdbcType=VARCHAR},#{skuName,jdbcType=VARCHAR},#{skuPrice,jdbcType=VARCHAR},#{skuStatus,jdbcType=VARCHAR},#{pkgContent,jdbcType=VARCHAR},#{pkgValidType,jdbcType=INTEGER},#{pkgValidValue,jdbcType=VARCHAR},
      #{pkgRemark,jdbcType=VARCHAR},#{createUser,jdbcType=VARCHAR},1,now(),now(),#{pkgItemId},#{pkgItemName})
  </insert>

  <select id="getInformationBypkgId" resultMap="CompositionMap"
          parameterType="com.jd.health.medical.examination.domain.personal.entity.InformationEntity">
    select
    <include refid="Base_Column_List"/>
    from examination_man_equity_package
    where pkg_id=#{pkgId,jdbcType=BIGINT} and yn = 1
  </select>


  <update id="updateByInformationBypkgId" parameterType="com.jd.health.medical.examination.domain.personal.entity.InformationEntity" >
    update examination_man_equity_package
    set pkg_name = #{pkgName,jdbcType=VARCHAR},
      pkg_desc = #{pkgDesc,jdbcType=VARCHAR},
      pkg_remark = #{pkgRemark,jdbcType=VARCHAR},
      update_time = now()
    where pkg_id = #{pkgId,jdbcType=BIGINT}
  </update>
    <update id="updateInformationItem">
        update examination_man_equity_package
        set pkg_item_id=#{pkgItemId},
        pkg_item_name=#{pkgItemName}
        where pkg_id=#{pkgId} and yn = 1
    </update>

    <select id="getInformationByskuNo" resultMap="CompositionMap"
          parameterType="java.lang.String">
    select
    <include refid="Base_Column_List"/>
    from examination_man_equity_package
    where sku_no=#{skuNo,jdbcType=VARCHAR} and yn = 1
  </select>

</mapper>