<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ComposeInfoDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="compose_id" jdbcType="BIGINT" property="composeId"/>
        <result column="compose_name" jdbcType="VARCHAR" property="composeName"/>
        <result column="sku_no" jdbcType="VARCHAR" property="skuNo"/>
        <result column="suitable" jdbcType="INTEGER" property="suitable"/>
        <result column="group_no" jdbcType="BIGINT" property="groupNo"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="group_suitable" jdbcType="INTEGER" property="groupSuitable"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


    <sql id="Base_Column_List">
      compose_id, compose_name, sku_no, suitable, group_no, group_suitable,group_name
    </sql>


    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity">
  insert into examination_man_goods_compose (id, compose_id, create_time,
    compose_name, sku_no, suitable,
    group_no, yn, update_time
    )
  values (#{id,jdbcType=BIGINT}, #{composeId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
    #{composeName,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, #{suitable,jdbcType=INTEGER},
    #{groupNo,jdbcType=BIGINT}, #{yn,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
    )
</insert>

    <insert id="insertBatch" parameterType="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity">
        insert into examination_man_goods_compose (
        compose_id,
        create_time,
        compose_name, sku_no, suitable,
        group_no,group_name,group_suitable, yn, update_time
        )
        values
        <foreach collection="composeEntityList" item="composeEntity" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{composeEntity.composeId,jdbcType=BIGINT},now(),
                #{composeEntity.composeName,jdbcType=VARCHAR}, #{composeEntity.skuNo,jdbcType=VARCHAR}, #{composeEntity.suitable,jdbcType=INTEGER},
                #{composeEntity.groupNo,jdbcType=BIGINT},#{composeEntity.groupName},#{composeEntity.groupSuitable}, #{composeEntity.yn,jdbcType=INTEGER}, now()
            </trim>
        </foreach>
    </insert>


    <insert id="insertSelective" parameterType="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity">
        insert into examination_man_goods_compose
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="composeId != null">
                compose_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="composeName != null">
                compose_name,
            </if>
            <if test="skuNo != null">
                sku_no,
            </if>
            <if test="suitable != null">
                suitable,
            </if>
            <if test="groupNo != null and groupNo !=''">
                group_no,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="composeId != null">
                #{composeId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="composeName != null">
                #{composeName,jdbcType=VARCHAR},
            </if>
            <if test="skuNo != null">
                #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="suitable != null">
                #{suitable,jdbcType=INTEGER},
            </if>
            <if test="groupNo != null and groupNo !=''">
                #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity">
        update examination_man_goods_compose
        <set>
            <if test="composeId != null">
                compose_id = #{composeId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="composeName != null">
                compose_name = #{composeName,jdbcType=VARCHAR},
            </if>
            <if test="skuNo != null">
                sku_no = #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="suitable != null">
                suitable = #{suitable,jdbcType=INTEGER},
            </if>
            <if test="groupNo != null and groupNo !=''">
                group_no = #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateByPrimaryKey"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity">
  update examination_man_goods_compose
  set compose_id = #{composeId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    compose_name = #{composeName,jdbcType=VARCHAR},
    sku_no = #{skuNo,jdbcType=VARCHAR},
    suitable = #{suitable,jdbcType=INTEGER},
    group_no = #{groupNo,jdbcType=BIGINT},
    yn = #{yn,jdbcType=INTEGER},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
  where id = #{id,jdbcType=BIGINT}
</update>


    <select id="findPageWithResult"
            parameterType="com.jd.health.medical.examination.domain.personal.entity.ComposeEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_goods_compose
        where yn=1
        <if test="composeId != null">
            and compose_id = #{composeId,jdbcType=BIGINT}
        </if>
        <if test="composeName != null and composeName != ''">
            and compose_name = #{composeName,jdbcType=VARCHAR}
        </if>
        <if test="skuNo != null and skuNo != ''">
            and sku_no = #{skuNo,jdbcType=VARCHAR}
        </if>
        <if test="suitable != null">
            and suitable = #{suitable,jdbcType=INTEGER}
        </if>
        <if test="groupNo != null and groupNo !=''">
            and group_no = #{groupNo,jdbcType=BIGINT}
        </if>
    </select>
    <select id="selectComposeEntityByComposeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_goods_compose
        where compose_id=#{composeId,jdbcType=BIGINT}
        and yn=1
    </select>
    <select id="getComposeEntityByComposeIdAndSuitable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_goods_compose
        where compose_id = #{composeId,jdbcType=BIGINT}
         and group_suitable like concat('%',#{groupSuitable,jdbcType=INTEGER},'%')
        and yn = 1
        limit 1
    </select>

    <update id="deleteComposeEntityByComposeId" parameterType="java.lang.Long">
        update
         examination_man_goods_compose  set yn=0
        where compose_id=#{composeId,jdbcType=BIGINT}
        and yn=1
    </update>

    <select id="selectComposeEntity" resultMap="BaseResultMap">
        select
            compose_id,compose_name,suitable,GROUP_CONCAT(group_no) as group_no
        from examination_man_goods_compose
        where  yn=1
        <if test="composeId != null">
            and compose_id = #{composeId,jdbcType=BIGINT}
        </if>
        <if test="composeName != null and composeName != ''">
            and compose_name like  concat('%',#{composeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="skuNo != null and skuNo != ''">
            and sku_no = #{skuNo,jdbcType=VARCHAR}
        </if>
        <if test="suitable != null">
            and suitable like concat('%',#{suitable,jdbcType=INTEGER},'%')
        </if>
        <if test="groupNo != null and groupNo !=''">
            and group_no = #{groupNo,jdbcType=BIGINT}
        </if>
        group  by compose_id
        order by create_time desc
    </select>

    <select id="selectComposeEntityBySkuNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_goods_compose
        where sku_no=#{skuNo}
        and yn=1
    </select>


    <select id="selectComposeEntityNoGroupNo" resultMap="BaseResultMap">
        select
            id, compose_id, compose_name, sku_no, suitable
        from examination_man_goods_compose
        where yn = 1
        group  by compose_id
    </select>
    <select id="selectComposeEntityByComposeName"
            resultMap="BaseResultMap">
         select
            id, compose_id, compose_name, sku_no, suitable
        from examination_man_goods_compose
        where compose_name = #{composeName}
        and yn = 1 limit 1
    </select>

    <select id="selectOneByGroupNo"
            resultMap="BaseResultMap">
        select
            id, compose_id, compose_name, sku_no, suitable
        from examination_man_goods_compose
        where group_no = #{groupNo}
        and yn = 1 limit 1
    </select>
</mapper>