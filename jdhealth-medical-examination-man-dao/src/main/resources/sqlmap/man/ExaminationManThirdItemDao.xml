<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManThirdItemDao">
    <resultMap type="com.jd.health.medical.examination.domain.report.entity.basic.ExaminationManThirdItemEntity" id="ExaminationManThirdItemMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="itemNo" column="item_no" jdbcType="INTEGER"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="INTEGER"/>
        <result property="yn" column="yn" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
               id,
               brand_id,
               item_no,
               item_name,
               `source`,
               yn,
               create_user,
               create_time,
               update_user,
               update_time
    </sql>

    <!--查询单个-->
    <select id="queryByItemNo" resultMap="ExaminationManThirdItemMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_item
        where item_no = #{itemNo}
        and yn = 1
    </select>

    <!--通过实体作为筛选条件分页查询-->
    <select id="queryPage" resultMap="ExaminationManThirdItemMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_item
        <where>
            yn = 1
            <if test="brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
            <if test="itemNo != null">
                and item_no = #{itemNo}
            </if>
            <if test="fuzzyItemName != null and fuzzyItemName != ''">
                and item_name like CONCAT('%',#{fuzzyItemName,jdbcType=VARCHAR},'%')
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into examination_man_third_item(brand_id, item_no, item_name,source,create_user, create_time,
                                                 update_user, update_time)
        values (#{brandId}, #{itemNo}, #{itemName},#{source}, #{createUser}, #{createTime}, #{updateUser}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="updateByItemNo">
        update examination_man_third_item
        <set>
            <if test="brandId != null and brandId != ''">
                brand_id = #{brandId},
            </if>
            <if test="itemName != null and itemName != ''">
                item_name = #{itemName},
            </if>
            <if test="source != null">
                source = #{source},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where item_no = #{itemNo}
    </update>

    <select id="queryListByItemNos" resultMap="ExaminationManThirdItemMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_item
        <where>
            yn = 1
            and item_no in
            <foreach collection="itemNos" item="itemNo" separator=","  open="(" close=")">
             #{itemNo}
            </foreach>
        </where>

    </select>
</mapper>

