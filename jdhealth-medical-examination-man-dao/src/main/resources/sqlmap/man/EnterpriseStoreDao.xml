<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseStoreDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseStoreEntity" id="enterpriseStoreMap">
        <result property="id" column="id"/>
        <result property="companyNo" column="company_no"/>
        <result property="skuNo" column="sku_no"/>
        <result property="goodsId" column="goods_id"/>
        <result property="skuName" column="sku_name"/>
        <result property="venderCompanyPrice" column="vender_company_price"/>
        <result property="venderPersonPrice" column="vender_person_price"/>
        <result property="storeId" column="store_id"/>
        <result property="channelNo" column="channel_no"/>
        <result property="priceStatus" column="price_status"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            company_no,
            sku_no,
            goods_id,
            sku_name,
            vender_company_price,
            vender_person_price,
            store_id,
            channel_no,
            price_status,
            yn,
            create_time,
            update_time,
        </trim>
    </sql>

    <!-- 根据Id查询 -->
    <select id="selectEnterpriseStoreById" resultMap="enterpriseStoreMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from enterprise_store
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryEnterpriseStoreList" resultMap="enterpriseStoreMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from enterprise_store
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="skuNo != null">
                and sku_no = #{skuNo,jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="skuName != null">
                and sku_name = #{skuName,jdbcType=VARCHAR}
            </if>
            <if test="venderCompanyPrice != null">
                and vender_company_price = #{venderCompanyPrice,jdbcType=INT}
            </if>
            <if test="venderPersonPrice != null">
                and vender_person_price = #{venderPersonPrice,jdbcType=INT}
            </if>
            <if test="storeId != null">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="priceStatus != null">
                and price_status = #{priceStatus,jdbcType=TINYINT}
            </if>
            and yn = 1
        </where>
    </select>


    <!-- 插入实体 -->
    <insert id="insertEnterpriseStore" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseStoreEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into enterprise_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            company_no,
            sku_no,
            goods_id,
            sku_name,
            vender_company_price,
            vender_person_price,
            store_id,
            channel_no,
            price_status,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{companyNo,jdbcType=BIGINT},
            #{skuNo,jdbcType=BIGINT},
            #{goodsId,jdbcType=VARCHAR},
            #{skuName,jdbcType=VARCHAR},
            #{venderCompanyPrice,jdbcType=INT},
            #{venderPersonPrice,jdbcType=INT},
            #{storeId,jdbcType=VARCHAR},
            #{channelNo,jdbcType=BIGINT},
            #{priceStatus,jdbcType=TINYINT},
            1,
            now(),
            now(),
        </trim>
    </insert>

    <!-- 修改实体 -->
    <update id="updateEnterpriseStore"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseStoreEntity">
        update enterprise_store
        <set>
            yn = 1
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 删除实体 -->
    <update id="deleteEnterpriseStoreById" parameterType="LONG">
        update enterprise_store
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>


</mapper>