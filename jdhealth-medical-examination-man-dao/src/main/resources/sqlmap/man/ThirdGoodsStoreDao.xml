<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdGoodsStoreDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity" id="thirdStoreGoodsMap">
        <result property="id" column="id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="storeId" column="store_id"/>
        <result property="goodsStatus" column="goods_status"/>
        <result property="storeStatus" column="store_status"/>
        <result property="dataStatus" column="data_status"/>
        <result property="channelNo" column="channel_no"/>
        <result column="vip_type" jdbcType="TINYINT" property="vipType"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="referencePrice" column="reference_price"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            goods_id,
            store_id,
            goods_status,
            store_status,
            data_status,
            channel_no,
            vip_type,
            yn,
            create_time,
            update_time,
            reference_price
        </trim>
    </sql>

    <!-- 根据Id查询 -->
    <select id="selectThirdStoreGoodsById" resultMap="thirdStoreGoodsMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store_goods
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryThirdStoreGoodsList" resultMap="thirdStoreGoodsMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store_goods
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="goodsId != null and goodsId != ''">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=VARCHAR}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId,jdbcType=VARCHAR}
            </if>
            <if test="goodsStatus != null">
                and goods_status = #{goodsStatus,jdbcType=INTEGER}
            </if>
            <if test="storeStatus != null">
                and store_status = #{storeStatus,jdbcType=INTEGER}
            </if>
            <if test="dataStatus != null">
                and data_status = #{dataStatus,jdbcType=INTEGER}
            </if>
            and yn = 1
        </where>
    </select>

    <!--查询是否存在对应关系-->
    <select id="selectThirdStoreGoodsByGoodsIdAndStoreId"
            resultMap="thirdStoreGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store_goods
        where channel_no=#{channelNo}
        and goods_id=#{goodsId}
        and store_id=#{storeId}
        and (data_status=1
        or data_status=0)
        and yn = 1
        limit 1
    </select>


    <!-- 插入实体 -->
    <insert id="insertThirdStoreGoods"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_third_store_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            goods_id,
            store_id,
            goods_status,
            store_status,
            data_status,
            vip_type,
            channel_no,
            yn,
            create_time,
            update_time,
            reference_price,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{goodsId,jdbcType=VARCHAR},
            #{storeId,jdbcType=VARCHAR},
            #{goodsStatus,jdbcType=INTEGER},
            #{storeStatus,jdbcType=INTEGER},
            #{dataStatus,jdbcType=INTEGER},
            #{vipType,jdbcType=TINYINT},
            #{channelNo,jdbcType=INTEGER},
            1,
            now(),
            now(),
            #{referencePrice,jdbcType=INTEGER},
        </trim>
    </insert>

    <!-- 套餐下架更新 -->
    <update id="updateGoodsOff"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        update examination_man_third_store_goods
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="goodsStatus != null and goodsStatus != ''">
                    goods_status = #{goodsStatus,jdbcType=INTEGER},
                </if>
                <if test="dataStatus != null and dataStatus != ''">
                    data_status = #{dataStatus,jdbcType=INTEGER},
                </if>
            </trim>
        </set>
        where goods_id = #{goodsId,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>

    <!-- 门店下架更新 -->
    <update id="updateStoreOff"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        update examination_man_third_store_goods
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="storeStatus != null and storeStatus != ''">
                    store_status = #{storeStatus,jdbcType=INTEGER},
                </if>
                <if test="dataStatus != null and dataStatus != ''">
                    data_status = #{dataStatus,jdbcType=INTEGER},
                </if>
            </trim>
        </set>
        where store_id = #{storeId,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>

    <!-- 套餐对应门店下架更新 -->
    <update id="updateGoodsStoreOff"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        update examination_man_third_store_goods
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="storeStatus != null and storeStatus != ''">
                    store_status = #{storeStatus,jdbcType=INTEGER},
                </if>
                <if test="dataStatus != null and dataStatus != ''">
                    data_status = #{dataStatus,jdbcType=INTEGER},
                </if>
            </trim>
        </set>
        where goods_id = #{goodsId,jdbcType=VARCHAR}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>

    <!-- 套餐对应门店状态更新 -->
    <update id="updateGoodsStoreStatus"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        update examination_man_third_store_goods
        <set>
            store_status = #{storeStatus,jdbcType=INTEGER},
        </set>
        where goods_id = #{goodsId,jdbcType=VARCHAR}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>

    <update id="updateGoodsStore" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        update examination_man_third_store_goods
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="dataStatus != null">
                    data_status = #{dataStatus,jdbcType=INTEGER},
                </if>
                <if test="goodsStatus != null">
                    goods_status = #{goodsStatus,jdbcType=INTEGER},
                </if>
                <if test="storeStatus != null">
                    store_status = #{storeStatus,jdbcType=INTEGER},
                </if>
                <if test="vipType != null">
                    vip_type = #{vipType,jdbcType=INTEGER},
                </if>
            </trim>
        </set>
        where goods_id = #{goodsId,jdbcType=VARCHAR}
        and store_id = #{storeId,jdbcType=VARCHAR}
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>

    <!-- 删除实体 -->
    <update id="deleteThirdStoreGoodsById" parameterType="LONG">
        update examination_man_third_store_goods
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listByGoodsId"
            resultMap="thirdStoreGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_store_goods
        where channel_no = #{channelNo}
          and goods_id = #{goodsId}
          and yn = 1
    </select>

    <!-- 插入实体 -->
    <insert id="insertThirdStoreGoodsBatch"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity">
        insert into examination_man_third_store_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            goods_id,
            store_id,
            goods_status,
            store_status,
            data_status,
            vip_type,
            channel_no,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <foreach collection="thirdStoreGoods" item="thirdStoreGood" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{thirdStoreGood.goodsId,jdbcType=VARCHAR},
                #{thirdStoreGood.storeId,jdbcType=VARCHAR},
                #{thirdStoreGood.goodsStatus,jdbcType=INTEGER},
                #{thirdStoreGood.storeStatus,jdbcType=INTEGER},
                #{thirdStoreGood.dataStatus,jdbcType=INTEGER},
                #{thirdStoreGood.vipType,jdbcType=INTEGER},
                #{thirdStoreGood.channelNo,jdbcType=INTEGER},
                1,
                now(),
                now(),
            </trim>
        </foreach>
    </insert>


    <!-- 套餐对应门店下架更新 -->
    <update id="offByGoodsAndStoreIds">
        update examination_man_third_store_goods
            set store_status = #{storeStatus, javaType=INTEGER}
        where goods_id = #{goodsId,jdbcType=VARCHAR}
        and store_id in
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId,jdbcType=VARCHAR}
        </foreach>
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>
    <!-- 批量更新 -->
    <update id="batchUpdate">
        update examination_man_third_store_goods
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="store_status = case store_id" suffix="end,">
                <foreach collection="entities" index="index" item="item">
                    <if test="item.storeStatus != null">
                        when #{item.storeId,jdbcType=VARCHAR} then #{item.storeStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
                else store_status
            </trim>
            <if test="entities != null and entities.size() > 0">
                <trim prefix="vip_type = case store_id" suffix="end,">
                    <foreach collection="entities" index="index" item="item">
                        <if test="item.vipType != null">
                            when #{item.storeId,jdbcType=VARCHAR} then #{item.vipType,jdbcType=INTEGER}
                        </if>
                    </foreach>
                    else vip_type
                </trim>
            </if>
        </trim>
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id = #{goodsId,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <select id="listByRelationIds" resultMap="thirdStoreGoodsMap">
        select
        goods_id,
        store_id,
        channel_no
        from examination_man_third_store_goods
        where yn = 1
        and store_status = #{status,jdbcType=INTEGER}
        and (goods_id, store_id, channel_no) in
        <foreach collection="relValues" item="item" open="(" separator="," close=")">
            (#{item.goodsId,jdbcType=VARCHAR}, #{item.storeId,jdbcType=VARCHAR},#{item.channelNo,jdbcType=BIGINT})
        </foreach>

    </select>

</mapper>