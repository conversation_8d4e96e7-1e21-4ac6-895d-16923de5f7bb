<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuUpgradeDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuUpgradeEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_no" property="companyNo" jdbcType="BIGINT"/>
        <result column="parent_sku_no" property="parentSkuNo" jdbcType="VARCHAR"/>
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="sku_upgrade_name" property="skuUpgradeName" jdbcType="VARCHAR"/>
        <result column="group_no" property="groupNo" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="parent_sku_company_price" property="parentSkuCompanyPrice" jdbcType="INTEGER"/>
        <result column="sku_person_price" property="skuPersonPrice" jdbcType="INTEGER"/>
        <result column="sku_upgrade_price" property="skuUpgradePrice" jdbcType="INTEGER"/>
        <result column="sku_market_price" property="skuMarketPrice" jdbcType="INTEGER"/>
        <result column="examination_notes" property="examinationNotes" jdbcType="VARCHAR"/>
        <result column="purchase_notes" property="purchaseNotes" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id, company_no, parent_sku_no, sku_no, sku_name, sku_upgrade_name, group_no,
            parent_sku_company_price, sku_person_price, sku_upgrade_price,sku_market_price,examination_notes,purchase_notes
        </trim>
    </sql>

    <!--添加升级包-->
    <insert id="addSkuUpgrade">
        insert into examination_qx_man_sku_upgrade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            company_no,
            parent_sku_no,
            sku_no,
            sku_name,
            sku_upgrade_name,
            group_no,
            parent_sku_company_price,
            sku_person_price,
            sku_upgrade_price,
            sku_market_price,
            examination_notes,
            purchase_notes,
            create_time,
            create_user,
            update_time,
            update_user
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{companyNo},
            #{parentSkuNo},
            #{skuNo},
            #{skuName},
            #{skuUpgradeName},
            #{groupNo},
            #{parentSkuCompanyPrice},
            #{skuPersonPrice},
            #{skuUpgradePrice},
            #{skuMarketPrice},
            #{examinationNotes},
            #{purchaseNotes},
            now(),
            #{createUser},
            now(),
            #{updateUser},
        </trim>
    </insert>

    <!--删除一个升级包信息-->
    <update id="deleteSkuUpgrade">
      update examination_qx_man_sku_upgrade
      set yn = 0
      where company_no=#{companyNo}
      and sku_no=#{skuNo}
      and parent_sku_no=#{parentSkuNo}
      and yn = 1
    </update>

    <!--更新升级包信息 只更新名称和价格字段-->
    <update id="updateSkuUpgrade">
      update examination_qx_man_sku_upgrade
      set sku_upgrade_price = #{skuUpgradePrice},
      sku_upgrade_name = #{skuUpgradeName},
      <if test="examinationNotes != null and examinationNotes != ''">
        examination_notes = #{examinationNotes,jdbcType=VARCHAR},
      </if>
      purchase_notes = #{purchaseNotes}
      where company_no = #{companyNo}
      and sku_no = #{skuNo}
      and parent_sku_no = #{parentSkuNo}
      and yn = 1
    </update>

    <update id="updateSkuUpgradeSelective">
        update examination_qx_man_sku_upgrade
        <set>
            <if test="skuUpgradePrice != null">
                sku_upgrade_price = #{skuUpgradePrice},
            </if>
            <if test="skuMarketPrice != null">
                sku_market_price = #{skuMarketPrice},
            </if>
            <if test="skuUpgradeName != null">
                sku_upgrade_name = #{skuUpgradeName},
            </if>
            <if test="examinationNotes != null and examinationNotes != ''">
                examination_notes = #{examinationNotes,jdbcType=VARCHAR},
            </if>
            <if test="purchaseNotes != null and purchaseNotes != ''">
                purchase_notes = #{purchaseNotes},
            </if>
        </set>
        where company_no = #{companyNo}
        and sku_no = #{skuNo}
        and parent_sku_no = #{parentSkuNo}
        and yn = 1
    </update>
    <!--根据公司编号和父级商品编号查询升级包信息-->
    <select id="querySkuUpgrade" resultMap="BaseResultMap">
      select
      e.company_no, e.parent_sku_no, e.sku_no, e.sku_name, e.sku_upgrade_name, e.group_no,e.parent_sku_company_price, e.sku_person_price, e.sku_upgrade_price,e.sku_market_price,
      g.group_name,e.purchase_notes,e.examination_notes
      from examination_qx_man_sku_upgrade e
      LEFT JOIN examination_man_group g ON e.group_no=g.group_no
      where e.company_no=#{companyNo}
      and e.parent_sku_no=#{parentSkuNo}
      and e.yn = 1
    </select>

    <!--查询sku是否存在-->
    <!--根据company_no，sku_no，parent_sku_no查询套餐升级列表，For 获取套餐项目详情数据，套餐PK模块-->
    <select id="selectSkuUpgradeBySkuNoAndCompanyNo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_upgrade
        where company_no=#{companyNo}
        and sku_no=#{skuNo}
        and parent_sku_no=#{parentSkuNo}
        and yn = 1
    </select>

    <select id="querySkuUpgradeAndGroupDesc"
            resultType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuUpgradeEntity">
        select company_no, parent_sku_no, sku_no, sku_name, sku_upgrade_name, e.group_no,g.group_name,g.group_desc,
            parent_sku_company_price, sku_person_price, sku_upgrade_price ,e.purchase_notes
        from examination_qx_man_sku_upgrade e left join examination_man_group g on e.group_no = g.group_no
        where  company_no=#{companyNo} and parent_sku_no=#{parentSkuNo}
        and e.yn = 1 and g.yn = 1
    </select>

    <select id="querySkuUpgradeBySkuAndCompanyNo"
            resultType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuUpgradeEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_upgrade
        where
        company_no=#{companyNo}
        and parent_sku_no=#{parentSkuNo}
        and yn = 1
    </select>


    <select id="updateEnterpriseSkuByBaseSkuInfo" resultType="java.lang.Integer">
        update examination_qx_man_sku_upgrade
        <set>
            <if test="skuName != null and skuName != ''">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                group_no = #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="skuPersonPrice != null">
                sku_person_price = #{skuPersonPrice},
            </if>
            <if test="purchaseNotes != null and purchaseNotes != ''">
                purchase_notes = #{purchaseNotes},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where sku_no = #{skuNo}
        and yn = 1
    </select>

    <update id="removeEnterpriseSkuNo">
        update examination_qx_man_sku_upgrade
        set yn = 0
        where sku_no = #{skuNo}
        and yn = 1
    </update>

    <select id="selectUpgradeListBySkuNo" resultType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuUpgradeEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_upgrade
        where sku_no = #{skuNo}
          and yn = 1
    </select>
    <select id="selectAll"
            resultType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuUpgradeEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_sku_upgrade
        where yn = 1 and sku_market_price is null
    </select>
</mapper>