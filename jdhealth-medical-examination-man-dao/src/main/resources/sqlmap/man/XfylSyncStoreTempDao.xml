<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.XfylSyncStoreTempDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.XfylSyncStoreTempEntity">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="store_id" property="storeId" jdbcType="VARCHAR" />
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        <result column="store_status" property="storeStatus" jdbcType="VARCHAR" />
        <result column="shop_id" property="shopId" jdbcType="VARCHAR" />
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
        <result column="vender_id" property="venderId" jdbcType="VARCHAR" />
        <result column="vender_name" property="venderName" jdbcType="VARCHAR" />
        <result column="vender_status" property="venderStatus" jdbcType="VARCHAR" />
        <result column="valid_flag" property="validFlag" jdbcType="VARCHAR" />
        <result column="channel_id" property="channelId" jdbcType="VARCHAR" />
        <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
        <result column="num" property="num" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,
        store_id,
        store_name,
        store_status,
        shop_id,
        shop_name,
        vender_id,
        vender_name,
        vender_status,
        valid_flag,
        channel_id,
        channel_name,
        num,
        remark,
        create_time,
        update_time
    </sql>

    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.XfylSyncStoreTempEntity" >
        INSERT INTO xfyl_sync_store_temp (
        store_id,
        store_name,
        store_status,
        shop_id,
        shop_name,
        vender_id,
        vender_name,
        vender_status,
        valid_flag,
        channel_id,
        channel_name,
        num, remark,
        create_time,
        update_time
        ) VALUES (
        #{storeId},
        #{storeName},
        #{storeStatus},
        #{shopId},
        #{shopName},
        #{venderId},
        #{venderName},
        #{venderStatus},
        #{validFlag},
        #{channelId},
        #{channelName},
        #{num},
        #{remark},
        now(),
        now()
        )
    </insert>

    <update id="updateByStoreId"
            parameterType="com.jd.health.medical.examination.domain.XfylSyncStoreTempEntity">
        UPDATE xfyl_sync_store_temp
        <set>
            <if test="num != null" >
                num = #{num},
            </if>
            <if test="remark != null and remark != ''" >
                remark = concat(ifnull(remark,''),#{remark}),
            </if>
            update_time = now()
        </set>
        WHERE store_id = #{storeId}
    </update>

    <select id="selectSyncStoreTemp" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.XfylSyncStoreTempEntity">
        SELECT
        <include refid="Base_Column_List" />
        FROM xfyl_sync_store_temp
        <where>
            <if test="storeId != null and storeId != ''">
                AND store_id = #{storeId}
            </if>
            <if test="num != null">
                AND num = #{num}
            </if>
        </where>
        ORDER BY store_id DESC limit 100
    </select>

    <select id="selectCountByEntity" resultType="java.lang.Long">
        SELECT
          COUNT(1)
        FROM
          xfyl_sync_store_temp
    </select>

    <delete id="deleteByStoreId" parameterType="java.lang.String">
        DELETE FROM
          xfyl_sync_store_temp
        WHERE
          store_id = #{storeId}
    </delete>

    <select id="selectCountStoreTempByEntity" resultType="java.lang.Long"
            parameterType="com.jd.health.medical.examination.domain.XfylSyncStoreTempEntity">
        SELECT
          COUNT(1)
        FROM
          xfyl_sync_store_temp
        <where>
            <if test="storeId != null and storeId != ''">
                AND store_id = #{storeId}
            </if>
            <if test="num != null">
                AND num = #{num}
            </if>
        </where>
    </select>

</mapper>