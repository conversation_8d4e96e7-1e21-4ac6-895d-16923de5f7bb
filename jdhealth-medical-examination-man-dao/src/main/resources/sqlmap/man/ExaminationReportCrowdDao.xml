<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationReportCrowdDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.report.ExaminationReportCrowdEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_id" jdbcType="VARCHAR" property="reportId" />
    <result column="min_age" jdbcType="BIGINT" property="minAge" />
    <result column="max_age" jdbcType="BIGINT" property="maxAge" />
    <result column="user_gender" jdbcType="TINYINT" property="userGender" />
    <result column="indicator_code" jdbcType="VARCHAR" property="indicatorCode" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="report_name" jdbcType="VARCHAR" property="reportName" />
    <result column="normal_range_value" jdbcType="VARCHAR" property="normalRangeValue" />
    <result column="max_range_value" jdbcType="VARCHAR" property="maxRangeValue" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="proportion" jdbcType="DECIMAL" property="proportion" />
    <result column="abnormal_mark_type" jdbcType="INTEGER" property="abnormalMarkType" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `min_age`,`max_age`, `report_id`, `user_gender`,`indicator_code`, `indicator_name`, `report_name`,`proportion`,
    `normal_range_value`, `unit`,`abnormal_mark_type`, `yn`,
    `create_time`, `update_time`, `max_range_value`
  </sql>
  <select id="selectByIndicatorCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from examination_report_crowd
    where indicator_code = #{indicatorCode} and yn = 1
    <if test="userGender != null">
      and user_gender = #{userGender}
    </if>
    <if test="minAge != null">
      and min_age &lt;= #{minAge} and max_age &gt; #{minAge}
    </if>
  </select>
</mapper>