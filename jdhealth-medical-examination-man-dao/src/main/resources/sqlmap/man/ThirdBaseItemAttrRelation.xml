<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ThirdBaseItemAttrRelationDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.merchant.entity.ThirdBaseItemAttrRelationEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="relation_id" jdbcType="BIGINT" property="relationId"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
        <result column="source_id" jdbcType="BIGINT" property="sourceId"/>
        <result column="attribute_id" jdbcType="VARCHAR" property="attributeId"/>
        <result column="parent_attribute_id" jdbcType="VARCHAR" property="parentAttributeId"/>
        <result column="attribute_name" jdbcType="VARCHAR" property="attributeName"/>
        <result column="c_show" jdbcType="INTEGER" property="cShow"/>
        <result column="aline_type" jdbcType="INTEGER" property="alineType"/>
        <result column="icon_link" jdbcType="VARCHAR" property="iconLink"/>
        <result column="show_attr_name" jdbcType="INTEGER" property="showAttrName"/>
        <result column="attribute_value_id" jdbcType="VARCHAR" property="attributeValueId"/>
        <result column="attribute_value_name" jdbcType="VARCHAR" property="attributeValueName"/>
        <result column="value_type" jdbcType="INTEGER" property="valueType"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="replace_parent_value" jdbcType="INTEGER" property="replaceParentValue"/>
        <result column="attribute_sort" jdbcType="INTEGER" property="attributeSort"/>
        <result column="attribute_value_sort" jdbcType="INTEGER" property="attributeValueSort"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifer" jdbcType="VARCHAR" property="modifer"/>
        <result column="modifed_time" jdbcType="TIMESTAMP" property="modifedTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id, relation_id, business_type, source_id, attribute_id, parent_attribute_id, attribute_name, c_show,
            aline_type, icon_link, show_attr_name, attribute_value_id, attribute_value_name, value_type,
            unit, replace_parent_value, attribute_sort, attribute_value_sort, yn, creator, create_time, modifer, modifed_time
        </trim>
    </sql>

    <select id="queryThirdBaseItemAttrList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_base_item_attr_relation
        <where>
            <if test="itemNoList != null and itemNoList.size() != 0">
                and source_id in
                <foreach collection="itemNoList" item="itemNo" open="(" close=")" separator=",">
                    #{itemNo, jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="attributeIds != null and attributeIds.size() != 0">
                and attribute_id in
                <foreach collection="attributeIds" item="attributeId" open="(" close=")" separator=",">
                    #{attributeId, jdbcType=VARCHAR}
                </foreach>
            </if>
            and yn = 1
        </where>
    </select>

    <insert id="insertBatch"  keyProperty="id" keyColumn="id" parameterType="java.util.List">
        insert into third_base_item_attr_relation (relation_id, business_type, source_id, attribute_id,
        parent_attribute_id, attribute_name, c_show, aline_type, icon_link, show_attr_name,
        attribute_value_id, attribute_value_name, value_type,
        unit, replace_parent_value, attribute_sort, attribute_value_sort, yn, creator, create_time, modifer, modifed_time)
        values
        <foreach collection="relationEntityList" item="entity" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{entity.relationId},
                #{entity.businessType},
                #{entity.sourceId},
                #{entity.attributeId},
                #{entity.parentAttributeId},
                #{entity.attributeName},
                #{entity.cShow},
                #{entity.alineType},
                #{entity.iconLink},
                #{entity.showAttrName},
                #{entity.attributeValueId},
                #{entity.attributeValueName},
                #{entity.valueType},
                #{entity.unit},
                #{entity.replaceParentValue},
                #{entity.attributeSort},
                #{entity.attributeValueSort},
                #{entity.yn},
                #{entity.creator},
                #{entity.createTime},
                #{entity.modifer},
                #{entity.modifedTime}
            </trim>
        </foreach>
    </insert>
</mapper>