<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ItemDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.ItemEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="item_no" jdbcType="BIGINT" property="itemNo"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="item_mean" jdbcType="VARCHAR" property="itemMean"/>
        <result column="item_suitable" jdbcType="INTEGER" property="itemSuitable"/>
        <result column="item_level" jdbcType="INTEGER" property="itemLevel"/>
        <result column="item_source" jdbcType="INTEGER" property="itemSource"/>
        <result column="sku_category" jdbcType="VARCHAR" property="skuCategory"/>
        <result column="wiki_entry_id" jdbcType="VARCHAR" property="wikiEntryId"/>
        <result column="product_mode_type" jdbcType="VARCHAR" property="productModeType"/>
    </resultMap>


    <sql id="Base_Column_List">
      id, item_no, item_name, item_mean, item_suitable, item_level, create_time, item_source, sku_category, wiki_entry_id, product_mode_type
    </sql>

    <!--添加菜单-->
    <insert id="insertItem">
        insert into examination_man_base_item(
        item_no,
        item_name,
        item_mean,
        item_suitable,
        <if test="itemSource != null">
            item_source,
        </if>
        <if test="skuCategory != null and skuCategory != ''">
            sku_category,
        </if>
        <if test="wikiEntryId != null and wikiEntryId != ''">
            wiki_entry_id,
        </if>
        <if test="productModeType != null">
            product_mode_type,
        </if>
        item_level,create_user,create_time)
        values (#{itemNo},#{itemName},#{itemMean},#{itemSuitable},
        <if test="itemSource != null">
            #{itemSource,jdbcType=INTEGER},
        </if>
        <if test="skuCategory != null and skuCategory != ''">
            #{skuCategory,jdbcType=VARCHAR},
        </if>
        <if test="wikiEntryId != null and wikiEntryId != ''">
            #{wikiEntryId,jdbcType=VARCHAR},
        </if>
        <if test="productModeType != null">
            #{productModeType,jdbcType=INTEGER},
        </if>
        #{itemLevel},#{createUser},now())
    </insert>

    <!--更新菜单-->
    <update id="updateItem">
        update examination_man_base_item
        set
        item_name=#{itemName},
        item_mean=#{itemMean},
        <if test="skuCategory != null and skuCategory != ''">
            sku_category = #{skuCategory,jdbcType=VARCHAR},
        </if>
        <if test="wikiEntryId != null and wikiEntryId != ''">
            wiki_entry_id = #{wikiEntryId,jdbcType=VARCHAR},
        </if>
        <if test="productModeType != null">
            product_mode_type = #{productModeType,jdbcType=INTEGER},
        </if>
        item_suitable=#{itemSuitable},
        update_user=#{updateUser},
        update_time=now()
        where item_no=#{itemNo}
        <if test="itemSource != null">
            and item_source=#{itemSource,jdbcType=INTEGER}
        </if>
    </update>

    <!--查询所有一级菜单 根据创建时间降序-->
    <select id="getAllNewOneLevelItem"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_level = 1
        and yn = 1
        and is_new=1
        and item_source = 1
<!--        order by item_weight asc-->
    </select>

    <select id="getAllNewPOPOneLevelItem" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_level = 1
        and yn = 1
        and is_new=1
        and item_source = 2
    </select>

    <!--根据itemNo查询二级菜单项-->
    <select id="getLevelItemByItemNo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_no = #{itemNo}
        and item_level = #{level}
        and yn = 1
        and item_source = 1
    </select>

    <!--根据项目编号查询项目信息-->
    <select id="getThreeLevelItemByItemNos" resultType="com.jd.health.medical.examination.domain.bo.ItemBO">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            item_no in
            <foreach collection="itemNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and yn = 1
            and item_source = 1
        </where>
<!--        order by item_weight asc-->
    </select>

    <select id="getThreeLevelItemByItemNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            item_name in
            <foreach collection="itemNames" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and yn = 1
            and item_level = 3
            and is_new = 1
            and item_source = 1
        </where>
    </select>

    <select id="selectLowerItemsByItemNo"
            resultMap="BaseResultMap">
        <if test="itemNos != null and itemNos.size() > 0">
            select
            <include refid="Base_Column_List"/>
            from examination_man_base_item
            <where>
                item_no in
                <foreach collection="itemNos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="isNew != null">
                    and is_new = #{isNew}
                </if>
                <if test="itemLevel != null">
                    and item_level = #{itemLevel}
                </if>
                and yn = 1
            </where>
<!--            order by item_weight asc-->
        </if>
    </select>

    <select id="selectNewList"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            <if test="itemName != null and itemName !=''">
                and item_name like CONCAT('%',#{itemName},'%')
            </if>
            <if test="itemLevel != null">
                and item_level = #{itemLevel}
            </if>
            <if test="itemSuitable != null">
                and item_suitable = #{itemSuitable}
            </if>
            <if test="itemSource != null">
                and item_source = #{itemSource}
            </if>
            and yn = 1
            and is_new = 1
        </where>
        order by id desc
    </select>

    <select id="queryByParam" parameterType="com.jd.health.medical.examination.domain.personal.entity.ItemEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            <if test="itemNo != null">
                and item_no = #{itemNo,jdbcType=BIGINT}
            </if>
            <if test="itemName != null and itemName !=''">
                and item_name like CONCAT('%',#{itemName},'%')
            </if>
            <if test="itemLevel != null">
                and item_level = #{itemLevel}
            </if>
            <if test="itemSuitable != null">
                and item_suitable = #{itemSuitable}
            </if>
            <if test="itemSource != null">
                and item_source = #{itemSource}
            </if>
            <if test="skuCategory != null and skuCategory != ''">
                and sku_category = #{skuCategory,jdbcType=VARCHAR}
            </if>
            <if test="itemNos != null and itemNos.size() > 0">
                and item_no in
                <foreach collection="itemNos" item="itemNo" open="(" separator="," close=")">
                    #{itemNo}
                </foreach>
            </if>
            and yn = 1
            and is_new = 1
        </where>
    </select>

    <select id="queryByItemNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_no = #{itemNo}
        and yn = 1
        and item_source = 1
    </select>

    <select id="queryByItemNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            item_no in
            <foreach collection="itemNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and yn = 1
        </where>
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where yn = 1
        and item_source = 1
        <if test="isNew != null">
            and is_new = #{isNew}
        </if>
        <if test="itemLevelList != null and itemLevelList.size() != 0">
            and item_level in
            <foreach collection="itemLevelList" index="index" item="itemLevel" open="(" separator="," close=")">
                #{itemLevel}
            </foreach>
        </if>
    </select>

    <insert id="insertDynamic">
        insert into examination_man_base_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="itemNo != null">
                item_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="itemName != null">
                item_name,
            </if>
            <if test="itemMean != null">
                item_mean,
            </if>
            <if test="itemSuitable != null">
                item_suitable,
            </if>
            <if test="itemLevel != null">
                item_level,
            </if>
            <if test="itemSource != null">
                item_source,
            </if>
            <if test="skuCategory != null and skuCategory != ''">
                sku_category,
            </if>
            <if test="wikiEntryId != null and wikiEntryId != ''">
                wiki_entry_id,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="itemNo != null">
                #{itemNo,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="itemName != null">
                #{itemName,jdbcType=VARCHAR},
            </if>
            <if test="itemMean != null">
                #{itemMean,jdbcType=VARCHAR},
            </if>
            <if test="itemSuitable != null">
                #{itemSuitable,jdbcType=INTEGER},
            </if>
            <if test="itemLevel != null">
                #{itemLevel,jdbcType=INTEGER},
            </if>
            <if test="itemSource != null">
                #{itemSource,jdbcType=INTEGER},
            </if>
            <if test="skuCategory != null and skuCategory != ''">
                #{skuCategory,jdbcType=VARCHAR},
            </if>
            <if test="wikiEntryId != null and wikiEntryId != ''">
                #{wikiEntryId,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectItemNo" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_no = #{itemNo,jdbcType=BIGINT}
        and item_source = 1
        limit 1
    </select>

    <insert id="batchInsertItem" parameterType="java.util.List">
        insert into examination_man_base_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            item_no,
            item_name,
            item_mean,
            item_suitable,
            item_level,
            item_source,
            sku_category,
            wiki_entry_id,
            yn,
            create_time,
            create_user,
            update_time,
        </trim>
        values
        <foreach collection="list" index="index" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.itemNo,jdbcType=BIGINT},
                #{item.itemName,jdbcType=VARCHAR},
                #{item.itemMean,jdbcType=VARCHAR},
                #{item.itemSuitable,jdbcType=INTEGER},
                #{item.itemLevel,jdbcType=INTEGER},
                #{item.itemSource,jdbcType=INTEGER},
                #{item.skuCategory,jdbcType=VARCHAR},
                #{item.wikiEntryId,jdbcType=VARCHAR},
                1,
                now(),
                'system',
                now(),
            </trim>
        </foreach>
    </insert>

    <select id="selectByName"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            <if test="itemName != null and itemName !=''">
                and item_name = #{itemName}
            </if>
            <if test="itemLevel != null">
                and item_level = #{itemLevel}
            </if>
            and yn = 1
            and is_new = 1
            <if test="itemSource != null">
                and item_source = #{itemSource,jdbcType=INTEGER}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getDetailByParam"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        <where>
            <if test="itemName != null and itemName !=''">
                and item_name like CONCAT('%',#{itemName},'%')
            </if>
            <if test="itemLevel != null">
                and item_level = #{itemLevel}
            </if>
            <if test="itemSuitable != null">
                and item_suitable = #{itemSuitable}
            </if>
            <if test="itemSource != null">
                and item_source = #{itemSource}
            </if>
            <if test="skuCategory != null and skuCategory != ''">
                and sku_category = #{skuCategory,jdbcType=VARCHAR}
            </if>
            <if test="itemNo != null">
                and item_no = #{itemNo,jdbcType=BIGINT}
            </if>
            <if test="isNew != null">
                and is_new = #{isNew}
            </if>
            and yn = 1
        </where>
    </select>
    <select id="queryFirstByItemName"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_Name = #{itemName}
        and yn = 1
        and item_source = 2
        and item_level = 1
    </select>
    <select id="querySecondByItemName"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_Name = #{itemName}
        and yn = 1
        and item_source = 2
        and item_level = 2
    </select>
    <select id="queryThirdByItemName"
             resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_Name = #{itemName}
        and yn = 1
        and item_source = 2
        and item_level = 3
    </select>

    <select id="queryByItemNoLevelTwo"
            resultType="com.jd.health.medical.examination.domain.personal.entity.ItemEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where item_no = #{itemNo}
        and yn = 1
        and item_source = 2
    </select>

    <select id="queryByItemName"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_base_item
        where
        item_name in
        <foreach collection="itemNames" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="itemSource != null">
            and item_source=#{itemSource,jdbcType=INTEGER}
        </if>
        <if test="itemLevel != null">
            and item_level = #{itemLevel}
        </if>
        and yn = 1
    </select>
</mapper>