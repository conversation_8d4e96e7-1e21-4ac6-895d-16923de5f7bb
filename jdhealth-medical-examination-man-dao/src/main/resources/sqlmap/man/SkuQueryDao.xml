<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.query.SkuQueryDao">

    <!-- skuInfo -->
    <resultMap type="com.jd.health.medical.examination.domain.query.sku.SkuGroupEntity" id="productGroup">
        <result property="skuNo" column="sku_no"/>
        <result property="skuName" column="sku_name"/>
        <result property="groupNo" column="group_no"/>
        <result property="groupSuitable" column="sku_suitable"/>
        <result property="skuPrice" column="sku_price"/>
    </resultMap>


    <select id="selectProductGroupList" resultMap="productGroup">
        SELECT
        sku_no,
        sku_name,
        s.group_no,
        sku_suitable,
        sku_price
        FROM
        examination_man_sku s
        <where>
            <if test="bo.groupSuitable != null">
                and sku_suitable LIKE CONCAT('%',#{bo.groupSuitable}, '%')
            </if>
            <if test="bo.groupSuitable != null and bo.groupSuitable=='13'">
                or sku_suitable LIKE CONCAT('%','123', '%')
            </if>
            <if test="bo.skuNo != null and bo.skuNo!=''">
                and sku_no = #{bo.skuNo,jdbcType=VARCHAR}
            </if>
            <if test="bo.skuName != null and bo.skuName!=''">
                and sku_name LIKE CONCAT('%',#{bo.skuName}, '%')
            </if>

            <if test="groupNos != null and groupNos.size() > 0">
                and group_no in
                <foreach collection="groupNos" item="groupNo" open="(" separator="," close=")">
                    #{groupNo}
                </foreach>
            </if>
            and sku_union_type = 1
            and s.group_no is not null
            and s.yn = 1 and s.sku_status != 3
        </where>
    </select>
</mapper>