<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.HospitalDepartmentDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.HospitalDepartment">
    <!--@mbg.generated-->
    <!--@Table examination_hospital_department-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="department_no" jdbcType="BIGINT" property="departmentNo" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="department_desc" jdbcType="VARCHAR" property="departmentDesc" />
    <result column="parent_department_no" jdbcType="BIGINT" property="parentDepartmentNo" />
    <result column="department_level" jdbcType="INTEGER" property="departmentLevel" />
    <result column="yn" jdbcType="INTEGER" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, department_no, department_name, department_desc, parent_department_no, department_level, 
    yn, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from examination_hospital_department
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="getHospitalDepartmentList" parameterType="com.jd.health.medical.examination.domain.HospitalDepartment" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_hospital_department
    <where>
      <if test="departmentNo != null">
        and department_no = #{departmentNo,jdbcType=BIGINT}
      </if>
      <if test="departmentName != null">
        and department_name = #{departmentName,jdbcType=VARCHAR}
      </if>
      <if test="departmentDesc != null">
        and department_desc = #{departmentDesc,jdbcType=VARCHAR}
      </if>
      <if test="parentDepartmentNo != null">
        and parent_department_no = #{parentDepartmentNo,jdbcType=BIGINT}
      </if>
      <if test="departmentLevel != null">
        and department_level = #{departmentLevel,jdbcType=INTEGER}
      </if>
      and yn = 1
    </where>
  </select>
  <select id="getAllHospitalDepartmentList" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_hospital_department
    where yn = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from examination_hospital_department
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.HospitalDepartment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_hospital_department (department_no, department_name, department_desc, 
      parent_department_no, department_level, yn, 
      create_user, create_time, update_user, 
      update_time)
    values (#{departmentNo,jdbcType=BIGINT}, #{departmentName,jdbcType=VARCHAR}, #{departmentDesc,jdbcType=VARCHAR}, 
      #{parentDepartmentNo,jdbcType=BIGINT}, #{departmentLevel,jdbcType=INTEGER}, #{yn,jdbcType=INTEGER}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.HospitalDepartment" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_hospital_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="departmentNo != null">
        department_no,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="departmentDesc != null">
        department_desc,
      </if>
      <if test="parentDepartmentNo != null">
        parent_department_no,
      </if>
      <if test="departmentLevel != null">
        department_level,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="departmentNo != null">
        #{departmentNo,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="departmentDesc != null">
        #{departmentDesc,jdbcType=VARCHAR},
      </if>
      <if test="parentDepartmentNo != null">
        #{parentDepartmentNo,jdbcType=BIGINT},
      </if>
      <if test="departmentLevel != null">
        #{departmentLevel,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.domain.HospitalDepartment">
    <!--@mbg.generated-->
    update examination_hospital_department
    <set>
      <if test="departmentNo != null">
        department_no = #{departmentNo,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="departmentDesc != null">
        department_desc = #{departmentDesc,jdbcType=VARCHAR},
      </if>
      <if test="parentDepartmentNo != null">
        parent_department_no = #{parentDepartmentNo,jdbcType=BIGINT},
      </if>
      <if test="departmentLevel != null">
        department_level = #{departmentLevel,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.HospitalDepartment">
    <!--@mbg.generated-->
    update examination_hospital_department
    set department_no = #{departmentNo,jdbcType=BIGINT},
      department_name = #{departmentName,jdbcType=VARCHAR},
      department_desc = #{departmentDesc,jdbcType=VARCHAR},
      parent_department_no = #{parentDepartmentNo,jdbcType=BIGINT},
      department_level = #{departmentLevel,jdbcType=INTEGER},
      yn = #{yn,jdbcType=INTEGER},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>