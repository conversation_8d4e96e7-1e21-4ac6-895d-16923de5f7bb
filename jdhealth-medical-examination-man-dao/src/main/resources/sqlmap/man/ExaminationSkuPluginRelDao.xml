<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationSkuPluginRelDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.SkuPluginRel" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
    <result column="group_no" property="groupNo" jdbcType="BIGINT" />
    <result column="sku_plugin_no" property="skuPluginNo" jdbcType="VARCHAR" />
    <result column="channel_no" property="channelNo" jdbcType="BIGINT" />
    <result column="goods_id" property="goodsId" jdbcType="VARCHAR" />
    <result column="goods_plugin_id" property="goodsPluginId" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku_no, group_no, sku_plugin_no, channel_no, goods_id, goods_plugin_id, yn, create_time, update_time
  </sql>
  
  <select id="queryList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from examination_man_sku_plugin_rel
      <where>
        <if test="skuNo != null and skuNo != ''">
          and sku_no = #{skuNo}
        </if>
        <if test="groupNo != null">
          and group_no = #{groupNo}
        </if>
        <if test="skuPluginNo != null and skuPluginNo != ''">
          and sku_plugin_no = #{skuPluginNo}
        </if>
        <if test="channelNo != null">
          and channel_no = #{channelNo}
        </if>
        <if test="goodsId != null and goodsId != ''">
          and goods_id = #{goodsId}
        </if>
        <if test="goodsPluginId != null and goodsPluginId != ''">
          and goods_plugin_id = #{goodsPluginId}
        </if>
        <if test="skuPluginNoList != null and skuPluginNoList.size() > 0">
          and sku_plugin_no in
          <foreach collection="skuPluginNoList" item="itemNo" open="(" separator="," close=")">
            #{itemNo}
          </foreach>
        </if>
      </where>
      and yn = 1
  </select>

  <insert id="addPluginToSku" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuPluginRel">
    insert into `examination_man_sku_plugin_rel`  (sku_no, group_no, sku_plugin_no, channel_no, goods_id, goods_plugin_id)
    values (
    #{skuNo,jdbcType=VARCHAR},
    #{groupNo,jdbcType=BIGINT},
    #{skuPluginNo,jdbcType=VARCHAR},
    #{channelNo,jdbcType=BIGINT},
    #{goodsId,jdbcType=VARCHAR},
    #{goodsPluginId,jdbcType=VARCHAR})
  </insert>

  <update id="deleteRelations" parameterType="com.jd.health.medical.examination.domain.personal.entity.SkuPluginRel">
    update examination_man_sku_plugin_rel
    set yn = 0
    <where>
      <if test="skuNo != null and skuNo !=''">
        and sku_no = #{skuNo}
      </if>
      <if test="groupNo != null">
        and group_no = #{groupNo}
      </if>
      <if test="skuPluginNo != null and skuPluginNo !=''">
        and sku_plugin_no = #{skuPluginNo}
      </if>
      <if test="channelNo != null">
        and channel_no = #{channelNo}
      </if>
      <if test="goodsId != null and goodsId != ''">
        and goods_id = #{goodsId}
      </if>
      <if test="goodsPluginId != null and goodsPluginId != ''">
        and goods_plugin_id = #{goodsPluginId}
      </if>
      and yn = 1
    </where>

  </update>

  <select id="selectCount" resultType="java.lang.Integer">
    select count(*) from examination_man_sku_plugin_rel
    <where>
      <if test="skuNo != null and skuNo !=''">
        and sku_no = #{skuNo}
      </if>
      <if test="groupNo != null">
        and group_no = #{groupNo}
      </if>
      <if test="skuPluginNo != null and skuPluginNo !=''">
        and sku_plugin_no = #{skuPluginNo}
      </if>
      <if test="channelNo != null">
        and channel_no = #{channelNo}
      </if>
      <if test="goodsId != null and goodsId != ''">
        and goods_id = #{goodsId}
      </if>
      <if test="goodsPluginId != null and goodsPluginId != ''">
        and goods_plugin_id = #{goodsPluginId}
      </if>
      and yn = 1
    </where>
  </select>

  <select id="listPluginSku"  resultType="java.lang.String">
    select distinct(sku_plugin_no) from examination_man_sku_plugin_rel
    where yn = 1
    and sku_no = #{skuNo}
  </select>

</mapper>