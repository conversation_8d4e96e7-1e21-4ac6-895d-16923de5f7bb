<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.PackageItemDetailEntityDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.PackageItemDetailEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="sku_alias" property="skuAlias" jdbcType="VARCHAR" />
    <result column="sku_price" property="skuPrice" jdbcType="INTEGER" />
    <result column="group_name" property="groupName" jdbcType="VARCHAR" />
    <result column="group_no" property="groupNo" jdbcType="BIGINT" />
    <result column="equity_type" property="equityType" jdbcType="INTEGER" />
    <result column="item_suitable" property="itemSuitable" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <select id="selectAll" resultMap="BaseResultMap" >
    select id, sku_no, sku_name, sku_alias, sku_price, group_name, group_no, equity_type, 
    item_suitable, create_time, update_time
    from examination_man_equity_package_item_detail
  </select>

</mapper>