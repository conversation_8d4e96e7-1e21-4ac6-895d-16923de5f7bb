<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.EquityGoodsDetailDao">

    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.personal.entity.EquityGoodsDetailEntity">
        <id column="id" property="id" />
        <result column="equity_composition_id" property="equityCompositionId" />
        <result column="equity_composition_name" property="equityCompositionName" />
        <result column="group_no" property="groupNo" />
        <result column="group_name" property="groupName" />
        <result column="sku_no" property="skuNo" />
        <result column="sku_name" property="skuName" />
        <result column="equity_price" property="equityPrice" />
        <result column="equity_type" property="equityType" />
        <result column="item_suitable" property="itemSuitable" />
        <result column="yn" property="yn" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="BaseBase_Column_List">
        equity_composition_id
        ,equity_composition_name
        ,group_no
        ,group_name
        ,sku_no
        ,sku_name
        ,sku_alias
        ,equity_price
        ,equity_type
        ,item_suitable
    </sql>
    <insert id="insertGoodsDetailList">
        insert into examination_man_equity_goods_detail(equity_composition_id,equity_composition_name,group_no,group_name,sku_no,sku_name,sku_alias,equity_price,equity_type,item_suitable,create_time,update_time)
        values
        <foreach collection="list" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{equityCompositionId},
                #{equityCompositionName},
                #{item.groupNo},
                #{item.groupName},
                #{item.skuNo},
                #{item.skuName},
                #{item.skuAlias},
                #{item.equityPrice},
                #{item.equityType},
                #{item.itemSuitable},
                now(),
                now(),
            </trim>
        </foreach>
    </insert>
    <update id="deleteByEquityCompositionId">
        update examination_man_equity_goods_detail
        set yn = 0,update_time=now()
        where equity_composition_id=#{equityCompositionId}
        and yn = 1
    </update>

    <select id="selectByEquityCompositionId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="BaseBase_Column_List" />
        from examination_man_equity_goods_detail
        <where>
            equity_composition_id = #{equityCompositionId, jdbcType=BIGINT} and yn = 1
        </where>
    </select>

    <select id="selectSkuNoListByEquityCompositionId" parameterType="java.lang.Long" resultType="java.lang.String">
        select
            sku_no
        from examination_man_equity_goods_detail
        <where>
            equity_composition_id = #{equityCompositionId, jdbcType=BIGINT} and yn = 1
        </where>
    </select>
    <select id="getGoodsDetailByEquityCompositionId" resultMap="BaseResultMap">
        select
        <include refid="BaseBase_Column_List"/>
        from examination_man_equity_goods_detail
        <where>
            equity_composition_id = #{equityCompositionId, jdbcType=BIGINT} and yn = 1
        </where>
    </select>
    <select id="selectGoodsDetailByEquityCompositionId"
            resultMap="BaseResultMap">
        select
        <include refid="BaseBase_Column_List" />
        from examination_man_equity_goods_detail
        <where>
            equity_composition_id = #{equityCompositionId, jdbcType=BIGINT} and yn = 1
        </where>
    </select>

    <update id="updateSelective">
        update examination_man_equity_goods_detail
        <set>
            <if test="skuAlias != null and skuNo != ''">
                sku_alias = #{skuAlias,jdbcType=VARCHAR},
            </if>
        </set>
        where yn = 1
        and equity_composition_id = #{equityCompositionId,jdbcType=BIGINT}
        and sku_no = #{skuNo,jdbcType=VARCHAR}
    </update>

    <select id="selectEquityCompositionBySkuNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="BaseBase_Column_List">
        </include>
        from examination_man_equity_goods_detail
        <where>
            sku_no = #{skuNo}
            and yn = 1
        </where>
    </select>
</mapper>