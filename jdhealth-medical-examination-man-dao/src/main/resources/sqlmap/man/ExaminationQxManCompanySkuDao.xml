<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.ExaminationQxManCompanySkuDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanySkuEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_no" property="companyNo" jdbcType="BIGINT"/>
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="sku_price" property="skuPrice" jdbcType="INTEGER"/>
        <result column="sku_jd_price" property="skuJdPrice" jdbcType="INTEGER"/>
        <result column="sku_market_price" property="skuMarketPrice" jdbcType="INTEGER"/>
        <result column="sku_type" property="skuType" jdbcType="TINYINT"/>
        <result column="sku_buyer_num" property="skuBuyerNum" jdbcType="VARCHAR"/>
        <result column="sku_desc" property="skuDesc" jdbcType="VARCHAR"/>
        <result column="second_category_id" property="secondCategoryId" jdbcType="VARCHAR"/>
        <result column="second_category_name" property="secondCategoryName" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, company_no,sku_no, sku_name, sku_price, sku_market_price,sku_type, sku_buyer_num, sku_desc,
        second_category_id,second_category_name,category_id,category_name,remark,create_time,create_user,
        update_time,update_user,yn,sku_jd_price
    </sql>

    <insert id="insert" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanySkuEntity" >
        insert into examination_qx_man_company_sku (
            company_no,
            sku_no,
            sku_name,
            sku_price,
            sku_market_price,
            sku_type,
            sku_buyer_num,
            sku_desc,
            second_category_id,
            second_category_name,
            category_id,
            category_name,
            remark,
            create_time,
            create_user,
            update_time,
            update_user,
            yn,
            sku_jd_price)
        values (
            #{companyNo},
            #{skuNo},
            #{skuName},
            #{skuPrice},
            #{skuMarketPrice},
            #{skuType},
            #{skuBuyerNum},
            #{skuDesc},
            #{secondCategoryId},
            #{secondCategoryName},
            #{categoryId},
            #{categoryName},
            #{remark},
            now(),
            #{createUser},
            now(),
            #{updateUser},
            #{yn},
            #{skuJdPrice})
    </insert>

    <update id="updateByCompanyNoAndSkuNo"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanySkuEntity">
        UPDATE examination_qx_man_company_sku
        <set>
            <if test="skuName != null and skuName != ''" >
                sku_name = #{skuName},
            </if>
            <if test="skuPrice != null" >
                sku_price = #{skuPrice},
            </if>
            <if test="skuJdPrice != null" >
                sku_jd_price = #{skuJdPrice},
            </if>
            <if test="skuMarketPrice != null" >
                sku_market_price = #{skuMarketPrice},
            </if>
            <if test="skuType != null" >
                sku_type = #{skuType},
            </if>
            <if test="skuBuyerNum != null" >
                sku_buyer_num = #{skuBuyerNum},
            </if>
            <if test="skuDesc != null and skuDesc != ''" >
                sku_desc = #{skuDesc},
            </if>
            <if test="secondCategoryId != null and secondCategoryId != ''" >
                second_category_id = #{secondCategoryId},
            </if>
            <if test="secondCategoryName != null and secondCategoryName != ''" >
                second_category_name = #{secondCategoryName},
            </if>
            <if test="categoryId != null and categoryId != ''" >
                category_id = #{categoryId},
            </if>
            <if test="categoryName != null and categoryName != ''" >
                category_name = #{categoryName},
            </if>
            <if test="remark != null and remark != ''" >
                remark = concat(ifnull(remark,''),#{remark}),
            </if>
            <if test="updateUser != null and updateUser != ''" >
                update_user = #{updateUser},
            </if>
            <if test="yn != null" >
                yn = #{yn},
            </if>
            update_time = now()
        </set>
        WHERE company_no=#{companyNo}
        AND sku_no=#{skuNo}
        AND yn = 1
    </update>

    <select id="selectListByCompanyNoOrSkuNo" resultMap="BaseResultMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.ExaminationQxManCompanySkuEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM examination_qx_man_company_sku
        <where>
            yn = 1
            <if test="skuNo != null and skuNo != ''">
                AND sku_no = #{skuNo}
            </if>
            <if test="companyNo != null">
                AND company_no=#{companyNo}
            </if>
            <if test="skuType != null" >
                sku_type = #{skuType}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectEntityByCompanyNoOrSkuNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM examination_qx_man_company_sku
        <where>
            yn = 1
            <if test="skuNo != null and skuNo != ''">
                AND sku_no = #{skuNo}
            </if>
            <if test="companyNo != null">
                AND company_no=#{companyNo}
            </if>
        </where>
    </select>
</mapper>