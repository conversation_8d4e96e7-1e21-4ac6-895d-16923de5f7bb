<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.supplier.XfylManApprovalRecordDao" >
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.approval.XfylManApprovalRecordEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="approval_no" jdbcType="VARCHAR" property="approvalNo" />
        <result column="business_type" jdbcType="TINYINT" property="businessType" />
        <result column="channel_no" jdbcType="BIGINT" property="channelNo" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="relation_no" jdbcType="VARCHAR" property="relationNo" />
        <result column="extend_no" jdbcType="VARCHAR" property="extendNo" />
        <result column="extend_url" jdbcType="VARCHAR" property="extendUrl" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
        <result column="result" jdbcType="VARCHAR" property="result" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="yn" jdbcType="TINYINT" property="yn" />
    </resultMap>
    <sql id="Base_Column_List">
        id, approval_no, business_type, channel_no, content, `type`, relation_no, extend_no, extend_url,`status`, submit_time, result,
        create_user, create_time, update_user, update_time, yn
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.approval.XfylManApprovalRecordEntity" useGeneratedKeys="true">
        insert into xfyl_man_approval_record (approval_no, business_type, channel_no, content,
          `type`, relation_no, extend_no, extend_url, `status`,
          submit_time, result, create_user, create_time, update_user,
          yn)
        values (#{approvalNo,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, #{channelNo,jdbcType=BIGINT}, #{content,jdbcType=VARCHAR},
          #{type,jdbcType=TINYINT}, #{relationNo,jdbcType=VARCHAR}, #{extendNo,jdbcType=VARCHAR}, #{extendUrl,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
          #{submitTime,jdbcType=TIMESTAMP}, #{result,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
          #{yn,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.approval.XfylManApprovalRecordEntity" useGeneratedKeys="true">
        insert into xfyl_man_approval_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="approvalNo != null and approvalNo != ''">
                approval_no,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="channelNo != null">
                channel_no,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="relationNo != null">
                relation_no,
            </if>
            <if test="extendNo != null">
                extend_no,
            </if>
            <if test="extendUrl != null and extendUrl != ''">
                extend_url,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="submitTime != null">
                submit_time,
            </if>
            <if test="result != null">
                result,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user,
            </if>
            <if test="yn != null">
                yn,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="approvalNo != null and approvalNo != ''">
                #{approvalNo,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="channelNo != null">
                #{channelNo,jdbcType=BIGINT},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="relationNo != null">
                #{relationNo,jdbcType=VARCHAR},
            </if>
            <if test="extendNo != null">
                #{extendNo,jdbcType=VARCHAR},
            </if>
            <if test="extendUrl != null and extendUrl != ''">
                #{extendUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="submitTime != null">
                #{submitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="result != null">
                #{result,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null and updateUser != ''">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="yn != null">
                #{yn,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateSelective">
        update xfyl_man_approval_record
        <set>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        where approval_no = #{approvalNo,jdbcType=VARCHAR}
        and yn = 1
    </update>

    <!--通过实体作为筛选条件查询-->
    <select id="queryByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xfyl_man_approval_record
        <where>
            yn = 1
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="typeList != null and typeList.size() != 0">
                and type in
                <foreach collection="typeList" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="relationNo != null and relationNo != ''">
                and relation_no = #{relationNo}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="statusList != null and statusList.size > 0 ">
                and status in
                <foreach collection="statusList" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="startTime != null">
                and create_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[ <= #{endTime,jdbcType=TIMESTAMP} ]]>
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="result != null and result != ''">
                and result = #{result,jdbcType=VARCHAR}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="content != null and content != ''">
                and content like CONCAT('%',#{content,jdbcType=VARCHAR},'%')
            </if>
            <if test="approvalNos != null and approvalNos.size > 0 ">
                and approval_no in
                <foreach collection="approvalNos" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="approvalNo != null and approvalNo !='' ">
                and approval_no = #{approvalNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="queryByApprovalNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xfyl_man_approval_record
        where yn = 1
        and approval_no = #{approvalNo,jdbcType=VARCHAR}
    </select>
    <select id="queryByRelationNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xfyl_man_approval_record
        where yn = 1
        and relation_no = #{relationNo,jdbcType=VARCHAR}
    </select>

    <select id="selectReportApprovalStatistical" resultType="com.jd.health.medical.examination.domain.approval.ReportApprovalStatistical">
        select
        channel_no as brandId,
        status,
        result,
        count(*)  as count
        from xfyl_man_approval_record
        where type = #{type}
        and business_type = #{businessType}
        and STATUS in
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{startDate} and #{endDate}
        and yn = 1
        GROUP BY channel_no,status,result
    </select>
</mapper>