<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.ReportAuthDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.third.ReportAuthEntity" id="myReportMap">
        <result property="id" column="id"/>
        <result property="channelNo" column="channel_no"/>
        <result property="jdAppointmentId" column="jd_appointment_id"/>
        <result property="appointmentNo" column="appointment_no"/>
        <result property="reportAuthUrl" column="report_auth_url"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            channel_no,
            jd_appointment_id,
            appointment_no,
            report_auth_url,
            yn,
            create_time,
            update_time,
        </trim>
    </sql>


    <!-- 插入实体 -->
    <insert id="insertReportAuth" parameterType="com.jd.health.medical.examination.domain.MyReportEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_report_auth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            channel_no,
            jd_appointment_id,
            appointment_no,
            report_auth_url,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{channelNo,jdbcType=BIGINT},
            #{jdAppointmentId,jdbcType=BIGINT},
            #{appointmentNo,jdbcType=VARCHAR},
            #{reportAuthUrl,jdbcType=VARCHAR},
            1,
            now(),
            now(),
        </trim>
    </insert>

    <!-- 修改-->
    <update id="updateReportAuth" parameterType="com.jd.health.medical.examination.domain.third.ReportAuthEntity">
        update examination_man_report_auth
        set report_auth_url = #{reportAuthUrl,jdbcType=VARCHAR},
            appointment_no = #{appointmentNo,jdbcType=VARCHAR}
        where jd_appointment_id=#{jdAppointmentId}
        and channel_no = #{channelNo}
        and yn = 1
    </update>

    <!--查询授权详情 url-->
    <select id="queryReportAuthDetail" resultType="com.jd.health.medical.examination.domain.third.ReportAuthEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_report_auth
        where jd_appointment_id=#{jdAppointmentId}
        and channel_no = #{channelNo}
        and yn = 1
    </select>

    <!--取消预约后删除对应的授权记录-->
    <select id="deleteReportAuth" resultType="com.jd.health.medical.examination.domain.third.ReportAuthEntity">
        delete from examination_man_report_auth
        where jd_appointment_id=#{jdAppointmentId}
        and channel_no = #{channelNo}
        and yn = 1
    </select>


</mapper>