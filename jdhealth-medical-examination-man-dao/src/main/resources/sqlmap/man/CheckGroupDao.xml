<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.CheckGroupDao">
    <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.CheckGroupEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_no" property="groupNo" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR"/>
        <result column="group_type" property="groupType" jdbcType="INTEGER"/>
        <result column="channel_no" property="channelNo" jdbcType="BIGINT"/>
        <result column="channel_name" property="channelName" jdbcType="VARCHAR"/>
        <result column="keywords" property="keywords" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            group_no,
            group_name,
            sku_no,
            group_type,
            channel_no,
            channel_name,
            keywords,
            status,
            yn,
            create_user,
            create_time,
            update_user,
            update_time,
        </trim>
    </sql>

    <!--添加检验套餐-->
    <insert id="addCheckGroup">
        insert into examination_man_check_group
            (group_no,
            group_name,
            sku_no,
            group_type,
            channel_no,
            channel_name,
            keywords,
            status,
            yn,
            create_user,
            create_time,
            update_user,
            update_time)
        values (#{groupNo},
                #{groupName},
                #{skuNo},
                #{groupType},
                #{channelNo},
                #{channelName},
                #{keywords},
                #{status},
                1,
                #{createUser},
                now(),
                #{updateUser},
                now())
    </insert>

    <update id="updateCheckGroup">
        update examination_man_check_group
        set group_name   = #{groupName},
            sku_no       = #{skuNo},
            group_type   = #{groupType},
            status       = #{status},
            channel_no   = #{channelNo},
            channel_name = #{channelName},
            keywords = #{keywords},
            update_user  = #{updateUser},
            update_time  = now()
        where group_no = #{groupNo}
          and yn = 1
    </update>
    <update id="updateCheckGroupStatus">
        update examination_man_check_group
        set status=#{status},
            update_time  = now()
        where group_no = #{groupNo}
          and yn = 1
    </update>

    <delete id="deleteCheckGroupByNo">
        delete
        from examination_man_check_group
        where group_no = #{groupNo}
          and yn = 1
    </delete>

    <select id="queryCheckGroupPage" resultType="com.jd.health.medical.examination.domain.CheckGroupEntity">
        <!--select
        <include refid="Base_Column_List"></include>
        from examination_man_check_group
        <where>
            <if test="skuNo != null and skuNo != ''">
                sku_no = #{skuNo}
            </if>
            <if test="groupNo != null">
                group_no = #{groupNo}
            </if>
            <if test="groupName != null and groupName != ''">
                and group_name like CONCAT("%",#{groupName,jdbcType=VARCHAR},"%")
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            and yn = 1
        </where>-->
        SELECT
        a.id AS id,
        a.channel_no AS channelNo,
        a.channel_name AS channelName,
        a.keywords as keywords,
        a.group_type AS groupType,
        a.sku_no AS skuNo,
        a.STATUS AS STATUS,
        b.template_id AS templateId,
        b.template_name AS templateName
        FROM
        examination_man_check_group a
        JOIN examination_man_check_group_template b ON a.group_no = b.group_no
        JOIN examination_man_check_template c ON c.template_id = b.template_id
        <where>
            <if test="skuNo != null and skuNo != ''">
                a.sku_no = #{skuNo}
            </if>
            <if test="groupNo != null">
                a.group_no = #{groupNo}
            </if>
            <if test="groupName != null and groupName != ''">
                and a.group_name like CONCAT("%",#{groupName,jdbcType=VARCHAR},"%")
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="template_name != null and template_name != ''">
                and b.template_name = #{templateName}
            </if>
            and yn = 1
        </where>
    </select>

    <select id="queryCheckGroupByNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_check_group
        <where>
            group_no = #{groupNo}
            and yn = 1
        </where>
    </select>

    <select id="queryGroupById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_check_group
        where id = #{id}
    </select>

    <select id="queryCheckGroupBySkuNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_check_group
        where sku_no = #{skuNo}
        and yn = 1 limit 1
    </select>

    <select id="queryProviderNoByGroupNo" resultType="java.lang.Long" parameterType="java.util.Set">
        select channel_no
        from  examination_man_check_group
        where group_no in <foreach collection="doorGroupNos" item ="item" open="(" separator="," close=")"> #{item}</foreach>
        and yn = 1
    </select>
</mapper>