<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.report.ExaminationIndicatorAliasDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO">
    <!--@mbg.generated-->
    <!--@Table examination_indicator_alias-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="indicator_code" jdbcType="VARCHAR" property="indicatorCode" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="ws_code" jdbcType="VARCHAR" property="wsCode" />
    <result column="indicator_alias_code" jdbcType="VARCHAR" property="indicatorAliasCode" />
    <result column="indicator_alias_name" jdbcType="VARCHAR" property="indicatorAliasName" />
    <result column="relevance_status" jdbcType="TINYINT" property="relevanceStatus" />
    <result column="operation_user" jdbcType="VARCHAR" property="operationUser" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, indicator_code, indicator_name, ws_code, indicator_alias_code, indicator_alias_name, 
    relevance_status, operation_user, operation_time, `source`, yn, create_time, update_time, 
    create_user, update_user,department_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from examination_indicator_alias
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from examination_indicator_alias
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_indicator_alias (indicator_code, indicator_name, ws_code, 
      indicator_alias_code, indicator_alias_name, 
      relevance_status, operation_user, operation_time, 
      `source`, yn, create_time, 
      update_time, create_user, update_user
      )
    values (#{indicatorCode,jdbcType=VARCHAR}, #{indicatorName,jdbcType=VARCHAR}, #{wsCode,jdbcType=VARCHAR}, 
      #{indicatorAliasCode,jdbcType=VARCHAR}, #{indicatorAliasName,jdbcType=VARCHAR}, 
      #{relevanceStatus,jdbcType=TINYINT}, #{operationUser,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP}, 
      #{source,jdbcType=INTEGER}, #{yn,jdbcType=TINYINT}, now(),
      now(), #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into examination_indicator_alias
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="indicatorCode != null">
        indicator_code,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="wsCode != null">
        ws_code,
      </if>
      <if test="indicatorAliasCode != null">
        indicator_alias_code,
      </if>
      <if test="indicatorAliasName != null">
        indicator_alias_name,
      </if>
      <if test="relevanceStatus != null">
        relevance_status,
      </if>
      <if test="operationUser != null">
        operation_user,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="indicatorCode != null">
        #{indicatorCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="wsCode != null">
        #{wsCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorAliasCode != null">
        #{indicatorAliasCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorAliasName != null">
        #{indicatorAliasName,jdbcType=VARCHAR},
      </if>
      <if test="relevanceStatus != null">
        #{relevanceStatus,jdbcType=TINYINT},
      </if>
      <if test="operationUser != null">
        #{operationUser,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateBySelective" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO">
    <!--@mbg.generated-->
    update examination_indicator_alias
    <set>
      <if test="indicatorCode != null">
        indicator_code = #{indicatorCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="wsCode != null">
        ws_code = #{wsCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorAliasCode != null">
        indicator_alias_code = #{indicatorAliasCode,jdbcType=VARCHAR},
      </if>
      <if test="indicatorAliasName != null">
        indicator_alias_name = #{indicatorAliasName,jdbcType=VARCHAR},
      </if>
      <if test="relevanceStatus != null">
        relevance_status = #{relevanceStatus,jdbcType=TINYINT},
      </if>
      <if test="operationUser != null">
        operation_user = #{operationUser,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
    </set>
    <where>
      and indicator_code = #{indicatorCode,jdbcType=VARCHAR}
      <if test="indicatorAliasOldName != null and indicatorAliasOldName != ''">
        and indicator_alias_name = #{indicatorAliasOldName,jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO">
    <!--@mbg.generated-->
    update examination_indicator_alias
    set indicator_code = #{indicatorCode,jdbcType=VARCHAR},
      indicator_name = #{indicatorName,jdbcType=VARCHAR},
      ws_code = #{wsCode,jdbcType=VARCHAR},
      indicator_alias_code = #{indicatorAliasCode,jdbcType=VARCHAR},
      indicator_alias_name = #{indicatorAliasName,jdbcType=VARCHAR},
      relevance_status = #{relevanceStatus,jdbcType=TINYINT},
      operation_user = #{operationUser,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      `source` = #{source,jdbcType=INTEGER},
      yn = #{yn,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByParam" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from examination_indicator_alias
    where yn = 1
    <if test="wsCode != null and wsCode != ''">
      and ws_code = #{wsCode,jdbcType=VARCHAR}
    </if>
    <if test="indicatorCode != null and indicatorCode != ''">
      and indicator_code = #{indicatorCode,jdbcType=VARCHAR}
    </if>
    <if test="indicatorName != null and indicatorName != ''">
      and indicator_name like CONCAT("%",#{indicatorName,jdbcType=VARCHAR},"%")
    </if>
    <if test="indicatorAliasName != null and indicatorAliasName != ''">
      and indicator_alias_name like CONCAT("%",#{indicatorAliasName,jdbcType=VARCHAR},"%")
    </if>
    <if test="source != null">
      and source = #{source,jdbcType=INTEGER}
    </if>
  </select>
  <update id="updateByIndicatorCode" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO">
    <!--@mbg.generated-->
    update examination_indicator_alias
    <set>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="wsCode != null">
        ws_code = #{wsCode,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
    </set>
    where indicator_code = #{indicatorCode,jdbcType=VARCHAR}
  </update>
  <update id="deleteByIndicatorCodeAndAlias" parameterType="com.jd.health.medical.examination.dao.report.DO.ExaminationIndicatorAliasDO">
    update examination_indicator_alias
    <set>
      <if test="operationUser != null">
        operation_user = #{operationUser,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      yn = 0,
    </set>
    where indicator_code = #{indicatorCode,jdbcType=VARCHAR}
    and indicator_alias_name = #{indicatorAliasName,jdbcType=VARCHAR}
  </update>
</mapper>