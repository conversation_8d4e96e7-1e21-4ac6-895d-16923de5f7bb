<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.dao.enterprise.CustomerDao" >
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.enterprise.entity.CustomerEntity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="customer_no" property="customerNo" jdbcType="BIGINT" />
    <result column="customer_simple_name" property="customerSimpleName" jdbcType="VARCHAR" />
    <result column="customer_name" property="customerName" jdbcType="VARCHAR" />
    <result column="customer_license_no" property="customerLicenseNo" jdbcType="VARCHAR" />
    <result column="customer_scale" property="customerScale" jdbcType="INTEGER" />
    <result column="customer_address" property="customerAddress" jdbcType="VARCHAR" />
    <result column="customer_lng" property="customerLng" jdbcType="DOUBLE" />
    <result column="customer_lat" property="customerLat" jdbcType="DOUBLE" />
    <result column="customer_contacter" property="customerContacter" jdbcType="VARCHAR" />
    <result column="customer_contacter_phone" property="customerContacterPhone" jdbcType="VARCHAR" />
    <result column="customer_contacter_email" property="customerContacterEmail" jdbcType="VARCHAR" />
    <result column="customer_contacter_telephone" property="customerContacterTelephone" jdbcType="VARCHAR" />
    <result column="customer_contacter_desc" property="customerContacterDesc" jdbcType="VARCHAR" />
    <result column="customer_operator" property="customerOperator" jdbcType="VARCHAR" />
    <result column="customer_sales_man" property="customerSalesMan" jdbcType="VARCHAR" />
    <result column="customer_sales_phone" property="customerSalesPhone" jdbcType="VARCHAR" />
    <result column="customer_desc" property="customerDesc" jdbcType="VARCHAR" />
    <result column="company_id" property="companyId" jdbcType="BIGINT" />
    <result column="account_no" property="accountNo" jdbcType="VARCHAR" />
    <result column="account_pwd" property="accountPwd" jdbcType="VARCHAR" />
    <result column="customer_source" property="customerSource" jdbcType="INTEGER" />
    <result column="yn" property="yn" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    <trim prefix="" suffix="" suffixOverrides=",">
      customer_no, customer_simple_name,
      customer_name, customer_license_no, customer_scale,
      customer_address, customer_lng, customer_lat,
      customer_contacter, customer_contacter_phone,
      customer_contacter_email, customer_contacter_telephone,
      customer_contacter_desc, customer_operator,
      customer_sales_man, customer_sales_phone, customer_desc,
      company_id,account_no,account_pwd,customer_source,
      yn, create_user, create_time,
      update_user, update_time
    </trim>
  </sql>
  <insert id="insert" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.CustomerEntity" >
    insert into examination_man_customer (customer_no, customer_simple_name,
      customer_name, customer_license_no, customer_scale, 
      customer_address, customer_lng, customer_lat, 
      customer_contacter, customer_contacter_phone, 
      customer_contacter_email, customer_contacter_telephone, 
      customer_contacter_desc, customer_operator, 
      customer_sales_man, customer_sales_phone, customer_desc,
      company_id,account_no,account_pwd,customer_source,
      yn, create_user, create_time, 
      update_user, update_time)
    values ( #{customerNo,jdbcType=BIGINT}, #{customerSimpleName,jdbcType=VARCHAR},
      #{customerName,jdbcType=VARCHAR}, #{customerLicenseNo,jdbcType=VARCHAR}, #{customerScale,jdbcType=INTEGER}, 
      #{customerAddress,jdbcType=VARCHAR}, #{customerLng,jdbcType=DOUBLE}, #{customerLat,jdbcType=DOUBLE}, 
      #{customerContacter,jdbcType=VARCHAR}, #{customerContacterPhone,jdbcType=VARCHAR}, 
      #{customerContacterEmail,jdbcType=VARCHAR}, #{customerContacterTelephone,jdbcType=VARCHAR}, 
      #{customerContacterDesc,jdbcType=VARCHAR}, #{customerOperator,jdbcType=VARCHAR},
      #{customerSalesMan,jdbcType=VARCHAR}, #{customerSalesPhone,jdbcType=VARCHAR}, #{customerDesc,jdbcType=VARCHAR},
      #{companyId},#{accountNo},#{accountPwd},#{customerSource},
      1, #{createUser,jdbcType=VARCHAR}, now(),
      #{updateUser,jdbcType=VARCHAR}, now())
  </insert>
  <update id="updateByCustomerNo" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.CustomerEntity" >
    update examination_man_customer
    <set>
      customer_simple_name = #{customerSimpleName,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_license_no = #{customerLicenseNo,jdbcType=VARCHAR},
      customer_scale = #{customerScale,jdbcType=INTEGER},
      customer_address = #{customerAddress,jdbcType=VARCHAR},
      customer_lng = #{customerLng,jdbcType=DOUBLE},
      customer_lat = #{customerLat,jdbcType=DOUBLE},
      customer_contacter = #{customerContacter,jdbcType=VARCHAR},
      customer_contacter_phone = #{customerContacterPhone,jdbcType=VARCHAR},
      customer_contacter_email = #{customerContacterEmail,jdbcType=VARCHAR},
      customer_contacter_telephone = #{customerContacterTelephone,jdbcType=VARCHAR},
      customer_contacter_desc = #{customerContacterDesc,jdbcType=VARCHAR},
      customer_operator = #{customerOperator,jdbcType=VARCHAR},
      customer_sales_man = #{customerSalesMan,jdbcType=VARCHAR},
      customer_sales_phone = #{customerSalesPhone,jdbcType=VARCHAR},
      customer_desc = #{customerDesc,jdbcType=VARCHAR},
      customer_source = #{customerSource,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = now()
    </set>
    where customer_no = #{customerNo}
  </update>
  <update id="deleteCustomer">
    update examination_man_customer
    set yn = 0,
    update_user = #{updateUser},
    update_time = now()
    where customer_no = #{customerNo}
    and yn = 1
  </update>
  <select id="selectAll" resultMap="BaseResultMap" >
    select <include refid="Base_Column_List"/>
    from examination_man_customer
  </select>
  <select id="queryCustomer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_customer
    <where>
      <if test="customerNo != null">
        and customer_no = #{customerNo}
      </if>
      <if test="customerSimpleName != null and customerSimpleName != ''">
        and customer_simple_name LIKE CONCAT('%',#{customerSimpleName}, '%')
      </if>
      <if test="customerScale != null">
        and customer_scale = #{customerScale}
      </if>
      <if test="customerOperator != null and customerOperator != ''">
        and customer_operator = #{customerOperator}
      </if>
      <if test="customerSalesMan != null and customerSalesMan != ''">
        and customer_sales_man = #{customerSalesMan}
      </if>
      and yn = 1
    </where>
  </select>
    <select id="queryCustomerByCustomerNo"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from examination_man_customer
      where customer_no = #{customerNo}
      and yn = 1
    </select>
  <select id="queryCustomerExist" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_customer
    where (customer_name = #{customerName} or customer_license_no = #{customerLicenseNo})
    and yn = 1
  </select>
  <select id="queryCustomerByCompanyId"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from examination_man_customer
    where company_id = #{companyId}
    and yn = 1
  </select>
    <select id="queryPageCustomerByOperatorAndSalesMan"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from (select <include refid="Base_Column_List"/> from examination_man_customer where customer_operator = #{customerOperatorAuth} or customer_sales_man = #{customerSalesManAuth}) t
      <where>
        <if test="customerNo != null and customerNo!=''">
          and customer_no = #{customerNo}
        </if>
        <if test="customerSimpleName != null and customerSimpleName != ''">
          and customer_simple_name LIKE CONCAT('%',#{customerSimpleName}, '%')
        </if>
        <if test="customerScale != null">
          and customer_scale = #{customerScale}
        </if>
        <if test="customerOperator != null and customerOperator != ''">
          and customer_operator = #{customerOperator}
        </if>
        <if test="customerSalesMan != null and customerSalesMan != ''">
          and customer_sales_man = #{customerSalesMan}
        </if>
        and yn = 1
      </where>
    </select>
    <insert id="insertQxDrc" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.CustomerEntity" >
      insert into examination_man_customer_qx_drc (customer_no, customer_simple_name,
      customer_name, customer_license_no, customer_scale,
      customer_address, customer_lng, customer_lat,
      customer_contacter, customer_contacter_phone,
      customer_contacter_email, customer_contacter_telephone,
      customer_contacter_desc, customer_operator,
      customer_sales_man, customer_sales_phone, customer_desc,
      company_id,account_no,account_pwd,customer_source,
      yn, create_user, create_time,
      update_user, update_time)
      values ( #{customerNo,jdbcType=BIGINT}, #{customerSimpleName,jdbcType=VARCHAR},
      #{customerName,jdbcType=VARCHAR}, #{customerLicenseNo,jdbcType=VARCHAR}, #{customerScale,jdbcType=INTEGER},
      #{customerAddress,jdbcType=VARCHAR}, #{customerLng,jdbcType=DOUBLE}, #{customerLat,jdbcType=DOUBLE},
      #{customerContacter,jdbcType=VARCHAR}, #{customerContacterPhone,jdbcType=VARCHAR},
      #{customerContacterEmail,jdbcType=VARCHAR}, #{customerContacterTelephone,jdbcType=VARCHAR},
      #{customerContacterDesc,jdbcType=VARCHAR}, #{customerOperator,jdbcType=VARCHAR},
      #{customerSalesMan,jdbcType=VARCHAR}, #{customerSalesPhone,jdbcType=VARCHAR}, #{customerDesc,jdbcType=VARCHAR},
      #{companyId},#{accountNo},#{accountPwd},#{customerSource},
      1, #{createUser,jdbcType=VARCHAR}, now(),
      #{updateUser,jdbcType=VARCHAR}, now())
    </insert>
    <update id="updateQxDrcByCustomerNo" parameterType="com.jd.health.medical.examination.domain.enterprise.entity.CustomerEntity" >
      update examination_man_customer_qx_drc
      <set>
        customer_simple_name = #{customerSimpleName,jdbcType=VARCHAR},
        customer_name = #{customerName,jdbcType=VARCHAR},
        customer_license_no = #{customerLicenseNo,jdbcType=VARCHAR},
        customer_scale = #{customerScale,jdbcType=INTEGER},
        customer_address = #{customerAddress,jdbcType=VARCHAR},
        customer_lng = #{customerLng,jdbcType=DOUBLE},
        customer_lat = #{customerLat,jdbcType=DOUBLE},
        customer_contacter = #{customerContacter,jdbcType=VARCHAR},
        customer_contacter_phone = #{customerContacterPhone,jdbcType=VARCHAR},
        customer_contacter_email = #{customerContacterEmail,jdbcType=VARCHAR},
        customer_contacter_telephone = #{customerContacterTelephone,jdbcType=VARCHAR},
        customer_contacter_desc = #{customerContacterDesc,jdbcType=VARCHAR},
        customer_operator = #{customerOperator,jdbcType=VARCHAR},
        customer_sales_man = #{customerSalesMan,jdbcType=VARCHAR},
        customer_sales_phone = #{customerSalesPhone,jdbcType=VARCHAR},
        customer_desc = #{customerDesc,jdbcType=VARCHAR},
        customer_source = #{customerSource,jdbcType=VARCHAR},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = now()
      </set>
      where customer_no = #{customerNo}
    </update>
</mapper>