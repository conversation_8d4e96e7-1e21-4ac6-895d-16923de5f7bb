<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.HealthWikiEntryDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.wiki.HealthWikiEntry">
    <!--@mbg.generated-->
    <!--@Table health_wiki_entry-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="entry_id" jdbcType="VARCHAR" property="entryId" />
    <result column="entry_name" jdbcType="VARCHAR" property="entryName" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, entry_id, entry_name, yn, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByParam" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntry" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from health_wiki_entry
    <where>
      <if test="entryId != null and entryId != ''">
        and entry_id = #{entryId,jdbcType=VARCHAR}
      </if>
      <if test="entryName != null and entryName != ''">
        and entry_name = #{entryName,jdbcType=VARCHAR}
      </if>
      and yn = 1
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from health_wiki_entry
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntry" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into health_wiki_entry (entry_id, entry_name, yn, 
      create_user, create_time, update_user, 
      update_time)
    values (#{entryId,jdbcType=VARCHAR}, #{entryName,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntry" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into health_wiki_entry
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="entryId != null">
        entry_id,
      </if>
      <if test="entryName != null">
        entry_name,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="entryId != null">
        #{entryId,jdbcType=VARCHAR},
      </if>
      <if test="entryName != null">
        #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByEntryIdSelective" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntry">
    <!--@mbg.generated-->
    update health_wiki_entry
    <set>
      <if test="entryName != null">
        entry_name = #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where entry_id = #{entryId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiEntry">
    <!--@mbg.generated-->
    update health_wiki_entry
    set entry_id = #{entryId,jdbcType=VARCHAR},
      entry_name = #{entryName,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>