<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.third.ThirdGoodsDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity" id="thirdGoodsMap">
        <result property="id" column="id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="channelNo" column="channel_no"/>
        <result property="goodsName" column="goods_name"/>
        <result property="goodsOriginalPrice" column="goods_original_price"/>
        <result property="goodsFavorPrice" column="goods_favor_price"/>
        <result property="goodsSuitable" column="goods_suitable"/>
        <result property="goodsMarry" column="goods_marry"/>
        <result property="status" column="status"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="appKey" column="app_key"/>
        <result column="goods_type" jdbcType="INTEGER" property="goodsType" />
        <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
        <result column="approval_status" property="approvalStatus" />
        <result column="goods_expire"  property="goodsExpire" />
        <result column="settle_price" property="settlePrice"/>
        <result column="business_type" property="businessType"/>
        <result column="age_floor" property="ageFloor" jdbcType="INTEGER"/>
        <result column="age_upper" property="ageUpper" jdbcType="INTEGER"/>
        <result column="vendor_type" property="vendorType"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            goods_id,
            channel_no,
            goods_name,
            goods_original_price,
            goods_favor_price,
            status,
            yn,
            create_time,
            update_time,
            goods_suitable,
            goods_marry,
            app_key,
            goods_type,
            goods_desc,
            goods_expire,
            approval_status,
            settle_price,
            business_type,
            age_floor,
            age_upper,
            vendor_type
        </trim>
    </sql>

    <!-- 根据Id查询 -->
    <!--<select id="selectThirdGoodsById" resultMap="thirdGoodsMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>-->


    <!-- 查询 -->
    <select id="selectThirdGoods" resultMap="thirdGoodsMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_id = #{goodsId,jdbcType=VARCHAR}
        <if test="goodsType != null">
            and goods_type = #{goodsType,jdbcType=INTEGER}
        </if>
        and yn = 1
    </select>

    <select id="selectThirdGoodsList" resultMap="thirdGoodsMap"
            parameterType="com.jd.health.medical.examination.domain.third.bo.ThirdGoodsBO">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where channel_no = #{channelNo,jdbcType=BIGINT}
        and goods_type = #{goodsType,jdbcType=INTEGER}
        <if test="goodsId != null">
            and goods_id = #{goodsId,jdbcType=VARCHAR}
        </if>
        <if test="goodsIds != null and goodsIds.size() != 0">
            and goods_id in
            <foreach collection="goodsIds" item="goodsId" separator="," open="(" close=")">
                #{goodsId,jdbcType=VARCHAR}
            </foreach>
        </if>
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryThirdGoodsList" resultMap="thirdGoodsMap"
            parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        <where>
            <if test="goodsId != null">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="channelNo != null">
                and channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="goodsType != null">
                and goods_type = #{goodsType,jdbcType=INTEGER}
            </if>
            <if test="goodsIds != null and goodsIds.size() != 0">
                and goods_id in
                <foreach collection="goodsIds" item="goodsId" separator="," open="(" close=")">
                    #{goodsId,jdbcType=VARCHAR}
                </foreach>
            </if>
            and yn = 1
        </where>
    </select>
    <select id="selectThirdGoodsByChannelNo"
            resultType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where channel_no = #{channelNo}
        and goods_type = #{goodsType,jdbcType=INTEGER}
        <if test="goodsId != null and goodsId != ''">
            and goods_id = #{goodsId,jdbcType=VARCHAR}
        </if>
        <if test="goodsName != null and goodsName != ''">
            and goods_name like CONCAT('%',#{goodsName},'%')
        </if>
        <if test="goodsNameFull != null and goodsNameFull != ''">
            and goods_name = #{goodsNameFull}
        </if>
        <if test="goodsMarry != null">
            and goods_marry = #{goodsMarry}
        </if>
        <if test="goodsSuitable != null">
            and goods_suitable = #{goodsSuitable}
        </if>
        and yn = 1 and status=1
        order by update_time desc
    </select>
    <select id="selectThirdGoodsByChannelNoAndAppKey"
            resultType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where channel_no = #{channelNo}
        and app_key = #{appKey}
        and yn = 1
        and status=1
        <if test="goodsType != null">
            and goods_type = #{goodsType,jdbcType=INTEGER}
        </if>
    </select>
    <select id="selectByChannelNoAndGoodsIds" resultMap="thirdGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where goods_type = #{goodsType,jdbcType=INTEGER}
        <if test="channelNo != null">
            and channel_no = #{channelNo}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=INTEGER}
        </if>
        and goods_id in
        <foreach collection="goodsIds" item="goodsId" separator="," open="(" close=")">
            #{goodsId,jdbcType=VARCHAR}
        </foreach>
        and yn = 1
    </select>
    <select id="selectByChannelNoAndGoodsId"
            resultType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where channel_no = #{channelNo} and goods_id = #{goodsId}
        and goods_type = #{goodsType,jdbcType=INTEGER}
        and yn = 1
    </select>

    <select id="selectThirdGoodsByChannelNos"
            resultType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        <where>
            <if test="channelNos != null">
                channel_no in
                <foreach item="channelNo" collection="channelNos" separator="," open="(" close=")" index="">
                    #{channelNo, jdbcType=BIGINT}
                </foreach>
            </if>
            and yn = 1 and status=1
            and goods_type = #{goodsType,jdbcType=INTEGER}
        </where>
    </select>


    <!-- 插入实体 -->
    <insert id="insertThirdGoods" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into examination_man_third_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            goods_id,
            channel_no,
            goods_name,
            goods_original_price,
            goods_favor_price,
            status,
            yn,
            create_time,
            update_time,
            goods_suitable,
            goods_marry,
            app_key,
            goods_type,
            approval_status,
            goods_expire,
            settle_price,
            <if test="businessType != null">
                business_type,
            </if>
            age_floor,
            age_upper,
            vendor_type
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{goodsId,jdbcType=VARCHAR},
            #{channelNo,jdbcType=BIGINT},
            #{goodsName,jdbcType=VARCHAR},
            #{goodsOriginalPrice,jdbcType=INTEGER},
            #{goodsFavorPrice,jdbcType=INTEGER},
            #{status,jdbcType=INTEGER},
            1,
            now(),
            now(),
            #{goodsSuitable,jdbcType=INTEGER},
            #{goodsMarry,jdbcType=INTEGER},
            #{appKey,jdbcType=VARCHAR},
            #{goodsType,jdbcType=INTEGER},
            #{approvalStatus,jdbcType=INTEGER},
            #{goodsExpire},
            #{settlePrice,jdbcType=INTEGER},
            <if test="businessType != null">
                #{businessType},
            </if>
            #{ageFloor,jdbcType=INTEGER},
            #{ageUpper,jdbcType=INTEGER},
            #{vendorType}
        </trim>
    </insert>
    <!-- 修改实体 -->
    <update id="updateThirdGoods" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        update examination_man_third_goods
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="goodsName != null and goodsName != ''">
                    goods_name = #{goodsName,jdbcType=VARCHAR},
                </if>
                <if test="goodsOriginalPrice != null">
                    goods_original_price = #{goodsOriginalPrice},
                </if>
                <if test="goodsMarry != null">
                    goods_marry = #{goodsMarry},
                </if>
                <if test="goodsSuitable != null">
                    goods_suitable = #{goodsSuitable},
                </if>
                <if test="goodsFavorPrice != null">
                    goods_favor_price = #{goodsFavorPrice},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="appKey != null">
                    app_key = #{appKey},
                </if>
                <if test="goodsDesc != null and goodsDesc != ''">
                    goods_desc = #{goodsDesc,jdbcType=VARCHAR},
                </if>
                <if test="approvalStatus != null">
                    approval_status = #{approvalStatus},
                </if>
                <if test = "goodsExpire != null">
                    goods_expire = #{goodsExpire},
                </if>
                <if test="settlePrice != null">
                    settle_price = #{settlePrice},
                </if>
                <if test="businessType != null">
                    business_type = #{businessType},
                </if>
                <if test="ageFloor != null">
                    age_floor = #{ageFloor,jdbcType=INTEGER},
                </if>
                <if test="ageUpper != null">
                    age_upper = #{ageUpper,jdbcType=INTEGER},
                </if>
                <if test="vendorType != null">
                    vendor_type = #{vendorType},
                </if>
                update_time=now()
            </trim>
        </set>
        where goods_id = #{goodsId}
        and channel_no = #{channelNo}
        and goods_type = #{goodsType,jdbcType=INTEGER}
    </update>

    <!-- 修改套餐为下架状态 -->
    <update id="updateThirdGoodsStatus" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        update examination_man_third_goods
        set status = #{status,jdbcType=INTEGER}
        <where>
            <if test="channelNo != null">
                channel_no = #{channelNo,jdbcType=BIGINT}
            </if>
            <if test="goodsId != null and goodsId != ''">
                and goods_id = #{goodsId,jdbcType=VARCHAR}
            </if>
            <if test="goodsType != null">
                and goods_type = #{goodsType,jdbcType=INTEGER}
            </if>
            <if test="vendorType != null">
                and vendor_type = #{vendorType}
            </if>
            and yn = 1
        </where>
    </update>

    <!-- 插入实体 -->
    <insert id="insertThirdGoodsBatch" parameterType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        insert into examination_man_third_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            goods_id,
            channel_no,
            goods_name,
            goods_original_price,
            goods_favor_price,
            status,
            yn,
            create_time,
            update_time,
            app_key,
            goods_type,
            vendor_type
        </trim>
        values
        <foreach collection="thirdGoods" item="thirdGood" separator=",">
            (
            #{thirdGood.goodsId,jdbcType=VARCHAR},
            #{thirdGood.channelNo,jdbcType=BIGINT},
            #{thirdGood.goodsName,jdbcType=VARCHAR},
            #{thirdGood.goodsOriginalPrice,jdbcType=INTEGER},
            #{thirdGood.goodsFavorPrice,jdbcType=INTEGER},
            #{thirdGood.status,jdbcType=INTEGER},
            1,
            now(),
            now(),
            #{thirdGood.appKey,jdbcType=VARCHAR},
            #{thirdGood.goodsType,jdbcType=INTEGER},
            #{thirdGood.vendorType})
        </foreach>
    </insert>
    <update id="updateStatusOffByGoodId" parameterType="LONG">
        update examination_man_third_goods
        <set>
            status = 2
        </set>
        where goods_id in
        <foreach collection="thirdGoods" item="thirdGood" separator="," open="(" close=")">
            #{thirdGood.goodsId,jdbcType=VARCHAR}
        </foreach>
        and channel_no = #{channelNo,jdbcType=BIGINT}
    </update>

    <select id="selectListByGoodsIds" resultMap="thirdGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where channel_no = #{channelNo}
        and goods_id in
        <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
        <if test="goodsType != null">
            and goods_type = #{goodsType,jdbcType=INTEGER}
        </if>
        and status = #{status}
        AND yn = 1
    </select>
    <select id="selectThirdGoodsListByCondition"
            resultType="com.jd.health.medical.examination.domain.third.ThirdGoodsEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods where yn = 1
        <if test="channelNos != null and channelNos.size() > 0">
            and channel_no in
            <foreach item="channelNo" collection="channelNos" separator="," open="(" close=")" index="">
                #{channelNo, jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="goodsName != null and goodsName != ''">
            and goods_name like CONCAT('%',#{goodsName},'%')
        </if>
        <if test="goodsNameFull != null and goodsNameFull != ''">
            and goods_name = #{goodsNameFull}
        </if>
        <if test="goodsMarry != null">
            and goods_marry = #{goodsMarry}
        </if>
        <if test="goodsSuitable != null">
            and goods_suitable = #{goodsSuitable}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="approvalStatus != null">
            and approval_status = #{approvalStatus}
        </if>
        <if test="goodsType != null">
            and goods_type = #{goodsType}
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="goodsId != null and goodsId != ''">
            and goods_id = #{goodsId}
        </if>
    </select>

    <!--查询套餐列表-->
    <select id="selectListByGoodsId" resultMap="thirdGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where goods_id in
        <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
        AND yn = 1
    </select>

    <select id="listAllGoodsId" resultMap="thirdGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods where yn=1
    </select>

    <update id="updateByLockSettlePrice" >
        update examination_man_third_goods
        <set>
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="entity.goodsName != null and entity.goodsName != ''">
                    goods_name = #{entity.goodsName,jdbcType=VARCHAR},
                </if>
                <if test="entity.goodsOriginalPrice != null">
                    goods_original_price = #{entity.goodsOriginalPrice},
                </if>
                <if test="entity.goodsMarry != null">
                    goods_marry = #{entity.goodsMarry},
                </if>
                <if test="entity.goodsSuitable != null">
                    goods_suitable = #{entity.goodsSuitable},
                </if>
                <if test="entity.goodsFavorPrice != null">
                    goods_favor_price = #{entity.goodsFavorPrice},
                </if>
                <if test="entity.status != null">
                    status = #{entity.status},
                </if>
                <if test="entity.appKey != null">
                    app_key = #{entity.appKey},
                </if>
                <if test="entity.goodsDesc != null and entity.goodsDesc != ''">
                    goods_desc = #{entity.goodsDesc,jdbcType=VARCHAR},
                </if>
                <if test="entity.approvalStatus != null">
                    approval_status = #{entity.approvalStatus},
                </if>
                <if test = "entity.goodsExpire != null">
                    goods_expire = #{entity.goodsExpire},
                </if>
                <if test="entity.settlePrice != null">
                    settle_price = #{entity.settlePrice},
                </if>
                update_time=now()
            </trim>
        </set>
        where goods_id = #{entity.goodsId}
        and channel_no = #{entity.channelNo}
        and goods_type = #{entity.goodsType,jdbcType=INTEGER}
        <!-- 之前没有结算价 -->
        <if test="oldSettlePrice == null">
            and settle_price is null
        </if>
        <!-- 之前结算价有值更新 -->
        <if test="oldSettlePrice != null">
            and settle_price = #{oldSettlePrice,jdbcType=INTEGER}
        </if>
    </update>

    <select id="selectByGoodsId" resultMap="thirdGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where goods_id = #{goodsId,jdbcType=INTEGER}
        and channel_no = #{channelNo, jdbcType=BIGINT}
        and yn = 1
    </select>
    <select id="selectByGoodsIds" resultMap="thirdGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from examination_man_third_goods
        where goods_id in
        <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
            #{goodsId,jdbcType=VARCHAR}
        </foreach>
        and channel_no = #{channelNo, jdbcType=BIGINT}
        and yn = 1
    </select>
</mapper>