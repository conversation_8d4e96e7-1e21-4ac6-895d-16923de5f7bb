<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jd.health.medical.examination.dao.enterprise.EnterpriseOperatorDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOperatorEntity" id="enterpriseOperatorMap">
        <result property="id" column="id"/>
        <result property="companyNo" column="company_no"/>
        <result property="operatorPin" column="operator_pin"/>
        <result property="yn" column="yn"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim prefix="" suffix="" suffixOverrides=",">
            id,
            company_no,
            operator_pin,
            yn,
            create_time,
            update_time,
        </trim>
    </sql>

    <!-- 根据Id查询 -->
    <select id="selectEnterpriseOperatorById" resultMap="enterpriseOperatorMap" parameterType="LONG">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company_operator
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </select>

    <!-- 根据条件查询列表 -->
    <select id="queryEnterpriseOperatorList" resultMap="enterpriseOperatorMap"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOperatorEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_qx_man_company_operator
        <where>
            <if test="companyNo != null">
                and company_no = #{companyNo,jdbcType=BIGINT}
            </if>
            <if test="operatorPin != null">
                and operator_pin = #{operatorPin,jdbcType=VARCHAR}
            </if>
            and yn = 1
        </where>
    </select>

    <select id="queryCompanyNoListByOperator" resultType="java.lang.Long"
            parameterType="java.lang.String">
        select
          company_no
        from examination_qx_man_company_operator
        <where>
            <if test="operatorPin != null">
                and operator_pin = #{operatorPin,jdbcType=VARCHAR}
            </if>
            and yn = 1
        </where>
    </select>


    <!-- 插入实体 -->
    <insert id="insertEnterpriseOperator"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOperatorEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into examination_qx_man_company_operator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            company_no,
            operator_pin,
            yn,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{companyNo,jdbcType=BIGINT},
            #{operatorPin,jdbcType=VARCHAR},
            1,
            now(),
            now(),
        </trim>
    </insert>

    <!-- 插入实体 -->
    <insert id="insertEnterpriseOperatorList"
            parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        insert into examination_qx_man_company_operator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            company_no,
            operator_pin,
            yn,
            create_time,
            update_time
        </trim>
        values
        <foreach collection="list" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.companyNo,jdbcType=BIGINT},
                #{item.operatorPin,jdbcType=VARCHAR},
                1,
                now(),
                now(),
            </trim>
        </foreach>
    </insert>

    <!-- 修改实体 -->
    <update id="updateEnterpriseOperator"
            parameterType="com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOperatorEntity">
        update examination_qx_man_company_operator
        <set>
            yn = 1
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 删除实体 -->
    <update id="deleteEnterpriseOperatorById" parameterType="LONG">
        update examination_qx_man_company_operator
        <set>
            yn = 0
        </set>
        where id = #{id,jdbcType=BIGINT}
        and yn = 1
    </update>

    <!-- 删除实体 -->
    <update id="deleteEnterpriseOperatorByNo" parameterType="LONG">
        update examination_qx_man_company_operator
        <set>
            yn = 0
        </set>
        where company_no = #{companyNo,jdbcType=BIGINT}
        and yn = 1
    </update>


</mapper>