<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.ExaminationManItemRelevanceDao">
    <resultMap type="com.jd.health.medical.examination.domain.ExaminationManItemRelevanceEntity" id="ExaminationManItemRelevanceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="relevanceNo" column="relevance_no" jdbcType="INTEGER"/>
        <result property="itemNo" column="item_no" jdbcType="INTEGER"/>
        <result property="relevanceWeight" column="relevance_weight" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="yn" column="yn" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
               relevance_no,
               item_no,
               relevance_weight,
               yn,
               create_user,
               create_time,
               update_user,
               update_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="ExaminationManItemRelevanceMap">
        select id,
               relevance_no,
               item_no,
               relevance_weight,
               create_time,
               create_user,
               update_time,
               update_user,
               yn
        from examination_man_item_relevance
        where id = #{id}
    </select>

    <select id="queryPage" resultType="com.jd.health.medical.examination.domain.ExaminationManItemRelevanceEntity">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_relevance
        <where>
            yn = 1
            <if test="relevanceNo != null">
                and relevance_no = #{relevanceNo}
            </if>
        </where>
        order by
        relevance_weight desc,
        create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into examination_man_item_relevance(relevance_no, item_no, relevance_weight, create_user,
                                                   update_user, yn)
        values (#{relevanceNo}, #{itemNo}, #{relevanceWeight}, #{createUser},
                #{updateUser}, #{yn})
    </insert>

    <insert id="insertBatch">
        insert into examination_man_item_relevance(relevance_no, item_no, relevance_weight, create_user, update_user)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.relevanceNo}, #{entity.itemNo}, #{entity.relevanceWeight}, #{entity.createUser},
            #{entity.updateUser})
        </foreach>
    </insert>

    <!--通过主键删除-->
    <update id="deleteByRelevanceNo">
        update
        examination_man_item_relevance
        set yn = 0,
        update_user = #{operator}
        where relevance_no = #{relevanceNo}
          and yn = 1
    </update>

    <!--通过主键删除-->
    <update id="deleteByRelevanceNoAndItemNo">
        update
        examination_man_item_relevance
        set yn = 0,
        update_user = #{operator}
        where relevance_no = #{relevanceNo}
        and yn = 1
        <if test="itemNo != null">
            and item_no = #{itemNo}
        </if>
    </update>

    <select id="queryRelevanceNoByItemNo" resultType="java.lang.Long">
        select
        relevance_no
        from examination_man_item_relevance
        <where>
            yn = 1
            and item_no in
            <foreach collection="itemNoList" item="itemNo" open="(" separator="," close=")">
                #{itemNo}
            </foreach>
        </where>
        order by
        relevance_weight desc,
        create_time desc
    </select>

    <select id="queryItemListByRelevance" resultMap="ExaminationManItemRelevanceMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from examination_man_item_relevance
                where yn = 1
                  and relevance_no in
        <foreach collection="relevanceList" item="relevanceNo" open="(" separator="," close=")">
            #{relevanceNo}
        </foreach>
    </select>
</mapper>

