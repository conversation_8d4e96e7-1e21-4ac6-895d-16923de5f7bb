<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.health.medical.examination.dao.HealthWikiDirectoryDao">
  <resultMap id="BaseResultMap" type="com.jd.health.medical.examination.domain.wiki.HealthWikiDirectory">
    <!--@mbg.generated-->
    <!--@Table health_wiki_directory-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="directory_id" jdbcType="VARCHAR" property="directoryId" />
    <result column="directory_name" jdbcType="VARCHAR" property="directoryName" />
    <result column="parent_directory_id" jdbcType="VARCHAR" property="parentDirectoryId" />
    <result column="directory_level" jdbcType="INTEGER" property="directoryLevel" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, directory_id, directory_name, parent_directory_id, directory_level, yn, create_user, 
    create_time, update_user, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from health_wiki_directory
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from health_wiki_directory
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiDirectory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into health_wiki_directory (directory_id, directory_name, parent_directory_id, 
      directory_level, yn, create_user, 
      create_time, update_user, update_time
      )
    values (#{directoryId,jdbcType=VARCHAR}, #{directoryName,jdbcType=VARCHAR}, #{parentDirectoryId,jdbcType=VARCHAR}, 
      #{directoryLevel,jdbcType=INTEGER}, #{yn,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiDirectory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into health_wiki_directory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="directoryId != null">
        directory_id,
      </if>
      <if test="directoryName != null">
        directory_name,
      </if>
      <if test="parentDirectoryId != null">
        parent_directory_id,
      </if>
      <if test="directoryLevel != null">
        directory_level,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="directoryId != null">
        #{directoryId,jdbcType=VARCHAR},
      </if>
      <if test="directoryName != null">
        #{directoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentDirectoryId != null">
        #{parentDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="directoryLevel != null">
        #{directoryLevel,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiDirectory">
    <!--@mbg.generated-->
    update health_wiki_directory
    <set>
      <if test="directoryId != null">
        directory_id = #{directoryId,jdbcType=VARCHAR},
      </if>
      <if test="directoryName != null">
        directory_name = #{directoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentDirectoryId != null">
        parent_directory_id = #{parentDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="directoryLevel != null">
        directory_level = #{directoryLevel,jdbcType=INTEGER},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jd.health.medical.examination.domain.wiki.HealthWikiDirectory">
    <!--@mbg.generated-->
    update health_wiki_directory
    set directory_id = #{directoryId,jdbcType=VARCHAR},
      directory_name = #{directoryName,jdbcType=VARCHAR},
      parent_directory_id = #{parentDirectoryId,jdbcType=VARCHAR},
      directory_level = #{directoryLevel,jdbcType=INTEGER},
      yn = #{yn,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>