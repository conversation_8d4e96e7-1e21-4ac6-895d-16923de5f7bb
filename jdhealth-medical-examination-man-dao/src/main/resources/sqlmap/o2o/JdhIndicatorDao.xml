<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhIndicatorDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhIndicatorEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="indicator_id" property="indicatorId" jdbcType="BIGINT"/>
        <result column="indicator_name" property="indicatorName" jdbcType="VARCHAR"/>
        <result column="indicator_mean" property="indicatorMean" jdbcType="VARCHAR"/>
        <result column="indicator_suitable" property="indicatorSuitable" jdbcType="VARCHAR"/>
        <result column="first_indicator_category" property="firstIndicatorCategory" jdbcType="BIGINT"/>
        <result column="first_indicator_category_name" property="firstIndicatorCategoryName" jdbcType="VARCHAR"/>
        <result column="second_indicator_category" property="secondIndicatorCategory" jdbcType="BIGINT"/>
        <result column="second_indicator_category_name" property="secondIndicatorCategoryName" jdbcType="VARCHAR"/>
        <result column="third_indicator_category" property="thirdIndicatorCategory" jdbcType="BIGINT"/>
        <result column="third_indicator_category_name" property="thirdIndicatorCategoryName" jdbcType="VARCHAR"/>
        <result column="range_value" property="rangeValue" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="analyse_method" property="analyseMethod" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        indicator_id,
        indicator_name,
        indicator_mean,
        indicator_suitable,
        first_indicator_category,
        first_indicator_category_name,
        second_indicator_category,
        second_indicator_category_name,
        third_indicator_category,
        third_indicator_category_name,
        range_value,
        unit,
        analyse_method,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_indicator (
        indicator_id,
        indicator_name,
        indicator_mean,
        indicator_suitable,
        first_indicator_category,
        first_indicator_category_name,
        second_indicator_category,
        second_indicator_category_name,
        third_indicator_category,
        third_indicator_category_name,
        range_value,
        unit,
        analyse_method,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
        )
        values
        <foreach collection="indicatorEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.indicatorId,jdbcType=BIGINT},
                #{item.indicatorName,jdbcType=VARCHAR},
                #{item.indicatorMean,jdbcType=VARCHAR},
                #{item.indicatorSuitable,jdbcType=VARCHAR},
                #{item.firstIndicatorCategory,jdbcType=BIGINT},
                #{item.firstIndicatorCategoryName,jdbcType=VARCHAR},
                #{item.secondIndicatorCategory,jdbcType=BIGINT},
                #{item.secondIndicatorCategoryName,jdbcType=VARCHAR},
                #{item.thirdIndicatorCategory,jdbcType=BIGINT},
                #{item.thirdIndicatorCategoryName,jdbcType=VARCHAR},
                #{item.rangeValue,jdbcType=VARCHAR},
                #{item.unit,jdbcType=VARCHAR},
                #{item.analyseMethod,jdbcType=VARCHAR},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateUser,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>


    <select id="queryByIndicator"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_indicator
        where indicator_id = #{indicator,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>
</mapper>