<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhServiceItemIndicatorRelDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceItemIndicatorRelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="indicator_id" property="indicatorId" jdbcType="BIGINT"/>
        <result column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        indicator_id,
        item_id
    </sql>

    <insert id="batchInsert">
        insert into jdh_service_item_indicator_rel (
        indicator_id,
        item_id,
        yn,
        version,
        create_user,
        create_time
        )
        values
        <foreach collection="jdhServiceItemIndicatorRelEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.indicatorId,jdbcType=BIGINT},
                #{item.itemId,jdbcType=BIGINT},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>

    <select id="queryByList"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_service_item_indicator_rel
        where item_id = #{itemNo,jdbcType=BIGINT}
        and yn = 1
    </select>
</mapper>