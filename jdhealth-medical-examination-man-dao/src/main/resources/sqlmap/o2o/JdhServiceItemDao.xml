<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhServiceItemDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceItemEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="first_sku_category" property="firstSkuCategory" jdbcType="BIGINT"/>
        <result column="second_sku_category" property="secondSkuCategory" jdbcType="BIGINT"/>
        <result column="third_sku_category" property="thirdSkuCategory" jdbcType="BIGINT"/>
        <result column="first_indicator_category" property="firstIndicatorCategory" jdbcType="BIGINT"/>
        <result column="first_indicator_category_name" property="firstIndicatorCategoryName" jdbcType="VARCHAR"/>
        <result column="second_indicator_category" property="secondIndicatorCategory" jdbcType="BIGINT"/>
        <result column="second_indicator_category_name" property="secondIndicatorCategoryName" jdbcType="VARCHAR"/>
        <result column="third_indicator_category" property="thirdIndicatorCategory" jdbcType="BIGINT"/>
        <result column="third_indicator_category_name" property="thirdIndicatorCategoryName" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="item_mean" property="itemMean" jdbcType="VARCHAR"/>
        <result column="item_type" property="itemType" jdbcType="INTEGER"/>
        <result column="item_price" property="itemPrice" jdbcType="DECIMAL"/>
        <result column="item_suitable" property="itemSuitable" jdbcType="VARCHAR"/>
        <result column="service_duration" property="serviceDuration" jdbcType="INTEGER"/>
        <result column="inspect_duration" property="inspectDuration" jdbcType="INTEGER"/>
        <result column="inspect_ext" property="inspectExt" jdbcType="VARCHAR"/>
        <result column="channel_no" property="channelNo" jdbcType="VARCHAR"/>
        <result column="vendor_type" property="vendorType" jdbcType="INTEGER"/>
        <result column="replace_indicator_category" property="replaceIndicatorCategory" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        item_id,
        first_sku_category,
        second_sku_category,
        third_sku_category,
        first_indicator_category,
        first_indicator_category_name,
        second_indicator_category,
        second_indicator_category_name,
        third_indicator_category,
        third_indicator_category_name,
        item_name,
        item_mean,
        item_type,
        item_price,
        item_suitable,
        service_duration,
        inspect_duration,
        inspect_ext,
        channel_no,
        vendor_type,
        replace_indicator_category,
        remark,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_service_item (
        item_id,
        first_sku_category,
        second_sku_category,
        third_sku_category,
        first_indicator_category,
        first_indicator_category_name,
        second_indicator_category,
        second_indicator_category_name,
        third_indicator_category,
        third_indicator_category_name,
        item_name,
        item_mean,
        item_type,
        item_price,
        item_suitable,
        service_duration,
        inspect_duration,
        inspect_ext,
        channel_no,
        vendor_type,
        replace_indicator_category,
        remark,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
        )
        values
        <foreach collection="jdhServiceItemEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.itemId,jdbcType=BIGINT},
                #{item.firstSkuCategory,jdbcType=VARCHAR},
                #{item.secondSkuCategory,jdbcType=VARCHAR},
                #{item.thirdSkuCategory,jdbcType=VARCHAR},
                #{item.firstIndicatorCategory,jdbcType=BIGINT},
                #{item.firstIndicatorCategoryName,jdbcType=VARCHAR},
                #{item.secondIndicatorCategory,jdbcType=BIGINT},
                #{item.secondIndicatorCategoryName,jdbcType=VARCHAR},
                #{item.thirdIndicatorCategory,jdbcType=BIGINT},
                #{item.thirdIndicatorCategoryName,jdbcType=VARCHAR},
                #{item.itemName,jdbcType=VARCHAR},
                #{item.itemMean,jdbcType=VARCHAR},
                #{item.itemType,jdbcType=VARCHAR},
                #{item.itemPrice,jdbcType=VARCHAR},
                #{item.itemSuitable,jdbcType=VARCHAR},
                #{item.serviceDuration,jdbcType=VARCHAR},
                #{item.inspectDuration,jdbcType=VARCHAR},
                #{item.inspectExt,jdbcType=VARCHAR},
                #{item.channelNo,jdbcType=VARCHAR},
                #{item.vendorType,jdbcType=VARCHAR},
                #{item.replaceIndicatorCategory,jdbcType=VARCHAR},
                #{item.remark,jdbcType=VARCHAR},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateUser,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>

    <select id="queryByItemNo"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_service_item
        where item_id = #{itemNo,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>

</mapper>