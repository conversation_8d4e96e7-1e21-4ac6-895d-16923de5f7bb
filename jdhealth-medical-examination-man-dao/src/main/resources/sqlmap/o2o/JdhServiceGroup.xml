<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhServiceGroupDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceGroupEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="serviceId" jdbcType="BIGINT"/>
        <result column="group_name" property="serviceName" jdbcType="VARCHAR"/>
        <result column="group_suitable" property="groupSuitable" jdbcType="VARCHAR"/>
        <result column="service_id" property="serviceId" jdbcType="BIGINT"/>
        <result column="service_name" property="serviceName" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        group_id,
        group_name,
        group_suitable,
        service_id,
        service_name,
        yn,
        version,
        create_user,
        create_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_service_group (
        group_id,
        group_name,
        group_suitable,
        service_id,
        service_name,
        yn,
        version,
        create_user,
        create_time
        )
        values
        <foreach collection="jdhServiceGroupEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.groupId,jdbcType=BIGINT},
                #{item.groupName,jdbcType=VARCHAR},
                #{item.groupSuitable,jdbcType=VARCHAR},
                #{item.serviceId,jdbcType=BIGINT},
                #{item.serviceName,jdbcType=VARCHAR},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>

    <select id="queryServiceGroup"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_service_group
        where group_id = #{groupId,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>
</mapper>