<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhIndicatorCategoryDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhIndicatorCategoryEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="category_level" property="categoryLevel" jdbcType="INTEGER"/>
        <result column="parent_category_id" property="parentCategoryId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        category_id,
        category_name,
        category_level,
        parent_category_id,
        status,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_indicator_category (
        category_id,
        category_name,
        category_level,
        parent_category_id,
        status,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
        )
        values
        <foreach collection="jdhIndicatorCategoryEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.categoryId,jdbcType=BIGINT},
                #{item.categoryName,jdbcType=VARCHAR},
                #{item.categoryLevel,jdbcType=INTEGER},
                #{item.parentCategoryId,jdbcType=BIGINT},
                #{item.status,jdbcType=INTEGER},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateUser,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>

    <select id="queryCategory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_indicator_category
        where category_id = #{itemNo,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>
</mapper>