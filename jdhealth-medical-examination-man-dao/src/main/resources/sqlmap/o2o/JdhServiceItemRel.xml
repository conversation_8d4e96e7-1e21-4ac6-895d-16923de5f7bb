<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhServiceItemRelDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceItemRelEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="service_id" property="serviceId" jdbcType="BIGINT"/>
        <result column="service_name" property="serviceName" jdbcType="VARCHAR"/>
        <result column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="important" property="important" jdbcType="INTEGER"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,
        service_id,
        service_name,
        item_id,
        important,
        yn,
        version,
        create_user,
        create_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_service_item_rel (
        service_id,
        service_name,
        item_id,
        important,
        yn,
        version,
        create_user,
        create_time
        )
        values
        <foreach collection="jdhServiceItemRelEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.serviceId,jdbcType=BIGINT},
                #{item.serviceName,jdbcType=VARCHAR},
                #{item.itemId,jdbcType=BIGINT},
                #{item.important,jdbcType=INTEGER},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>

    <select id="queryByRelationNo"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_service_item_rel
        where service_id = #{serviceId,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>
</mapper>