<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhBusinessIndicatorCategoryDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhBusinessIndicatorCategoryEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="business_id" property="businessId" jdbcType="BIGINT"/>
        <result column="first_sku_category" property="firstSkuCategory" jdbcType="BIGINT"/>
        <result column="second_sku_category" property="secondSkuCategory" jdbcType="BIGINT"/>
        <result column="third_sku_category" property="thirdSkuCategory" jdbcType="BIGINT"/>
        <result column="first_indicator_category" property="firstIndicatorCategory" jdbcType="BIGINT"/>
        <result column="second_indicator_category" property="secondIndicatorCategory" jdbcType="VARCHAR"/>
        <result column="third_indicator_category" property="thirdIndicatorCategory" jdbcType="BIGINT"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        business_id,
        first_sku_category,
        second_sku_category,
        third_sku_category,
        first_indicator_category,
        second_indicator_category,
        third_indicator_category,
        yn,
        version,
        create_user,
        create_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_business_indicator_category (
        business_id,
        first_sku_category,
        second_sku_category,
        third_sku_category,
        first_indicator_category,
        second_indicator_category,
        third_indicator_category,
        yn,
        version,
        create_user,
        create_time
        )
        values
        <foreach collection="jdhBusinessIndicatorCategoryEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.businessId,jdbcType=BIGINT},
                #{item.firstSkuCategory,jdbcType=VARCHAR},
                #{item.secondSkuCategory,jdbcType=VARCHAR},
                #{item.thirdSkuCategory,jdbcType=VARCHAR},
                #{item.firstIndicatorCategory,jdbcType=BIGINT},
                #{item.secondIndicatorCategory,jdbcType=VARCHAR},
                #{item.thirdIndicatorCategory,jdbcType=BIGINT},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>

</mapper>