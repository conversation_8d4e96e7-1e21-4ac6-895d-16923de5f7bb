<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jd.health.medical.examination.o2odao.JdhServiceDao">
    <resultMap id="BaseResultMap"
               type="com.jd.health.medical.examination.domain.enterprise.o2oentity.JdhServiceEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="service_id" property="serviceId" jdbcType="BIGINT"/>
        <result column="service_name" property="serviceName" jdbcType="VARCHAR"/>
        <result column="service_desc" property="serviceDesc" jdbcType="VARCHAR"/>
        <result column="service_suitable" property="serviceSuitable" jdbcType="VARCHAR"/>
        <result column="service_item_num" property="serviceItemNum" jdbcType="INTEGER"/>
        <result column="age_floor" property="ageFloor" jdbcType="INTEGER"/>
        <result column="age_upper" property="ageUpper" jdbcType="INTEGER"/>
        <result column="yn" property="yn" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        service_id,
        service_name,
        service_desc,
        service_suitable,
        service_item_num,
        age_floor,
        age_upper,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
    </sql>

    <insert id="batchInsert">
        insert into jdh_service (
        service_id,
        service_name,
        service_desc,
        service_suitable,
        service_item_num,
        age_floor,
        age_upper,
        yn,
        version,
        create_user,
        create_time,
        update_user,
        update_time
        )
        values
        <foreach collection="jdhServiceEntityList" index="index" item="item" open="" separator="," close="">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.serviceId,jdbcType=BIGINT},
                #{item.serviceName,jdbcType=VARCHAR},
                #{item.serviceDesc,jdbcType=VARCHAR},
                #{item.serviceSuitable,jdbcType=VARCHAR},
                #{item.serviceItemNum,jdbcType=INTEGER},
                #{item.ageFloor,jdbcType=INTEGER},
                #{item.ageUpper,jdbcType=INTEGER},
                #{item.yn,jdbcType=INTEGER},
                #{item.version,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateUser,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>


    <select id="queryService"  parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_service
        where service_id = #{serviceId,jdbcType=BIGINT}
        and yn = 1
        limit 1
    </select>
</mapper>