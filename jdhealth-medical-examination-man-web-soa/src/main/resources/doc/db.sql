CREATE TABLE `examination_man_third_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `store_id` varchar(32) NOT NULL COMMENT '门店id-商家侧',
  `store_name` varchar(127) NOT NULL COMMENT '门店名称',
  `channel_no` bigint(20) NOT NULL COMMENT '供应商渠道编码',
  `store_addr` varchar(255) NOT NULL COMMENT '门店地址',
  `store_phone` varchar(127) NOT NULL COMMENT '门店电话',
  `store_type` tinyint(4) DEFAULT NULL COMMENT '门店类型1公立医院 2私立医院 3专业体检机构',
  `report_support` tinyint(4) DEFAULT NULL COMMENT '是否支持电子报告回传 0不支持 1支持',
  `store_level` tinyint(4) DEFAULT NULL COMMENT '医院等级,公立医院可区分',
  `province_id` int(11) DEFAULT NULL COMMENT '省id',
  `province_name` varchar(32) DEFAULT NULL COMMENT '所属省',
  `city_id` int(11) DEFAULT NULL COMMENT '市id',
  `city_name` varchar(64) DEFAULT NULL COMMENT '所属市',
  `county_id` int(11) DEFAULT NULL COMMENT '区id',
  `county_name` varchar(64) DEFAULT NULL COMMENT '所属县/区',
  `store_status` tinyint(4) NOT NULL COMMENT '上下架状态 0 已下架 1上架',
  `lng` double(12, 6) DEFAULT NULL COMMENT '经度',
  `lat` double(12, 6) DEFAULT NULL COMMENT '纬度',
  `yn` bigint(4) NOT NULL DEFAULT '1' COMMENT '是否有效 1有效 0 无效',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `support_write_off` tinyint(4) DEFAULT '1' COMMENT '是否支持核销 0 不支持 1支持',
  `store_hours` varchar(64) DEFAULT NULL COMMENT '门店营业时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_examination_third_store_id_channel_type` (`store_id`, `channel_no`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '商家可预约门店信息表';

CREATE TABLE `examination_man_third_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `goods_id` varchar(32) NOT NULL COMMENT '商家套餐ID',
  `channel_no` bigint(20) NOT NULL COMMENT '供应商渠道编码',
  `goods_name` varchar(255) NOT NULL COMMENT '商品名称',
  `goods_original_price` int(10) NOT NULL COMMENT '商品原价',
  `goods_favor_price` int(10) DEFAULT NULL COMMENT '优惠价',
  `status` tinyint(4) NOT NULL COMMENT '上下架状态 1上架 2下架',
  `yn` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效 1有效 0无效',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `goods_suitable` tinyint(4) DEFAULT NULL COMMENT '套餐适用性别 0男女通用 1 男性  2女性',
  `goods_marry` tinyint(4) DEFAULT NULL COMMENT '婚姻状态 0 男女通用 1 男性 2 女性',
  `goods_age` int(3) DEFAULT '18' COMMENT '套餐适用年龄 默认18岁',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_examination_man_third_goods_channel_no` (`goods_id`, `channel_no`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '套餐门店对应关系表';

CREATE TABLE `examination_man_third_store_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `goods_id` varchar(32) NOT NULL COMMENT '商家套餐ID',
  `store_id` varchar(32) NOT NULL COMMENT '商家门店ID',
  `goods_status` tinyint(10) DEFAULT NULL COMMENT '套餐上架状态 0 下架 1 上架',
  `store_status` tinyint(4) NOT NULL COMMENT '门店上架状态 0 下架 1 上架',
  `channel_no` bigint(20) NOT NULL COMMENT '供应商渠道编码',
  `data_status` tinyint(4) DEFAULT NULL COMMENT '数据状态 0 正常 1新增 2删除',
  `yn` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效1有效0无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_examination_third_store_goods_channel` (`store_id`, `channel_no`) USING BTREE,
  KEY `idx_examination_man_third_store_goods_id` (`goods_id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '商家套餐门店对应关系表';

CREATE TABLE `examination_man_third_goods_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `goods_id` varchar(32) NOT NULL COMMENT '系统编码',
  `item_name` varchar(255) NOT NULL COMMENT '项目名称',
  `item_no` varchar(32) NOT NULL COMMENT '项目编码',
  `channel_no` bigint(20) DEFAULT NULL COMMENT '渠道商编码',
  `item_means` varchar(255) DEFAULT NULL COMMENT '项目意义',
  `item_top_category` varchar(127) DEFAULT NULL COMMENT '项目一级分类名称',
  `item_sec_category` varchar(127) DEFAULT NULL COMMENT '项目二级分类名称',
  `item_suitable` varchar(32) DEFAULT NULL COMMENT '适用性别',
  `yn` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效 1有效 0无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_examination_man_third_goods_item_goods_id` (`goods_id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '商家套餐对应项目组信息表';CREATE TABLE `examination_man_third_data_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id主键',
  `data_type` tinyint(4) NOT NULL COMMENT '数据类型 1套餐 2门店',
  `operate_type` tinyint(4) NOT NULL COMMENT '操作类型 1新增 2上架 3下架',
  `channel_type` bigint(20) NOT NULL COMMENT '供应商渠道编码',
  `goods_id` varchar(50) NOT NULL COMMENT '商家套餐编码',
  `store_id` varchar(50) NOT NULL COMMENT '商家门店编码',
  `confirm` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否确认该操作 1确认 0未确认',
  `yn` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效 1有效 0无效',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_examination_third_data_channel_type` (`channel_type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COLLATE = utf8_bin COMMENT = '商家推送数据记录表';


CREATE TABLE `examination_man_sku_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sku_no` varchar(32) NOT NULL COMMENT 'sku编码',
  `store_name` varchar(255) DEFAULT NULL COMMENT '门店名称',
  `goods_id` varchar(32) DEFAULT NULL COMMENT '商家套餐ID',
  `store_addr` varchar(255) DEFAULT NULL COMMENT '预约门店地址',
  `store_id` varchar(32) DEFAULT NULL COMMENT '门店编码',
  `channel_no` bigint(20) DEFAULT NULL COMMENT '渠道商家编码',
  `sku_store_price` int(12) DEFAULT NULL COMMENT '套餐门店价格',
  `store_phone` varchar(127) DEFAULT NULL COMMENT '门店电话,可能多个',
  `store_type` tinyint(4) DEFAULT NULL COMMENT '门店类型1公立医院 2私立医院 3专业体检机构',
  `store_level` tinyint(4) DEFAULT NULL COMMENT '门店等级',
  `store_remark` varchar(255) DEFAULT '' COMMENT '门店备注',
  `province_id` int(12) DEFAULT NULL COMMENT '京东-省编码',
  `province_name` varchar(64) DEFAULT NULL COMMENT '省名称',
  `city_id` int(12) DEFAULT NULL COMMENT '京东-市编码',
  `city_name` varchar(127) DEFAULT NULL COMMENT '市名称',
  `county_id` int(12) DEFAULT NULL COMMENT '京东-县/区编码',
  `county_name` varchar(127) DEFAULT NULL COMMENT '县/区名称',
  `status` tinyint(4) DEFAULT '0' COMMENT '价格状态 0未维护 1已维护',
  `third_store_status` tinyint(4) DEFAULT '1' COMMENT '门店状态  0下架 1上架',
  `group_no` bigint(20) DEFAULT NULL COMMENT '套餐编码',
  `lng` double(10, 6) DEFAULT NULL COMMENT '经度',
  `lat` double(10, 6) DEFAULT NULL COMMENT '纬度',
  `yn` bigint(4) DEFAULT '1' COMMENT '是否有效 1有效 0 无效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `store_hours` varchar(64) DEFAULT NULL COMMENT '门店营业时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_examination_sku_store_name` (`store_name`) USING BTREE,
  KEY `idx_examination_sku_store_goods_id` (`goods_id`) USING BTREE,
  KEY `idx_examination_sku_store_no` (`sku_no`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '自营套餐对应服务机构关系表';