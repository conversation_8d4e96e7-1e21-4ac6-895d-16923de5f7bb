<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.jd.com/schema/jmq
	    http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <jmq:transport id="examinationman.jmq.transport"
                   address="${examinationman.jmq.address}"
                   user="${examinationman.jmq.user}"
                   password="${examinationman.password}"
                   app="${examinationman.jmq.app}"/>

    <!--监听商家回调消息，异步处理数据，如预约成功授权信息-->
    <jmq:consumer id="appointResultListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${producer.healthcare.third.appoint.result.topic}" listener="appointResultListener"/>
    </jmq:consumer>

    <jmq:transport id="examinationman.ctt.transport"
                   address="${examinationman.ctt.address}"
                   user="${examinationman.ctt.user}"
                   password="${examinationman.ctt.password}"
                   app="${examinationman.ctt.app}"/>

    <jmq:transport id="examinationman.ctt.transport.report"
                   address="${examinationman.ctt.address}"
                   user="${examinationman.ctt.user}"
                   password="${examinationman.ctt.password}"
                   app="${examinationman.ctt.app}.${examinationman.ctt.report.parse.group}"/>

    <!-- 门店更新binlake-->
    <jmq:consumer id="syncSkuStoreListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.store.topic}" listener="syncSkuStoreInfoLakeListener"/>
    </jmq:consumer>

    <bean id="syncSkuStoreInfoLakeListener" class="com.jd.health.medical.examination.web.listener.SyncSkuStoreInfoLakeListener"/>

    <!-- 体检报告报告binlake-->
    <jmq:consumer id="syncExaminationReportListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.examination.report.topic}" listener="syncExaminationReportLakeListener"/>
    </jmq:consumer>

    <bean id="syncExaminationReportLakeListener"
          class="com.jd.health.medical.examination.web.listener.SyncExaminationReportLakeListener">
    </bean>

    <!-- 体检报告报告binlake-->
    <jmq:consumer id="parseExaminationReportListenerConsumer" transport="examinationman.ctt.transport.report">
        <jmq:listener topic="${healthcare.man.examination.report.topic}" listener="parseExaminationReportLakeListener"/>
    </jmq:consumer>

    <bean id="parseExaminationReportLakeListener"
          class="com.jd.health.medical.examination.web.listener.ParseExaminationReportLakeListener">
    </bean>


    <!-- 门店更新binlake-->
    <jmq:consumer id="syncThirdStoreListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.third.store.topic}" listener="syncThirdStoreInfoLakeListener"/>
    </jmq:consumer>

    <!-- 延时消息 mq -->
    <jmq:consumer id="delayMsgManListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${xfyl_delay_message_topic}" listener="xfylManDelayMsgListener"/>
    </jmq:consumer>
    <bean id="xfylManDelayMsgListener" class="com.jd.health.medical.examination.web.job.XfylManDelayMsgListener"/>

    <!-- 事件MQ监听器 mq -->
    <jmq:consumer id="xfylEventListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${xfyl.man.event.topic}" listener="xfylEventListener"/>
    </jmq:consumer>
</beans>