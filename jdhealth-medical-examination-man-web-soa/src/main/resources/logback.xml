<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true">

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <!--<property name="LEVEL" value="INFO" />
    <property name="PRINT_LOG_BASE_PATH" value="/export/Logs/jdhealth-medical-examination" />
    <property name="MAX_HISTORY" value="7"/>
    <property name="ASYNC_QUEUE_SIZE" value="512"/>-->

    <contextName>medical-examination</contextName>

    <jmxConfigurator />

    <timestamp key="bySecond" datePattern="yyyyMMdd'T'HHmmss" />

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${medical.examination.log.ConversionPattern}</pattern>
        </encoder>
    </appender>
    <appender name="LOGGER-DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${medical.examination.log.path}/admin/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${medical.examination.log.path}/admin/debug.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>${medical.examination.log.maxHistory}</maxHistory>
            <totalSizeCap>${medical.examination.log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${medical.examination.log.ConversionPattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="LOGGER-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${medical.examination.log.path}/admin/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${medical.examination.log.path}/admin/error.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>${medical.examination.log.maxHistory}</maxHistory>
            <totalSizeCap>${medical.examination.log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${medical.examination.log.ConversionPattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="LOGGER-SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${medical.examination.log.path}/admin/sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${medical.examination.log.path}/admin/sql.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>${medical.examination.log.maxHistory}</maxHistory>
            <totalSizeCap>${medical.examination.log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${medical.examination.log.ConversionPattern}</pattern>
        </encoder>
    </appender>
    <appender name="LOGGER-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${medical.examination.log.path}/admin/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${medical.examination.log.path}/admin/info.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>${medical.examination.log.maxHistory}</maxHistory>
            <totalSizeCap>${medical.examination.log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${medical.examination.log.ConversionPattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="LOGGER-SECURITY" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${medical.examination.log.path}/admin/security.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${medical.examination.log.path}/admin/security.log.%d{yyyy-w}</fileNamePattern>
            <maxHistory>${medical.examination.log.maxHistory}</maxHistory>
            <totalSizeCap>${medical.examination.log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- sql -->
    <logger name="org.apache.ibatis"  additivity="false"  level="INFO">
        <appender-ref ref="LOGGER-SQL" />
        <appender-ref ref="LOGGER-ERROR" />
    </logger>

    <logger name="org.mybatis" additivity="false"  level="DEBUG">
        <appender-ref ref="LOGGER-SQL" />
        <appender-ref ref="LOGGER-ERROR" />
        <appender-ref ref="CONSOLE" />
    </logger>

    <logger name="com.jd.health" additivity="false" level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="LOGGER-DEBUG" />
        <appender-ref ref="LOGGER-INFO" />
        <appender-ref ref="LOGGER-ERROR" />
    </logger>

    <logger name="com.jd.pioneer.hand" additivity="false" level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="LOGGER-DEBUG" />
        <appender-ref ref="LOGGER-INFO" />
        <appender-ref ref="LOGGER-ERROR" />
    </logger>
    <logger name="security" additivity="false" level="INFO">
        <appender-ref ref="LOGGER-SECURITY"/>
    </logger>
<!--    <root level="${medical.examination.log.level}">-->
    <root level="DEBUG">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="LOGGER-DEBUG" />
        <appender-ref ref="LOGGER-INFO" />
        <appender-ref ref="LOGGER-ERROR" />
    </root>

</configuration>