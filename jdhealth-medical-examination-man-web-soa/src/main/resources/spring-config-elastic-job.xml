<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:reg="http://www.dangdang.com/schema/ddframe/reg" xmlns:job="http://www.dangdang.com/schema/ddframe/job"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.dangdang.com/schema/ddframe/reg
        http://www.dangdang.com/schema/ddframe/reg/reg.xsd http://www.dangdang.com/schema/ddframe/job http://www.dangdang.com/schema/ddframe/job/job.xsd"
       default-autowire="byName">

    <!--配置作业注册中心 home-zk.jd.local-->
    <reg:zookeeper id="regCenter"
                   server-lists="10.170.170.82:2181"
                   namespace="jdh/healthcare/examination/man/devtest"
                   base-sleep-time-milliseconds="1000"
                   max-sleep-time-milliseconds="3000"
                   max-retries="3"/>


    <!--报告状态推送回调异常处理补偿,如有未更新处理状态记录,重新推送MQ消息,30分钟跑一次-->
    <job:simple id="reportExceptionHandlerJob" class="com.jd.health.medical.examination.web.job.ReportExceptionHandlerJob"
                registry-center-ref="regCenter" cron="0 0/30 * * * ?"
                sharding-total-count="1"
                overwrite="true"
                failover="true"
                misfire="false"
                sharding-item-parameters="0=A"
    />

    <!--报告状态推送回调异常处理补偿,如有未更新处理状态记录,重新推送MQ消息,30分钟跑一次-->
    <job:simple id="appointDateCacheRefreshJob" class="com.jd.health.medical.examination.web.job.AppointDateCacheJob"
                registry-center-ref="regCenter" cron="0 0/1 * * * ?"
                sharding-total-count="1"
                overwrite="true"
                failover="true"
                misfire="false"
                sharding-item-parameters="0=A"
    />

    <!--固定sku有效期愈过期定时提醒任务    每天早上9:00跑一次-->
    <job:simple id="skuExpiredHandlerJob" class="com.jd.health.medical.examination.web.job.SkuExpiredHandlerJob"
                registry-center-ref="regCenter" cron="0 0 9 * * ?"
                sharding-total-count="1"
                overwrite="true"
                failover="true"
                misfire="false"
                sharding-item-parameters="0=A"
    />

    <!--未关联标准结论报告job    每天20:00跑一次-->
    <job:simple id="unresolvedReportHandlerJob" class="com.jd.health.medical.examination.web.job.UnresolvedReportHandlerJob"
                registry-center-ref="regCenter" cron="0 0 20 * * ?"
                sharding-total-count="1"
                overwrite="true"
                failover="true"
                misfire="false"
                sharding-item-parameters="0=A"
    />

</beans>
