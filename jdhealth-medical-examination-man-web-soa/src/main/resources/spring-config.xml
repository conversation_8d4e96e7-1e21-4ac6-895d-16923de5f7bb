<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
	    http://www.springframework.org/schema/aop     http://www.springframework.org/schema/aop/spring-aop.xsd
	    http://www.springframework.org/schema/tx      http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/beans   http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/mvc     http://www.springframework.org/schema/mvc/spring-mvc.xsd"

>
   <!-- <bean class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        &lt;!&ndash; 为集成安全部配置文件加解密组件, 需在项目中所有的PropertySourcesPlaceholderConfigurer配置上ignoreUnresolveablePlaceholders为true &ndash;&gt;
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <value>classpath:common.properties</value>
                <value>classpath:important.properties</value>
                <value>classpath:aces.properties</value>
                <value>classpath*:properties/env-config-devtest.properties</value>
            </list>
        </property>
    </bean>-->

 <context:component-scan base-package="com.jd.health.medical.examination"/>
 <aop:aspectj-autoproxy expose-proxy="true" proxy-target-class="true"/>
 <mvc:annotation-driven/>

 <!--本地环境跑job配置存在注册失败的情况-->
    <import resource="classpath:spring-config-security.xml"/>
    <import resource="classpath:spring-config-elastic-job.xml"/>
    <import resource="classpath*:spring/spring-config-datasource.xml"/>
    <import resource="classpath*:spring/spring-config-dao.xml"/>
    <import resource="classpath*:spring/spring-config-redis.xml"/>
    <import resource="classpath*:spring/spring-config-ump.xml"/>
    <import resource="classpath:spring-config-jsf-provider.xml"/>
    <import resource="classpath:spring-config-mq-consumer.xml"/>
    <import resource="classpath*:spring/spring-mvc-servlet.xml"/>
    <import resource="classpath*:spring/spring-config-jsf-consumer.xml"/>
    <import resource="classpath*:spring/spring-config-jss.xml"/>
    <import resource="classpath*:spring/spring-config-mq-producer.xml"/>
    <import resource="classpath*:spring/spring-config-ducc.xml"/>
    <import resource="classpath*:spring/spring-config-es.xml"/>

</beans>