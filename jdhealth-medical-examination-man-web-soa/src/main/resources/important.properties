#jdbc
jdbc.mysql.url=********************************************************************************************
jdbc.mysql.driver=com.mysql.jdbc.Driver
jdbc.mysql.password=r3kenGvYLBN7j3gW
jdbc.mysql.user=healthcare_admin

o2o.jdbc.mysql.url=****************************************************************************************************************************************************************
o2o.jdbc.mysql.driver=com.mysql.cj.jdbc.Driver
o2o.jdbc.mysql.user=o2o_service_rw
o2o.jdbc.mysql.password={{db:HZaSk5MSGLfEIXBymaQoYA3kNAyCXl2g}}


#\u81EA\u8425\u4F53\u68C0\u5957\u9910 \u9884\u7EA6\u77ED\u4FE1 senderNum \u77ED\u4FE1\u8D26\u53F7
appointment.sms.senderNum=xiaofeiyiliao
#\u9884\u7EA6\u77ED\u4FE1token
appointment.sms.token=3m339x69S143

#\u5361\u5BC6\u4F53\u68C0 \u9884\u7EA6\u77ED\u4FE1extension \u77ED\u4FE1\u8D26\u53F7
appointment.card.sms.senderNum=example3.xx.jd.com
#\u9884\u7EA6\u77ED\u4FE1token
appointment.card.sms.token=63544240ZVV4

#\u77ED\u8FDE\u63A5 \u7EBF\u4E0A\u73AF\u5883alias\u4E3AshortUrlService-prd
#shortUrlService.alias=shortUrlService-test
shortUrlService.alias=shortUrlService-prd
#\u751F\u6210\u77ED\u8FDE\u57DF\u540D
shortUrlService.domain=3.cn
#\u751F\u6210\u77ED\u94FE\u957F\u5EA6
shortUrlService.length=8
#\u751F\u6210\u77ED\u94FE\u79D8\u94A5\uFF0C\u767B\u5F55http://s.3.cn/userIndex.action \u7EBF\u4E0A\u70B9\u51FBAPI\u6587\u6863\u83B7\u53D6\uFF0C\u5EFA\u8BAE\u4EA7\u54C1\u63D0\u4F9B\u7A33\u5B9A\u7684key
shortUrlService.key=4a5873d27bddb4142ed6cb6d70ee0bec

#\u4E0A\u4F20\u4F01\u4E1A\u5934\u50CF\u7684bucketName
enterprise.company.logo.url=enterprise.logo

# \u81EA\u8425\u95E8\u5E97\u5BA2\u670Durl
xfyl.jd.vender.url=3.cn/-12pt6wy


