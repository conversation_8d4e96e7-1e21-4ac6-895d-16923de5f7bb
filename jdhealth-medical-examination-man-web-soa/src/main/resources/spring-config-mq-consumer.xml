<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.jd.com/schema/jmq
	    http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <jmq:transport id="examinationman.jmq.transport"
                   address="${examinationman.jmq.address}"
                   user="${examinationman.jmq.user}"
                   password="${examinationman.password}"
                   app="${examinationman.jmq.app}"/>
    <!--监听过滤大客订单，企销结算使用-->
    <jmq:consumer id="orderOFCListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${orderNoSplitListener_topic}" listener="orderNoSplitListener"/>
        <jmq:listener topic="${orderSplitListener_topic}" listener="orderSplitListener"/>
<!--        <jmq:listener topic="${temp_orderNoSplitListener_topic}" listener="tempOrderNoSplitListener"/>
        <jmq:listener topic="${temp_orderSplitListener_topic}" listener="tempOrderSplitListener"/>-->
    </jmq:consumer>

    <!--监听商家回调消息，异步处理数据，如预约成功授权信息-->
    <jmq:consumer id="appointResultListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${producer.healthcare.third.appoint.result.topic}" listener="appointResultListener"/>
    </jmq:consumer>

    <!--主数据 发送报告 -->
    <jmq:consumer id="doctorReportConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${producer.patient.report.topic}" listener="doctorReportListener"/>
    </jmq:consumer>

    <!-- Loc体检报告mq -->
    <jmq:consumer id="locReportListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${loc_report_topic}" listener="locReportListener"/>
    </jmq:consumer>
    <bean id="locReportListener" class="com.jd.health.medical.examination.web.listener.LocReportListener"/>

    <!-- VC建品审核 mq -->
    <jmq:consumer id="skuCreateApplyListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${vc_create_sku_apply_topic}" listener="skuCreateApplyListener"/>
    </jmq:consumer>

    <jmq:transport id="examinationman.ctt.transport"
                   address="${examinationman.ctt.address}"
                   user="${examinationman.ctt.user}"
                   password="${examinationman.ctt.password}"
                   app="${examinationman.ctt.app}"/>

    <jmq:transport id="examinationman.ctt.transport.report"
                   address="${examinationman.ctt.address}"
                   user="${examinationman.ctt.user}"
                   password="${examinationman.ctt.password}"
                   app="${examinationman.ctt.app}.${examinationman.ctt.report.parse.group}"/>

    <!-- 门店更新binlake-->
    <jmq:consumer id="syncSkuStoreListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.store.topic}" listener="syncSkuStoreInfoLakeListener"/>
    </jmq:consumer>

    <bean id="syncSkuStoreInfoLakeListener" class="com.jd.health.medical.examination.web.listener.SyncSkuStoreInfoLakeListener"/>

    <!-- 检验的信息更新-->
    <jmq:consumer id="syncCheckGroupListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.check.group.topic}" listener="syncCheckGroupListener"/>
    </jmq:consumer>

    <bean id="syncCheckGroupListener" class="com.jd.health.medical.examination.web.listener.SyncCheckGroupListener">
        <property name="idScheme" value="id"/>
    </bean>

    <!-- 体检报告报告binlake-->
    <jmq:consumer id="syncExaminationReportListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.examination.report.topic}" listener="syncExaminationReportLakeListener"/>
    </jmq:consumer>

    <bean id="syncExaminationReportLakeListener"
          class="com.jd.health.medical.examination.web.listener.SyncExaminationReportLakeListener">
    </bean>

    <!-- 体检报告报告binlake 报告同步数据中心-->
    <jmq:consumer id="syncReportCenterBinLakeListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.examination.report.sync.center.topic}" listener="syncReportCenterBinLakeListener"/>
    </jmq:consumer>

    <!-- 体检报告报告binlake-->
    <jmq:consumer id="parseExaminationReportListenerConsumer" transport="examinationman.ctt.transport.report">
        <jmq:listener topic="${healthcare.man.examination.report.topic}" listener="parseExaminationReportLakeListener"/>
    </jmq:consumer>

    <bean id="parseExaminationReportLakeListener"
          class="com.jd.health.medical.examination.web.listener.ParseExaminationReportLakeListener">
    </bean>

    <!-- 体检报告报告binlake-->
    <jmq:consumer id="syncAccountListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.account.create.topic}" listener="syncAccountListener"/>
    </jmq:consumer>


    <!-- 门店更新binlake-->
    <jmq:consumer id="syncThirdStoreListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.third.store.topic}" listener="syncThirdStoreInfoLakeListener"/>
    </jmq:consumer>

    <!-- 门店更新binlake-->
    <jmq:consumer id="syncThirdStoreGoodsListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.third.store.goods.topic}" listener="syncThirdStoreGoodsInfoLakeListener"/>
    </jmq:consumer>

    <!-- 数据中台解析报告接收-->
    <jmq:consumer id="dataCenterReportReceiveListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.man.examination.report.receive.topic}"
                      listener="dataCenterReportReceiveListener"/>
    </jmq:consumer>

    <!-- joysky审批消息接收-->
    <jmq:consumer id="processApprovalSkyMsgListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${joysky.approval.topic}"
                      listener="processApprovalSkyMsgListener"/>
    </jmq:consumer>

    <!-- 延时消息 mq -->
    <jmq:consumer id="delayMsgManListenerConsumer" transport="examinationman.jmq.transport">
        <jmq:listener topic="${xfyl_delay_message_topic}" listener="xfylManDelayMsgListener"/>
    </jmq:consumer>
    <bean id="xfylManDelayMsgListener" class="com.jd.health.medical.examination.web.job.XfylManDelayMsgListener"/>

    <!-- 事件MQ监听器 mq -->
    <jmq:consumer id="xfylEventListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${xfyl.man.event.topic}" listener="xfylEventListener"/>
    </jmq:consumer>

    <!-- 检后报告解读消息接收-->
    <jmq:consumer id="reportInterpretationListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${healthcare.report.interpretation.topic}"
                      listener="reportInterpretationListener"/>
    </jmq:consumer>

    <!-- 检后报告解读消息接收-->
    <jmq:consumer id="reportParseListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${report.interpretation.event.topic}"
                      listener="reportParseListener"/>
    </jmq:consumer>

    <!-- 检后报告解读消息接收-->
    <jmq:consumer id="enterpriseEmployeeListenerConsumer" transport="examinationman.ctt.transport">
        <jmq:listener topic="${enterprise.employee.event.topic}"
                      listener="enterpriseEmployeeListener"/>
    </jmq:consumer>

</beans>