<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd" default-autowire="byName">

    <!--服务端 -->
    <jsf:server id="jsf" protocol="jsf" threads="400" threadpool="cached" queuetype="normal" queues="0"/>

    <!--服务商接口-->
    <jsf:provider id="providerExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ProviderExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="providerExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商品sku接口-->
    <jsf:provider id="skuInfoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.SkuInfoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="skuInfoExportService"
                  compress="snappy"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--提供给C端的 第三方门店信息-->
    <jsf:provider id="thirdStoreExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ThirdStoreExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="thirdStoreExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--提供给C端的 sku商家门店信息-->
    <jsf:provider id="skuStoreExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.SkuStoreExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="skuStoreExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商家门店套餐数据推送接口-->
    <jsf:provider id="thirdDataExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ThirdDataExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="thirdDataExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商家检验报告数据推送接口-->
    <jsf:provider id="thirdDataCheckExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ThirdDataCheckExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="thirdDataCheckExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商家门店预约接口-->
    <jsf:provider id="appointmentApiExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.AppointmentApiExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="appointmentApiExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--体检报告查看-->
    <jsf:provider id="manReportExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ManReportExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="manReportExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--项目组管理-->
    <jsf:provider id="examinationManGroupServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManGroupExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManGroupExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 体检项目管理 -->
    <jsf:provider id="ItemExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ItemExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="itemExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 提供给运营端服务商门店信息接口 -->
    <jsf:provider id="thirdStoreManExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ThirdStoreManExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="thirdStoreManExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--运营后端工具接口-->
    <jsf:provider id="devManExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.DevManExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="devManExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销客户信息接口-->
    <jsf:provider id="enterpriseInfoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseInfoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseInfoExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销客户信息配置接口-->
    <jsf:provider id="enterpriseInfoConfigExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseInfoConfigExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseInfoConfigExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销客户履约配置接口-->
    <jsf:provider id="enterpriseContractConfigExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseContractConfigExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseContractConfigExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销商品池接口-->
    <jsf:provider id="enterpriseSkuExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseSkuExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销账号接口-->
    <jsf:provider id="enterpriseAccountExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseAccountExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseAccountExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销账号接口-->
    <jsf:provider id="enterpriseAccountSystemExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseAccountSystemExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseAccountSystemExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销亲属同购套餐相关接口-->
    <jsf:provider id="enterpriseSkuRelationExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuRelationExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseSkuRelationExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销套餐升级相关接口-->
    <jsf:provider id="enterpriseSkuUpgradeExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuUpgradeExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseSkuUpgradeExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企销交易流程订单管理接口-->
    <jsf:provider id="enterpriseOrderExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseOrderExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseOrderExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--企业权益包接口-->
    <jsf:provider id="compositionExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.CompositionExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="compositionExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--消费医疗通用权益包管理接口-->
    <jsf:provider id="informationExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.InformationExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="informationExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--美年体检接口-->
    <jsf:provider id="health100AppointExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.Health100AppointExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="health100AppointExportService"
                  server="jsf" cache="false" timeout="20000">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 套餐组合-->
    <jsf:provider id="manComposeExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ManComposeExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="manComposeExportService"
                  server="jsf" cache="false" timeout="20000">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--消费医疗通用权益包管理详情接口-->
    <jsf:provider id="packageItemExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.PackageItemExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="packageItemExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--消费医疗集团体检代预约接口-->
    <jsf:provider id="groupAppointmentExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.GroupAppointmentExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="groupAppointmentExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--检验单对外接口(提供给互医)-->
    <jsf:provider id="checkExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.CheckExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="checkExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--检验单对内接口-->
    <jsf:provider id="checkGroupExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.CheckGroupExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="checkGroupExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--检验单对内接口-->
    <jsf:provider id="checkItemExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.CheckItemExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="checkItemExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--报告更换手机号-->
    <jsf:provider id="reportApplyPhoneExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ReportApplyPhoneExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="reportApplyPhoneExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--报告更换手机号-->
    <jsf:provider id="customerExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.CustomerExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="customerExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 报告结论-->
    <jsf:provider id="conclusionExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ConclusionExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="conclusionExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 团检报告-->
    <jsf:provider id="groupInspectionReportExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.GroupInspectionReportExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="groupInspectionReportExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 诊室接口-->
    <jsf:provider id="examinationHospitalDepartExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationHospitalDepartExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationHospitalDepartExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 商品加项包接口-->
    <jsf:provider id="skuPluginBindExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.sku.SkuPluginBindExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="skuPluginBindExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 企销套餐插件接口-->
    <jsf:provider id="enterpriseSkuPluginExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuPluginExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseSkuPluginExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>


    <!--检验单预约接口-->
    <jsf:provider id="checkAppointmentExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.CheckAppointmentExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="checkAppointmentExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <jsf:provider id="checkTemplateExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.CheckTemplateExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="checkTemplateExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>



    <!--门店品牌-->
    <jsf:provider id="providerBrandExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ProviderBrandExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="providerBrandExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--供应商门店-->
    <jsf:provider id="supplierThirdStoreExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.supplier.SupplierThirdStoreExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="supplierThirdStoreExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--供应商账号-->
    <jsf:provider id="examinationAccountExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationAccountExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationAccountExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--审批记录-->
    <jsf:provider id="xfylManApprovalRecordExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.supplier.XfylManApprovalRecordExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="xfylManApprovalRecordExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>


    <!--待办事项-->
    <jsf:provider id="supplierTaskTodoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.supplier.SupplierTaskTodoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="supplierTaskTodoExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--供应商套餐列表-->
    <jsf:provider id="supplierGoodsExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.supplier.SupplierGoodsExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="supplierGoodsExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>


    <!--商家门店预约接口-->
    <jsf:provider id="xfylManAddressExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.basic.XfylManAddressExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="xfylManAddressExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--申请单Man 接口 -->
    <jsf:provider id="xfylManApplyInfoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.basic.XfylManApplyInfoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="xfylManApplyInfoExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--预约服务单导出接口-->
    <jsf:provider id="appointmentInfoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.AppointmentInfoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="appointmentInfoExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>


    <!--excel处理服务运营端-->
    <jsf:provider id="xfylExcelExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.basic.XfylExcelExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="xfylExcelExportService"
                  server="jsf" cache="false" timeout="20000">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--运营端excel处理服务-->
    <jsf:provider id="enterpriseSmsTemplateExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.notice.EnterpriseSmsTemplateExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="enterpriseSmsTemplateExportService"
                  server="jsf" cache="false" timeout="20000">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--员工通知优化-项目短信模板接口-->
    <jsf:provider id="projectSmsTemplateExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.enterprise.service.notice.ProjectSmsTemplateExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="projectSmsTemplateExportService"
                  server="jsf" cache="false" timeout="20000">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--运营端采购单服务-->
    <jsf:provider id="bipManExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ebs.BipManExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="bipManExportService"
                  server="jsf" cache="false" timeout="20000">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--个检报告指标对内接口-->
    <jsf:provider id="examinationManThirdIndicatorExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManThirdIndicatorExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManThirdIndicatorExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--个检报告修订记录对内接口-->
    <jsf:provider id="structReportReviseRecordExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.StructReportReviseRecordExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="structReportReviseRecordExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--个检报告项目对内接口-->
    <jsf:provider id="examinationManThirdItemExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManThirdItemExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManThirdItemExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--个检报告项目关系对内接口-->
    <jsf:provider id="examinationManThirdItemRelExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManThirdItemRelExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManThirdItemRelExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--SKU VC申请记录接口-->
    <jsf:provider id="skuApplyRecordExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.sku.SkuApplyRecordExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="skuApplyRecordExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--消费医疗人资数据接口-->
    <jsf:provider id="xfylManHrInfoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.basic.XfylManHrInfoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="xfylManHrInfoExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--SKU 服务商品和实物商品关系接口-->
    <jsf:provider id="skuApparatusRelExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.sku.SkuApparatusRelExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="skuApparatusRelExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商品关联关系服务接口-->
    <jsf:provider id="examinationManRelevanceExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManRelevanceExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManRelevanceExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--体检项项目关联关系服务接口-->
    <jsf:provider id="examinationManItemRelevanceExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManItemRelevanceExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManItemRelevanceExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--体检报告解析接口-->
    <jsf:provider id="examinationReportExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.report.ExaminationReportExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationReportExportServiceImpl"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--身体部位系统服务接口-->
    <jsf:provider id="examinationReportBodyPartExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.report.ExaminationReportBodyPartExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationReportBodyPartExportServiceImpl"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--检后报告人群接口-->
    <jsf:provider id="examinationManReportCrowdExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ExaminationManReportCrowdExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="examinationManReportCrowdExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 报告推送入口 -->
    <jsf:provider id="pushReportExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.report.PushReportExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="pushReportExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 报告查询入口 -->
    <jsf:provider id="queryReportExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.report.QueryReportExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="queryReportExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>
    <!-- 消费医疗文件管理 -->
    <jsf:provider id="fileManageExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.basic.FileManageExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="fileManageExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--三方项目信息-->
    <jsf:provider id="thirdBaseItemExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.ThirdBaseItemExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="thirdBaseItemExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--sku类目信息-->
    <jsf:provider id="skuCategoryExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.sku.SkuCategoryExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="skuCategoryExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 体检项目管理 -->
    <jsf:provider id="baseThirdItemRelExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.BaseThirdItemRelExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="baseThirdItemRelExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 医生门店关系 -->
    <jsf:provider id="doctorStoreRelExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.doctor.DoctorStoreRelExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="doctorStoreRelExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 医生门店商品关系 -->
    <jsf:provider id="doctorStoreSkuRelExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.doctor.DoctorStoreSkuRelExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="doctorStoreSkuRelExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 医生信息关系 -->
    <jsf:provider id="doctorInfoExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.doctor.DoctorInfoExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="doctorInfoExportServiceImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--结构化发品服务项目属性-->
    <jsf:provider id="structuralSkuAttributeExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.StructuralSkuAttributeExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="structuralSkuAttributeExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>


    <!-- 报告封皮配置 -->
    <jsf:provider id="reportCoverExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.report.ReportCoverExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="reportCoverExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>


    <!--报告分享-->
    <jsf:provider id="reportShareExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.report.ReportShareExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="reportShareExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>
    <!--企销DB转JSF-->
    <jsf:provider id="qxHandoverExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.QxHandoverExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="qxHandoverExportService"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

    <!--日账单服务-->
    <jsf:provider id="billRecordExportServiceJsf"
                  interface="com.jd.health.medical.examination.export.service.BillRecordExportService"
                  alias="${default.provider.alias}${healthcare.provider.demand}"
                  ref="billRecordExportService" serialization="hessian"
                  server="jsf" cache="false">
        <jsf:parameter key="token" value="${default.provider.token}" hide="true"/>
    </jsf:provider>

</beans>