<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        <!-- 为集成安全部配置文件加解密组件, 需在项目中所有的PropertySourcesPlaceholderConfigurer配置上ignoreUnresolveablePlaceholders为true -->
        <property name="ignoreUnresolvablePlaceholders" value="false"/>
        <property name="locations">
            <list>
                <value>classpath:common.properties</value>
                <value>classpath:important.properties</value>
                <value>classpath:aces.properties</value>
                <value>classpath*:properties/env-config-${profiles.active}.properties</value>
                <value>classpath:parallel-jsf-consumer.properties</value>
            </list>
        </property>
        <!-- 为集成安全部配置文件加解密组件, 将所有引用了important.properties文件的PropertyPlaceholder改造成PropertyFactoryBean + PropertySourcesPlaceholderConfigurer的形式 -->
        <property name="properties" ref="secApplicationProperties"/>
    </bean>

    <!-- 为集成安全部配置文件加解密组件, 将所有引用了important.properties文件的PropertyFactoryBean改为com.jd.security.configsec.spring.config.JDSecurityPropertyFactoryBean-->
    <bean id="secApplicationProperties" class="com.jd.security.configsec.spring.config.JDSecurityPropertyFactoryBean">
        <property name="ignoreResourceNotFound" value="true"/>
        <!-- 为集成安全部配置文件加解密组件, 需将important.properties文件配置在secLocation中, 并去除locations中的important.properties-->
        <property name="secLocation" value="classpath:important.properties"/><!-- 不能使用classpath*: -->
    </bean>

</beans>