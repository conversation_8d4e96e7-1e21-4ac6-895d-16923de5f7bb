#\u672C\u7CFB\u7EDF\u6770\u592B\u7EDF\u4E00\u914D\u7F6E
healthcare.provider.demand=

healthcare.consumer.self.demand=
#\u9879\u76EE\u4E3B\u57DF\u540D
medical.examination.domain.name=medical-examination.soa.jd.com
#\u65E5\u5FD7\u914D\u7F6E
medical.examination.log.level=INFO
medical.examination.log.path=/export/Logs/${medical.examination.domain.name}
medical.examination.log.maxHistory=7
medical.examination.log.totalSizeCap=512MB
medical.examination.log.ConversionPattern=%d{"yyyy-MM-dd HH:mm:ss.SSS"} %-5level [%-20.20thread] %30.30logger | [%X{logid}][%X{Jst_TRACE_ID}] %msg%n

# maven active
maven.profiles.active=${profiles.active}