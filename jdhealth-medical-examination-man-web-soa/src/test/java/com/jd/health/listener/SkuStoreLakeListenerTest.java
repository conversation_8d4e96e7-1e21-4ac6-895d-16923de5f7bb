package com.jd.health.listener;

import com.jd.binlog.client.WaveEntry;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.SkuSpeciesEnum;
import com.jd.health.medical.examination.domain.bo.SkuStoreInfoBO;
import com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity;
import com.jd.health.medical.examination.service.selfpersonal.SkuStoreApiService;
import com.jd.health.medical.examination.service.selfpersonal.SkuStoreService;
import com.jd.health.medical.examination.web.listener.SyncSkuStoreInfoLakeListener;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.ump.profiler.proxy.Profiler;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName SkuStoreLakeListenerTest
 * @Description
 * <AUTHOR>
 * @Date 2021/6/17 1:45 下午
 **/
public class SkuStoreLakeListenerTest extends BaseTest {
    
    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(SkuStoreLakeListenerTest.class);
    
    /**
     *
     */
    @Autowired
    private SkuStoreApiService skuStoreApiService;
    
    /**
     *
     */
    @Autowired
    private SkuStoreService skuStoreService;
    
    /**
     *
     */
    @Autowired
    private SyncSkuStoreInfoLakeListener syncSkuStoreInfoLakeListener;
    
    /**
     *
     */
    @Test
    public void test() {
        try {
            Long id = 1241L;
            List<WaveEntry.Column> beforeList = new ArrayList<>();
            WaveEntry.Column.Builder builder = WaveEntry.Column.newBuilder();

            builder.setName("id");
            builder.setValue("1241");
            WaveEntry.Column column =builder.build();

            beforeList.add(column);

            WaveEntry.RowData.Builder rowBuilder= WaveEntry.RowData.newBuilder();
            rowBuilder.addBeforeColumns(0, column);
            WaveEntry.RowData rowData = rowBuilder.build();
            syncSkuStoreInfoLakeListener.refreshByDelete(rowData);
        } catch (Throwable e) {
            log.error("SyncEmployeeInfoLakeListener -> onMessage 体检门店表 binlake消息刷新es异常", e);
            Profiler.businessAlarm("com.jd.health.medical.examination.web.listener.SyncSkuStoreInfoLakeListener.onMessage", JsonUtil.toJSONString(e.getMessage()));
        }
    }
}