package com.jd.health.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jd.health.medical.examination.domain.personal.entity.ItemData;
import com.jd.health.medical.examination.service.selfpersonal.ItemService;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 模板的读取类
 *
 * <AUTHOR>
 */
// 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
public class ItemDataUpdateListener extends AnalysisEventListener<ItemData> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ItemDataUpdateListener.class);
    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private List<ItemData> list = new ArrayList<>();
    /**
     * 一级项的权重
     */
    private int oneItemWeight = 1;
    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */
    private ItemService itemService;

    public ItemDataUpdateListener() {
    }

    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param itemService
     */
    public ItemDataUpdateListener(ItemService itemService) {
        this.itemService = itemService;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     *            one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ItemData data, AnalysisContext context) {
        if (StringUtil.isEmpty(data.getOneItemName())){
            list.add(data);
        }else {
            if (list.size() > 0 ){
                saveData(oneItemWeight*10);
                oneItemWeight++;
                list.clear();
            }
            list.add(data);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData(oneItemWeight*10);
        LOGGER.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData(int oneItemWeight) {
        LOGGER.info("{}条数据，开始存储数据库！", list.size());
        System.out.println(JsonUtil.toJSONString(list));
        if (list.size() > 0){
            //itemService.updateItemList(list,oneItemWeight);
        }
        //demoDAO.save(list);
        LOGGER.info("存储数据库成功！");
    }
}
