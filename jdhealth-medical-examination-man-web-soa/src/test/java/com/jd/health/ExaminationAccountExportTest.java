package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.ProviderDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseAccountDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseAccountParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.param.ProviderParam;
import com.jd.health.medical.examination.export.service.ExaminationAccountExportService;
import com.jd.health.medical.examination.export.service.ProviderExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/30
 */
public class ExaminationAccountExportTest extends BaseTest {

    @Autowired
    private ExaminationAccountExportService accountExportService;

    @Autowired
    private ProviderExportService providerExportService;

    @Test
    public void test1(){
        System.out.println(accountExportService.getProviderBusinessType().getData());
    }

    @Test
    public void addProvider(){
        ProviderParam providerParam = new ProviderParam();
        providerParam.setChannelName("test");
        providerParam.setStatus(2);
        providerParam.setProviderJdNo("test_no");
        providerParam.setProviderJdName("testJd");
        providerParam.setAddress("test_address");
        providerParam.setBusinessLicense("test1234");
        providerParam.setContactName("test_zh");
        providerParam.setContactPhone("***********");
        System.out.println(providerExportService.addMerchantProvider(providerParam).getData());
    }

    @Test
    public void addAccount(){
        EnterpriseAccountParam param = new EnterpriseAccountParam();

        //System.out.println(accountExportService.addSunAccountInfo());
    }

    @Test
    public void pageProvider(){
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(0);
        pageParam.setPageSize(10);
        ProviderParam providerParam = new ProviderParam();
        providerParam.setProviderJdNo("t");
        providerParam.setChannelNo(123L);
        providerParam.setStatus(1);
        providerParam.setChannelName("t");
        JsfResult<PageInfo<ProviderDTO>> pageInfoJsfResult = accountExportService.queryProviderByPage(pageParam, providerParam);
        System.out.println(pageInfoJsfResult.getData());
    }

    @Test
    public void pageAccount(){
        EnterpriseAccountParam accountParam = new EnterpriseAccountParam();
        accountParam.setCompanyNo(9234756822L);
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(10);
        pageParam.setPageNum(0);
        accountExportService.queryAccountByPage(accountParam, pageParam);
    }

    @Test
    public void selectAccount(){
        JsfResult<EnterpriseAccountDTO> yintao = accountExportService.queryAccountInfoByAccountNo("yintao");
        System.out.println(yintao.getData());
    }

    @Test
    public void selectManAccount(){
        JsfResult<EnterpriseAccountDTO> result = accountExportService.queryLeaderAccountByProvider(9234756822L);
        System.out.println(result.getData());
    }
}
