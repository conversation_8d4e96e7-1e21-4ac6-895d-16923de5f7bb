package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.ComposeInfoDTO;
import com.jd.health.medical.examination.export.param.ComposeInfoParam;
import com.jd.health.medical.examination.export.service.ManComposeExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xbj
 * @Date: 2020/5/19 16:12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class ManComposeTest {

    @Autowired
    private ManComposeExportService manComposeExportService;
    @Test
    public void pageTest() {
        List<ComposeInfoParam> composeInfoParamList=new ArrayList<>();
        ComposeInfoParam composeInfoParam=new ComposeInfoParam();
        composeInfoParam.setComposeId("12345678");
        composeInfoParam.setComposeName("测试123");
        composeInfoParam.setGroupNo("13456789098765");
        composeInfoParam.setSuitable(1);
        ComposeInfoParam composeInfoParam1=new ComposeInfoParam();
        composeInfoParam1.setComposeId("12345678");
        composeInfoParam1.setComposeName("测试456");
        composeInfoParam1.setGroupNo("1345678909876545465");
        composeInfoParam1.setSuitable(2);
        composeInfoParamList.add(composeInfoParam);
        JsfResult<Boolean>  jsfResult=manComposeExportService.saveExaminationManCompose(composeInfoParamList);
         System.out.println("=================="+ JsonUtil.toJSONString(jsfResult));
    }

    @Test
    public  void updateExaminationManCompose(){
        List<ComposeInfoParam> composeInfoParamList=new ArrayList<>();
        ComposeInfoParam composeInfoParam=new ComposeInfoParam();
        composeInfoParam.setComposeId("12345678");
        composeInfoParam.setComposeName("测试1");
        composeInfoParam.setGroupNo("11111111");
        composeInfoParam.setSuitable(1);
        ComposeInfoParam composeInfoParam1=new ComposeInfoParam();
        composeInfoParam1.setComposeId("00000000000000000");
        composeInfoParam1.setComposeName("测试2");
        composeInfoParam1.setGroupNo("1345678909876545465");
        composeInfoParam1.setSuitable(2);
        composeInfoParamList.add(composeInfoParam);
        composeInfoParamList.add(composeInfoParam1);
        JsfResult<Boolean>  jsfResult=manComposeExportService.updateExaminationManCompose(composeInfoParamList);
        System.out.println("=================="+ JsonUtil.toJSONString(jsfResult));
    }

    @Test
    public  void selectExaminationManComposePage(){
        ComposeInfoParam composeInfoParam=new ComposeInfoParam();
        composeInfoParam.setPageNum(1);
        composeInfoParam.setPageSize(10);
        JsfResult<PageInfo<ComposeInfoDTO>> jsfResult=manComposeExportService.selectExaminationManComposePage(composeInfoParam);
        System.out.println("=================="+ JsonUtil.toJSONString(jsfResult));
    }
    @Test
    public  void selectExaminationManComposeDetail(){
        JsfResult<ComposeInfoDTO> jsfResult=manComposeExportService.selectExaminationManComposeDetail("12345678");
        System.out.println("=================="+ JsonUtil.toJSONString(jsfResult));
    }

    @Test
    public  void delExaminationManCompose(){
        JsfResult<Boolean> jsfResult=manComposeExportService.delExaminationManCompose("12345678");
        System.out.println("=================="+ JsonUtil.toJSONString(jsfResult));
    }
}
