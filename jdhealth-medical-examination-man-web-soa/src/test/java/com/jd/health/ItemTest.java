package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.domain.bo.ItemBO;
import com.jd.health.medical.examination.domain.personal.entity.ItemEntity;
import com.jd.health.medical.examination.export.dto.ProviderDTO;
import com.jd.health.medical.examination.export.dto.ThirdStoreDTO;
import com.jd.health.medical.examination.export.param.ThirdStoreParam;
import com.jd.health.medical.examination.export.param.item.GetThreeLevelItemPageParam;
import com.jd.health.medical.examination.export.service.DevManExportService;
import com.jd.health.medical.examination.export.service.ProviderExportService;
import com.jd.health.medical.examination.export.service.ThirdStoreExportService;
import com.jd.health.medical.examination.service.selfpersonal.ItemService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.List;

/**
 * 体检项目测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/15 11:03
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class ItemTest {
    @Resource
    private ItemService enhanceCacheItemService;
    @Autowired
    ProviderExportService providerExportService;

    @Autowired
    private ThirdStoreExportService thirdStoreExportService;

    @Autowired
    DevManExportService devManExportService;

    @Test
    public void getAllOneLevelItemTest() {
        List<ItemEntity> allOneLevelItem = enhanceCacheItemService.getAllOneLevelItem();
        System.out.println(allOneLevelItem);
    }

    @Test
    public void getTwoLevelItemByParentNoTest(){
        List<ItemEntity> twoLevelItemByParentNo = enhanceCacheItemService.getTwoLevelItemByParentNo(1L);
        System.out.println(twoLevelItemByParentNo);
    }

    @Test
    public void parseLongTest(){
        long l = Long.parseLong("123");
        System.out.println(l);
    }

    @Test
    public void getPageThreeItems(){
        GetThreeLevelItemPageParam itemEntity = new GetThreeLevelItemPageParam();
        PageInfo<ItemBO> pageThreeLevelItem = enhanceCacheItemService.getPageThreeLevelItem(itemEntity, 1, 10);
        System.out.println(pageThreeLevelItem.getList());
        System.out.println(JsonUtil.toJSONString(pageThreeLevelItem.getList()));
    }

    @Test
    public void thirdStoreExportServiceTest(){
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(7975142641L);
        thirdStoreParam.setStoreId("YY201507211522260001");
        JsfResult<ThirdStoreDTO> thirdStoreByStoreId =
                thirdStoreExportService.getThirdStoreByStoreId(thirdStoreParam);
        System.out.println(thirdStoreByStoreId.getCode());
        System.out.println(thirdStoreByStoreId.getMsg());
    }

    @Test
    public void getAllProviders(){
        JsfResult<List<ProviderDTO>> providerList = providerExportService.getProviderList();
        System.out.println(JsonUtil.toJSONString(providerList));
    }

   /* @Test
    public void batchInsertItem(){
        String str = "[{\"createTime\":1585559438000,\"createUser\":\"system\",\"id\":58,\"itemLevel\":1,\"itemMean\":\"全数字X光（DR）\",\"itemName\":\"全数字X光（DR）\",\"itemNo\":1585559437827,\"itemSuitable\":123,\"updateTime\":1585563266000,\"yn\":1},{\"createTime\":1585559438000,\"createUser\":\"system\",\"id\":59,\"itemLevel\":1,\"itemMean\":\"临床基础检查\",\"itemName\":\"临床基础检查\",\"itemNo\":1585559437854,\"itemSuitable\":123,\"updateTime\":1585563266000,\"yn\":1},{\"createTime\":1585559438000,\"createUser\":\"system\",\"id\":60,\"itemLevel\":1,\"itemMean\":\"磁共振成像（MRI）\",\"itemName\":\"磁共振成像（MRI）\",\"itemNo\":1585559437871,\"itemSuitable\":123,\"updateTime\":1585563266000,\"yn\":1}]";
        JsfResult<Integer> integerJsfResult = devManExportService.batchInsertItem(str);
        System.out.println(JsonUtil.toJSONString(integerJsfResult));
    }*/
}
