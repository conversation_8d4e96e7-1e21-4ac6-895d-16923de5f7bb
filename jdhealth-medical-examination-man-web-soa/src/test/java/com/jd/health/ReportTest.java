package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.dao.MyReportDao;
import com.jd.health.medical.examination.domain.MyReportEntity;
import com.jd.health.medical.examination.export.param.BindReportParam;
import com.jd.health.medical.examination.export.param.ExaminationReportUrlParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.MyReportService;
import com.jd.health.report.client.dto.ReportInfoDTO;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.List;

/**
 * Note:
 * Create by: yintao
 * Create date: 2020/1/20 18:27
 */
public class ReportTest extends BaseTest {



    @Autowired
    MyReportService myReportService;

    /**
     * 插入报告
     */
    @Test
    public void insertReportTest(){
        MyReportEntity myReportEntity = new MyReportEntity();
        myReportEntity.setOrderType("200");
        myReportEntity.setUserPin("yintao");
        myReportEntity.setJdAppointmentId(System.currentTimeMillis());
        myReportEntity.setUserPhone("16619887367");
        myReportEntity.setReportUrl("/we/ser");
        myReportEntity.setReportSource(1);
        myReportEntity.setCompanyNo("123456");
        myReportEntity.setReportVerifyStatus(0);
        myReportEntity.setUserName("银涛");
        Integer result = myReportService.insertReport(myReportEntity);
        System.out.println(result);
    }


    /**
     * 获取报告url
     */
    @Test
    public void getReportInfoTest(){
        ExaminationReportUrlParam examinationReportUrlParam = new ExaminationReportUrlParam();
        examinationReportUrlParam.setReportId(1234567854L);
        examinationReportUrlParam.setUserPin("yintao");
        examinationReportUrlParam.setReportType(200);
        MyReportEntity myReportEntity = myReportService.getReportInfo(examinationReportUrlParam);
        System.out.println(JsonUtil.toJSONString(myReportEntity));
    }

    @Autowired
    MyReportDao myReportDao;
    /**
     * 报告绑定
     */
    @Test
    public void bindReport(){
        BindReportParam bindReportParam = new BindReportParam();
        bindReportParam.setUserName("银涛");
        bindReportParam.setUserPhone("16619887367");
        bindReportParam.setUserPin("yintao22");
        MyReportEntity myReportQuery = new MyReportEntity();
        myReportQuery.setUserName(bindReportParam.getUserName());
        myReportQuery.setUserPhone(bindReportParam.getUserPhone());
        List<MyReportEntity> entities = myReportDao.queryUnbindMyReportList(myReportQuery);
        System.out.println("queryUnbindMyReportList:"+JsonUtil.toJSONString(entities));
    }


    /**
     * 构建结构化报告bo
     */
    @Test
    public void saveStructReportTest() throws Exception {
    
        BufferedReader input = null;
        StringBuilder sb = null;
        MyReportEntity myReportEntity = myReportService.queryReport(1679218370332854786L);
        ReportInfoDTO parseResult = null;
        String fileName = "/Users/<USER>/Documents/Work/工作/20211118个人结构化体检报告（美年试点）/美年-结构化解析数据.json";
        File file = new File(fileName);
        InputStream is = new FileInputStream(file);
        input = new BufferedReader(new InputStreamReader(is));
        sb = new StringBuilder();
        String s;
        while ((s = input.readLine()) != null) {
            sb.append(s).append("\n");
        }
        parseResult = JsonUtil.parseObject(sb.toString(), ReportInfoDTO.class);
        Long time = 1000000000000L;
        myReportService.analysisStructReport(myReportEntity, parseResult, time);
    }
    
    /**
     * 定时任务处理未关联标准结论的报告
     */
    @Test
    public void batchUpdateUnresolvedReportTest() {
        try {
            myReportService.batchUpdateUnresolvedReport();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
