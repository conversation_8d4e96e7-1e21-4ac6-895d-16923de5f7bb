package com.jd.health.check;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.export.param.CheckPushOrderInfoParam;
import com.jd.health.medical.examination.export.param.ConsigneeInfoParam;
import com.jd.health.medical.examination.export.param.InvoiceParam;
import com.jd.health.medical.examination.export.param.SkuParam;
import com.jd.health.medical.examination.rpc.domain.param.mofeishi.CheckReportInfo;
import com.jd.health.medical.examination.rpc.domain.param.mofeishi.MoFeiShiResult;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 莫费时-居家检测
 * <AUTHOR>
 * @date 2022/1/14 - 1:30 下午
 **/
public class MoFeiShiTest {
    
    private static final Logger log = LoggerFactory.getLogger(MoFeiShiTest.class);
    
    private static final String AES_KEY = "JDH#MoFeiShi2022";
    
    public static void main(String[] args) throws Exception {
//        pushCheckOrderInfo();
//        getCheckReport();
        tet();
    }
    
    private static String token() {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Authorization", "Basic bWF0ZTptYXRlX3NlY3JldA==");
        // body
        Map<String, String> bodyMap = Maps.newHashMap();
        bodyMap.put("grant_type", "client_credentials");
        bodyMap.put("scope", "all");
        // https
        String result = HttpSimpleClient.simplePostReport("http://139.129.240.194/api/mate-uaa/oauth/token", headerMap, JsonUtil.toJSONString(bodyMap));
        log.info("CheckSheetMoFeiShiRpcImpl --> getToken  result = {}", result);
        if (StringUtils.isNotEmpty(result)) {
            JSONObject jsonObject = JsonUtil.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (code != 200) {
                log.error("CheckSheetMoFeiShiRpcImpl -> getToken error {}", result);
                throw new BusinessException(BaseErrorCode.RICH_TOKEN_ERROR);
            }
            if (dataObject != null) {
                String accessToken = dataObject.getString("accessToken");
                if (StringUtils.isNotEmpty(accessToken)) {
                    return accessToken;
                }
            }
        }
        return "";
    }
    
    private static void pushCheckOrderInfo() throws Exception {
        ConsigneeInfoParam consigneeInfoParam = new ConsigneeInfoParam();
        consigneeInfoParam.setName("测试1");
        consigneeInfoParam.setMobile("***********");
        consigneeInfoParam.setDeatilAddr("测试地址");
        consigneeInfoParam.setProvince("北京");
        consigneeInfoParam.setCity("北京");
        consigneeInfoParam.setArea("大兴");
        consigneeInfoParam.setTown("亦庄开发区");
        consigneeInfoParam.setProvinceId("1001");
        consigneeInfoParam.setCityId("2001");
        consigneeInfoParam.setAreaId("3001");
        consigneeInfoParam.setTownId("4001");
        
        List<SkuParam> skuParamList = new ArrayList<>();
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuId("1");
        skuParam.setSkuName("1");
        skuParam.setWeight(null);
        skuParam.setCheckItem("1");
        skuParam.setServiceMode("3");
        skuParam.setNum(1);
        skuParam.setPrice(BigDecimal.TEN);
        skuParamList.add(skuParam);
        
        InvoiceParam invoiceParam = new InvoiceParam();
        invoiceParam.setSelectInvoiceType(3);
        invoiceParam.setInvoiceHeaderType(4);
        invoiceParam.setInvoiceTitle("测试");
        invoiceParam.setCode("1234567890");
        
        CheckPushOrderInfoParam param = new CheckPushOrderInfoParam();
        param.setChannelType(3475528491L);
        param.setVenderid("123456");
        param.setOrderId(1L);
        param.setOrderType("22");
        param.setConsigneeInfoParam(consigneeInfoParam);
        param.setSkuList(skuParamList);
        param.setInvoiceParam(invoiceParam);
        param.setCreationTime("2022-01-14 11:11:11");
        
        
        String encryptData = Aes.encryptData(JsonUtil.toJSONString(param), AES_KEY);
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("accessToken", token());
        
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("secStr", encryptData);
        
        String result = HttpSimpleClient.simplePostReport("http://139.129.240.194/api/mate-system/jos/check/pushCheckOrderInfo", headerMap, JsonUtil.toJSONString(paramMap));
        log.info("CheckSheetMoFeiShiRpcImpl --> pushCheckOrderInfo  result = {}", result);
    }
    
    public static void getCheckReport() {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("accessToken", token());
        
        Map<String, String> param = Maps.newHashMap();
        param.put("orderId", "123456");
        param.put("appointmentNo", "123456");
        param.put("reportId", "1481811807939584002");
        String result = HttpSimpleClient.simplePostReport("http://139.129.240.194/api/mate-system/jos/check/downloadCheckReportInfo", headerMap, JsonUtil.toJSONString(param));
        log.info("CheckSheetMoFeiShiRpcImpl -> getCheckReport, result={}", result);
    }
    
    public static void tet() {
        String result = "{\"code\":200,\"msg\":\"处理成功\",\"time\":1642400066848,\"data\":{\"appointmentNo\":\"123456\",\"orderId\":\"123456\",\"reportId\":\"1481811807939584002\",\"reportStr\":\"data:application/pdf;base64,JVBERi0xLjMKJcTl8uXrp/Og0MTGCjMgMCBvYmoKPDwgL0ZpbHRlciAvRmxhdGVEZWNvZGUgL0xlbmd0aCA3MzkyID4+CnN0cmVhbQp4AbVd+b9kR1X//f4VdwYS+gmvc+/tvre72UI2QvCFZMwzqAxBmRASeA9DJhBkEdGJMCgquOASFsWFRRE33FFRcUFBRdnc17gC7gtu31NVZ6mqe6e73+35+EEeZ+p8q86pqnO+VXWr+oXlmfKF5VXXna/Lc+fLyv3fVPRgo=\"}}";
        MoFeiShiResult<CheckReportInfo> moFeiShiResult = JsonUtil.parseObject(result, new TypeReference<MoFeiShiResult<CheckReportInfo>>(){});
        String reportStr = moFeiShiResult.getData().getReportStr();
        System.out.println(reportStr);
    }
    
}
