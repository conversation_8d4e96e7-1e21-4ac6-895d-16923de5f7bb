package com.jd.health.check;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jd.health.domain.ImportAliasItemBO;
import com.jd.health.medical.examination.common.constant.CommonConstant;
import com.jd.health.medical.examination.common.enums.ItemLevelEnum;
import com.jd.health.medical.examination.common.util.EasyExcelUtil;
import com.jd.health.medical.examination.domain.bo.report.SaveBatchThirdIndicatorBO;
import com.jd.health.medical.examination.domain.bo.report.SaveBatchThirdItemBO;
import com.jd.health.medical.examination.domain.report.entity.basic.StructReportReviseRecordEntity;
import com.jd.health.medical.examination.export.dto.report.ExaminationManThirdIndicatorDTO;
import com.jd.health.medical.examination.export.dto.report.ExaminationManThirdItemDTO;
import com.jd.health.medical.examination.export.param.BaseItemAliasParam;
import com.jd.health.medical.examination.export.param.report.ExaminationManThirdIndicatorPageParam;
import com.jd.health.medical.examination.export.param.report.ExaminationManThirdItemPageParam;
import com.jd.health.medical.examination.export.param.report.ExaminationManThirdItemRelParam;
import com.jd.health.medical.examination.export.service.ExaminationManThirdIndicatorExportService;
import com.jd.health.medical.examination.export.service.ExaminationManThirdItemExportService;
import com.jd.health.medical.examination.export.service.ExaminationManThirdItemRelExportService;
import com.jd.health.medical.examination.service.report.ExaminationManThirdItemService;
import com.jd.health.medical.examination.service.report.StructReportReviseRecordService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.security.tde.util.Base64;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import sun.misc.BASE64Decoder;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-25 16:23
 * @description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class ReportTest {

    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(ReportTest.class);

    /**
     * service
     */
    @Autowired
    private ExaminationManThirdItemService examinationManThirdItemService;

    /**
     *
     */
    @Autowired
    private ExaminationManThirdItemRelExportService examinationManThirdItemRelExportService;

    /**
     *
     */
    @Autowired
    private StructReportReviseRecordService structReportReviseRecordService;

    /**
     *
     */
    @Autowired
    private ExaminationManThirdItemExportService examinationManThirdItemExportService;

    /**
     *
     */
    @Autowired
    private ExaminationManThirdIndicatorExportService examinationManThirdIndicatorExportService;

    /**
     * test
     */
    @Test
    public void insertItemTest(){

        String brandId = "100000004";

        List<SaveBatchThirdItemBO> itemBOList = new ArrayList<>();
        SaveBatchThirdItemBO itemBO = new SaveBatchThirdItemBO();
        itemBO.setItemName("一般检查");
        itemBO.setOperationUser("zzk");
        itemBO.setSource(1);
        itemBOList.add(itemBO);
        SaveBatchThirdItemBO itemBO1 = new SaveBatchThirdItemBO();
        itemBO1.setItemName("耳鼻喉科");
        itemBO1.setOperationUser("zzk");
        itemBO1.setSource(1);
        itemBOList.add(itemBO1);


        List<SaveBatchThirdIndicatorBO > indicatorBOList = new ArrayList<>();
        SaveBatchThirdIndicatorBO indicatorBO = new SaveBatchThirdIndicatorBO();
        indicatorBO.setIndicatorName("眼科");
        indicatorBO.setNormalRangeValue("0-100");
        indicatorBO.setOperationUser("zzk");
        indicatorBO.setThirdItemName("一般检查");
        indicatorBO.setUnit("m");
        indicatorBO.setSource(1);
        indicatorBOList.add(indicatorBO);

        SaveBatchThirdIndicatorBO indicatorBO1 = new SaveBatchThirdIndicatorBO();
        indicatorBO1.setIndicatorName("鼻内窥镜检查");
        indicatorBO1.setNormalRangeValue("--");
        indicatorBO1.setOperationUser("zzk");
        indicatorBO1.setThirdItemName("耳鼻喉科");
        indicatorBO1.setUnit("");
        indicatorBO1.setSource(1);
        indicatorBOList.add(indicatorBO1);

        SaveBatchThirdIndicatorBO indicatorBO2 = new SaveBatchThirdIndicatorBO();
        indicatorBO2.setIndicatorName("喉内窥镜检查");
        indicatorBO2.setNormalRangeValue("--");
        indicatorBO2.setOperationUser("zzk");
        indicatorBO2.setThirdItemName("耳鼻喉科");
        indicatorBO2.setUnit("");
        indicatorBO2.setSource(1);
        indicatorBOList.add(indicatorBO2);


        examinationManThirdItemService.saveThirdItemsIndicatorAfterApproval(itemBOList,indicatorBOList,brandId);
    }

    /**
     * test
     */
    @Test
    public void updateThirdItemRel(){
        String brandId = "100000004";

        ExaminationManThirdItemRelParam param = new ExaminationManThirdItemRelParam();
        param.setBrandId(brandId);
        param.setItemLevel(ItemLevelEnum.ITEM_THREE.getType());
        param.setItemNo(906225042946L);
        param.setParentNo(466665810L);
        param.setUpdateUser("zzk");

        examinationManThirdItemRelExportService.updateThirdItemRel(param);
    }

    /**
     * test
     */
    @Test
    public void insertRevise(){
        String brandId = "100000004";

        StructReportReviseRecordEntity param = new StructReportReviseRecordEntity();
        param.setBrandId(brandId);
        param.setStructReportId("12312");
        param.setDataType(1);
        param.setSourceName("学唱蛋白");
        param.setUpdateName("血红蛋白");
        param.setCreateUser("zzk");

        structReportReviseRecordService.insert(param);
    }

    /**
     * test
     */
    @Test
    public void itemQueryPage(){
        String brandId = "100000004";

        ExaminationManThirdItemPageParam param = new ExaminationManThirdItemPageParam();
        param.setBrandId(brandId);
        param.setPageNum(1);
        param.setPageSize(2);
        param.setItemName("喉科");


        JsfResult<PageInfo<ExaminationManThirdItemDTO>> pageInfoJsfResult = examinationManThirdItemExportService.queryPage(param);

        System.out.println(JsonUtil.toJSONString(pageInfoJsfResult));
    }

    /**
     * test
     */
    @Test
    public void indicatorQueryPage(){
        String brandId = "100000004";

        ExaminationManThirdIndicatorPageParam param = new ExaminationManThirdIndicatorPageParam();
        param.setBrandId(brandId);
        param.setPageNum(1);
        param.setPageSize(2);
        param.setFuzzyIndicatorName("检查");


        JsfResult<PageInfo<ExaminationManThirdIndicatorDTO>> pageInfoJsfResult = examinationManThirdIndicatorExportService.queryPage(param);

        System.out.println(JsonUtil.toJSONString(pageInfoJsfResult));
    }

    /**
     * fileToBase64
     *
     * @param filePath
     * @return
     */
    public static String fileToBase64(String filePath) {
        File file = new File(filePath);
        String base64 = null;
        InputStream in = null;
        try {
            in = new FileInputStream(file);
            byte[] bytes = new byte[in.available()];
            int length = in.read(bytes);
            base64 = Base64.encodeToString(bytes, 0, length, Base64.DEFAULT);
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return base64;
    }

    /**
     * importItemAlias
     */
    @Test
    public void importItemAlias() throws Exception {
        String file = fileToBase64("/Users/<USER>/Downloads/别名库与标准库 (3).xlsx");
        //处理excel文件的头部信息
        byte[] bytes = new BASE64Decoder().decodeBuffer(file);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        //获得excel导入内容数据
        List<ImportAliasItemBO> read = EasyExcelUtil.read(inputStream, CommonConstant.ONE, ImportAliasItemBO.class);
        //根据供应商体检项聚合
        Map<String, List<ImportAliasItemBO>> thirdName2baseItemList = read.stream().collect(Collectors.groupingBy(ImportAliasItemBO::getThirdItemName));

        List<BaseItemAliasParam> params = new ArrayList<>();
        for (Map.Entry<String, List<ImportAliasItemBO>> entry : thirdName2baseItemList.entrySet()) {
            BaseItemAliasParam param = new BaseItemAliasParam();
            param.setThirdItemName(entry.getKey());
            Set<String> set = new HashSet<String>();
            for (ImportAliasItemBO bo : entry.getValue()) {
                String[] split = bo.getBaseItemName().split(";");
                set.addAll(Arrays.stream(split).map(this::replaceChineseBrackets).collect(Collectors.toList()));
            }
            param.setBaseItemNames(Lists.newArrayList(set));
            params.add(param);
        }
        String jsonString = JsonUtil.toJSONString(params);
        log.info("ReportTest -> importItemAlias, params={}", jsonString);
        JsfResult<Integer> result = examinationManThirdItemExportService.importItemAlias(jsonString);
        log.info("ReportTest -> importItemAlias, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * replaceChineseBrackets
     *
     * @param itemName
     * @return
     */
    private String replaceChineseBrackets(String itemName) {
        if (StringUtil.isNotEmpty(itemName) && (itemName.contains("（") || itemName.contains("）"))) {
            return itemName.replaceAll("（", "(").replaceAll("）", ")");
        }
        return itemName;
    }
}
