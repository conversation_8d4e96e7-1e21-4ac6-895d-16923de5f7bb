package com.jd.health.check;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jd.health.medical.examination.common.constant.CommonConstant;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.domain.CheckGroupEntity;
import com.jd.health.medical.examination.domain.CheckGroupItemEntity;
import com.jd.health.medical.examination.domain.bo.CheckGroupEntityEsBO;
import com.jd.health.medical.examination.domain.enterprise.bo.SkuIsLocAndXfylBO;
import com.jd.health.medical.examination.export.dto.CheckOrderInfo;
import com.jd.health.medical.examination.export.dto.ExaminationManCheckTemplateDTO;
import com.jd.health.medical.examination.export.param.*;
import com.jd.health.medical.examination.export.service.CheckAppointmentExportService;
import com.jd.health.medical.examination.export.service.CheckGroupExportService;
import com.jd.health.medical.examination.export.service.CheckTemplateExportService;
import com.jd.health.medical.examination.export.service.ThirdDataCheckExportService;
import com.jd.health.medical.examination.rpc.CheckSheetRpc;
import com.jd.health.medical.examination.rpc.CheckSheetRpc;
import com.jd.health.medical.examination.rpc.gms.FeatureWriteServiceRpc;
import com.jd.health.medical.examination.service.CheckGroupService;
import com.jd.health.medical.examination.service.CheckGroupTemplateService;
import com.jd.health.medical.examination.service.CheckTemplateService;
import com.jd.health.medical.examination.service.common.gms.SkuFeatureWriteService;
import com.jd.health.medical.examination.service.enterprise.EnterpriseSkuService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 检验单测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-25 16:23
 * @description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class CheckTest {

    /**
     * service
     */
    @Autowired
    private ThirdDataCheckExportService thirdDataCheckExportService;
    @Autowired
    CheckGroupService checkGroupService;

    @Autowired
    private CheckGroupExportService checkGroupExportService;

    @Autowired
    private FeatureWriteServiceRpc featureWriteServiceRpc;

    @Autowired
    private CheckAppointmentExportService checkAppointmentExportService;
    @Autowired
    private CheckTemplateExportService checkTemplateExportService;
    @Autowired
    private CheckGroupTemplateService checkGroupTemplateService;
    @Autowired
    private CheckTemplateService checkTemplateService;
//    @Autowired
//    private CheckSheetRpc checkSheetMoFeiShiRpc;
    @Autowired
    private SkuFeatureWriteService skuFeatureWriteService;
    @Autowired
    private EnterpriseSkuService enterpriseSkuService;



    /**
     * test
     */
    @Test
    public void queryCheckGroupByNoTest(){
        ThirdCheckReportNoticeParam param = new ThirdCheckReportNoticeParam();
        param.setOrderId("123");
        param.setAppointmentNo("123");
        param.setChannelType(1316673423L);
        param.setCheckDate("2020-10-10");
        param.setCredentialNo("123123");
        param.setIsLastReport(true);
        param.setUserPin("wds_it_m");
        param.setReportTotalNumber(2);
        List<SkuInfo> skuInfos = new ArrayList<>();
        SkuInfo skuInfo = new SkuInfo();
        skuInfo.setSkuId(111L);
        skuInfo.setSkuName("test");
        skuInfos.add(skuInfo);
        param.setSkus(skuInfos);

        List<CheckReportItem> list = new ArrayList<>();
        CheckReportItem item = new CheckReportItem();
        item.setReportId("123");
        item.setCheckItemName("血常规测试");
        list.add(item);
        param.setReportList(list);
        JsfResult<Boolean> result = thirdDataCheckExportService.pushCheckSheetReportNotice(param);
        System.out.println("result=>" + JsonUtil.toJSONString(result));
    }




    /**
     * test
     */
    @Test
    public void insertCheckTest(){
        CheckGroupEntity checkGroupEntity = new CheckGroupEntity();
        checkGroupEntity.setChannelName("test");
        checkGroupEntity.setChannelNo(123L);
        checkGroupEntity.setSkuNo("12314");
        checkGroupEntity.setGroupType(1);
        checkGroupEntity.setGroupName("测试套餐名称");
        checkGroupEntity.setGroupNo(123L);
        //JSON城市
        List<String> proviceList = new ArrayList<>();
        proviceList.add("1");
        proviceList.add("2");
        checkGroupEntity.setProvinceArea(JsonUtil.toJSONString(proviceList));
        List<String> cityList = new ArrayList<>();
        cityList.add("100");
        cityList.add("200");
        checkGroupEntity.setCityArea(JsonUtil.toJSONString(cityList));
        checkGroupEntity.setStatus(1);

        List<CheckGroupItemEntity> itemEntityList = new ArrayList<>();
        CheckGroupItemEntity entity = new CheckGroupItemEntity();
        entity.setItemName("测试项目");
        entity.setItemNo(456L);
        entity.setItemDesc("测试描述");
        entity.setCreateUser("yintao");
        entity.setUpdateUser("yintao");
        itemEntityList.add(entity);
        checkGroupEntity.setCreateUser("yintao");
        checkGroupEntity.setUpdateUser("yintao");
        checkGroupEntity.setItemEntities(itemEntityList);
        checkGroupService.addCheckGroup(checkGroupEntity);
    }

    /**
     * test
     */
    @Test
    public void queryCheckTest(){

        featureWriteServiceRpc.mergeSkuFeature("",100002541255L,"xfyl","1");

        //Boolean aBoolean = skuFeatureWriteService.setSkuDimensionFlagValue(CommonConstant.POP_SKU_XFYL_FIELD,10020010083403L,"1");
        System.out.println("asdas");

//        Boolean a = skuFeatureWriteService.setSkuDimensionFlagValue(CommonConstant.POP_SKU_XFYL_FIELD,10020010086357L,"6");
//        System.out.println("asdas");
//
//        Boolean c = skuFeatureWriteService.setSkuDimensionFlagValue(CommonConstant.POP_SKU_XFYL_FIELD,10020010083501L,"6");
//        System.out.println("asdas");



//        CheckGroupEntity checkGroupEntity = checkGroupService.queryGroupEsById(3L);
//        if(null != checkGroupEntity){
//            System.out.println(checkGroupEntity.getProvinceArea());
//            System.out.println(checkGroupEntity.getCityArea());
//        }
//        System.out.println(JsonUtil.toJSONString(checkGroupEntity));
    }

    @Test
    public void updateGroupStatusTest(){
        SkuIsLocAndXfylBO skuIsLocAndXfyl = enterpriseSkuService.getSkuIsLocAndXfyl("100002558323");
        System.out.println(skuIsLocAndXfyl);
    }

    @Test
    public void testUpdateCheckAppointmentStatus(){


        Integer groupType = 1;

        SkuIsLocAndXfylBO skuIsLocAndXfyl = new SkuIsLocAndXfylBO();
        skuIsLocAndXfyl.setXfyl("");

        //到店服务：isLOC必须等于1，且xfyl必须等于0或null。否则，提示错误
        if (CheckGroupTypeEnum.TO_SHOP.getType().equals(groupType)) {
            if (CommonConstant.CHARACTER_ONE.equals(skuIsLocAndXfyl.getIsLoc())
                    &&
                    (StringUtil.isEmpty(skuIsLocAndXfyl.getXfyl()) || CommonConstant.CHARACTER_ZERO.equals(skuIsLocAndXfyl.getXfyl()))) {
                return;
            } else {
                throw new BusinessException(BaseErrorCode.TO_SHOP_MUST_BE_LOC_GOODS);
            }
        }

        //居家检测服务：isLOC必须等于0或null，且skuxfyl必须等于0或null
        if (CheckGroupTypeEnum.SELF_SAMPLING.getType().equals(groupType)) {
            if ((StringUtil.isEmpty(skuIsLocAndXfyl.getIsLoc()) || CommonConstant.CHARACTER_ZERO.equals(skuIsLocAndXfyl.getIsLoc()))
                    &&
                    (StringUtil.isEmpty(skuIsLocAndXfyl.getXfyl()) || CommonConstant.CHARACTER_ZERO.equals(skuIsLocAndXfyl.getXfyl()))) {
                return;
            } else {
                throw new BusinessException(BaseErrorCode.SELF_SAMPLING_MUST_BE_SOP_GOODS);
            }
        }

        //上门服务：
        //先判断：isLOC必须等于0，否则，点击保存时toast提示：“保存失败，上门服务商品不可为LOC商品”
        //再判断：xfyl是否为null或0或6。若不是，则保存时toast提示：“保存失败，商品标有误，xfyl≠null或0或6”
        //若xfyl=6或0或null时，则在保存时，调用商品特殊属性写接口，将sku的xfyl的值写为“6”，
        if (CheckGroupTypeEnum.ON_SITE.getType().equals(groupType)) {
            if (CommonConstant.CHARACTER_ONE.equals(skuIsLocAndXfyl.getIsLoc())) {
                throw new BusinessException(BaseErrorCode.ON_SITE_CANT_BE_LOC_GOODS);
            }
            if (CommonConstant.CHARACTER_SIX.equals(skuIsLocAndXfyl.getXfyl())
                    || CommonConstant.CHARACTER_ZERO.equals(skuIsLocAndXfyl.getXfyl())
                    || StringUtil.isEmpty(skuIsLocAndXfyl.getXfyl())) {
                System.out.println("asda");
                //skuFeatureWriteService.setPopSkuXfylFlag(skuIsLocAndXfyl.getVenderId(), Long.parseLong(checkGroupEntity.getSkuNo()));
            } else {
                throw new BusinessException(BaseErrorCode.ON_SITE_SKU_FLAG_ERROR_GOODS);
            }
        }
    }

    @Test
    public void testQueryCheckOrderInfo(){

        QueryCheckOrderInfoParam param = new QueryCheckOrderInfoParam();
        param.setOrderId("12312");
        JsfResult<CheckOrderInfo> checkOrderInfoJsfResult = thirdDataCheckExportService.queryCheckOrderInfo(param);
        System.out.println(checkOrderInfoJsfResult);
    }

    @Test
    public void appointment(){

        SkuInfo skuInfo = new SkuInfo();
        skuInfo.setSkuId(23123L);
        skuInfo.setSkuName("sku名字");

        CheckAppointmentParam param = new CheckAppointmentParam();
        param.setOrderId("1231212312");
        param.setAppointmentAddr("北京市东城区定案南路37号");
        param.setAppointmentTimeEnd("2021-02-22 18:00:00");
        param.setAppointmentTimeStart("2021-02-22 18:00:00");
        param.setJdAppointmentId(123123321312L);
        param.setChannelType(1316673423L);
        param.setCityName("海淀区");
        param.setDistrictName("海淀街道");
        param.setProvinceName("北京市");
        param.setLng(123.23);
        param.setLat(22.32);
        param.setSkuList(Lists.newArrayList(skuInfo));
        param.setUserPhone("17611261608");
        param.setUserName("测试预约人");
        JsfResult<Boolean> appointment = checkAppointmentExportService.appointment(param);
        System.out.println(appointment);
    }

    @Test
    public void cancelAppointment(){

        CheckCancelAppointmentParam param = new CheckCancelAppointmentParam();
        param.setOrderId("12312");
        param.setAppointmentNo("123123");
        param.setJdAppointmentId(3213123L);
        param.setChannelType(1316673423L);
        JsfResult<Boolean> booleanJsfResult = checkAppointmentExportService.cancelAppointment(param);
        System.out.println(booleanJsfResult);
    }
    @Test
    public void queryCheckTemplatePageList(){
        ExaminationManCheckTemplateParam param = new ExaminationManCheckTemplateParam();
        JsfResult<PageInfo<ExaminationManCheckTemplateDTO>> pageInfoJsfResult = checkTemplateExportService.queryCheckTemplatePageList(param);
        System.out.println(pageInfoJsfResult);
    }

    @Test
    public void testCheckES(){

        CheckGroupEntityEsBO checkGroupEntityEsBO = checkGroupService.queryGroupEsById(4L);


        CheckGroupEntityEsBO checkGroupEntityEsBO1 = checkGroupTemplateService.queryGroupEsById(1L);

        List<CheckGroupEntityEsBO> checkGroupEntityEsBOList = checkTemplateService.queryGroupEsById(1L);

        System.out.println("asdas");
    }

    @Test
    public void pushCheckOrderInfo() {
        ConsigneeInfoParam consigneeInfoParam = new ConsigneeInfoParam();
        consigneeInfoParam.setName("测试1");
        consigneeInfoParam.setMobile("18600001111");
        consigneeInfoParam.setDeatilAddr("测试地址");
        consigneeInfoParam.setProvince("北京");
        consigneeInfoParam.setCity("北京");
        consigneeInfoParam.setArea("大兴");
        consigneeInfoParam.setTown("亦庄开发区");
        consigneeInfoParam.setProvinceId("1001");
        consigneeInfoParam.setCityId("2001");
        consigneeInfoParam.setAreaId("3001");
        consigneeInfoParam.setTownId("4001");

        List<SkuParam> skuParamList = new ArrayList<>();
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuId("1");
        skuParam.setSkuName("1");
        skuParam.setWeight(null);
        skuParam.setCheckItem("1");
        skuParam.setServiceMode("3");
        skuParam.setNum(1);
        skuParam.setPrice(BigDecimal.TEN);
        skuParamList.add(skuParam);

        InvoiceParam invoiceParam = new InvoiceParam();
        invoiceParam.setSelectInvoiceType(3);
        invoiceParam.setInvoiceHeaderType(4);
        invoiceParam.setInvoiceTitle("测试");
        invoiceParam.setCode("1234567890");

        CheckPushOrderInfoParam param = new CheckPushOrderInfoParam();
        param.setChannelType(1316673423L);
        param.setVenderid("123456");
        param.setOrderId(1L);
        param.setOrderType("22");
        param.setConsigneeInfoParam(consigneeInfoParam);
        param.setSkuList(skuParamList);
        param.setInvoiceParam(invoiceParam);
        param.setCreationTime("2022-01-14 11:11:11");
        checkAppointmentExportService.pushCheckOrderInfo(param);
    }

    @Test
    public void report() {
//        PullCheckReportReqParam param = new PullCheckReportReqParam();
//        param.setAppointmentNo("123456");
//        param.setOrderId("123456");
//        param.setReportId("1481811807939584002");
//        checkSheetMoFeiShiRpc.getCheckReport(param);
    }

}
