package com.jd.health.util;

import com.jd.medicine.base.common.annotation.ExcelField;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.StringUtil;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Descroption
 * <AUTHOR>
 * @Date 2020/07/16 22:02
 **/
public class ExcelImportUtils {

    private static final String EXCEL2007_SUFFIX = "xlsx";
    private static final String EXCEL2003_SUFFIX = "xls";
    private Workbook wb;
    private Sheet sheet;
    private int headerNum;

    public ExcelImportUtils(InputStream inputStream, String fileType, int sheetNum, int headerNum) throws IOException, InvalidFormatException {
        if (fileType.toLowerCase().endsWith("xls")) {
            this.wb = new HSSFWorkbook(inputStream);
        } else {
            if (!fileType.toLowerCase().endsWith("xlsx")) {
                throw new RuntimeException("文件格式错误");
            }
            this.wb = new XSSFWorkbook(inputStream);
        }
//        this.wb = WorkbookFactory.create(inputStream);
        this.sheet = this.wb.getSheetAt(sheetNum);
        this.headerNum = headerNum;
    }

    public ExcelImportUtils(InputStream inputStream, String fileType) throws IOException, InvalidFormatException {
        this(inputStream, fileType,0, 0);
    }


    public <T> List<T> execute(Class<T> cls) throws Exception {
        Field[] fs = cls.getDeclaredFields();
        Map<String, String> fieldMap = new HashMap(16);
        Field[] arr$ = fs;
        int len$ = fs.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            Field f = arr$[i$];
            ExcelField ef = f.getAnnotation(ExcelField.class);
            if (null != ef) {
                fieldMap.put(ef.header(), f.getName());
            }
        }

        if (fieldMap.isEmpty()) {
            throw new RuntimeException("未找到@ExcelField注解");
        } else {
            Row headerRow = this.sheet.getRow(this.headerNum);
            Map<Integer, String> ruleMap = new HashMap(16);
            Set<String> headerNames = fieldMap.keySet();

            for(int i = 0; i < headerRow.getLastCellNum(); ++i) {
                String value = this.getCellValue(headerRow.getCell(i));
                if (headerNames.contains(value)) {
                    ruleMap.put(i, fieldMap.get(value));
                }
            }

            List<T> list = new ArrayList(500);

            for(int i = this.headerNum + 1; i < this.sheet.getPhysicalNumberOfRows(); ++i) {
                T t = cls.newInstance();
                Row row = this.sheet.getRow(i);
                Iterator i$ = ruleMap.entrySet().iterator();

                while(i$.hasNext()) {
                    Object iObj = i$.next();
                    if(iObj == null || !(iObj instanceof Map.Entry)) {
                        continue;
                    }
                    Map.Entry<Integer, String> entry = (Map.Entry)iObj;
                    int index = (Integer)entry.getKey();
                    Cell cell = row.getCell(index);

                    String value;
                    try {
                        // 把所有数字类型的字段设置为String，避免数字格式问题
                        value = this.getCellValue(cell);
                    } catch (Exception var18) {
                        throw new RuntimeException("第" + (i + 1) + "行第" + (index + 1) + "列读取数据失败", var18);
                    }

                    if (StringUtil.isNotEmpty(value)) {
                        Field field = cls.getDeclaredField(ruleMap.get(index));
                        field.setAccessible(true);
                        Class z = field.getType();
                        if (z == Integer.class) {
                            field.set(t, (int)Double.parseDouble(value));
                            field.set(t, value);
                        } else if (z == String.class) {
                            field.set(t, value);
                        } else if (z == Double.class) {
                            field.set(t, Double.parseDouble(value));
                        } else if (z == Long.class) {
                            field.set(t, (long)Double.parseDouble(value));
                            field.set(t, value);
                        } else if (z == Date.class) {
                            field.set(t, DateUtil.parseDate(value, new String[]{"yyyy-MM-dd"}));
                        } else if (z == BigDecimal.class) {
                            field.set(t, new BigDecimal(value));
                        } else {
                            field.set(t, value);
                        }
                    }
                }

                list.add(t);
            }

            return list;
        }
    }

    private String getCellValue(Cell cell) {
        String value = null;
        if (null != cell) {
            if (cell.getCellType() == 0) {
                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    if (null != date) {
                        value = DateUtil.formatDate(date, "yyyy-MM-dd");
                    }
                } else {
                    // 注意：这里会把数字转为字符串
                    cell.setCellType(CellType.STRING);
                    value = cell.getStringCellValue();
//                    value = String.valueOf(cell.getNumericCellValue());
                }
            } else if (cell.getCellType() == 1) {
                value = cell.getStringCellValue();
            } else if (cell.getCellType() == 2) {
                value = cell.getCellFormula();
            } else if (cell.getCellType() == 4) {
                value = cell.getBooleanCellValue() ? "true" : "false";
            } else if (cell.getCellType() == 5) {
                value = String.valueOf(cell.getErrorCellValue());
            }
        }

        return value;
    }
}
