package com.jd.health.dao;

import com.jd.health.medical.examination.dao.enterprise.EnterpriseAccountDao;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseInfoDao;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseOperatorDao;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseAccountEntity;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOperatorEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户信息DAO测试类
 *
 * <AUTHOR>
 * @date 2020-01-08 18:48
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class EnterpriseInfoDaoTest {
    /**
     * 大客户运营人员dao
     */
    @Autowired
    EnterpriseOperatorDao enterpriseOperatorDao;

    /**
     * 客户信息dao
     */
    @Autowired
    EnterpriseInfoDao enterpriseInfoDao;

    /**
     * 账号信息dao
     */
    @Autowired
    EnterpriseAccountDao enterpriseAccountDao;

    @Test
    public void updateUserStatusByAccountNoTest() {
        EnterpriseAccountEntity entity = new EnterpriseAccountEntity();
        entity.setAccountNo("没事找抽002");
        entity.setAccountStatus(1);
        Integer result = enterpriseAccountDao.updateUserStatusByAccountNo(entity);
        System.out.println(result);
    }

    @Test
    public void queryHrPhoneTest() {
        List<String> result = enterpriseAccountDao.queryHrPhoneByCompanyNo(22848835L);
        System.out.println(result);
    }

    /**
     * 增加客户信息
     */
    @Test
    public void addEnterpriseInfoTest() {
        List<EnterpriseOperatorEntity> list = new ArrayList<>();
        EnterpriseOperatorEntity enterpriseOperatorEntity1 = new EnterpriseOperatorEntity();
        enterpriseOperatorEntity1.setCompanyNo(1L);
        enterpriseOperatorEntity1.setOperatorPin("operator1");
        EnterpriseOperatorEntity enterpriseOperatorEntity2 = new EnterpriseOperatorEntity();
        enterpriseOperatorEntity2.setCompanyNo(1L);
        enterpriseOperatorEntity2.setOperatorPin("operator2");
        list.add(enterpriseOperatorEntity1);
        list.add(enterpriseOperatorEntity2);
        enterpriseOperatorDao.insertEnterpriseOperatorList(list);
    }

    /**
     * 查询客户编码list
     */
    @Test
    public void queryCompanyNoListTest() {
        List<Long> companyNoList = enterpriseOperatorDao.queryCompanyNoListByOperator("operator1");
    }

    /**
     * 根据编码查询客户信息list
     */
    @Test
    public void queryInfoListByOperators() {
        List<Long> companyNoList = new ArrayList<>();
        companyNoList.add(66423298L);
        List<EnterpriseInfoEntity> list = enterpriseInfoDao.queryEnterpriseInfoListByOperators(null, null, companyNoList);
    }

    @Test
    public void batchUpdateServiceStatusTest() {
//        Date startDate = EnterpriseDateUtil.getStartOfDay(new Date());
//        Integer updateResult = enterpriseInfoDao.batchUpdateServiceStatusForService(EnterpriseDateUtil.getNowDate());
    }

    @Test
    public void test() {
    }

}