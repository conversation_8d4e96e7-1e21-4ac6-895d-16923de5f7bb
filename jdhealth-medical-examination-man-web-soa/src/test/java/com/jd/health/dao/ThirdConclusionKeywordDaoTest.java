package com.jd.health.dao;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.dao.ThirdConclusionKeywordDao;
import com.jd.health.medical.examination.domain.report.entity.basic.ThirdConclusionKeyWordEntity;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/12/17 10:41 上午
 * @version: 1.0
 * @see
 */
public class ThirdConclusionKeywordDaoTest extends BaseTest {


    @Resource
    private ThirdConclusionKeywordDao thirdConclusionKeywordDao;
    @Test
    public void test1(){
        List<ThirdConclusionKeyWordEntity> list = new ArrayList<>();
        ThirdConclusionKeyWordEntity e = new ThirdConclusionKeyWordEntity();
        e.setConclusionNo(1231234L);
        e.setConclusionSuggest("asdfasdas");
        e.setThirdConclusionNo(488888L);
        e.setOperationUser("yangxiyu");
        e.setRelevanceStatus(1);
        e.setChannelNo(2111L);
        e.setSource(1);

        list.add(e);
        thirdConclusionKeywordDao.insertBatch(list);
    }
}
