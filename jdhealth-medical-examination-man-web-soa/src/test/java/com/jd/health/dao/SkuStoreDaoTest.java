package com.jd.health.dao;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.ThirdDataStatusEnum;
import com.jd.health.medical.examination.dao.SkuStoreDao;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

/**
 * @author: yang<PERSON>yu
 * @date: 2021/6/24 21:36
 * @version: 1.0
 * @see
 */
public class SkuStoreDaoTest extends BaseTest {

    @Resource
    SkuStoreDao skuStoreDao;

    @Test
    public void updateBatch(){
        Set<Long> oldIds = new HashSet<>();
        Integer count = skuStoreDao.updateStoreStatusById(oldIds, ThirdDataStatusEnum.OFF.getCode());
        Assert.notNull(count);
    }
}
