package com.jd.health.dao;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.dao.basic.XfylEventRecordDao;
import com.jd.health.medical.examination.dao.basic.pojo.XfylEventRecord;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: yang<PERSON>yu
 * @date: 2023/9/4 6:49 下午
 * @version: 1.0
 */
public class XfylEventRecordDaoTest extends BaseTest {

    @Resource
    private XfylEventRecordDao xfylEventRecordDao;

    @Test
    public void insert(){
        XfylEventRecord record = XfylEventRecord.builder()
                .eventId(76878L)
                .domainCode("aaa")
                .aggregateCode("asdsa")
                .aggregateId("21345")
                .eventCode("12343")
                .publishTime(new Date())
                .consumerIds("asdaasd")
                .score(1)
                .dev("test")
                .build();
        xfylEventRecordDao.insert(record);
    }
}
