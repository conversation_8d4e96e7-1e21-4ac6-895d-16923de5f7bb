package com.jd.health.dao;

import com.jd.health.medical.examination.dao.ExaminationManGroupDao;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseInfoDao;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuRelationDao;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuRelationEntity;
import com.jd.health.medical.examination.domain.personal.entity.ExaminationManGroupEntity;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.List;

/**
 * 亲属同购套餐Dao测试类
 *
 * <AUTHOR>
 * @date 2020-03-17 18:43
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class SkuRelationDaoTest {

    private static final Logger log = LoggerFactory.getLogger(SkuRelationDaoTest.class);

    /**
     * 亲属套餐dao
     */
    @Autowired
    private EnterpriseSkuRelationDao enterpriseSkuRelationDao;

    /**
     * 客户信息dao
     */
    @Autowired
    private EnterpriseInfoDao enterpriseInfoDao;

    /**
     * 项目组信息dao
     */
    @Autowired
    private ExaminationManGroupDao examinationManGroupDao;

    /**
     * 查询亲属同购停启用状态
     */
    @Test
    public void selectRelationStatusTest() {
        /**
         * 7096528386 停用
         * 23485789 启用
         */
        Long companyNo = 7096528386L;
        Integer result = enterpriseInfoDao.selectRelationStatus(companyNo);
        System.out.println(result);
    }

    /**
     * 修改亲属套餐停启用状态
     */
    @Test
    public void updateRelationStatusTest() {
        EnterpriseInfoEntity entity = new EnterpriseInfoEntity();
        entity.setCompanyNo(7096528386L);
        entity.setRelationStatus(1);
        Integer result = enterpriseInfoDao.updateRelationStatus(entity);
        System.out.println(result);
    }

    /**
     * 分页查询亲属套餐列表
     * 根据companyNo
     */
    @Test
    public void querySkuRelationPageTest() {
        try {
            List<EnterpriseSkuRelationEntity> result = enterpriseSkuRelationDao.querySkuRelationPage(22848835L, null, null, null);
            System.out.println(result);
        } catch (Exception e) {
            log.error("SkuRelationDaoTest -> querySkuRelationPageTest exception, e={}", e.getMessage());
        }

    }

    /**
     * 添加亲属套餐
     */
    @Test
    public void insertSkuRelationTest() {
        EnterpriseSkuRelationEntity entity = buildSkuRelationEntity();
        Integer result = enterpriseSkuRelationDao.insertSkuRelation(entity);
        System.out.println(result);
    }

    /**
     * 更新亲属套餐
     */
    @Test
    public void updateSkuRelationTest() {
        EnterpriseSkuRelationEntity entity = buildSkuRelationEntity();
        Integer result = enterpriseSkuRelationDao.updateSkuRelation(entity);
        System.out.println(result);
    }

    /**
     * 删除亲属套餐
     */
    @Test
    public void deleteSkuRelationTest() {
        EnterpriseSkuRelationEntity entity = buildSkuRelationEntity();
        Integer result = enterpriseSkuRelationDao.deleteSkuRelation(entity);
        System.out.println(result);
    }

    /**
     * 批量获取groupName
     */
    @Test
    public void selectGroupNameListTest() {
        List<Long> groupNoList = new ArrayList<>();
        groupNoList.add(1L);
        groupNoList.add(2L);
        groupNoList.add(1346929815931847170L);
        List<ExaminationManGroupEntity> list = examinationManGroupDao.selectGroupNos(groupNoList);
        log.info("SkuRelationDaoTest -> test end, list={}", JsonUtil.toJSONString(list));
    }

    /**
     * 批量获取已维护的亲属同购sku
     */
    @Test
    public void batchSelectSkuRelationTest() {
        Long companyNo = 1001L;
        List<String> skuNoList = new ArrayList<>();
        skuNoList.add("1001");
        skuNoList.add("1002");
        skuNoList.add("1111");
        skuNoList.add("2222");
        List<EnterpriseSkuRelationEntity> result = enterpriseSkuRelationDao.batchSelectSkuRelation(companyNo, skuNoList);
        log.info("SkuRelationDaoTest -> test end, result", JsonUtil.toJSONString(result));
    }

    /**
     * 构建skuRelationEntity
     * @return
     */
    private EnterpriseSkuRelationEntity buildSkuRelationEntity() {
        EnterpriseSkuRelationEntity enterpriseSkuRelationEntity = new EnterpriseSkuRelationEntity();
        enterpriseSkuRelationEntity.setCompanyNo(1001L);
        enterpriseSkuRelationEntity.setCompanyName("客户1");
        enterpriseSkuRelationEntity.setSkuNo("1003");
        enterpriseSkuRelationEntity.setSkuName("套餐2");
        enterpriseSkuRelationEntity.setGroupNo(1002L);
        // 大客户售价，可修改
        enterpriseSkuRelationEntity.setSkuCompanyPrice(1300);
        enterpriseSkuRelationEntity.setSkuPersonPrice(2300);
        // 亲属套餐名称，可修改
        enterpriseSkuRelationEntity.setRelationSkuName("新的亲属套餐2");
        // 亲属类型，可修改
        enterpriseSkuRelationEntity.setRelationType(12);
        enterpriseSkuRelationEntity.setCreateUser("hzs3");
        enterpriseSkuRelationEntity.setUpdateUser("hzs3");
        return enterpriseSkuRelationEntity;
    }

}
