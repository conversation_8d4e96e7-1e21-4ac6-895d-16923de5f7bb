package com.jd.health.dao;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.ReportApplyPhoneDTO;
import com.jd.health.medical.examination.export.param.ReportApplyPhoneParam;
import com.jd.health.medical.examination.export.param.ReportApplyPhoneSubmitParam;
import com.jd.health.medical.examination.export.param.UpdateReportStatusParam;
import com.jd.health.medical.examination.service.ReportApplyPhoneService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;


/**
 * <AUTHOR>
 * @description
 * @date 2020-12-08
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class ReportApplyPhoneServiceImplTest {

    @Autowired
    private ReportApplyPhoneService reportApplyPhoneService;

    @Test
   public void queryReportPage() {

        String a = "{\n" +
                "\t\"phone\": \"13312344321\",\n" +
                "\t\"reportSource\": \"\",\n" +
                "\t\"userName\": \"\"\n" +
                "}";


        ReportApplyPhoneParam param = JsonUtil.parseObject(a, ReportApplyPhoneParam.class);


        System.out.println(param);

        PageInfo<ReportApplyPhoneDTO> info = reportApplyPhoneService.queryReportPage(param);
        System.out.println(JsonUtil.toJSONString(info));

    }

    @Test
    public  void updateReportStatus() {

        UpdateReportStatusParam param = new UpdateReportStatusParam();
        param.setJdAppointmentId(16380311110656L);
        param.setApplyId(696100079106L);
        param.setStatus(2);
        param.setOperator("operator pin ");
        param.setPhone("134336618");
        Boolean aBoolean = reportApplyPhoneService.updateReportStatus(param);
        System.out.println(aBoolean);
    }

    @Test
    public  void submitApply() {


        ReportApplyPhoneSubmitParam submitParam = new ReportApplyPhoneSubmitParam();
        submitParam.setJdAppointmentId(16380311110656L);
        submitParam.setUserPin("386222021-331928");
        submitParam.setUserName("userpintest");
        submitParam.setCredentialNo("userpintest");
        submitParam.setPhone("13312344321");
        submitParam.setReason("13312344321133123443211331234432113312344321");
        submitParam.setReportSource(1);

        Boolean aBoolean = reportApplyPhoneService.submitApply(submitParam);
        System.out.println(aBoolean);
    }
}