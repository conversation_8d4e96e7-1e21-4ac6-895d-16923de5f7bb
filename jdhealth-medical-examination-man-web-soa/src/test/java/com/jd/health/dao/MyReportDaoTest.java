package com.jd.health.dao;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.dao.MyReportDao;
import com.jd.health.medical.examination.dao.third.ThirdStoreDao;
import com.jd.health.medical.examination.domain.MyReportEntity;
import com.jd.health.medical.examination.domain.third.ThirdStoreEntity;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/20 12:13
 */
public class MyReportDaoTest extends BaseTest {
    @Autowired
    private MyReportDao myReportDao;
    @Test
    public void insertTest(){
        MyReportEntity myReportEntity = new MyReportEntity();
        myReportEntity.setUserPin("小小宋先森");
        myReportEntity.setPatientId(1150724288402L);
        myReportEntity.setOrderId(110062918330L);
        myReportEntity.setJdAppointmentId(1391910630465735170L);
        myReportEntity.setOrderType("200");
        myReportEntity.setAppointDate(new Date());
        myReportEntity.setSkuName("测试商品，请勿下单，下单会被取消！");
        myReportEntity.setStoreName("普惠西安高新体检中心");
        myReportEntity.setUserName("京东测试");
        myReportEntity.setUserGender(2);
        myReportEntity.setUserPhone("135****5246");
        myReportEntity.setReportVerifyStatus(0);
        myReportEntity.setReportUrl("/examin.report/820496_1391910630465735170_200220.pdf");
        myReportEntity.setExpireDate(new Date());
        myReportEntity.setReportSource(1);
        myReportEntity.setUserMarriage(0);
        myReportDao.insertMyReport(myReportEntity);
    }


    @Autowired
    ThirdStoreDao thirdStoreDao;

    @Test
    public void testThirdStoreQuery(){
        Set<String> ids = new HashSet<>();
        ids.add("48");
        ids.add("49");
        Long channelNo = 5879687598L;
        List<ThirdStoreEntity> thirdStoreEntities = thirdStoreDao.selectThirdStoreByIds(ids, channelNo);
        System.out.println(JsonUtil.toJSONString(thirdStoreEntities));
    }

    @Test
    public void queryById(){
        System.out.println(myReportDao.queryReport(1391910630465735170L));
    }
}
