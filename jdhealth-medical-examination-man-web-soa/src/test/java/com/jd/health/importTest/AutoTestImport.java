package com.jd.health.importTest;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.jd.health.medical.examination.common.constant.CommonConstant;
import com.jd.health.medical.examination.common.constant.GlobalConstant;
import com.jd.health.medical.examination.common.factory.ExecutorPoolFactory;
import com.jd.health.medical.examination.dao.SkuStoreDao;
import com.jd.health.medical.examination.export.param.EmployeeBatchImportParam;
import com.jd.health.medical.examination.export.service.GroupAppointmentExportService;
import com.jd.health.medical.examination.service.common.tools.HealthcareOssService;
import com.jd.health.util.FileUtils;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * 批量导入业务测试
 * <AUTHOR>
 * @version 1.0
 * @date 2020-07-25 10:41
 * @description
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class AutoTestImport {

    /**
     *
     */
    @Autowired
    private GroupAppointmentExportService batchEmployeeImport;
    /**
     *
     */
    @Autowired
    private SkuStoreDao skuStoreDao;
    /**
     *
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;
    /**
     *
     */
    @Autowired
    private HealthcareOssService ossService;

    /**
     * 获取文件地址
     */
    @Test
    public void getHttpsUrl(){
        String path = "/examin.common/测试批量预约Test_v1.0_result_1596549898425.xlsx";
        System.out.println(JsonUtil.toJSONString(batchEmployeeImport.getHttpsUrl(path)));
    }

    /**
     * 测试获取文件http地址
     */
    @Test
    public void testGetHttpsUrl(){
        String jssUrl = "/examin.report/127343542551_BJ20092416424054693_201012_1847095810.pdf";
        String templateBucketName = "examin.report";
        String templateBaseUrl = "storage.jd.local";
        String ossHttpUrl = ossService.getOssHttpUrl(templateBucketName,jssUrl, GlobalConstant.OSS_FILE_TIMEOUT,templateBaseUrl,Boolean.FALSE);
        System.out.println(ossHttpUrl);
    }

    /**
     *
     */
    @Test
    public void testImport(){
//        String req = "{operatorId=zhangzengke, excelStr=data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,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, type=1, fileName=批量预约和取消预约模板_v1.0., fileType=xlsx, companyNo=32683094}";
//        EmployeeBatchImportParam employeeBatchImportParam = JsonUtil.parseObject(req,EmployeeBatchImportParam.class);

//        String fileBase64Str = "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,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";
        String fileBase64Str = "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";
        EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
        importParam.setFileName("测试批量预约Test_v1.0");
        importParam.setExcelStr(fileBase64Str);
        importParam.setFileType("xlsx");//xls xlsx
        importParam.setOperatorId("wudashuai5");
        importParam.setCompanyNo(22848835L);
        importParam.setType(1);
        System.out.println(JsonUtil.toJSONString(importParam));
        JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
        System.out.println("result==>" + JsonUtil.toJSONString(result));

    }

    /**
     *
     */
    @Test
    public void testData() throws Exception{
        String appointDate = "2020/10/18";
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.yyyyMMdd);
        Date checkDate2 = DateUtil.parseDateWithPatterns(appointDate);
        System.out.println(checkDate2.toString());
        System.out.println(DateUtil.formatDate(checkDate2,"yyyy-MM-dd"));
        Date checkDate1 = sdf.parse(appointDate);
        System.out.println(checkDate1.toString());
    }
    /**
     *
     */
    @Test
    public void testLocalCache(){
        Cache<String,String> cache = CacheBuilder.newBuilder().maximumSize(2).build();
        cache.put("wds1","1Hello Guava Cache");
        cache.put("wds2","2Hello Guava Cache");
        cache.put("wds3","3Hello Guava Cache");
        System.out.println(cache.getIfPresent("wds1"));
        System.out.println(cache.getIfPresent("wds2"));
        System.out.println(cache.getIfPresent("wds3"));
        cache.invalidate("wds3");
        System.out.println(cache.getIfPresent("wds1"));
        System.out.println(cache.getIfPresent("wds2"));
        System.out.println(cache.getIfPresent("wds3"));
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV2() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\批量预约或取消预约模板_v1.0.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("testwudashuai5");
            importParam.setCompanyNo(29820232L);
            importParam.setType(1);
            System.out.println(JsonUtil.toJSONString(importParam));
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV3() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V3.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            System.out.println(JsonUtil.toJSONString(importParam));
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV4() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V4.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            for(int i = 0 ; i< 10 ; i ++ ){
                String finalFileBase64Str = fileBase64Str;
                int key = i * 100 + 100;
                CompletableFuture.runAsync(() -> {
                    EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
                    importParam.setFileName("测试批量预约Test_v1.0");
                    importParam.setExcelStr(finalFileBase64Str);
                    importParam.setFileType("xlsx");//xls xlsx
                    importParam.setOperatorId("wudashuai5");
                    importParam.setCompanyNo(123456L);
                    importParam.setCompanyNo(Long.valueOf(key));
                    importParam.setType(1);
                    System.out.println("result==> start");
                    JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
                    System.out.println("result==>" + JsonUtil.toJSONString(result));
                });
                System.out.println("result==>" + i);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV5() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V5_5001.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(128488010L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV6() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V6.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            System.out.println(JsonUtil.toJSONString(importParam));
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV7() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V7.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
//            System.out.println(fileBase64Str);
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV8() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V8.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
//            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV9() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V9.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("doc");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV10() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V10.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(3);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV11() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V11.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
//            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result11==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV12() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V12.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
//            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result12==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV13() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V13.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr("123456");
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result13==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV14() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V14.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV15() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V15.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV16() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V16.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV17() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V17.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV18() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V18.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV19() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V19.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV20() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V20.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result20==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV21() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V21.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV22() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V22.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV23() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V23.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(2);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result23==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV24() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V24.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV25() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V25.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV26() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V26.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV27() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V27.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV28() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V28.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV29() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V29.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV30() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V30.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV31() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V31.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV32() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V32.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV33() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V33.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV34() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V34.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result34==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV35() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V35.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV36() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V36.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV37() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V37.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV38() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V38.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV39() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V39.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV40() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V40.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV41() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V41.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV42() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V42.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV43() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V43.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV44() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V44.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV45() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V45.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV46() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V46.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV47() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V47.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV48() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V48.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV49() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V49.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV50() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V50.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV51() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V51.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV52() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V52.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV53() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V53.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV54() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V54.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV55() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V55.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
//    @Test
    public void importV56() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V56.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(6666L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
//    @Test
    public void importV57() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V57.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(126L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
//    @Test
    public void importV58() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V58.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(125L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
//    @Test
    public void importV59() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V59.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(124L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
//    @Test
    public void importV60() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V60.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(123L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV61() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V61.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            CountDownLatch countDownLatch = new CountDownLatch(5);
            for(int i = 1 ; i< 6 ; i ++ ){
                String finalFileBase64Str = fileBase64Str;
                int key = i * 88;
                CompletableFuture.runAsync(() -> {
                    EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
                    importParam.setFileName("测试批量预约Test_v1.0");
                    importParam.setExcelStr(finalFileBase64Str);
                    importParam.setFileType("xlsx");//xls xlsx
                    importParam.setOperatorId("wudashuai5");
//                    importParam.setCompanyNo(123456L);
                    importParam.setCompanyNo(Long.valueOf(key));
                    importParam.setType(1);
                    System.out.println("result==> start");
                    JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
                    System.out.println("result==>" + JsonUtil.toJSONString(result));
                    countDownLatch.countDown();
                });
                System.out.println("result==>" + i);
            }
            System.out.println("---await");
            countDownLatch.await();
            System.out.println("---end");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV62() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V62.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV63() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V63.xlsx";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xlsx");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入代预约或代取消测试 case
     */
    @Test
    public void importV64() {
        String fileBase64Str = "";
        String path = "D:\\wudashuai5\\Desktop\\importTestFile\\测试批量预约Test_V64.txt";
        try {
            fileBase64Str = FileUtils.encodeBase64File(path);
            System.out.println("length=" + fileBase64Str.length());
            EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
            importParam.setFileName("测试批量预约Test_v1.0");
            importParam.setExcelStr(fileBase64Str);
            importParam.setFileType("xls");//xls xlsx
            importParam.setOperatorId("wudashuai5");
            importParam.setCompanyNo(22848835L);
            importParam.setType(1);
            long startTime = System.currentTimeMillis();
            System.out.println("=======start======" + System.currentTimeMillis());
            JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
            System.out.println("=======end======cost:" + (System.currentTimeMillis() - startTime));
            System.out.println("result==>" + JsonUtil.toJSONString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
