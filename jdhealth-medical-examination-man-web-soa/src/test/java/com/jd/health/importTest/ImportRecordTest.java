package com.jd.health.importTest;

import com.jd.health.medical.examination.dao.es.datasource.EsDataSource;
import com.jd.health.medical.examination.domain.employeeImport.EmployeeImportParam;
import com.jd.health.medical.examination.domain.employeeImport.EmployeeInfo;
import com.jd.health.medical.examination.export.param.EmployeeBatchImportParam;
import com.jd.health.medical.examination.export.service.GroupAppointmentExportService;
import com.jd.health.util.ExcelImportUtils;
import com.jd.health.util.FileUtils;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import sun.misc.BASE64Decoder;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-07-21 10:47
 * @description 团检代预约/代取消测试
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class ImportRecordTest {

    private static String indexName = "examination_enterprise_sales";
    private static String typeName = "sales_employee_info";

    @Autowired
    private GroupAppointmentExportService batchEmployeeImport;

    @Autowired
    private EsDataSource esDataSource;

    @Test
    public void testEs(){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("idCard", "123"));
        boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("companyNo", "456"));
        boolQueryBuilder.must(QueryBuilders.termQuery("yn", 1));
        SearchResponse response = esDataSource.getClient().prepareSearch(indexName).setTypes(typeName).setSearchType(SearchType.QUERY_THEN_FETCH)
                .setQuery(boolQueryBuilder).setFrom(0).setSize(1).execute().actionGet();
        SearchHits searchHits = response.getHits();
        if (null != searchHits) {
            EmployeeInfo employeeInfo = searchHits(searchHits);
            System.out.println("get es result ==>" + JsonUtil.toJSONString(employeeInfo));
        }
    }

    private EmployeeInfo searchHits(SearchHits searchHits) {
        if (null != searchHits) {
            List<Map<String, Object>> mapList = new ArrayList<>();
            for (SearchHit hit : searchHits) {
                if (null != hit) {
                    mapList.add(hit.getSourceAsMap());
                }
            }
            if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 0) {
                String dataJson = JsonUtil.toJSONString(mapList);
                List<EmployeeInfo> list = JsonUtil.parseArray(dataJson, EmployeeInfo.class);
                return list.get(0);
            }
        }
        return null;
    }

    /**
     * 导入代预约测试
     */
    @Test
    public void importInfo() {
        String fileBase64Str = "";
        try {
            fileBase64Str = FileUtils.encodeBase64File("D:\\wudashuai5\\Desktop\\测试批量预约Test_v1.1.xlsx");
            System.out.println("length=" + fileBase64Str.length());
//            System.out.println(fileBase64Str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        EmployeeBatchImportParam importParam = new EmployeeBatchImportParam();
        importParam.setFileName("测试批量预约Test_v1.0");
        importParam.setExcelStr(fileBase64Str);
        importParam.setFileType("xlsx");//xls xlsx
        importParam.setOperatorId("wudashuai5");
        importParam.setCompanyNo(22848835L);
        importParam.setType(1);
        JsfResult<Boolean> result = batchEmployeeImport.batchEmployeeImport(importParam);
        System.out.println("result==>" + JsonUtil.toJSONString(result));
    }

    /**
     * 解析excel数据测试
     * @return
     * @throws Exception
     */
    @Test
    public void resolvingExcelData() throws Exception {
        String fileBase64Str = "";
        try {
            fileBase64Str = FileUtils.encodeBase64File("D:\\wudashuai5\\Desktop\\测试批量预约Test_v1.0.xlsx");
            System.out.println("length=" + fileBase64Str.length());
//            System.out.println(fileBase64Str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        byte[] bytes = new BASE64Decoder().decodeBuffer(fileBase64Str.trim());
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        List<EmployeeImportParam> list = new ExcelImportUtils(inputStream, "xlsx").execute(EmployeeImportParam.class);
        for(EmployeeImportParam o : list){
            System.out.println(JsonUtil.toJSONString(o));
        }
    }


    // 示例：长度为length=13058字节的excel文件base64串
    /*

    UEsDBBQABgAIAAAAIQBBN4LPbgEAAAQFAAATAAgCW0NvbnRlbnRfVHlwZXNdLnhtbCCiBAIooAAC
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACs
    VMluwjAQvVfqP0S+Vomhh6qqCBy6HFsk6AeYeJJYJLblGSj8fSdmUVWxCMElUWzPWybzPBit2iZZ
    QkDjbC76WU8kYAunja1y8T39SJ9FgqSsVo2zkIs1oBgN7+8G07UHTLjaYi5qIv8iJRY1tAoz58Hy
    TulCq4g/QyW9KuaqAvnY6z3JwlkCSyl1GGI4eINSLRpK3le8vFEyM1Ykr5tzHVUulPeNKRSxULm0
    +h9J6srSFKBdsWgZOkMfQGmsAahtMh8MM4YJELExFPIgZ4AGLyPdusq4MgrD2nh8YOtHGLqd4662
    dV/8O4LRkIxVoE/Vsne5auSPC/OZc/PsNMilrYktylpl7E73Cf54GGV89W8spPMXgc/oIJ4xkPF5
    vYQIc4YQad0A3rrtEfQcc60C6Anx9FY3F/AX+5QOjtQ4OI+c2gCXd2EXka469QwEgQzsQ3Jo2PaM
    HPmr2w7dnaJBH+CW8Q4b/gIAAP//AwBQSwMEFAAGAAgAAAAhALVVMCP0AAAATAIAAAsACAJfcmVs
    cy8ucmVscyCiBAIooAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAACskk1PwzAMhu9I/IfI99XdkBBCS3dBSLshVH6ASdwPtY2jJBvdvyccEFQa
    gwNHf71+/Mrb3TyN6sgh9uI0rIsSFDsjtnethpf6cXUHKiZylkZxrOHEEXbV9dX2mUdKeSh2vY8q
    q7iooUvJ3yNG0/FEsRDPLlcaCROlHIYWPZmBWsZNWd5i+K4B1UJT7a2GsLc3oOqTz5t/15am6Q0/
    iDlM7NKZFchzYmfZrnzIbCH1+RpVU2g5abBinnI6InlfZGzA80SbvxP9fC1OnMhSIjQS+DLPR8cl
    oPV/WrQ08cudecQ3CcOryPDJgosfqN4BAAD//wMAUEsDBBQABgAIAAAAIQCBPpSX8wAAALoCAAAa
    AAgBeGwvX3JlbHMvd29ya2Jvb2sueG1sLnJlbHMgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAACsUk1LxDAQvQv+hzB3m3YVEdl0LyLsVesPCMm0KdsmITN+9N8bKrpdWNZLLwNvhnnv
    zcd29zUO4gMT9cErqIoSBHoTbO87BW/N880DCGLtrR6CRwUTEuzq66vtCw6acxO5PpLILJ4UOOb4
    KCUZh6OmIkT0udKGNGrOMHUyanPQHcpNWd7LtOSA+oRT7K2CtLe3IJopZuX/uUPb9gafgnkf0fMZ
    CUk8DXkA0ejUISv4wUX2CPK8/GZNec5rwaP6DOUcq0seqjU9fIZ0IIfIRx9/KZJz5aKZu1Xv4XRC
    +8opv9vyLMv072bkycfV3wAAAP//AwBQSwMEFAAGAAgAAAAhAClaTy7hAQAAiAMAAA8AAAB4bC93
    b3JrYm9vay54bWysk02P2jAQhu+V+h8s34OTELKACKtSqIpUVauW7p6NMyEW/ohsZwFV/e+dJEq7
    1V720JPtGfuZ9/XYq/urVuQZnJfWFDSZxJSAEbaU5lTQH4dP0ZwSH7gpubIGCnoDT+/X79+tLtad
    j9aeCQKML2gdQrNkzIsaNPcT24DBTGWd5gGX7sR844CXvgYIWrE0jnOmuTR0ICzdWxi2qqSArRWt
    BhMGiAPFA8r3tWz8SNPiLTjN3bltImF1g4ijVDLceiglWiz3J2MdPyq0fU1mIxmnr9BaCme9rcIE
    UWwQ+cpvErMkGSyvV5VU8DhcO+FN85XrroqiRHEfdqUMUBY0x6W9wD8B1zabVirMJlmWxpSt/7Ti
    wRHEBnAPTj5zccMtlJRQ8VaFA7ZlLIjxPIuTpDvbtfBRwsX/xXRLcn2SprSXguKDuL2YX/rwkyxD
    XdA0TXPMD7HPIE91QHaaZ/MOzV6w+65jjX4kpnf7vXsJqLCP7TtDlLilxInbl704Nh4TXAl01w39
    xjxdJNOuBlzDFx/6kbROFvRnksUf7uJFFsW76SzK5os0mmfTNPqYbdPd7G633W1mv/5vL/FFLMfv
    0KmsuQsHx8UZP9E3qDbcY28HQ6gXL2ZUzcZT698AAAD//wMAUEsDBBQABgAIAAAAIQDZYtq0awEA
    AGQDAAAUAAAAeGwvc2hhcmVkU3RyaW5ncy54bWyk001LwzAYB/C74Hcoubs0LWxT2u4wEGQXQQde
    y5qthTWpTSbupqDiC6IydB7GxJeK4GF4cbCXb7N022lfwThFBG/2mIT8nv+TFyO361eVHRwyjxIT
    oJQKFExK1PFIxQTFzdWlLFAYt4ljVynBJqhjBnLW4oLBGFfkXsJM4HIerEDISi72bZaiASZypUxD
    3+ZyGFYgC0JsO8zFmPtVqKlqGvq2R4BSojXCZV1Ztka87RrO/0xYBvMsg1vi6lZ0o0lnf9R/H7/1
    RfvMgNwyAlfm4V5pPVTKlPA1xwQaUHg9kCEJzVPy3RSAlgE/pb+auOgmoUbDRvy4J6Lm9OlyPLiR
    2mxwvFEozgYniRLOwWnzRfSanpNEmj4cjHvPcTOKW3eJEnUG4jAa9xtxu5XEiVvn4vReXB99nVYS
    atJ7HfWH8lHMkV/3qyNV09JoOZtBCGVQFm0lKVMoaDrK6kjX/qdA+UusDwAAAP//AwBQSwMEFAAG
    AAgAAAAhADttMkvBAAAAQgEAACMAAAB4bC93b3Jrc2hlZXRzL19yZWxzL3NoZWV0MS54bWwucmVs
    c4SPwYrCMBRF9wP+Q3h7k9aFDENTNyK4VecDYvraBtuXkPcU/XuzHGXA5eVwz+U2m/s8qRtmDpEs
    1LoCheRjF2iw8HvaLb9BsTjq3BQJLTyQYdMuvpoDTk5KiceQWBULsYVRJP0Yw37E2bGOCamQPubZ
    SYl5MMn5ixvQrKpqbfJfB7QvTrXvLOR9V4M6PVJZ/uyOfR88bqO/zkjyz4RJOZBgPqJIOchF7fKA
    YkHrd/aea30OBKZtzMvz9gkAAP//AwBQSwMEFAAGAAgAAAAhAMuqJoekBgAAkxoAABMAAAB4bC90
    aGVtZS90aGVtZTEueG1s7Flbixs3FH4v9D8M8+74NuPLEm+wx3bSZjcJWSclj7Ite5TVjMxI3o0J
    gZA8lUKhkJa8FEpf+lBKAw00tA/9L92SkKY/okeasUday9kk3ZS0ZHdZZjSfjj6dc/TpdvbcrYg6
    BzjhhMUtt3ym5Do4HrExiact99qgX2i4DhcoHiPKYtxyF5i757Y//OAs2hIhjrAD9WO+hVpuKMRs
    q1jkIyhG/Ayb4Ri+TVgSIQGvybQ4TtAh2I1osVIq1YoRIrHrxCgCs5cnEzLCzh9Pfn3+7cPf734G
    f+72so0ehYZiwWXBiCZ7sgVsVFTY8X5ZIviCBzRxDhBtudDcmB0O8C3hOhRxAR9abkn9uMXts0W0
    lVWiYkNdrV5f/WT1sgrj/YpqM5kOV416nu/V2iv7CkDFOq5X79V6tZU9BUCjEfQ05aLb9DvNTtfP
    sBoofbTY7ta71bKB1+xX1zi3fflr4BUote+t4fv9ALxo4BUoxfsWn9QrgWfgFSjF19bw9VK769UN
    vAKFlMT7a+iSX6sGy96uIBNGL1jhTd/r1yuZ8RwF2bDKLtnEhMViU65F6CZL+gCQQIoEiR2xmOEJ
    GkEyB4iSYUKcHTINIfFmKGYcikuVUr9Uhf/y11NPyiNoCyOttuQFTPhakeTj8FFCZqLlfgxWXQ3y
    9MmTo3uPj+79fHT//tG9H7O2lSmj3gUUT/V6L7774q+v7zp//vTNiwdfpk0fx3Md/+yHT5/98tvL
    zEOPc1c8/erRs8ePnj78/Pn3DyzW2wka6vABiTB3LuFD5yqLoIMW/niYvF6NQYiIUQOFYNtiuidC
    A3hpgagN18GmC68noDI24Pn5TYPrXpjMBbG0fDGMDOAuY7TDEqsDLsq2NA8P5vHU3ngy13FXETqw
    tR2g2Ahwbz4DeSU2k0GIDZpXKIoFmuIYC0d+Y/sYW3p3gxDDr7tklDDOJsK5QZwOIlaXDMjQSKS8
    0gUSQVwWNoIQasM3u9edDqO2XnfxgYmEYYGohfwAU8ON59FcoMhmcoAiqjt8B4nQRnJvkYx0XI8L
    iPQUU+b0xphzW53LCfRXC/pFUBh72HfpIjKRiSD7Nps7iDEd2WX7QYiimZUziUMd+xHfhxRFzhUm
    bPBdZo4Q+Q5xQPHGcF8n2Aj3yUJwDcRVp5QniPwyTyyxPI+ZOR4XdIKwUhnQfkPSIxKfqO/HlN3/
    d5TdrtGnoOl2w/9EzdsJsY6pC8c0fBPuP6jcXTSPr2AYLOsz13vhfi/c7v9euDeN5dOX61yhQbzz
    tbpauUcbF+4TQumeWFC8w9XancO8NO5DodpUqJ3laiM3C+Ex2yYYuGmCVB0nYeITIsK9EM1ggV9W
    29Apz0xPuTNjHNb9qljti/Ex22r3MI922Tjdr5bLcm+aigdHIi8v+aty2GuIFF2r53uwlXm1q52q
    vfKSgKz7OiS0xkwSVQuJ+rIQovAyEqpnp8KiaWHRkOaXoVpGceUKoLaKCiycHFhutVzfS88BYEuF
    KB7LOKVHAsvoyuCcaqQ3OZPqGQCriGUG5JFuSq4buyd7l6baK0TaIKGlm0lCS8MQjXGWnfrByWnG
    upmH1KAnXbEcDTmNeuNtxFqKyDFtoLGuFDR2DlturerDEdkIzVruBPb98BjNIHe4XPAiOoUztJFI
    0gH/JsoyS7joIh6mDleik6pBRAROHEqiliu7v8oGGisNUdzKFRCEd5ZcE2TlXSMHQTeDjCcTPBJ6
    2LUS6en0FRQ+1QrrV1X9zcGyJptDuPfC8aEzpPPkKoIU8+tl6cAx4XD8U069OSZwnrkSsjz/jk1M
    mezqB4oqh9JyRGchymYUXcxTuBLRFR31tvKB9pb1GRy67sLhVE6w/3jWPXmqlp7TRDOfMw1VkbOm
    XUzf3iSvsconUYNVKt1q28BzrWsutQ4S1TpLnDDrvsKEoFHLGzOoScbrMiw1Oys1qZ3igkDzRG2D
    31ZzhNUTbzrzQ73jWSsniOW6UiW+uv/Q7ybY8CaIRxdOgedUcBVKuHlIECz60nPkVDZgiNwS2RoR
    npx5Qlru7ZLf9oKKHxRKDb9X8KpeqdDw29VC2/er5Z5fLnU7lTswsYgwKvvp3UsfDqLoIruBUeVr
    tzDR8qztzIhFRaZuWYqKuLqFKVdstzADeb/iOgRE53at0m9Wm51aoVlt9wtet9MoNINap9CtBfVu
    vxv4jWb/juscKLDXrgZerdco1MpBUPBqJUm/0SzUvUql7dXbjZ7XvpMtY6DnqXxkvgD3Kl7bfwMA
    AP//AwBQSwMEFAAGAAgAAAAhAISKZbrLAwAAMwsAAA0AAAB4bC9zdHlsZXMueG1svFZLj+NEEL4j
    8R8sn9iDx4/EmSTYXm0ellZa0IoZJCQWoY7dTlrbj6jdmU1A3LjwQxAn7iskxK9By8+gutuOPZod
    JpORuNjd5eqvqr56uJPne0adGyxrInjqhheB62BeiJLwdep+fZ17Y9epFeIlooLj1D3g2n2effpJ
    UqsDxVcbjJUDELxO3Y1S26nv18UGM1RfiC3m8KUSkiEFW7n2663EqKz1IUb9KAhGPkOEuxZhyopT
    QBiSb3dbrxBsixRZEUrUwWC5DiumL9dcSLSi4Oo+HKLC2YcjGbUWjOiOEUYKKWpRqQsA9UVVkQLf
    9XXiT3xUdEgAex5SGPtBZAPPEr5jOVO1U4gdV5AAtxU59svLEoSXkATL41yUEFnw/bPPv/0Kl9+9
    +Sx488z1s8RvcLKkEryDG4K7moXpWy7e8Vx/sja0VpbUPzg3iIIk1BiFoEI6CrIHJoyEI4atxoff
    f/nwx19aq0KM0IOVRlpg8t2oMQLsG3+sgfvNyPUqdfMcSiAMAn3iJFvFBskaKs56PRg+zv7kFDsD
    Q8VZdlaajY9Q+uhYz/LBN5mHBBFKj/U0gHrSgiyBZlFY8hw2TrO+Pmwh0xz62qbM6D2gvZboEEbx
    6QdqQUmpvVjPTX01ZMxHy3y+NDA9z6CQrX3zqrNkJWQJw6nfHlaUJRRXCgiXZL3RbyW28FwJpaCH
    s6QkaC04oro32hPNAmALTOmVHmDfVEfsCJzcV722g1GoKdUdqJfATrO0eHYD+PcdCrtDMID6hxy0
    3dLDlzu2wjI389GYMNKZibjbv6BkzRnWwwG8MCqvpVC4UGZiB+A1alX0IFek0C1dwAlsW3FfAQf9
    iG38vdAvzwrd2VcPcjC4nwN7GkgyMenZpKeO3fWCNoPoFsXDSZeYk/Fvk/1Eq/eXw5GTj6X4bijN
    ZH+oyB6LCmO/rdz/j6CwZ/UpBJlSheLsdemtHj1WsqN/Gan79/v3//z2M/zommp0VjtCFeHH2rxz
    4Nc/W22og552NDLz6KgPLpT7bkCY35TSdwszOo5OQbAlrtCOquvjx9Tt1l/gkuwYjIBG6zW5EcpA
    pG63fqXnWGg8wHv1qobfM7ydnSSp++NydjlZLPPIGwezsTcc4NibxLOFFw/ns8UinwRRMP+pd8l5
    whXHXMhgqIXDaU3hIiSbYBvnrzpZ6vY21n3DH7jd930SjYIXcRh4+SAIveEIjb3xaBB7eRxGi9Fw
    tozzuOd7fOalKvDDsL1U7cN4qgjDlPA2V22G+lJIEmz/Iwi/zYTf3XazfwEAAP//AwBQSwMEFAAG
    AAgAAAAhAB50E0OJAwAACggAABgAAAB4bC93b3Jrc2hlZXRzL3NoZWV0MS54bWyUVT2P2zYY3gv0
    PwicG31YsmUbsoPr+ZzeEKDoJelMS5RFnCSqJH0+d+vQpUvRqWOXAh0KNGOAS/pv0jaZ+hf6ULIV
    ywoO10WkyIfPy/d5Pxg9vi1y64ZJxUU5I57tEouVsUh4uZ6R58+Wj8bEUpqWCc1FyWZkxxR5PP/0
    k2gr5LXKGNMWGEo1I5nW1dRxVJyxgipbVKzETipkQTV+5dpRlWQ0qQ8VuTNw3ZFTUF6ShmEqH8Ih
    0pTHbCHiTcFK3ZBIllON+6uMV+rAVsQPoSuovN5Uj2JRVKBY8ZzrXU1KrCKeXq5LIekqh9+3XkDj
    A3f906MveCyFEqm2Qec0F+37PHEmDpjmUcLhgZHdkiydkTNv+mRAnHlU6/OCs606mluarq5YzmLN
    EoSJWEb+lRDXBniJJReMqgYYRhprfsPOWZ7PyDJEBL+pbWAKA05r4Xh+sLasA/altBKW0k2uvxLb
    LxhfZxpmfRvZUDs/TXYLpmKEAKbtwdDwxiIHCb5WwU0uQUJ621yWJzrDbGSHYegHfjg0SbUzuvrE
    ijdKi+LrPWbP1HAM9hwYt83+wLWHw2A07lKsmNJLbq54Lx2M1VfCeKAL7IF3TBbcS4DdmgDjnsAL
    7fF4FP4PDvhec2BsOR4ox2h/FGP/KBbvURJJUFvF+EHJrusfDYXTRLVOmQXVdB5JsbVQqlBaVdQU
    vjcF6cezAulgsGcGPCOIIkKkkKk3czdybpB+8R7xeR/hdRHnDQLflmPQRSz6iFEXcdG34ncRywaB
    2LRWgi7iSZ9j2CIciNMqBHcfrpAB19XQ2g1PFOojxicKNYig1jfwJ7479r2Jf6Lj4hjlA+CeyHjR
    NzQ5EalBjBpD7mT8gaERoOkwTbpUGd4NzWN0lFSU2vQqkwe7CsVfinNR7h8fU/cJEuwFzTlG086t
    WGxMg/FMt+xs7c/nXGnolontZVlt9FOmFF2D14QIixdSCnm8yMzCM65N43n/+qe3d3fvf/7jrx9f
    /fvml7ZHog+feW4wHobGP/N8bXLqzcm7u9/fvv7z3cvvPvv7h1//+f43EjntZuR0r4fEPnFlHlW4
    2VMq1xx+5Syt+ybqRjat1bUx16Iy3dS0tpXQ6ImHvwzPJkMduTZqNBVCH36gmeG9YnpTWRWtmLzi
    38K5CbGE5OjOtZAzUgmpJeUQS045IiAvE69+CtpXfP4fAAAA//8DAFBLAwQUAAYACAAAACEA6mYz
    U0QBAABRAgAAEQAIAWRvY1Byb3BzL2NvcmUueG1sIKIEASigAAEAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAfJJRS8MwFIXfBf9DyXubZN1mDW0HKntyIFhRfAvJ3VZs0pJEu/1703arHQwf7z0n
    3z33knR1UFXwA8aWtc4QjQgKQItalnqXobdiHSYosI5ryataQ4aOYNEqv71JRcNEbeDF1A0YV4IN
    PElbJpoM7Z1rGMZW7EFxG3mH9uK2Noo7X5odbrj44jvAM0KWWIHjkjuOO2DYjER0QkoxIptvU/UA
    KTBUoEA7i2lE8Z/XgVH26oNemThV6Y6N3+kUd8qWYhBH98GWo7Ft26iN+xg+P8Ufm+fXftWw1N2t
    BKA8lYIJA9zVJk/xtPCHq7h1G3/jbQny4ej1Kz0p+rgDBGTgA7Ah7ll5jx+fijXKZ4QuQrIMyaKg
    CaP3LJ5/diMv3neBhoY6Df6XOCMhuQvpoiAJixM2pxPiGTDkvvwE+S8AAAD//wMAUEsDBBQABgAI
    AAAAIQBjgObtQwAAANwAAAAnAAAAeGwvcHJpbnRlclNldHRpbmdzL3ByaW50ZXJTZXR0aW5nczEu
    YmluYmCgDDCyMLPdARrh/L+BnZGBk2EWtwlHCgMjAztDBBMTAxOYZGRwZDCh0B5k7YxQDohmArGB
    jP9AgG4FAAAA//8DAFBLAwQUAAYACAAAACEAOl30EZoBAAAQAwAAEAAIAWRvY1Byb3BzL2FwcC54
    bWwgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
    AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACcksFu2zAMhu8F+g6G7o2cbiiG
    QFZRpCt62LAASXpXZToWKkuCxBrJnmWXHQbsDXba23TAHqO0jaZO21NvJH/i10dS4nzb2KyFmIx3
    BZtOcpaB0740blOw9erq5BPLEipXKusdFGwHiZ3L4yOxiD5ARAMpIwuXClYjhhnnSdfQqDQh2ZFS
    +dgopDRuuK8qo+HS6/sGHPLTPD/jsEVwJZQnYW/IBsdZi+81Lb3u+NLNahcIWIqLEKzRCmlK+dXo
    6JOvMPu81WAFH4uC6Jag76PBncwFH6diqZWFORnLStkEgj8XxDWobmkLZWKSosVZCxp9zJL5Tms7
    ZdmtStDhFKxV0SiHhNW1DUkf25Awyn9/fj38/fH/52/BSR9qfThuHcfmo5z2DRQcNnYGAwcJh4Qr
    gxbSt2qhIr4BPB0D9wwD7oCzrAFweHPM109ML73wnvsmKLcjYR99Me4urcPKXyqEp20eFsWyVhFK
    OsB+2/uCuKZFRtuZzGvlNlA+9bwWutvfDB9cTs8m+YeczjqqCf78leUjAAAA//8DAFBLAQItABQA
    BgAIAAAAIQBBN4LPbgEAAAQFAAATAAAAAAAAAAAAAAAAAAAAAABbQ29udGVudF9UeXBlc10ueG1s
    UEsBAi0AFAAGAAgAAAAhALVVMCP0AAAATAIAAAsAAAAAAAAAAAAAAAAApwMAAF9yZWxzLy5yZWxz
    UEsBAi0AFAAGAAgAAAAhAIE+lJfzAAAAugIAABoAAAAAAAAAAAAAAAAAzAYAAHhsL19yZWxzL3dv
    cmtib29rLnhtbC5yZWxzUEsBAi0AFAAGAAgAAAAhAClaTy7hAQAAiAMAAA8AAAAAAAAAAAAAAAAA
    /wgAAHhsL3dvcmtib29rLnhtbFBLAQItABQABgAIAAAAIQDZYtq0awEAAGQDAAAUAAAAAAAAAAAA
    AAAAAA0LAAB4bC9zaGFyZWRTdHJpbmdzLnhtbFBLAQItABQABgAIAAAAIQA7bTJLwQAAAEIBAAAj
    AAAAAAAAAAAAAAAAAKoMAAB4bC93b3Jrc2hlZXRzL19yZWxzL3NoZWV0MS54bWwucmVsc1BLAQIt
    ABQABgAIAAAAIQDLqiaHpAYAAJMaAAATAAAAAAAAAAAAAAAAAKwNAAB4bC90aGVtZS90aGVtZTEu
    eG1sUEsBAi0AFAAGAAgAAAAhAISKZbrLAwAAMwsAAA0AAAAAAAAAAAAAAAAAgRQAAHhsL3N0eWxl
    cy54bWxQSwECLQAUAAYACAAAACEAHnQTQ4kDAAAKCAAAGAAAAAAAAAAAAAAAAAB3GAAAeGwvd29y
    a3NoZWV0cy9zaGVldDEueG1sUEsBAi0AFAAGAAgAAAAhAOpmM1NEAQAAUQIAABEAAAAAAAAAAAAA
    AAAANhwAAGRvY1Byb3BzL2NvcmUueG1sUEsBAi0AFAAGAAgAAAAhAGOA5u1DAAAA3AAAACcAAAAA
    AAAAAAAAAAAAsR4AAHhsL3ByaW50ZXJTZXR0aW5ncy9wcmludGVyU2V0dGluZ3MxLmJpblBLAQIt
    ABQABgAIAAAAIQA6XfQRmgEAABADAAAQAAAAAAAAAAAAAAAAADkfAABkb2NQcm9wcy9hcHAueG1s
    UEsFBgAAAAAMAAwAJgMAAAkiAAAAAA==

     */

}