//package com.jd.health.importTest;
//
//import com.jd.health.medical.examination.domain.employeeImport.EmployeeInfo;
//import com.jd.medicine.base.common.util.JsonUtil;
//import org.apache.commons.collections.CollectionUtils;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.action.search.SearchType;
//import org.elasticsearch.client.transport.TransportClient;
//import org.elasticsearch.common.settings.Settings;
//import org.elasticsearch.common.transport.TransportAddress;
//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.SearchHits;
//import org.elasticsearch.transport.client.PreBuiltTransportClient;
//
//import java.net.InetAddress;
//import java.net.UnknownHostException;
//import java.nio.ByteBuffer;
//import java.nio.CharBuffer;
//import java.nio.charset.StandardCharsets;
//import java.util.*;
//
//public class EsTest {
//
//    private static String indexName = "examination_enterprise_sales";
//    private static String typeName = "sales_employee_info";
//
//    private static final String ClusterName = "jiesi-6.3";// jiesi-5.4
//    private static final String GatewayIpPorts = "**************:20100";
//    private static final String SecurityKey = "request.headers.Authorization";//固定值
//    private static final String SecurityUser="jiesi-6.3";
//    private static final String SecurityPassword="A5F4B854D85E052C9BA3C3EC051BFDFBF7952167";
//    private static TransportClient client;
//
//    public static TransportClient client() throws UnknownHostException {
//        if (client != null) {
//            return client;
//        }
//        synchronized (EsTest.class) {
//            if (client == null) {
//                Settings settings = Settings.builder().put("cluster.name", ClusterName)
//                        .put("client.transport.sniff", false)
//                        .put(SecurityKey, basicAuthHeaderValue(SecurityUser, SecurityPassword))
//                        .build();
//
//                client = new PreBuiltTransportClient(settings);
//
//                String[] oneInstance = GatewayIpPorts.split(",");
//                for (String item : oneInstance) {
//                    String[] ipPort = item.split(":");
//                    client.addTransportAddresses(new TransportAddress(InetAddress.getByName(ipPort[0]), Integer.parseInt(ipPort[1])));
//                }
//                return client;
//            }
//            return client;
//        }
//    }
//
//            /**
//      * 基础的base64生成
//      * @param username 用户名
//      * @param passwd 密码
//      * @return
//      */
//    private static String basicAuthHeaderValue(String username, String passwd) {
//        CharBuffer chars = CharBuffer.allocate(username.length() + passwd.length() + 1);
//        byte[] charBytes = null;
//        try {
//            chars.put(username).put(':').put(passwd.toCharArray());
//            charBytes = toUtf8Bytes(chars.array());
//
//            String basicToken = Base64.getEncoder().encodeToString(charBytes);
//            return "Basic " + basicToken;
//        } finally {
//            Arrays.fill(chars.array(), (char) 0);
//            if (charBytes != null) {
//                Arrays.fill(charBytes, (byte) 0);
//            }
//        }
//    }
//
//    public static byte[] toUtf8Bytes(char[] chars) {
//        CharBuffer charBuffer = CharBuffer.wrap(chars);
//        ByteBuffer byteBuffer = StandardCharsets.UTF_8.encode(charBuffer);
//        byte[] bytes = Arrays.copyOfRange(byteBuffer.array(), byteBuffer.position(), byteBuffer.limit());
//        Arrays.fill(byteBuffer.array(), (byte) 0);
//        return bytes;
//    }
//
//    public static void main(String[] args) {
//        try {
//            System.out.println(EsTest.client().listedNodes().size());
//
//            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("idCard", "110101199003079542"));
////            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("companyNo", "123"));
//            boolQueryBuilder.must(QueryBuilders.termQuery("yn", 1));
//
//            SearchResponse response = EsTest.client().prepareSearch(indexName).setTypes(typeName).setSearchType(SearchType.QUERY_THEN_FETCH)
//                    .setQuery(boolQueryBuilder).setFrom(0).setSize(1).execute().actionGet();
//            SearchHits searchHits = response.getHits();
//            if (null != searchHits) {
//                EmployeeInfo employeeInfoDTO = searchHits(searchHits);
//                System.out.println(JsonUtil.toJSONString("result:===>" + employeeInfoDTO));
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private static EmployeeInfo searchHits(SearchHits searchHits) {
//        if (null != searchHits) {
//            List<Map<String, Object>> mapList = new ArrayList<>();
//            for (SearchHit hit : searchHits) {
//                if (null != hit) {
//                    mapList.add(hit.getSourceAsMap());
//                }
//            }
//            if (CollectionUtils.isNotEmpty(mapList) && mapList.size() > 0) {
//                String dataJson = JsonUtil.toJSONString(mapList);
//                List<EmployeeInfo> list = JsonUtil.parseArray(dataJson, EmployeeInfo.class);
//                return list.get(0);
//            }
//        }
//        return null;
//    }
//
//}
