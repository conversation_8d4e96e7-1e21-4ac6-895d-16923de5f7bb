package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentBo;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentReportBo;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentStoreCapBo;
import com.jd.health.medical.examination.rpc.domain.dto.ThirdOrderSimpleDto;
import com.jd.health.medical.examination.rpc.domain.dto.ThirdReportDto;
import com.jd.health.medical.examination.rpc.provider.impl.AppointBeiJingGzRpc;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/8/11 11:15 上午
 * @version: 1.0
 * @see
 */
public class AppointGzRpcTest extends BaseTest {

    @Resource
    private AppointBeiJingGzRpc appointBeiJingGzRpc;

    private static final long channelNo = 1584739620L;
    @Test
    public void getStoreDate(){
        AppointmentStoreCapBo capBo = new AppointmentStoreCapBo();
        capBo.setStoreId("0302001");
        capBo.setGoodsId("03011711090023");
        capBo.setChannelType(channelNo);

        List<AppointDateDTO> res = appointBeiJingGzRpc.refreshAppointDate(capBo);
        System.out.println("---------------------------");
        System.out.println(JsonUtil.toJSONString(res));
    }


    @Test
    public void apoint(){
        AppointmentBo bo = new AppointmentBo();

        Long jdAppointmentId = Long.valueOf(channelNo + "0");
        bo.setJdAppointmentId(jdAppointmentId);
        bo.setGoodsId("03011711090023");
        bo.setStoreId("0302001");
        bo.setAppointmentDate("2021-08-31");
        bo.setUserName("yang");
        bo.setUserPhone("15910531120");
        bo.setUserMarriage(2);
        bo.setUserGender(1);
        bo.setUserBirth("1993-5-20");
        bo.setCredentialType(1);
        bo.setCredentialNo("110101199303078637");
        bo.setChannelType(channelNo);
        Boolean entity = appointBeiJingGzRpc.appoint(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }

    @Test
    public void update(){
        AppointmentBo bo = new AppointmentBo();
        Long jdAppointmentId = Long.valueOf(channelNo + "0");
        bo.setJdAppointmentId(jdAppointmentId);
        bo.setStoreId("0302001");
        bo.setAppointmentDate("2021-09-13");
        bo.setAppointmentNo("0512108170002");
        bo.setChannelType(channelNo);

        Boolean entity = appointBeiJingGzRpc.update(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }


    @Test
    public void cancel(){
        AppointmentBo bo = new AppointmentBo();
        Long jdAppointmentId = Long.valueOf(channelNo + "0");
        bo.setJdAppointmentId(jdAppointmentId);
        bo.setAppointmentNo("0512108170002");
        bo.setChannelType(channelNo);

        Boolean entity = appointBeiJingGzRpc.cancel(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }

    @Test
    public void getStatus(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(4444444444L);
        bo.setAppointmentNo("1425715598608179200");
        bo.setChannelType(channelNo);
        ThirdOrderSimpleDto dto = appointBeiJingGzRpc.queryStatus(bo);
        System.out.println(JsonUtil.toJSONString(dto));
    }

    @Test
    public void download(){
        AppointmentReportBo bo = new AppointmentReportBo();
        bo.setJdAppointmentId(1795070740865749506L);
        bo.setAppointmentNo("0512108170003");
        bo.setReportId("0512108170003");
        bo.setChannelType(channelNo);
        ThirdReportDto dto = appointBeiJingGzRpc.doGetReport(bo);
        System.out.println(JsonUtil.toJSONString(dto));
    }

}
