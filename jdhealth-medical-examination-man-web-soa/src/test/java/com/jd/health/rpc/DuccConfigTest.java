package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.config.CommonDuccConfig;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Set;

/**
 * @author: yang<PERSON>yu
 * @date: 2021/9/6 2:10 下午
 * @version: 1.0
 * @see
 */
public class DuccConfigTest extends BaseTest {

    /**
     * ducc
     */
    @Resource
    private CommonDuccConfig commonDuccConfig;

    /**
     *
     */
    @Test
    public void get(){
        Set<Long> set =  commonDuccConfig.getNoAuthorizationReportAppointmentIds();
        System.out.println(JsonUtil.toJSONString(set));
    }
}
