package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentBo;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentReportBo;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentStoreCapBo;
import com.jd.health.medical.examination.rpc.domain.dto.ThirdOrderSimpleDto;
import com.jd.health.medical.examination.rpc.domain.dto.ThirdReportDto;
import com.jd.health.medical.examination.rpc.provider.impl.AppointSjxRpc;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/8/11 11:15 上午
 * @version: 1.0
 * @see
 */
public class AppointSjxRpcTest extends BaseTest {

    @Resource
    private AppointSjxRpc appointSjxRpc;


    @Test
    public void getStoreDate(){
        AppointmentStoreCapBo capBo = new AppointmentStoreCapBo();
        capBo.setStoreId("2682eabeb0e641acba07764a507c13f6");
        capBo.setGoodsId("78c654a2d88e404cba8c02f5c26ee1c4");
        capBo.setChannelType(4746231558L);

        List<AppointDateDTO> res = appointSjxRpc.refreshAppointDate(capBo);
        System.out.println("---------------------------");
        System.out.println(JsonUtil.toJSONString(res));
    }


    @Test
    public void apoint(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(6666666666L);
        bo.setGoodsId("dca7b0c9f0474dc1ab523751d78d37f4");
        bo.setStoreId("7dd1fade9aa543ca92c95e90d725db9f");
        bo.setAppointmentDate("2021-08-27");
        bo.setUserName("yangxiyuTwo");
        bo.setUserPhone("15910531120");
        bo.setUserMarriage(2);
        bo.setUserGender(1);
        bo.setUserBirth("1993-5-20");
        bo.setCredentialType(1);
        bo.setCredentialNo("110101199303078637");
        bo.setChannelType(4746231558L);
        Boolean entity = appointSjxRpc.appoint(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }

    @Test
    public void update(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(1793491395491752962L);
        bo.setStoreId("2682eabeb0e641acba07764a507c13f6");
        bo.setAppointmentDate("2021-08-13");
        bo.setAppointmentNo("1426048976507248640");
        bo.setChannelType(4746231558L);

        Boolean entity = appointSjxRpc.update(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }


    @Test
    public void cancel(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(1111L);
        bo.setAppointmentNo("1426018786821414912");
        bo.setChannelType(4746231558L);

        Boolean entity = appointSjxRpc.cancel(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }

    @Test
    public void getStatus(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(4444444444L);
        bo.setAppointmentNo("1425715598608179200");
        bo.setChannelType(4746231558L);
        ThirdOrderSimpleDto dto = appointSjxRpc.queryStatus(bo);
        System.out.println(JsonUtil.toJSONString(dto));
    }

    @Test
    public void download(){
        AppointmentReportBo bo = new AppointmentReportBo();
        bo.setJdAppointmentId(1793491395491752962L);
        bo.setAppointmentNo("1426048976507248640");
        bo.setChannelType(4746231558L);
        ThirdReportDto dto = appointSjxRpc.doGetReport(bo);
        System.out.println(JsonUtil.toJSONString(dto));
    }
    /**
     * 测试获取token
     */
    @Test
    public void getToken(){
        AppointSjxRpc.TokenEntity entity = appointSjxRpc.getToken();
        System.out.println(JsonUtil.toJSONString(entity));

    }



    @Test
    public void refreshToken(){
//        AppointSjxRpc.TokenEntity entity = appointSjxRpc.refreshToken();
//        System.out.println(JsonUtil.toJSONString(entity));

    }
}
