package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentBo;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentStoreCapBo;
import com.jd.health.medical.examination.rpc.provider.impl.AppointBeijingSmhRpc;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/8/20 2:08 下午
 * @version: 1.0
 * @see
 */
public class AppointBeijingSmhRpcTest extends BaseTest {

    @Resource
    AppointBeijingSmhRpc appointBeijingSmhRpc;

    private static final Long channelNo = 7201122104L;
    @Test
    public void getStoreDate(){
        AppointmentStoreCapBo capBo = new AppointmentStoreCapBo();
        capBo.setStoreId("241059690744811520");
        capBo.setGoodsId("441055185494765569");
        capBo.setChannelType(channelNo);

        List<AppointDateDTO> res = appointBeijingSmhRpc.refreshAppointDate(capBo);
        System.out.println("---------------------------");
        System.out.println(JsonUtil.toJSONString(res));
    }

    @Test
    public void apoint(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(972742463972L);
        bo.setGoodsId("441055185494765560");
        bo.setStoreId("241059690744811520");
        bo.setAppointmentDate("2021-08-30");
        bo.setUserName("yangxiyuCancel");
        bo.setUserPhone("15910531170");
        bo.setUserMarriage(2);
        bo.setUserGender(1);
        bo.setUserBirth("1994-5-20");
        bo.setCredentialType(1);
        bo.setCredentialNo("110101199303078637");
        bo.setChannelType(channelNo);
        Boolean entity = appointBeijingSmhRpc.appoint(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }


    @Test
    public void update(){
        AppointmentBo bo = new AppointmentBo();

        bo.setStoreId("241059690744811520");
        bo.setAppointmentDate("2021-09-10");

//        bo.setJdAppointmentId(1111111111L);
//        bo.setAppointmentNo("441111870720610304");
        bo.setJdAppointmentId(972742463972L);
        bo.setAppointmentNo("441448217822007296");
        bo.setChannelType(channelNo);

        Boolean entity = appointBeijingSmhRpc.update(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }



    @Test
    public void cancel(){
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(972742463972L);
        bo.setAppointmentNo("441448217822007296");
        bo.setChannelType(channelNo);

        Boolean entity = appointBeijingSmhRpc.cancel(bo);
        System.out.println(JsonUtil.toJSONString(entity));
    }
}
