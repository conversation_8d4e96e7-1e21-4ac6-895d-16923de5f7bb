package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.rpc.SkuInfoRpc;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.pap.priceinfo.sdk.domain.response.PriceResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/3/18 21:01
 * @Doc
 **/
public class SkuInfoRpcTest extends BaseTest {

    @Autowired
    SkuInfoRpc skuInfoRpc;

    /**
     * 商品信息
     */
    @Test
    public void testProduct(){
        Set<String> set = new HashSet<>();
        set.add("100000395549");
        Map<String, Map<String, String>> skuInfo = skuInfoRpc.getSkuInfo(set);
        System.out.println(JsonUtil.toJSONString(skuInfo));
    }

    /**
     * 商品价格
     */
    @Test
    public void testSkuPrice(){
        Set<String> set = new HashSet<>();
        set.add("100000395549");
        Map<String, PriceResult> skuPrice = skuInfoRpc.getSkuPrice(set);
        System.out.println(JsonUtil.toJSONString(skuPrice));
    }
}
