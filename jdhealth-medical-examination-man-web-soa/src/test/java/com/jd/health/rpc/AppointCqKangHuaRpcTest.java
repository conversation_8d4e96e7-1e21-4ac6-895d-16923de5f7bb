package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdAppointmentParam;
import com.jd.health.medical.examination.rpc.domain.dto.ThirdOrderSimpleDto;
import com.jd.health.medical.examination.rpc.factory.AppointTemplate;
import com.jd.health.medical.examination.rpc.impl.AppointCqKangHuaRpcImpl;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.util.List;

/**
 * 康华自测
 * @author: yang<PERSON>yu
 * @date: 2021/7/22 18:31
 * @version: 1.0
 * @see AppointCqKangHuaRpcImpl
 */
public class AppointCqKangHuaRpcTest extends BaseTest {

    @Resource
    private AppointTemplate appointCqKangHuaRpc;

    /**
     * 获取排期
     */
    @Test
    public void getAppointDate(){
        AppointmentStoreCapParam param = new AppointmentStoreCapParam();
        param.setStoreId("1");
        param.setGoodsId("4");
        param.setChannelNo(1214281987L);

        List<AppointDateDTO> res = appointCqKangHuaRpc.getAppointDateByStore(param);
        System.out.println("---------------------------");
        System.out.println(JsonUtil.toJSONString(res));
    }


    /**
     * 预约测试
     */
    @Test
    public void testAppoit(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(22222222L);
        appointmentParam.setGoodsId("1");
        appointmentParam.setStoreId("1");
        appointmentParam.setAppointmentDate("2021-07-27");
        appointmentParam.setUserName("yangxiyuTwo");
        appointmentParam.setUserPhone("15910531120");
        appointmentParam.setUserMarriage(2);
        appointmentParam.setUserGender(1);
        appointmentParam.setUserBirth("1993-5-20");
        appointmentParam.setCredentialType(1);
        appointmentParam.setCredentialNo("110101199303078637");

        Boolean res = appointCqKangHuaRpc.appoint(appointmentParam);
        Assert.assertEquals(res, Boolean.TRUE);
    }


    /**
     * 预约更新
     */
    @Test
    public void updateAppoit(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setAppointmentNo("20210723153904-2861");
        appointmentParam.setJdAppointmentId(111111111L);
        appointmentParam.setGoodsId("1");
        appointmentParam.setStoreId("1");
        appointmentParam.setAppointmentDate("2024-07-29");
        appointmentParam.setUserName("yangxiyuTest");
        appointmentParam.setUserPhone("15910531170");
        appointmentParam.setUserMarriage(2);
        appointmentParam.setUserGender(1);
        appointmentParam.setUserBirth("1992-5-20");
        appointmentParam.setCredentialType(1);
        appointmentParam.setCredentialNo("110101199403078637");

        Boolean res = appointCqKangHuaRpc.updateAppoint(appointmentParam);
        Assert.assertEquals(res, Boolean.TRUE);
    }


    /**
     * 查询预约单状态
     */
    @Test
    public void queryStatus(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setAppointmentNo("jd_test100002");
        appointmentParam.setJdAppointmentId(111111111L);

        ThirdOrderSimpleDto thirdOrderDTO = appointCqKangHuaRpc.queryStatus(appointmentParam);
        System.out.println(JsonUtil.toJSONString(thirdOrderDTO));
    }

    /**
     * 取消预约
     */
    @Test
    public void cancel(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setAppointmentNo("jd_test100002");
        appointmentParam.setJdAppointmentId(111111111L);

        Boolean res = appointCqKangHuaRpc.cancelAppoint(appointmentParam);
        System.out.println(JsonUtil.toJSONString(res));
    }

    /**
     * 获取报告
     */
    @Test
    public void getReport() throws Exception{
        ThirdAppointmentParam thirdAppointmentParam = new ThirdAppointmentParam();
        thirdAppointmentParam.setAppointmentNo("20210723153904-2861");
        thirdAppointmentParam.setJdAppointmentId(111111111L);


        String res = appointCqKangHuaRpc.getReport(thirdAppointmentParam);

        byte[] dataArr = new BASE64Decoder().decodeBuffer(res);

//        File report = new File("D:/yangxiyu/Desktop/新建文件夹/temp.pdf");
//        File report = new File("tempUrl");
//        FileOutputStream fos = new FileOutputStream(report);
//        fos.write(dataArr);
    }
}
