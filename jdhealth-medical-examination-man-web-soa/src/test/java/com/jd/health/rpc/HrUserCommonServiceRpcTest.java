package com.jd.health.rpc;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.bo.sku.SkuApplyResultBO;
import com.jd.health.medical.examination.rpc.domain.dto.UserDTO;
import com.jd.health.medical.examination.rpc.domain.param.SkuCreateParam;
import com.jd.health.medical.examination.rpc.gms.ProductServiceRpc;
import com.jd.health.medical.examination.rpc.omdm.HrUserCommonServiceRpc;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * 描述
 * HrUserCommonServiceRpc
 * <AUTHOR>
 * @date 2022/4/11 18:25
 */
public class HrUserCommonServiceRpcTest extends BaseTest {
    
    /**
     * HrUserCommonServiceRpc
     */
    @Autowired
    private HrUserCommonServiceRpc hrUserCommonServiceRpc;
    
    /**
     * 建品 ProductServiceRpc
     */
    @Autowired
    private ProductServiceRpc productServiceRpc;
    
    /**
     * 通用商品发品接口
     */
    @Test
    public void testCreateSkuToVc(){
        System.out.println("111");
        SkuCreateParam skuCreateParam = new SkuCreateParam();
        skuCreateParam.setBuyerErp("yangli492");
        skuCreateParam.setBuyerName("杨采购");
        skuCreateParam.setSalerErp("yangli492");
        skuCreateParam.setSalerName("杨销售");
        skuCreateParam.setJdPrice(new BigDecimal("200"));
        skuCreateParam.setSkuName("消费医疗测试建品");
        skuCreateParam.setUserPin("yangli492");
        SkuApplyResultBO applyId = productServiceRpc.createSkuToVc(skuCreateParam);
        System.out.println("222::"+applyId);
        
    }
    
    /**
     * 根据ERP查询人员信息
     */
    @Test
    public void testQueryUserBaseInfoByErp(){
        System.out.println("111");
        UserDTO dto = hrUserCommonServiceRpc.queryUserBaseInfoByErp("yangli492");
        System.out.println("222"+dto);
        
    }
}
