package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseSkuDao;
import com.jd.health.medical.examination.domain.bo.SkuStoreInfoBO;
import com.jd.health.medical.examination.domain.enterprise.bo.EnterpriseSkuBO;
import com.jd.health.medical.examination.export.dto.SkuInfoDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuExportService;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.param.SkuInfoParam;
import com.jd.health.medical.examination.export.service.SkuInfoExportService;
import com.jd.health.medical.examination.service.bo.SkuInfoESParam;
import com.jd.health.medical.examination.service.selfpersonal.SkuInfoService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/8 15:08
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class EnterpriseSkuTest {
    @Autowired
    private EnterpriseSkuExportService enterpriseSkuExportService;

    @Autowired
    private SkuInfoExportService skuInfoExportService;

    @Autowired
    private EnterpriseSkuDao enterpriseSkuDao;

    @Resource
    private SkuInfoService enhanceCacheSkuInfoService;


    @Test
    public void pageTest(){
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(20);
        pageParam.setPageNum(1);
        JsfResult<PageInfo<EnterpriseSkuDTO>> pageInfoJsfResult = enterpriseSkuExportService.queryEnterpriseSkuPage(23485789L, pageParam);
        System.out.println(pageInfoJsfResult.getData().getList());
    }
    @Test
    public void pageTest2(){
        EnterpriseSkuParam enterpriseSkuParam = new EnterpriseSkuParam();
        enterpriseSkuParam.setCompanyNo(23485789L);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<EnterpriseSkuDTO>> pageInfoJsfResult = enterpriseSkuExportService.querySkuInfoPage(enterpriseSkuParam, pageParam);
        System.out.println(pageInfoJsfResult.getData().getList());
    }

    @Test
    public void insertTest(){
        JsfResult<List<SkuInfoDTO>> result = skuInfoExportService.queryEnableSkuList(new SkuInfoParam());
        System.out.println(result.getData());
    }

    @Test
    public void daoTest(){
        EnterpriseSkuBO bo = new EnterpriseSkuBO();

        List<EnterpriseSkuBO> bos = enterpriseSkuDao.querySkuInfoPage(bo);
        System.out.println(bos);
    }

    /**
     * 根据公司编号查询可用的sku
     */
    @Test
    public void test(){
        EnterpriseSkuParam enterpriseSkuParam = new EnterpriseSkuParam();
        enterpriseSkuParam.setCompanyNo(23485789L);
        JsfResult<List<EnterpriseSkuDTO>> result = enterpriseSkuExportService.queryAllEnterpriseSku(enterpriseSkuParam);
        System.out.println(JsonUtil.toJSONString(result));
        System.out.println(result.getData());
    }

    /**
     * 根据公司编号查询可用的sku
     */
    @Test
    public void addStoreInfotest() throws Exception {
        /*boolean result = skuInfoService.saveStoreInfoToES("","");
        System.out.println(result);
        SkuInfoESParam skuInfoParam = new SkuInfoESParam();
        skuInfoParam.setChannelNo("10987654");
        skuInfoParam.setProvinceId("11");
        skuInfoParam.setCityId("22");
        double lat = 26.45572;
        double lon = 39.82827;
        List<SkuStoreInfoBO> dateList = skuInfoService.searchStoreInfoOfEs(skuInfoParam,1,10,lat,lon);
        System.out.println(JsonUtil.toJSONString(dateList));*/
    }

    /**
     * 根据公司编号查询可用的sku
     */
    @Test
    public void findStoreInfotest(){

    }


}
