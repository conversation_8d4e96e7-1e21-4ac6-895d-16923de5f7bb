package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.domain.personal.entity.ProviderEntity;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.selfpersonal.ProviderService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName ProviderTest
 * @Description
 * <AUTHOR>
 * @Date 2020/12/3 11:40
 **/
public class ProviderTest extends BaseTest{

	/**
	 * providerService
	 */
	@Resource
	private ProviderService providerService;

	/**
	 * getAllProviderAndGoods
	 */
	@Test
	public void getAllProviderAndGoods() {
		List<ProviderEntity> allProviderAndGoods = providerService.getAllProviderAndGoods();
		System.out.println(JsonUtil.toJSONString(allProviderAndGoods));
	}

	/**
	 * getAllProviders
	 */
	@Test
	public void getAllProviders() {
		List<ProviderEntity> allProviders = providerService.getAllProviders();
		System.out.println(JsonUtil.toJSONString(allProviders));
	}

	/**
	 * getPageProvider
	 */
	@Test
	public void getPageProvider() {
		PageParam pageParam = new PageParam();
		pageParam.setPageSize(5);
		pageParam.setPageNum(1);
		PageInfo<ProviderEntity> pageInfo = providerService.getPageProvider(pageParam);
		System.out.println(JsonUtil.toJSONString(pageInfo));
	}

	/**
	 * updateProvider
	 */
	@Test
	public void updateProvider() {
		ProviderEntity providerEntity = new ProviderEntity();
		providerEntity.setChannelName("袋鼠健康");
		providerEntity.setChannelNo(9787771002L);
		providerEntity.setUpdateUser("lvyifan3");
		providerEntity.setProviderJdName("袋鼠健康京东自营店");
		providerEntity.setProviderJdNo("dsj");
		providerEntity.setStatus(2);
		Boolean aBoolean = providerService.updateProvider(providerEntity);
		System.out.println(aBoolean);
	}

	/**
	 * addProvider
	 */
	@Test
	public void addProvider() {
		ProviderEntity providerEntity = new ProviderEntity();
		providerEntity.setChannelName("中康");
		providerEntity.setChannelNo(9139571268L);
		providerEntity.setCreateUser("lvyifan3");
		providerEntity.setUpdateUser("lvyifan3");
		providerEntity.setProviderJdName("中康");
		providerEntity.setProviderJdNo("ebk1189");
		Boolean aBoolean = providerService.addProvider(providerEntity);
		System.out.println(aBoolean);
	}

	/**
	 * getProvider
	 */
	@Test
	public void getProvider() {
		ProviderEntity provider = providerService.getProvider(9787771002L);
		System.out.println(JsonUtil.toJSONString(provider));
	}

	@Test
	public void selectProviderByGoodsId() {
		List<ProviderEntity> providerEntities =
				providerService.selectProviderByGoodsId(2120006285L);
		System.out.println(JsonUtil.toJSONString(providerEntities));
	}

	/**
	 * deleteProvider
	 */
	@Test
	public void deleteProvider() {
		Boolean aBoolean = providerService.deleteProvider(24L);
		System.out.println(aBoolean);
	}
}
