package com.jd.health;

import com.alibaba.fastjson.JSON;
import com.jd.health.medical.examination.export.param.*;
import com.jd.health.medical.examination.export.service.GroupAppointmentExportService;
import com.jd.health.medical.examination.service.GroupPhysicalInsteadAppointmentService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import sun.misc.BASE64Encoder;

import java.io.File;
import java.io.FileInputStream;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 16:12
 */
public class GroupAppointmentTest extends BaseTest{
    
    /**
     * groupAppointmentExportService
     */
    @Autowired
    private GroupAppointmentExportService groupAppointmentExportService;
    
    /**
     * groupPhysicalInsteadAppointmentService
     */
    @Autowired
    private GroupPhysicalInsteadAppointmentService groupPhysicalInsteadAppointmentService;
    
    
    @Test
    public void getGroupEmployeePage(){
        
        GroupEmployeeInfoParam groupEmployeeInfoParam = new GroupEmployeeInfoParam();
        groupEmployeeInfoParam.setUserPin("zhangzengke");
        groupEmployeeInfoParam.setCompanyNo(22848835L);
        groupEmployeeInfoParam.setPageNum(1);
        groupEmployeeInfoParam.setPageSize(10);
        System.out.println(JsonUtil.toJSONString(groupAppointmentExportService.getGroupEmployeePage(groupEmployeeInfoParam)));
    }
    
    @Test
    public void getGroupEmployeeFilterByJdIdTest(){
        
        GroupEmployeeInfoParam groupEmployeeInfoParam = new GroupEmployeeInfoParam();
        groupEmployeeInfoParam.setUserPin("id01201020");
        groupEmployeeInfoParam.setCompanyNo(8301587970L);
        groupEmployeeInfoParam.setJdAppointmentId("1775890645282466306");
//        groupEmployeeInfoParam.setJdAppointmentId("1774829573611981826");
        System.out.println(JsonUtil.toJSONString(groupPhysicalInsteadAppointmentService.getGroupEmployeeFilterByJdId(groupEmployeeInfoParam)));
    }

    @Test
    public void getImportRecordInfoPage(){

        ImportRecordInfoParam importRecordInfoParam = new ImportRecordInfoParam();
        importRecordInfoParam.setCompanyNo(123L);
        importRecordInfoParam.setPageNum(1);
        importRecordInfoParam.setPageSize(10);
        importRecordInfoParam.setUserPin("zhangzengke");
        System.out.println(JsonUtil.toJSONString(groupAppointmentExportService.getImportRecordInfoPage(importRecordInfoParam)));
    }

    @Test
    public void getHttpsUrl(){

        System.out.println(JsonUtil.toJSONString(groupAppointmentExportService.getHttpsUrl("/examin.common/批量预约Test_v1.0_1505558660573561354.xlsx")));
    }

    @Test
    public void getTemplateUrl(){

        ImportTemplateParam importTemplateParam = new ImportTemplateParam();
        importTemplateParam.setType(2);
        importTemplateParam.setCompanyNo(123L);
        importTemplateParam.setUserPin("zhangzengke");
        System.out.println(JsonUtil.toJSONString(groupAppointmentExportService.getTemplateUrl(importTemplateParam)));
    }

    /**
     * batchStoreInfoImport
     */
    @Test
    public void batchStoreInfoImport(){
        try{
            File file = new File("D:\\zhangzengke\\\\Desktop\\"+"批量延期模板.xlsx");
            FileInputStream fin = new FileInputStream(file);
            byte[] bytes = new byte[fin.available()];
            fin.read(bytes);
            String encodeBuffer = new BASE64Encoder().encodeBuffer(bytes);
//			System.out.println(encodeBuffer);
            ExcelImportParam param = new ExcelImportParam();
            param.setExcelStr(encodeBuffer);
            param.setFileName("批量延期模板");
            param.setFileType("xlsx");
            param.setOperatorId("zhangzengke");
            param.setType(3);
            param.setCompanyNo(12321L);
//            JsfResult<Boolean> jsfResult = groupAppointmentExportService.batchPostInfoImport(param);
//            System.out.println(JsonUtil.toJSONString(jsfResult));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
