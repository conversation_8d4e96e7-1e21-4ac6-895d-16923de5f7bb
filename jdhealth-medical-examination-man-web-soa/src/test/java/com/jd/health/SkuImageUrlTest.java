package com.jd.health;

import com.jd.health.medical.examination.rpc.SkuInfoRpc;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/10 11:23
 */
public class SkuImageUrlTest extends BaseTest{
    @Autowired
    private SkuInfoRpc skuInfoRpc;
    @Test
    public void getSkuImageUrlTest(){
        Set<String> set = new HashSet<>();
        set.add("4717433");
        Map<String, Map<String, String>> skuInfo = null;
        try {
            skuInfo = skuInfoRpc.getSkuInfo(set);
        }catch (Exception e){
            System.out.println(e);
        }
        System.out.println(JsonUtil.toJSONString(skuInfo));
    }
}
