package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.enums.AppointStoreStatusEnum;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.domain.bo.AddressInfoBO;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.rpc.domain.param.ikang.IKangCapResult;
import com.jd.health.medical.examination.rpc.domain.param.ikang.IKangResult;
import com.jd.lbs.geocode.api.dto.GisPointDto;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/4/14 17:06
 * @Doc
 **/
public class IKangTest {
    public static final String STRING = "JDH@IK123456789!";

    public static void main(String[] args) throws Exception {

//        getStoreCap();
//        appoint();
//        updateAppoint();
//        cancelAppoint();

//        testParam();

        getReport();
    }

    private static void getReport() {
        String reportUrl = "https://api.daoyitong.com/cloud/tailor/jDPhyExam/downloadReport";
        try {
            HashMap<String,String> param = new HashMap<>();
            param.put("appointmentNo","1352438612436729856");
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json");
            String result = HttpSimpleClient.simplePostReport(reportUrl,headerMap,JsonUtil.toJSONString(param));
            IKangResult<String> reportResult = JsonUtil.parseObject(result, IKangResult.class);
            System.out.println(JsonUtil.toJSONString(reportResult));
            if (null != reportResult.getResults()) {
                String reportStr = reportResult.getResults();
                //返回报告String
                System.out.println(reportStr);
                Base64Utils.base64StringToPDF(reportStr);
            }
        }catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static void testParam() {
        AddressInfoBO addressInfoBO = new AddressInfoBO();
        GisPointDto lngLatByAddress = new GisPointDto();
        lngLatByAddress.setLatitude(new BigDecimal(30.611769000));
        lngLatByAddress.setLongitude(new BigDecimal(114.254375000));
        addressInfoBO.setLatitude(String.valueOf(lngLatByAddress.getLatitude().doubleValue()));
        addressInfoBO.setLongitude(String.valueOf(lngLatByAddress.getLongitude().doubleValue()));
        System.out.println(JsonUtil.toJSONString(addressInfoBO));
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("appointmentNo", "123");
//        jsonObject.put("jdAppointmentId", "123");
//        jsonObject.put("appointmentDate", "2020-05-28");
//        jsonObject.put("storeId", "123");
//        System.out.println(jsonObject.toJSONString());
    }

    /**
     * 预约
     */
    // {"appointmentDate":"2020-08-12","channelType":**********,"credentialNo":"110102199507110167","credentialType":1,"goodsId":"1258958399531651073","jdAppointmentId":1520393709542180866,"storeId":"750","userBirth":"1995-07-11","userGender":2,"userMarriage":2,"userName":"测试佟","userPhone":"18601362267"}
    //{"orderId":1586859049520,"customerGender":"1","idCardNo":"******************","hospitalSubId":"BJ001","customerName":"京东测试","phoneNo":"16619887367","hasAuthorized":"1","medicalStatus":"1","idCardType":"1","appointmentTime":"20200418080000","medicalPackage":"cs_m","customerBirthday":"1990-09-01","company":"","department":""}
    private static void appoint() throws Exception {
        String appointUrl = "https://api-uat.daoyitong.com/cloud/tailor/jDPhyExam/appointment";
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(System.currentTimeMillis());
        appointmentParam.setGoodsId("1258958399531651073");
        appointmentParam.setStoreId("750");
        appointmentParam.setAppointmentDate("2020-08-12");
        appointmentParam.setUserName("测试佟");
        appointmentParam.setUserPhone("18601362267");
        appointmentParam.setUserGender(2);
        appointmentParam.setUserBirth("1995-07-11");
        appointmentParam.setCredentialType(1);
        appointmentParam.setCredentialNo("110102199507110167");
        appointmentParam.setUserMarriage(2);
        System.out.println(JsonUtil.toJSONString(appointmentParam));
        String secStr = Aes.encryptData(JsonUtil.toJSONString(appointmentParam), STRING);
        System.out.println(secStr);
        HashMap<String,String> paramMap = new HashMap<>();
        paramMap.put("secStr",secStr);
        String result = HttpSimpleClient.simplePost(appointUrl,JsonUtil.toJSONString(paramMap));
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 修改预约
     */
    private static void updateAppoint() {
        String appointUrl = "https://api-uat.daoyitong.com/cloud/tailor/jDPhyExam/updateAppointment";
        JSONObject jsonObject = new JSONObject();
        String result = HttpSimpleClient.simplePost(appointUrl,jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 取消预约
     */
    private static void cancelAppoint() {
        String url = "https://api-uat.daoyitong.com/cloud/tailor/jDPhyExam/cancelAppointment";
        JSONObject jsonObject = new JSONObject();
        String orderId = "1586871905333";
        String hospitalOrderId = "000199220041408782";
        String result = HttpSimpleClient.simplePost(url,jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 熙康预约日历查询
     */
    private static void getStoreCap() {
        String appointStoreDateUrl = "https://api-uat.daoyitong.com/cloud/tailor/jDPhyExam/getStoreAvailableInfo";
        JSONObject param = new JSONObject();
        param.put("storeId","750");
        param.put("goodsId","1258958399531651073");
        param.put("startTime","2020-08-11");
        param.put("endTime","2020-09-12");
        String result = HttpSimpleClient.simplePost(appointStoreDateUrl, JsonUtil.toJSONString(param));
        System.out.println(JsonUtil.toJSONString(result));
        IKangResult<List<IKangCapResult>> iKangResult  = JsonUtil.parseObject(result, IKangResult.class);
        List<IKangCapResult> iKangCapResults = JsonUtil.parseArray(iKangResult.getResults().toString(), IKangCapResult.class);
        if(CollectionUtil.isEmpty(iKangCapResults)){
            return;
        }
        List<AppointDateDTO> list = new ArrayList<>();
        for(IKangCapResult iKangCapResult : iKangCapResults){
            AppointDateDTO appointDateDTO = new AppointDateDTO();
            try {
                appointDateDTO.setDate(DateUtil.formatDate(DateUtil.parseDate(iKangCapResult.getDate(),"yyyy-MM-dd"),"yyyy-MM-dd"));
            } catch (ParseException e) {
                throw new BusinessException(BaseErrorCode.APPOINT_PARAM_APPOINT_DATE_NULL_ERROR);
            }
            //判断是否可预约
            Integer maxNum = iKangCapResult.getMaxCap();
            Integer usedNum = iKangCapResult.getUsedCap();
            if(usedNum >= maxNum ){
                appointDateDTO.setNum(0);
                appointDateDTO.setStatus(AppointStoreStatusEnum.MAX.getCode());
            }else {
                appointDateDTO.setNum(maxNum - usedNum);
                appointDateDTO.setStatus(AppointStoreStatusEnum.ON.getCode());
            }
            list.add(appointDateDTO);
        }
        System.out.println(JsonUtil.toJSONString(list));
    }


}
