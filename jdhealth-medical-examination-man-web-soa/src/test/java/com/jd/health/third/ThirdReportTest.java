package com.jd.health.third;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.constant.ExaminationConstant;
import com.jd.jss.Credential;
import com.jd.jss.JingdongStorageService;
import com.jd.jss.client.ClientConfig;
import com.jd.jss.domain.StorageObject;
import com.jd.medicine.base.common.util.StringUtil;
import org.junit.Test;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.net.URI;

/**
 * @Descroption
 * <AUTHOR>
 * @Date 2020/3/9 19:40
 **/
public class ThirdReportTest extends BaseTest {

    @Resource
    private JingdongStorageService jssClient;

    private JingdongStorageService localClient;
    /**
     * 康康报告字符串转pdf上传
     */
    @Test
    public void testKangReport(){
        String bucketName = "healthcare.test";
        String fileName = "PDF_TEST_"+System.currentTimeMillis()+".pdf";
        String str =Base64Utils.getPDFBinary(new File("D:\\test.pdf"));
        System.out.println(str.length());
        byte[] dataArr = new byte[0];
        try {
            dataArr = new BASE64Decoder().decodeBuffer(str);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ByteArrayInputStream bis = new ByteArrayInputStream(dataArr);
        //上传oss
        String uploadMD5 = jssClient.bucket(bucketName).object(fileName).entity(bis.available(), bis).put();
        if (StringUtil.isNotEmpty(uploadMD5)) {
            // 公有bucket需用户拼接url： 域名/bucket/object
            System.out.println("/" + bucketName + "/" + fileName);
        }
        //获取http链接
        //获取报告的url访问链接
        jssClient.setEndpoint("storage.360buyimg.com");
        URI uri = jssClient.bucket(bucketName).object(fileName).generatePresignedUrl(ExaminationConstant.REPORT_GETREPORT_TIMEOUT);
        System.out.println(uri.toString());
    }

    /**
     * 康康部分读取不了的文件是因为base64加号与斜杠的处理不一致导致的
     * 1、查询日志确认发送过来的数据是否有+号(如没有需咨询宙斯)
     * 2、本地读取和上传的都正常(已确认)
     * 3、康康发送的原始字符串有加号编码(已确认)
     */
    @Test
    public void testDownKangReport(){
        String bucketName = "examin.report";
        String fileName = "820496_1406214022612650498_200309.pdf";
        //从oss下载
        StorageObject storageObject = getJssClient().bucket(bucketName).object(fileName).get();
        String result = Base64Utils.getPDFBinary(storageObject.getInputStream());
        System.out.println(result);
        Base64Utils.base64StringToPDF(result);

    }

    public JingdongStorageService getJssClient(){
        if(null == localClient) {
            synchronized (this) {
                ClientConfig config = new ClientConfig();
                config.setSocketTimeout(50000);//默认是50s
                config.setConnectionTimeout(50000);//默认是50s
                config.setEndpoint("storage.jd.local");
                config.setMaxConnections(128);//默认连接数128
                String accessKey = "3HjdNBAYZlaeZ2xy";
                String secretKey = "Eka7PFe2G5TNOE93U0V8YSK1ahrS6AaGuB9mlMf6";
                localClient = new JingdongStorageService(new Credential(accessKey, secretKey), config);
            }
            return localClient;
        }else{
            localClient.destroy();
            localClient = null;
            return getJssClient();
        }
    }


}
