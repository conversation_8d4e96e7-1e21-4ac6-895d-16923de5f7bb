//package com.jd.health.third;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jd.health.medical.examination.common.util.Aes;
//import com.jd.health.medical.examination.common.util.HttpSimpleClient;
//import com.jd.health.medical.examination.export.param.AppointmentParam;
//import com.jd.health.medical.examination.rpc.domain.param.sonqao.SonQaoResult;
//import com.jd.health.medical.examination.rpc.domain.param.sonqao.SonQaoStoreCapResult;
//import com.jd.medicine.base.common.util.DateUtil;
//import com.jd.medicine.base.common.util.JsonUtil;
//
//import java.util.*;
//
///**
// * @Description
// * <AUTHOR>
// * @Date 2020/4/14 17:06
// * @Doc
// **/
//public class SongqiaoTest {
//
//    public static final String pw = "XK&&JD0123456789";
//
//    public static void main(String[] args) throws Exception {
//
////        getStoreCap();
////        appoint();
////        updateAppoint();
////        cancelAppoint();
//        getReport();
//    }
//
//    private static void getReport() {
//        String reportUrl = "http://*************:8001/api/jd/downloadReportInfo";
//        HashMap<String, String> param = new HashMap<>();
//        param.put("appointmentNo", "DJ02764155");
//        param.put("reportId", "1604383203764732930");
//        String result = HttpSimpleClient.simplePost(reportUrl,JsonUtil.toJSONString(param));
//        SonQaoResult reportResult = JsonUtil.parseObject(result, SonQaoResult.class);
//        System.out.println("AppointSonQaoRpcImpl -> getReport, result="+JsonUtil.toJSONString(reportResult));
//    }
//
//    /**
//     * 预约
//     */
//    //{"orderId":1586859049520,"customerGender":"1","idCardNo":"******************","hospitalSubId":"BJ001","customerName":"京东测试","phoneNo":"16619887367","hasAuthorized":"1","medicalStatus":"1","idCardType":"1","appointmentTime":"20200418080000","medicalPackage":"cs_m","customerBirthday":"1990-09-01","company":"","department":""}
//    private static void appoint() throws Exception {
//        String appointUrl = "http://*************:8001/api/jd/appointment";
//        AppointmentParam appointmentParam = new AppointmentParam();
//        appointmentParam.setJdAppointmentId(System.currentTimeMillis());
//        appointmentParam.setGoodsId("STC-200317000017");
//        appointmentParam.setStoreId("000001");
//        appointmentParam.setAppointmentDate("2020-04-25");
//        appointmentParam.setUserName("京东测试");
//        appointmentParam.setUserPhone("16619887367");
//        appointmentParam.setUserGender(1);
//        appointmentParam.setUserBirth("2002-01-01");
//        appointmentParam.setCredentialType(1);
//        appointmentParam.setCredentialNo("110101200201017137");
//        appointmentParam.setUserMarriage(1);
//        System.out.println(JsonUtil.toJSONString(appointmentParam));
//        String secStr = Aes.encryptData(JsonUtil.toJSONString(appointmentParam), pw);
//        System.out.println(secStr);
//        HashMap<String,String> paramMap = new HashMap<>();
//        paramMap.put("secStr",secStr);
//        String result = HttpSimpleClient.simplePost(appointUrl,JsonUtil.toJSONString(paramMap));
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    /**
//     * 修改预约
//     */
//    private static void updateAppoint() {
//        String appointUrl = "http://*************:8001/api/jd/updateAppointment";
//        JSONObject jsonObject = new JSONObject();
//        String result = HttpSimpleClient.simplePost(appointUrl,jsonObject.toJSONString());
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    /**
//     * 取消预约
//     */
//    private static void cancelAppoint() {
//        String url = "http://*************:8001/api/jd/cancelAppointment";
//        JSONObject jsonObject = new JSONObject();
//        String orderId = "1586871905333";
//        String hospitalOrderId = "000199220041408782";
//        String result = HttpSimpleClient.simplePost(url,jsonObject.toJSONString());
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    /**
//     * 预约日历查询
//     */
//    private static void getStoreCap() {
//        String appointStoreDateUrl = "http://*************:8001/api/jd/getStoreAvailableInfo";
//        HashMap<String, String> paramMap = new HashMap<>();
//        paramMap.put("storeId", "0316002");
//        paramMap.put("goodsId", "20090200088");
//        paramMap.put("startTime", DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd"));
//        paramMap.put("endTime", DateUtil.formatDate(DateUtil.addDays(new Date(), 60), "yyyy-MM-dd"));
//        String result = HttpSimpleClient.simplePost(appointStoreDateUrl, JsonUtil.toJSONString(paramMap));
//        SonQaoResult<List<SonQaoStoreCapResult>> SonQaoResult = JsonUtil.parseObject(result, com.jd.health.medical.examination.rpc.domain.param.sonqao.SonQaoResult.class);
//        System.out.println("AppointSonQaoRpcImpl -> getAppointDateByStoreNow end, result="+JsonUtil.toJSONString(SonQaoResult));
//    }
//
//
//}
