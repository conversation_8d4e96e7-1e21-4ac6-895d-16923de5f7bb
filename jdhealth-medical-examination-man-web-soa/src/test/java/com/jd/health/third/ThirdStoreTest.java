package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.dao.third.ThirdGoodsStoreDao;
import com.jd.health.medical.examination.domain.third.ThirdGoodsStoreEntity;
import com.jd.health.medical.examination.domain.third.ThirdStoreEntity;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdStoreParam;
import com.jd.health.medical.examination.export.service.AppointmentApiExportService;
import com.jd.health.medical.examination.export.service.DevManExportService;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.health.medical.examination.service.manage.base.ThirdStoreService;
import com.jd.health.medical.examination.rpc.domain.param.kangkang.KangParamUtil;
import com.jd.health.medical.examination.rpc.domain.param.rich.RichAppInfo;
import com.jd.health.medical.examination.rpc.domain.param.rich.RichResult;
import com.jd.health.medical.examination.rpc.domain.param.rich.RichTokenInfo;
import com.jd.health.medical.examination.service.AddressService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.spring.service.JimRedisService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;


/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/20 10:25
 */
public class ThirdStoreTest extends BaseTest {
    /**
     *
     */
    @Autowired
    private AppointmentApiExportService appointmentApiExportService;
    /**
     *
     */
    @Autowired
    private ThirdGoodsStoreDao thirdGoodsStoreDao;
    /**
     *
     */
    @Resource
    private AddressService addressService;
    /**
     *
     */
    @Autowired
    JimRedisService jimRedisService;
    /**
     *
     */
    @Autowired
    private DevManExportService devManExportService;


    /**
     * 获取瑞慈接口调用token，提供其他方法调用
     */
    @Test
    public void getRichToken(){
        JSONObject param = new JSONObject();
        param.put("identityID", "JDJK");
        param.put("passWord", "SE2cDDhBmNeca13F4c");
        param.put("macAddress", "9C-DA-3E-A2-A2-9C");
        RichAppInfo richAppInfo = new RichAppInfo();
        String richGetTokenUrl = "https://api.rich-healthcare.com/open/api/GetToken";
        try {
            String result = HttpSimpleClient.simplePost(richGetTokenUrl, param.toJSONString());
            System.out.println("result:"+JsonUtil.toJSONString(result));
            RichResult<RichTokenInfo> richResult = JsonUtil.parseObject(result, RichResult.class);
            if("0000000".equals(richResult.getResultCode())) {
                RichTokenInfo richTokenInfo = JsonUtil.parseObject(JsonUtil.toJSONString(richResult.getData()), RichTokenInfo.class);
                richAppInfo.setIdentityId("JDJK");
                richAppInfo.setToken(richTokenInfo.getTokenValue());
                System.out.println(JsonUtil.toJSONString(richAppInfo));
                //return richAppInfo;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        //return null;
    }

    /**
     * 康康预约日历查询
     */
    @Test
    public void findKKAppointmentDateTest() {
        AppointmentStoreCapParam param = new AppointmentStoreCapParam();
        param.setChannelNo(6621685943L);
        param.setGoodsId("TC201604081442440001");
        param.setStoreId("YY201510161906120001");
        String appointStoreDateKangUrl = "https://qytj.kktijian.com/tencentapi/api/jdsop/getStoreAvailableInfo";
        System.out.println("param:"+JsonUtil.toJSONString(KangParamUtil.bulidKangStoreCapParam(param)));
        String result = HttpSimpleClient.simplePost(appointStoreDateKangUrl, KangParamUtil.bulidKangStoreCapParam(param));
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 瑞慈预约日历查询
     */
    @Test
    public void findRichAppointmentDateTest() {
        AppointmentStoreCapParam param = new AppointmentStoreCapParam();
        param.setChannelNo(7975142641L);
        param.setGoodsId("**********");
        param.setStoreId("12");
        String appointStoreDateUrl = "https://api.rich-healthcare.com/open/api/GetStoreAvailableInfo";
//        String param2 = RichParamUtil.bulidRichAppointStoreCapParam(param,getRichToken());
        System.out.println("param:"+JsonUtil.toJSONString(param));
        //String result = HttpSimpleClient.simplePost(appointStoreDateUrl,param2);
        // System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 预约门店
     */
    @Test
    public void findAppointmentStoreTest(){

    }

    @Test
    public void appointmentTest(){
        AppointmentParam param = new AppointmentParam();
        param.setJdAppointmentId(1111L);
        param.setAppointmentDate("2020-09-04");
        param.setCredentialType(1);
        param.setCredentialNo("120203199207070058");
        param.setStoreId("0316002");
        param.setGoodsId("03160021907300012");
        param.setUserGender(GenderEnum.MAN.getType());
        param.setUserMarriage(MarryTypeEnum.YES.getType());
        param.setUserName("京东测试");
        param.setUserPhone("18842626795");
        param.setChannelType(3632970185L);
        appointmentApiExportService.appointment(param);
    }

    @Test
    public void testHttp(){
        AppointmentStoreCapParam param = new AppointmentStoreCapParam();
        param.setChannelNo(6621685943L);
        param.setGoodsId("TC201705151705060001");
        param.setStoreId("YY201603091536110001");
        String url = "http://m.51kys.cn:6677/YY/api/jdsop/getStoreAvailableInfo";
        System.out.println(KangParamUtil.bulidKangStoreCapParam(param));
        String result = HttpSimpleClient.simplePost(url, KangParamUtil.bulidKangStoreCapParam(param));
        System.out.println(result);
    }

    @Test
    public void testThirdStore(){
        ThirdGoodsStoreEntity entities = thirdGoodsStoreDao.selectThirdStoreGoodsByGoodsIdAndStoreId(6621685943L, "TC201705151708260001", "YY201603091723510001");
        System.out.println(entities);
    }

    @Autowired
    ThirdDataExportService thirdDataExportService;

    @Autowired
    ThirdStoreService thirdStoreService;
    @Test
    public void testInsertStore(){
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(6621685943L);
        thirdStoreParam.setCityName("西安市");
        thirdStoreParam.setCountyName("新城区");
        thirdStoreParam.setOperateType(1);
        thirdStoreParam.setProvinceName("陕西省");
        thirdStoreParam.setReportSupport(0);
        thirdStoreParam.setStatus(1);
        thirdStoreParam.setStoreAddr("西安市高新四路1号高科广场A座3层（南二环与高新四路什字东南角）");
        thirdStoreParam.setStoreHours("周二至周日");
        thirdStoreParam.setStoreId("YY201510161906120001");
        thirdStoreParam.setStoreName("普惠西安高新体检中心");
        thirdStoreParam.setStoreType(3);
        thirdStoreParam.setStorePhone("400-1000-920");
        thirdStoreParam.setStoreLng(108.907108);
        thirdStoreParam.setStoreLat(34.248486);
        System.out.println(JsonUtil.toJSONString(convertStoreEntity(thirdStoreParam)));
        Integer result = thirdStoreService.insertThirdStore(convertStoreEntity(thirdStoreParam));
//        JsfResult result = thirdDataExportService.pushStoreInfo(thirdStoreParam);
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 商家门店接口参数，数据转换
     * @param thirdStoreParam
     * @return
     */
    public static ThirdStoreEntity convertStoreEntity(ThirdStoreParam thirdStoreParam){
        ThirdStoreEntity thirdStoreEntity = new ThirdStoreEntity();
        thirdStoreEntity.setChannelNo(thirdStoreParam.getChannelType());
        thirdStoreEntity.setStoreAddr(thirdStoreParam.getStoreAddr());
        thirdStoreEntity.setCityName(thirdStoreParam.getCityName());
        thirdStoreEntity.setProvinceName(thirdStoreParam.getProvinceName());
        thirdStoreEntity.setCountyName(thirdStoreParam.getCountyName());
        thirdStoreEntity.setStoreId(thirdStoreParam.getStoreId());
        thirdStoreEntity.setStoreName(thirdStoreParam.getStoreName());
        thirdStoreEntity.setStoreType(thirdStoreParam.getStoreType());
        thirdStoreEntity.setStorePhone(thirdStoreParam.getStorePhone());
        thirdStoreEntity.setStoreStatus(thirdStoreParam.getStatus());
        thirdStoreEntity.setStoreHours(thirdStoreParam.getStoreHours());
        thirdStoreEntity.setReportSupport(thirdStoreParam.getReportSupport());
        thirdStoreEntity.setLat(thirdStoreParam.getStoreLat());
        thirdStoreEntity.setLng(thirdStoreParam.getStoreLng());
        thirdStoreEntity.setOperateType(thirdStoreParam.getOperateType());
        return thirdStoreEntity;
    }

    @Test
    public void flushThirdStoreAddrTest(){
        JsfResult<Boolean> result = devManExportService.flushAllThirdStoreAddressId(6621685943L);
        System.out.println(JsonUtil.toJSONString(result));
    }

    @Test
    public void testAddr(){
        BaseAddressInfo addr = addressService.getJDAddressByAddress("北京市朝阳区工体北路8号三里屯SOHO6号商场3楼");
        System.out.println(addr);
    }


    /**
     * 查询门店id
     */
    @Test
    public void queryStoreInfoByStoreIdTest(){
        ThirdStoreEntity thirdStoreEntity = thirdStoreService.queryStoreInfoByStoreId("testyt333", ProviderTypeEnum.RICH.getTypeNo());
        System.out.println(JsonUtil.toJSONString(thirdStoreEntity));
    }
}
