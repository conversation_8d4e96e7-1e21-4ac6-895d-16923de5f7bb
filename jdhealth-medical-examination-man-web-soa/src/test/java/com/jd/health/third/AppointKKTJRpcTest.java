package com.jd.health.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.rpc.domain.bo.AppointmentBo;
import com.jd.health.medical.examination.rpc.domain.param.provider.DefaultAppointParam;
import com.jd.health.medical.examination.rpc.provider.impl.CommonProviderRpcHelper;
import com.jd.medicine.base.common.util.JsonUtil;

/**
 * 康康体检新对接
 * 供应商简码：kktj 
 * 供应商渠道：**********
 *
 * <AUTHOR>
 */
public class AppointKKTJRpcTest {

    /**
     * 成功返回
     */
    private static final String SUCCESS = "0000";

    /**
     * AES KEY
     */
    private static final String AES_KEY = "JDH#KKTJ55328770";

    public static void main(String[] args) throws Exception {
        AppointmentBo bo = new AppointmentBo();
        bo.setJdAppointmentId(1981111111111111111L);
        bo.setGoodsId("goodsId000001");
        bo.setStoreId("storeId000001");
        bo.setAppointmentDate("2024-09-09");
        bo.setUserName("预约人姓名");
        bo.setUserPhone("18606069999");
        bo.setUserMarriage(1);
        bo.setUserBirth("1980-01-01");
        bo.setCredentialType(1);
        bo.setCredentialNo("110101202408215017");
        System.out.println(JSON.toJSONString(bo));
        DefaultAppointParam appointParam = CommonProviderRpcHelper.buildDefaultAppointParam(bo);
        String appointStr = Aes.encryptData(JsonUtil.toJSONString(appointParam), AES_KEY);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("secStr",appointStr);
        String jsonParam = JsonUtil.toJSONString(jsonObject);
        System.out.println(JSON.toJSONString(jsonParam));
        String result = HttpSimpleClient.postJson("http://test.51kys.cn:7681/api/jdzy/appointment", jsonParam);
        System.out.println(JSON.toJSONString(result));
    }
}
