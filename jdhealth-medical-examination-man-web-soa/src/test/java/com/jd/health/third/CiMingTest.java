//package com.jd.health.third;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jd.health.medical.examination.common.enums.AppointStoreStatusEnum;
//import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
//import com.jd.health.medical.examination.common.util.Aes;
//import com.jd.health.medical.examination.common.util.HttpSimpleClient;
//import com.jd.health.medical.examination.export.dto.AppointDateDTO;
//import com.jd.health.medical.examination.rpc.domain.param.ciming.CiMingCapResult;
//import com.jd.health.medical.examination.rpc.domain.param.ciming.CimingStoreCap;
//import com.jd.medicine.base.common.exception.BusinessException;
//import com.jd.medicine.base.common.util.DateUtil;
//import com.jd.medicine.base.common.util.JsonUtil;
//import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
//
//import java.text.ParseException;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//
///**
// * @Description
// * <AUTHOR>
// * @Date 2020/4/14 17:06
// * @Doc
// **/
//public class CiMingTest {
//
//    public static final String userName = "testorder";
//    public static final String D_24 = "ee93def9b34879325edeb6de840a0d24";
//
//    public static void main(String[] args) throws Exception {
//
////        getStoreCap();
//
////        System.out.println(DateUtil.formatDate(DateUtil.parseDate("20200420","yyyyMMdd"),"yyyy-MM-dd"));
//
////        appoint();
//
////        updateAppoint();
//
////        cancelAppoint();
//
//        getReport();
//    }
//
//    /**
//     * 获取报告
//     */
//    private static void getReport() throws Exception {
//        String reportUrl = "http://urltest.test.ciming.com/comForData/jd/book.do?userName="+userName+"&passWord="+ D_24;
//        String s = HttpSimpleClient.simplePostReport(reportUrl, new HashMap<>());
//        System.out.println(s);
//    }
//
//    /**
//     * 预约
//     */
//    //{"orderId":1586859049520,"customerGender":"1","idCardNo":"******************","hospitalSubId":"BJ001","customerName":"京东测试","phoneNo":"16619887367","hasAuthorized":"1","medicalStatus":"1","idCardType":"1","appointmentTime":"20200418080000","medicalPackage":"cs_m","customerBirthday":"1990-09-01","company":"","department":""}
//    private static void appoint() throws Exception {
//        String appointUrl = "http://urltest.test.ciming.com/comForData/jd/book.do?userName="+userName+"&passWord="+ D_24;
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("orderId",System.currentTimeMillis());
//        jsonObject.put("customerBirthday","1990-09-01");
//        jsonObject.put("customerName","京东测试");
//        jsonObject.put("customerGender","1");
//        jsonObject.put("phoneNo","16619887367");
//        jsonObject.put("medicalStatus","1");
//        jsonObject.put("company","");
//        jsonObject.put("department","");
//        jsonObject.put("hasAuthorized","1");
//        jsonObject.put("hospitalSubId","BJ001");
//        jsonObject.put("idCardType","1");
//        jsonObject.put("idCardNo","******************");
//        jsonObject.put("medicalPackage","cs_m");
//        jsonObject.put("appointmentTime","20200418080000");
//        System.out.println(jsonObject.toJSONString());
//        String secStr = Aes.encryptData(jsonObject.toJSONString(), "JDH@CM123456789!");
//        System.out.println(secStr);
//        String result = HttpSimpleClient.simplePost(appointUrl,secStr);
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    /**
//     * 修改预约
//     */
//    private static void updateAppoint() {
//        String appointUrl = "http://urltest.test.ciming.com/comForData/jd/updateBook.do?userName="+userName+"&passWord="+ D_24;
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("orderId","1586871905333");
//        jsonObject.put("hospitalOrderId","000199220041408782");
//        jsonObject.put("appointmentTime","20200419080000");
//        String result = HttpSimpleClient.simplePost(appointUrl,jsonObject.toJSONString());
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    /**
//     * 取消预约
//     */
//    private static void cancelAppoint() {
//        String appointUrl = "http://urltest.test.ciming.com/comForData/jd/cancelBook.do?userName="+userName+"&passWord="+ D_24;
//        String orderId = "1586871905333";
//        String hospitalOrderId = "000199220041408782";
//        String url = buildCancelUrl(appointUrl,orderId,hospitalOrderId);
//        String result = HttpSimpleClient.simplePostMap(url);
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    private static String buildCancelUrl(String appointUrl, String orderId, String hospitalOrderId) {
//        return appointUrl+"&orderId="+orderId+"&hospitalOrderId="+hospitalOrderId;
//    }
//
//    /**
//     * 慈铭预约日历查询
//     */
//    private static void getStoreCap() {
//        String storeId = "BJ001";
//        String appointStoreDateUrl = "http://urltest.test.ciming.com/comForData/jd/query.do?userName="+userName+"&passWord="+ D_24 +"&hospitalSubId="+storeId;
//        String result = SimpleHttpClient.simpleGet(appointStoreDateUrl);
//        System.out.println(JsonUtil.toJSONString(result));
//        CiMingCapResult ciMingResult = JsonUtil.parseObject(result, CiMingCapResult.class);
//        System.out.println(JsonUtil.toJSONString(ciMingResult.getResults()));
//        List<AppointDateDTO> list = new ArrayList<>();
//        for(CimingStoreCap cimingStoreCap : ciMingResult.getResults()){
//            AppointDateDTO appointDateDTO = new AppointDateDTO();
//            appointDateDTO.setDate(cimingStoreCap.getDate());
//            appointDateDTO.setChannelNo(123L);
//            appointDateDTO.setStoreId("BJ001");
//            String tDate = cimingStoreCap.getDate();
//            try {
//                appointDateDTO.setDate(DateUtil.formatDate(DateUtil.parseDate(tDate),"yyyy-MM-dd"));
//            } catch (ParseException e) {
//                throw new BusinessException(BaseErrorCode.APPOINT_PARAM_APPOINT_DATE_NULL_ERROR);
//            }
//            //判断是否可预约
//            if(1 == cimingStoreCap.getCanOrder()){
//                if(cimingStoreCap.getMaxNum()- cimingStoreCap.getOrderNum() <= 0){
//                    appointDateDTO.setNum(0);
//                    appointDateDTO.setStatus(AppointStoreStatusEnum.MAX.getCode());
//                }else {
//                    appointDateDTO.setNum(cimingStoreCap.getMaxNum());
//                    appointDateDTO.setStatus(AppointStoreStatusEnum.ON.getCode());
//                }
//            }else{
//                appointDateDTO.setNum(0);
//                appointDateDTO.setStatus(AppointStoreStatusEnum.MAX.getCode());
//            }
//            list.add(appointDateDTO);
//        }
//        System.out.println(JsonUtil.toJSONString(list));
//    }
//
//    private static HashMap<String,String> buildBaseMap(){
//        HashMap<String,String> map = new HashMap<>();
//        map.put("userName", "testorder");
//        map.put("passWord", "ee93def9b34879325edeb6de840a0d24");
//        return map;
//    }
//
//    private static HashMap<String,String> buildBaseDateMap(String storeId){
//        HashMap<String,String> map = new HashMap<>();
//        map.put("userName", "testorder");
//        map.put("passWord", "ee93def9b34879325edeb6de840a0d24");
//        map.put("hospitalSubId",storeId);
//        return map;
//    }
//
//
//
//
//}
