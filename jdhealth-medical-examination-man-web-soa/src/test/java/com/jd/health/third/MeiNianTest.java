package com.jd.health.third;

import com.google.common.collect.Maps;
import com.jd.health.medical.examination.common.constant.CommonConstant;
import com.jd.health.medical.examination.common.constant.UmpKeyEnum;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.common.errorcode.RpcResultErrorCode;
import com.jd.health.medical.examination.common.util.MD5Util;
import com.jd.health.medical.examination.common.util.UmpUtil;
import com.jd.health.medical.examination.domain.enterprise.entity.AppointmentLogEntity;
import com.jd.health.medical.examination.domain.third.health100.Health100GoodsEntity;
import com.jd.health.medical.examination.domain.third.health100.Health100StoreEntity;
import com.jd.health.medical.examination.rpc.domain.param.health100.Health100GoodsResult;
import com.jd.health.medical.examination.rpc.domain.param.health100.Health100IsCheckedResult;
import com.jd.health.medical.examination.rpc.domain.param.health100.Health100StoreResult;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/12/1 14:24
 * @Doc
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class MeiNianTest {

    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(MeiNianTest.class);

    /**
     * APP_KEY
     */
    private static final String APP_KEY = "JDQX000888820200222";

    /**
     * SEC_KEY
     */
    private static final String SEC_KEY = "JDQX000888820200222TEST4@)!&22mndjk";

    /**
     * main
     *
     * @param args
     */
    public static void main(String[] args) {

//        getChecked();

//        getReport();

//        getHaveReportResult();

//        getGoodsInfoByAppkey();

        //queryStore("JDQX000888820200222.0.001");

        queryUserAppointInProvider();


    }

    /**
     * queryUserAppointInProvider
     */
    private static void queryUserAppointInProvider() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String appKey = "201720231";
        String password = "e673614c809e4118aeba6a4e7968adfe";
        String idCard = "622627199203132611";
        String name = "黄富鹏";
        String outOrderID = "2508395178721718786";


        String timestamp = sdf.format(new Date());

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(password);
        stringBuffer.append(appKey);
        stringBuffer.append(timestamp);
        String sign = stringBuffer.toString();

        Map<String, String> paramsMap = Maps.newHashMap();
        paramsMap.put("sign", MD5Util.getMD5String(sign));
        paramsMap.put("timestamp", timestamp);
        paramsMap.put("appKey", appKey);
        paramsMap.put("idCard", idCard);
        paramsMap.put("name", name);
        paramsMap.put("outOrderID", outOrderID);
        Map<String, String> headerMap = new HashMap<>(CommonConstant.INIT_COLL_SIZE);
        String result = SimpleHttpClient.simplePost("http://api.health-100.cn/StandardService/api/Reservation/PostQueryServiceBillState", paramsMap, headerMap);
        paramsMap.put("ServiceNumber", "80000202407199141");
        String jsonString = JsonUtil.toJSONString(paramsMap);
        System.out.println("jsonString=" + jsonString);
        String appointInfoResult = SimpleHttpClient.simplePost("http://api.health-100.cn/StandardService/api/Company/PostQueryReservation", paramsMap, headerMap);
        System.out.println(JsonUtil.toJSONString(result));
        System.out.println(JsonUtil.toJSONString(appointInfoResult));
    }

    /**
     * 查询美年套餐数据
     * JDQX000888820200222.0.001
     * JDQX000888820200222.0.002
     * JDQX000888820200222.0.003
     */
    private static void getGoodsInfoByAppkey() {
        String goodsUrl = "http://test.health-100.cn:20280/api/Reservation/PostQueryPackageCodeList";
        try {
            log.info("SyncGoodsStoreServiceRpcImpl -> getGoodsInfoByAppkey start");
            Map<String, String> paramsMap = new HashMap<>(8);
            String result = callMeiNianApi(goodsUrl, APP_KEY, SEC_KEY, paramsMap);
            log.info("SyncGoodsStoreServiceRpcImpl -> getGoodsInfoByAppkey 获取商家套餐返回值为result={} ", result);
            Health100GoodsResult health100GoodsEntity = JsonUtil.parseObject(result, Health100GoodsResult.class);
            if (null == health100GoodsEntity || !CommonConstant.HTTP_STATUS.equals(health100GoodsEntity.getState())) {
                log.error("SyncGoodsStoreServiceRpcImpl -> getGoodsInfoByAppkey exception 取商家套餐返回值为空");
                throw new BusinessException(RpcResultErrorCode.buildDefaultErrorResult());
            }
            List<Health100GoodsEntity> health100GoodsEntities = health100GoodsEntity.getResult();
            if (CollectionUtil.isEmpty(health100GoodsEntities)) {
                log.error("SyncGoodsStoreServiceRpcImpl -> getGoodsInfoByAppkey exception 取商家套餐成功无套餐");
            }
            System.out.println("success:" + JsonUtil.toJSONString(health100GoodsEntities));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询是否到检
     */
    private static void getChecked() {
        AppointmentLogEntity appointmentLogEntity = new AppointmentLogEntity();
        appointmentLogEntity.setAppointmentNo("80000202210110447");
        appointmentLogEntity.setAppKey("201014635");
        appointmentLogEntity.setAppSecret("d187f06d531f4665becfeba6d16dc97c");
        String appointmentCheckedUrl = "http://api.health-100.cn/StandardService/api/Reservation/PostQueryIsChecked";
        try {
            log.info("Health100AppointmentServiceRpcImpl -> getCheckedResult start,获取美年是否到检开始 appointmentLogEntity={}", appointmentLogEntity);
            if (StringUtil.isEmpty(appointmentLogEntity.getAppKey()) || StringUtil.isEmpty(appointmentLogEntity.getAppSecret())) {
                log.warn("Health100AppointmentServiceRpcImpl -> getCheckedResult appKey和appSecret为空");
                throw new BusinessException(BaseErrorCode.ILLEGAL_ARG_ERROR);
            }
            Map<String, String> paramsMap = new HashMap<>(8);
            paramsMap.put("serviceNumber", appointmentLogEntity.getAppointmentNo());
            String result = callMeiNianApi(appointmentCheckedUrl, appointmentLogEntity.getAppKey(), appointmentLogEntity.getAppSecret(), paramsMap);
            Health100IsCheckedResult health100IsCheckedResult = JsonUtil.parseObject(result, Health100IsCheckedResult.class);
            if (!CommonConstant.HTTP_STATUS.equals(health100IsCheckedResult.getState())) {
                UmpUtil.showWarnMsg(UmpKeyEnum.HEALTH100_CHECKED_RESULT_RPC_UMP_KEY, health100IsCheckedResult.getMessage());
                log.error("Health100AppointmentServiceRpcImpl -> getCheckedResult exception,获取美年是否到检返回为失败");
            }
            Map<String, Object> map = JsonUtil.parseObject(health100IsCheckedResult.getResult(), Map.class);
            Boolean isChecked = (Boolean) map.get("isChecked");
            System.out.println("success:" + isChecked);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询美年门店数据
     */
    private static void queryStore(String goodsId) {
        String storeUrl = "http://test.health-100.cn:20280/api/Reservation/PostQueryCheckUnitListByPackageCode";
        try {
            log.info("Health100AppointmentServiceRpcImpl -> getStoreInfoByGoodsId start,获取美年门店");
            if (StringUtil.isEmpty(APP_KEY) || StringUtil.isEmpty(SEC_KEY)) {
                log.warn("Health100AppointmentServiceRpcImpl -> getStoreInfoByGoodsId appKey和appSecret为空");
                throw new BusinessException(BaseErrorCode.ILLEGAL_ARG_ERROR);
            }
            Map<String, String> paramsMap = new HashMap<>(8);
            paramsMap.put("PackageCode", goodsId);
            String result = callMeiNianApi(storeUrl, APP_KEY, SEC_KEY, paramsMap);
            log.info("SyncGoodsStoreServiceRpcImpl -> getStoreInfoByGoodsId 获取套餐对应门店返回值为result={} ", result);
            Health100StoreResult health100StoreResult = JsonUtil.parseObject(result, Health100StoreResult.class);
            if (null == health100StoreResult || !CommonConstant.HTTP_STATUS.equals(health100StoreResult.getState())) {
                log.error("SyncGoodsStoreServiceRpcImpl -> getStoreInfoByGoodsId exception 获取套餐对应门店返回值为空");
                throw new BusinessException(RpcResultErrorCode.buildDefaultErrorResult());
            }
            List<Health100StoreEntity> health100StoreEntities = health100StoreResult.getResult();
            if (CollectionUtil.isEmpty(health100StoreEntities)) {
                log.error("SyncGoodsStoreServiceRpcImpl -> getStoreInfoByGoodsId exception 获取套餐对应门店返回值为空");
            }
            System.out.println("success:" + JsonUtil.toJSONString(health100StoreEntities));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询报告
     */
    public static void getReport() {
        AppointmentLogEntity appointmentLogEntity = new AppointmentLogEntity();
        appointmentLogEntity.setAppointmentNo("80000202012127657");
        appointmentLogEntity.setAppKey(APP_KEY);
        appointmentLogEntity.setAppSecret(SEC_KEY);
        String url = "http://api.health-100.cn/StandardService/api/Report/PostQueryReport";
        try {
            log.info("Health100AppointmentServiceRpcImpl -> getReport start,获取美年报告开始 appointmentLogEntity={}", appointmentLogEntity);
            if (StringUtil.isEmpty(appointmentLogEntity.getAppKey()) || StringUtil.isEmpty(appointmentLogEntity.getAppSecret())) {
                log.warn("Health100AppointmentServiceRpcImpl -> getReport appKey和appSecret为空");
                throw new BusinessException(BaseErrorCode.ILLEGAL_ARG_ERROR);
            }
            Map<String, String> paramsMap = new HashMap<>(8);
            paramsMap.put("serviceNumber", appointmentLogEntity.getAppointmentNo());
            String result = callMeiNianApi(url, appointmentLogEntity.getAppKey(), appointmentLogEntity.getAppSecret(), paramsMap);
            log.info("Health100AppointmentServiceRpcImpl -> getReport ,获取美年报告开始 出参 result={}", result);
            if (StringUtil.isBlank(result)) {
                log.error("Health100AppointmentServiceRpcImpl -> getReport exception, 获取美年报告开始返回为空");
                throw new BusinessException(RpcResultErrorCode.buildDefaultErrorResult());
            }
            Health100IsCheckedResult health100IsCheckedResult = JsonUtil.parseObject(result, Health100IsCheckedResult.class);
            if (!CommonConstant.HTTP_STATUS.equals(health100IsCheckedResult.getState())) {
                UmpUtil.showWarnMsg(UmpKeyEnum.HEALTH100_CHECKED_RESULT_RPC_UMP_KEY, health100IsCheckedResult.getMessage());
                log.error("Health100AppointmentServiceRpcImpl -> getReport exception,获取美年报告开始返回为失败");
            }
            System.out.println(JsonUtil.toJSONString(health100IsCheckedResult));
            Map<String, String> map = JsonUtil.parseObject(health100IsCheckedResult.getResult(), Map.class);
            String report = map.get("PDFBase64String");
            System.out.println(report);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 获取已到检用户是否出报告
     * @return
     */
    public static Boolean getHaveReportResult() {
        AppointmentLogEntity appointmentLogEntity = buildParam("80000202012127657");
        try {
            log.info("Health100AppointmentServiceRpcImpl -> getHaveReportResult start, 美年-获取用户是否已出报告,appointmentLogEntity={}", JsonUtil.toJSONString(appointmentLogEntity));
            if (StringUtil.isEmpty(appointmentLogEntity.getAppKey()) || StringUtil.isEmpty(appointmentLogEntity.getAppSecret())) {
                log.error("Health100AppointmentServiceRpcImpl -> getHaveReportResult appKey和appSecret为空");
                throw new BusinessException(BaseErrorCode.ILLEGAL_ARG_ERROR);
            }
            String appointmentCheckReportUrl = "http://api.health-100.cn/StandardService/api/Reservation/PostQueryIsCreateReport";
            Map<String, String> paramsMap = new HashMap<>(8);
            paramsMap.put("serviceNumber", appointmentLogEntity.getAppointmentNo());
            String result = callMeiNianApi(appointmentCheckReportUrl, appointmentLogEntity.getAppKey(), appointmentLogEntity.getAppSecret(), paramsMap);
            Map<String, Boolean> map = JsonUtil.parseObject(result, Map.class);
            String isCreateReport = "isCreateReport";
            if (null != map.get(isCreateReport)) {
                log.info("Health100AppointmentServiceRpcImpl -> getHaveReportResultNew end,jdId={}", appointmentLogEntity.getJdAppointmentId());
                return map.get("isCreateReport");
            }
            return Boolean.FALSE;
        } catch (BusinessException e) {
            log.error("Health100AppointmentServiceRpcImpl -> getHaveReportResult,美年-获取用户是否已出报告业务异常 e={}", e);
            UmpUtil.showWarnMsg(UmpKeyEnum.HEALTH100_GET_PDF_RPC_UMP_KEY, e.getErrorCode());
            return Boolean.FALSE;
        } catch (Throwable e) {
            UmpUtil.showWarnMsg(UmpKeyEnum.HEALTH100_GET_PDF_RPC_UMP_KEY, e.getMessage());
            log.error("Health100AppointmentServiceRpcImpl -> getHaveReportResult,美年-获取用户是否已出报告运行异常 e={}", e);
            return Boolean.FALSE;
        }
    }

    /**
     * callMeiNianApi
     *
     * @param url
     * @param appKey
     * @param appSecret
     * @return
     */
    public static String callMeiNianApi(String url, String appKey, String appSecret, Map<String, String> paramsMap) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String sign = buildSign(timestamp, appKey, appSecret);
        paramsMap.put("appKey", appKey);
        paramsMap.put("timestamp", timestamp);
        paramsMap.put("sign", MD5Util.getMD5String(sign));
        log.info("MeiNianTest -> callMeiNianApi start, param={}", paramsMap);
        Map<String, String> headerMap = new HashMap<>(CommonConstant.INIT_COLL_SIZE);
        String result = SimpleHttpClient.simplePost(url, paramsMap, headerMap);
        log.info("MeiNianTest -> callMeiNianApi end, result={}", result);
        if (StringUtil.isBlank(result)) {
            log.error("Health100AppointmentServiceRpcImpl -> getHaveReportResult exception,  美年-接口调用返回为空");
            throw new BusinessException(BaseErrorCode.RPC_CALL_RESULT_ERROR);
        }
        return result;
    }

    /**
     * buildParam
     * @param appointNo
     * @return
     */
    private static AppointmentLogEntity buildParam(String appointNo) {
        AppointmentLogEntity appointmentLogEntity = new AppointmentLogEntity();
        appointmentLogEntity.setAppKey(APP_KEY);
        appointmentLogEntity.setAppSecret(SEC_KEY);
        appointmentLogEntity.setAppointmentNo(appointNo);
        return appointmentLogEntity;
    }

    /**
     * 组装认证
     *
     * @param timestamp
     * @param appKey
     * @param password
     * @return
     */
    private static String buildSign(String timestamp, String appKey, String password) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(password);
        stringBuffer.append(appKey);
        stringBuffer.append(timestamp);
        return stringBuffer.toString();
    }

    @Test
    public void test() {


    }

}
