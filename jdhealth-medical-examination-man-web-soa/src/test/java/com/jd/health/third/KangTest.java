package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.rpc.domain.param.kangkang.KangParamUtil;
import com.jd.medicine.base.common.util.JsonUtil;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/3 11:43
 * @Doc
 **/
public class KangTest {

    public static final String aesKey = "6865616C746863617265323031393132";

    public static void main(String[] args) throws Exception {

        appoint();

    }

    private static void appoint() throws Exception {
        String param = "{\"appointmentDate\":\"2020-07-23\",\"channelType\":**********,\"credentialNo\":\"140729199209220120\",\"credentialType\":1,\"goodsId\":\"TC201510100925420001\",\"jdAppointmentId\":1491517776827779074,\"storeId\":\"YY201510100923150001\",\"userBirth\":\"1992-09-22\",\"userGender\":2,\"userMarriage\":2,\"userName\":\"银陪你\",\"userPhone\":\"16619887367\"}";
        AppointmentParam appointmentParam = JsonUtil.parseObject(param,AppointmentParam.class);
        param = KangParamUtil.bulidKangAppointParam(appointmentParam);
        String secStr = Aes.encryptData(param,aesKey);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("secStr",secStr);
        System.out.println("AppointKangRpcImpl -> appoint, 康康预约调用 param="+JsonUtil.toJSONString(jsonObject));
    }

}
