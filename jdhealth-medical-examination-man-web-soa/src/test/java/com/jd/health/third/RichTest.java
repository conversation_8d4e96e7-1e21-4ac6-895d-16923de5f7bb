package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.constant.AppointApiConstant;
import com.jd.health.medical.examination.common.enums.CredentialEnum;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.factory.ExecutorPoolFactory;
import com.jd.health.medical.examination.common.factory.ThreadPoolConfigEnum;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.rpc.domain.param.rich.RichAppInfo;
import com.jd.health.medical.examination.rpc.domain.param.rich.RichResult;
import com.jd.health.medical.examination.rpc.domain.param.rich.RichTokenInfo;
import com.jd.health.medical.examination.service.AppointmentApiService;
import com.jd.health.medical.examination.service.util.GenerateIdService;
import com.jd.medicine.base.common.spring.service.JimRedisService;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/7 10:37
 * @Doc
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class RichTest {

    private static final Logger log = LoggerFactory.getLogger(RichTest.class);


    @Autowired
    AppointmentApiService appointmentApiService;

    /**
     * 线程池
     */
    @Autowired
    ExecutorPoolFactory executorPoolFactory;

    @Autowired
    GenerateIdService generateIdService;

    @Autowired
    JimRedisService jimRedisService;

    @Test
    public void testAppiontBatch() throws InterruptedException {
        for (int i = 0; i < 1; i++) {

            executorPoolFactory.get(ThreadPoolConfigEnum.APPOINT_CAP_THREAD_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Long jdId = generateIdService.getId();
                        long start = System.currentTimeMillis();
                        AppointmentParam appointmentParam = new AppointmentParam();
                        appointmentParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
                        appointmentParam.setJdAppointmentId(jdId);
                        appointmentParam.setUserPhone("16619887367");
                        appointmentParam.setUserName("京东预约测试");
                        appointmentParam.setGoodsId("822492680906084352");
                        appointmentParam.setStoreId("511");
                        appointmentParam.setCredentialType(CredentialEnum.CID.getType());
                        appointmentParam.setCredentialNo("430523198809020075");
                        appointmentParam.setUserMarriage(MarryTypeEnum.YES.getType());
                        appointmentParam.setUserGender(GenderEnum.MAN.getType());
                        appointmentParam.setAppointmentDate("2020-07-28");
                        appointmentParam.setUserBirth("1988-09-02");
                        appointmentApiService.appoint(appointmentParam);
                        System.out.println("jdId:"+jdId+",cost:"+(System.currentTimeMillis()-start));
                    } catch (Exception e) {
                        System.out.println("error="+e.getMessage());
                    }
                }
            });
        }
        TimeUnit.SECONDS.sleep(10);
    }

    @Test
    public void getRichToken() {
        String richIdentityID = "JingYiYue";
        String richGetTokenUrl = "https://api-test.rich-healthcare.com/open/api/GetToken";
        synchronized (this) {
            RichAppInfo richAppInfo = new RichAppInfo();
            //从缓存获取token
            String token = jimRedisService.get(AppointApiConstant.RICH_TOKEN_PREFIX + richIdentityID);
            if (StringUtil.isNotEmpty(token)) {
                richAppInfo.setIdentityId("JingYiYue");
                richAppInfo.setToken(token);
                System.out.println("get from cache");
                return;
            }
            JSONObject param = new JSONObject();
            param.put("identityID", richIdentityID);
            param.put("passWord", "123456");
            param.put("macAddress", "9C-DA-3E-A2-A2-9C");
            try {
                String result = HttpSimpleClient.simplePost(richGetTokenUrl, param.toJSONString());
                RichResult<RichTokenInfo> richResult = JsonUtil.parseObject(result, RichResult.class);
                log.info("richResult:"+JsonUtil.toJSONString(richResult));
                if ("0000000".equals(richResult.getResultCode())) {
                    RichTokenInfo richTokenInfo = JsonUtil.parseObject(JsonUtil.toJSONString(richResult.getData()), RichTokenInfo.class);
                    richAppInfo.setIdentityId(richIdentityID);
                    richAppInfo.setToken(richTokenInfo.getTokenValue());
                    Long cacheTime = DateUtil.parseDateWithPatterns(richTokenInfo.getExpiredDate()).getTime() - System.currentTimeMillis();
                    jimRedisService.setEx(AppointApiConstant.RICH_TOKEN_PREFIX + richIdentityID, richTokenInfo.getTokenValue(), cacheTime, TimeUnit.MILLISECONDS);
                    return;
                } else {
                    jimRedisService.del(AppointApiConstant.RICH_TOKEN_PREFIX + richIdentityID);
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

}
