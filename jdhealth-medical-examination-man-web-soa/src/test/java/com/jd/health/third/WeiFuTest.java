package com.jd.health.third;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.CredentialEnum;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdAppointmentParam;
import com.jd.health.medical.examination.export.service.AppointmentApiExportService;
import com.jd.health.medical.examination.rpc.factory.AppointTemplate;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 卫服商家接口测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/3 15:31
 */

public class WeiFuTest extends BaseTest {
    /**
     * appointmentApiExportService
     */
    @Autowired
    private AppointmentApiExportService appointmentApiExportService;

    /**
     * appointWeiFuRpc
     */
    @Autowired
    private AppointTemplate appointWeiFuRpc;
    /**
     *
     */
    private static final Long channelNo = 2305926764L;

    @Test
    public void appointTest(){
        AppointmentParam param = new AppointmentParam();
        /**
         * storeId,goodsId,channelType,userName,userPhone,userGender,userMarriage,credentialNo,credentialType
         */
        param.setGoodsId("A001");
        param.setUserName("京东测试");
        param.setUserPhone("18842626795");
        param.setCredentialType(CredentialEnum.CID.getType());
        param.setCredentialNo("130203199207070058");
        param.setUserBirth("1992-07-07");
        param.setUserGender(GenderEnum.MAN.getType());
        param.setUserMarriage(MarryTypeEnum.NO.getType());
        param.setStoreId("001");
        param.setAppointmentDate("2020-09-27");
        param.setJdAppointmentId(2222L);
        param.setChannelType(channelNo);
        JsfResult<Boolean> appointment = appointmentApiExportService.appointment(param);
        System.out.println(JsonUtil.toJSONString(appointment));
    }
    @Test
    public void cancelAppointTest(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(2222L);
        appointmentParam.setAppointmentNo("47");
        appointmentParam.setChannelType(channelNo);
        appointmentApiExportService.cancelAppointment(appointmentParam);
    }

    @Test
    public void updateAppointTest(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(2222L);
        appointmentParam.setAppointmentNo("47");
        appointmentParam.setAppointmentDate("2020-09-22");
        appointmentParam.setStoreId("001");
        appointmentParam.setChannelType(channelNo);
        appointmentApiExportService.modifyAppointment(appointmentParam);
    }

    @Test
    public void findAppointDateTest(){
        AppointmentStoreCapParam param = new AppointmentStoreCapParam();
        param.setStoreId("001");
        param.setGoodsId("A001");
        param.setChannelNo(channelNo);
        appointmentApiExportService.findAppointmentDate(param);
    }
    @Test
    public void getReport(){
        ThirdAppointmentParam thirdAppointmentParam = new ThirdAppointmentParam();
        thirdAppointmentParam.setReportId("12344");
        thirdAppointmentParam.setChannelType(2305926764L);
        thirdAppointmentParam.setAppointmentNo("48");
        thirdAppointmentParam.setJdAppointmentId(2222L);
        appointWeiFuRpc.getReport(thirdAppointmentParam);
    }
}
