package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.CredentialEnum;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdAppointmentParam;
import com.jd.health.medical.examination.export.service.AppointmentApiExportService;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.health.medical.examination.rpc.factory.AppointTemplate;
import com.jd.health.medical.examination.service.AppointmentApiService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * 橙意健康测试类
 *
 * <AUTHOR>
 * @date 2021/04/19
 */
public class ChengYiTest extends BaseTest {
	
	/**
	 * appointWeiFuRpc
	 */
	@Autowired
	private AppointTemplate appointChengYiRpc;
	/**
	 *
	 */
	private static final Long channelNo = 1101876710L;
	
	@Test
	public void appointTest(){
		AppointmentParam param = new AppointmentParam();
		/**
		 * storeId,goodsId,channelType,userName,userPhone,userGender,userMarriage,credentialNo,credentialType
		 */
		param.setGoodsId("gtest002");
		param.setUserName("京东测试");
		param.setUserPhone("18210595327");
		param.setCredentialType(CredentialEnum.CID.getType());
		param.setCredentialNo("130203199207070058");
		param.setUserBirth("1992-07-07");
		param.setUserGender(GenderEnum.MAN.getType());
		param.setUserMarriage(MarryTypeEnum.NO.getType());
		param.setStoreId("test001");
		param.setAppointmentDate("2021-05-11");
		param.setJdAppointmentId(210421001L);
		param.setChannelType(channelNo);
		Boolean appointment = appointChengYiRpc.appoint(param);
		System.out.println("返回结果:" + JsonUtil.toJSONString(appointment));
	}
	@Test
	public void cancelAppointTest(){
		AppointmentParam appointmentParam = new AppointmentParam();
		appointmentParam.setJdAppointmentId(1111L);
		appointmentParam.setAppointmentNo("2021041949975397");
		appointmentParam.setChannelType(channelNo);
		Boolean appointment = appointChengYiRpc.cancelAppoint(appointmentParam);
		System.out.println("返回结果:" + JsonUtil.toJSONString(appointment));
	}
	
	@Test
	public void updateAppointTest(){
		AppointmentParam appointmentParam = new AppointmentParam();
		appointmentParam.setJdAppointmentId(1111L);
		appointmentParam.setAppointmentNo("2021041949975397");
		appointmentParam.setAppointmentDate("2021-05-01");
		appointmentParam.setStoreId("test001");
		appointmentParam.setChannelType(channelNo);
		Boolean appointment = appointChengYiRpc.updateAppoint(appointmentParam);
		System.out.println("返回结果:" + JsonUtil.toJSONString(appointment));
	}
	
	@Test
	public void findAppointDateTest(){
		AppointmentStoreCapParam param = new AppointmentStoreCapParam();
		param.setStoreId("test001");
		param.setGoodsId("gtest002");
		param.setChannelNo(channelNo);
		List<AppointDateDTO> appointment = appointChengYiRpc.getAppointDateByStore(param);
		System.out.println("返回结果:" + JsonUtil.toJSONString(appointment));
	}
	@Test
	public void getReport(){
		ThirdAppointmentParam thirdAppointmentParam = new ThirdAppointmentParam();
		thirdAppointmentParam.setReportId("report6075b9830163e");
		thirdAppointmentParam.setChannelType(channelNo);
		thirdAppointmentParam.setAppointmentNo("2021040699521025");
		thirdAppointmentParam.setJdAppointmentId(2222L);
		String appointment = appointChengYiRpc.getReport(thirdAppointmentParam);
		System.out.println("返回结果:" + JsonUtil.toJSONString(appointment));
	}
}
