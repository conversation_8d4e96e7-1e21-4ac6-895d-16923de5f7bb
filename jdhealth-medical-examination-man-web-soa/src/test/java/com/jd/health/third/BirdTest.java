package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.enums.AppointStoreStatusEnum;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdCheckReportParam;
import com.jd.health.medical.examination.rpc.domain.param.bird.BirdReportResult;
import com.jd.health.medical.examination.rpc.domain.param.bird.BirdResult;
import com.jd.health.medical.examination.rpc.domain.param.bird.BirdStoreCapResult;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.*;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.junit.Test;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.ParseException;
import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/7/10 9:36
 * @Doc
 **/
public class BirdTest {
    //私钥
    private static final String TOKEN_SECRET =
            "MIICXAIBAAKBgQC64TVaIuy83/LMrrsMW6Jq0QOxYP7VAOrh6JR0UegGxXQvE5b2\n" +
                    "29YKvJLKXwnPjQDgMKXaT72ojD5k2OYWDLpR0MYbVs/0g4/qaTRPqw9ucO4VpiCM\n" +
                    "K4ScPl7ASR9qHlk8rf7SdTiZQnYS+3vAYl57jKmtYM4dcuIGj4c7an10IwIDAQAB\n" +
                    "AoGACu+Ayrx+c/L650VWRrvZ/jwq5idR4i8Cj6OnhEMCu9q0GMkhrWJh3RD52gPD\n" +
                    "tDSBF51hJyxBYeqLACW9GQ83m9Y4CWpx+QxwJvkll9e7wBVvTCF/Ba3o4GW7E228\n" +
                    "AOC+f+ahh9UMleydCk/aViGzsQqgDyJ3i8xAwj/+w6MZADkCQQD4fczRxB8C3Osu\n" +
                    "VEP3zMVqevIXWi9OHtLcwTyQJKoNk29Jdgu+NOjsUZ0KwmaV/PDuaHk4BrrvMsAo\n" +
                    "Dl9fRCjvAkEAwIbP/U9sI11CB8VYT8IJzjfB2CB355LkPhIfVn02b7zhiyfix4qZ\n" +
                    "flSYU1ew+dodlqNY+N7/dyoigda9WK6gDQJBAJVh2w2HnzTzZhaV5b5ERbG7c2oC\n" +
                    "d0m5Uf4P1L66q9g5d79rzads21Glxoi/UTh9tME94RGDSkGd+tKZwU/eA9UCQFxO\n" +
                    "Blg/hOwroSXSovWSZVsedmylnHdFgPKVg7rWy4uk9knedKcQt2jPLD0BFROlVHxN\n" +
                    "krG3UKoUZHj1uNBU4N0CQB6a08Z4gDsEqzqnQu285A/fA9gQdHY5ZSF03gb3k4mA\n" +
                    "J9JF3APBZ9O7pi5/vKBD0EylqFWxqT00q8HZpV68AUg=\n";


    public static void main(String[] args) throws Exception {
        getAppointDate();

//        getToken();

//        appoint();

//        updateAppoint();

//        cancelAppoint();

//        getReport();
    }

    private static void appoint() throws Exception {
        String appointUrl = "https://jd.xiaoniaokuaiyan.com/v1/r/jd/appintment";
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(System.currentTimeMillis());
        appointmentParam.setGoodsId("testgoodsid");
        appointmentParam.setStoreId("teststoreid");
        appointmentParam.setAppointmentDate("2020-07-15");
        appointmentParam.setUserName("京东测试");
        appointmentParam.setUserPhone("16619887367");
        appointmentParam.setUserGender(1);
        appointmentParam.setUserBirth("2002-01-01");
        appointmentParam.setCredentialType(1);
        appointmentParam.setCredentialNo("110101200201017137");
        appointmentParam.setUserMarriage(1);
        System.out.println("param:" + JsonUtil.toJSONString(appointmentParam));
        String secStr = Aes.encryptData(JsonUtil.toJSONString(appointmentParam), "JDH#Bird12345678");
        System.out.println("secStr:" + secStr);
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("secStr", secStr);
        String result = HttpSimpleClient.simplePost(appointUrl, getHeaderMap(), JsonUtil.toJSONString(paramMap));
        System.out.println("appoint result : " + result);
    }

    private static void updateAppoint() {
        String appointUrl = "https://jd.xiaoniaokuaiyan.com/v1/r/jd/updateappointment";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appointmentNo","JD20071016401804856");
        jsonObject.put("jdAppointmentId",1594370416703L);
        jsonObject.put("storeId","teststoreid");
        jsonObject.put("appointmentDate","2020-07-16");
        System.out.println("param:"+jsonObject.toJSONString());
        String result = HttpSimpleClient.simplePost(appointUrl,getHeaderMap(),jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
    }

    private static void cancelAppoint() {
        String url = "https://jd.xiaoniaokuaiyan.com/v1/r/jd/cancelappointment";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appointmentNo","JD20071016401804856");
        jsonObject.put("jdAppointmentId",1594370416703L);
        System.out.println("param:"+jsonObject.toJSONString());
        String result = HttpSimpleClient.simplePost(url,getHeaderMap(),jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
    }

    private static void getReport() {
        String url = "https://jd.xiaoniaokuaiyan.com/v1/r/jd/downloadreportinfo";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appointmentNo","JD20071514093558366");
        jsonObject.put("reportId","JD20071514093558366");
        String result = HttpSimpleClient.simplePostReport(url,getHeaderMap(),jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
        BirdResult reportResult = JsonUtil.parseObject(result, BirdResult.class);
        if ("0".equals(reportResult.getCode()) && null != reportResult.getData()) {
            BirdReportResult birdReportResult = JsonUtil.parseObject(reportResult.getData().toString(),BirdReportResult.class);
            String reportStr = birdReportResult.getReportStr();
            //返回报告String
            System.out.println(reportStr);
        }
    }

    public static void getAppointDate() {
        String dateUrl = "https://jd.xiaoniaokuaiyan.com/v1/r/jd/getstoreavailableinfo";
        AppointmentStoreCapParam appointmentStoreCapParam = new AppointmentStoreCapParam();
        appointmentStoreCapParam.setChannelNo(ProviderTypeEnum.BIRD.getTypeNo());
        appointmentStoreCapParam.setStoreId("8c8fc81c5864caef09ee6eeceb0d5606");
        appointmentStoreCapParam.setGoodsId("47d1e990583c9c67424d369f3414728e");
        System.out.println("param:" + buildStoreCapParam(appointmentStoreCapParam));
        String result = HttpSimpleClient.simplePost(dateUrl, getHeaderMap(), buildStoreCapParam(appointmentStoreCapParam));
        BirdResult<List<BirdStoreCapResult>> birdResult = JsonUtil.parseObject(result, BirdResult.class);
        System.out.println(result);
        if ("0".equals(birdResult.getCode())) {
            List<BirdStoreCapResult> birdStoreCapResults = JsonUtil.parseArray(birdResult.getData().toString(), BirdStoreCapResult.class);
            if(CollectionUtil.isNotEmpty(birdStoreCapResults)) {
                List<AppointDateDTO> dateDTOList = buildAppointDateResult(appointmentStoreCapParam, birdStoreCapResults);
                System.out.println(JsonUtil.toJSONString(dateDTOList));
            }
        }
    }

    /**
     * 预约日历入参
     * @param appointmentStoreCapParam
     * @return
     */
    private static String buildStoreCapParam(AppointmentStoreCapParam appointmentStoreCapParam) {
        HashMap<String,String> paramMap = new HashMap<>();
        paramMap.put("storeId",appointmentStoreCapParam.getStoreId());
        paramMap.put("goodsId",appointmentStoreCapParam.getGoodsId());
        paramMap.put("startTime", DateUtil.formatDate(DateUtil.addDays(new Date(),1),"yyyy-MM-dd"));
        paramMap.put("endTime",DateUtil.formatDate(DateUtil.addDays(new Date(),60),"yyyy-MM-dd"));
        return JsonUtil.toJSONString(paramMap);
    }

    /**
     * 组装小鸟快验预约日历列表
     *
     * @param birdStoreCapResults
     * @return
     */
    private static List<AppointDateDTO> buildAppointDateResult(AppointmentStoreCapParam appointmentStoreCapParam, List<BirdStoreCapResult> birdStoreCapResults){
        List<AppointDateDTO> list = new ArrayList<>();
        if(CollectionUtil.isEmpty(birdStoreCapResults)){
            return list;
        }
        for(BirdStoreCapResult birdStoreCapResult : birdStoreCapResults){
            AppointDateDTO appointDateDTO = new AppointDateDTO();
            appointDateDTO.setChannelNo(appointmentStoreCapParam.getChannelNo());
            appointDateDTO.setStoreId(appointmentStoreCapParam.getStoreId());
            try {
                appointDateDTO.setDate(DateUtil.formatDate(DateUtil.parseDate(birdStoreCapResult.getDate(),"yyyy-MM-dd"),"yyyy-MM-dd"));
            } catch (ParseException e) {
                throw new BusinessException(BaseErrorCode.APPOINT_PARAM_APPOINT_DATE_NULL_ERROR);
            }
            //判断是否可预约
            Integer maxNum = birdStoreCapResult.getMaxCap();
            Integer usedNum = birdStoreCapResult.getUsedCap();
            if(usedNum >= maxNum ){
                appointDateDTO.setNum(0);
                appointDateDTO.setStatus(AppointStoreStatusEnum.MAX.getCode());
            }else {
                appointDateDTO.setNum(maxNum - usedNum);
                appointDateDTO.setStatus(AppointStoreStatusEnum.ON.getCode());
            }
            list.add(appointDateDTO);
        }
        return list;
    }

    private static HashMap<String, String> getHeaderMap() {
        String auth = getToken();
        HashMap<String, String> headMap = new HashMap<>();
        headMap.put("Authorization", "Bearer " + auth);
        return headMap;
    }

    public static String getToken() {
        String url = "https://jd.xiaoniaokuaiyan.com/v1/auth/token";
        String outhStr = "grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=" + sign();
        String result = HttpSimpleClient.simplePost(url, outhStr);
        JSONObject jsonObject = JsonUtil.parseObject(result);
        String token = jsonObject.getString("data");
        System.out.println("token:"+token);
        if (StringUtil.isNotEmpty(token)) {
            return token;
        }
        throw new BusinessException(BaseErrorCode.RICH_TOKEN_ERROR);
//        return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.VBiqenOUs_GjBxPqxTa5HV7ZnymbyJufAYLZWqDi8Qg";
    }

    /**
     * 生成签名，15分钟过期
     *
     * @param **username**
     * @param **password**
     * @return
     */
    public static String sign() {
        try {
            //TODO 需要缓存23小时，然后进行异步刷新

            // 设置过期时间
            Date date = new Date(System.currentTimeMillis() - 10 * 1000);
            // 私钥和加密算法
            SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.RS256;
            // 设置头部信息
            Map<String, Object> header = new HashMap<>(2);
            header.put("Type", "Jwt");
            header.put("alg", "RS256");
            // 返回token字符串
            JwtBuilder builder = Jwts.builder()
                    .setAudience("api.xiaoniaokuaiyan.com/auth/token")
                    .setHeader(header)
                    .setIssuer("jingdong")
                    .setIssuedAt(new Date())
                    .setNotBefore(date)
                    .signWith(signatureAlgorithm, getPrivateKey(TOKEN_SECRET));
            return builder.compact();
        } catch (Exception e) {
            throw new BusinessException(BaseErrorCode.RICH_TOKEN_ERROR);
        }
    }

    /**
     *      * 获取密钥
     *      *
     *      * @param bytes 私钥的字节形式
     *      * @return
     *      * @throws Exception
     *      
     */
    public static PrivateKey getPrivateKey(String privateKey) throws Exception {
        byte[] keyBytes = EncodeUtil.decodeBase64(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
        return priKey;
    }

//    public String encode() {
//        String retStr = null;
//
//        Claims claims = Jwts.claims();
//        claims.put("issuer", "14a2fecb-ddd7-4823-a9cc-67515bc01734");
//        claims.put("scope", "signature");
//        claims.put("subject", "13f7982d-1f78-46e2-a843-3273568fce89");
//        claims.put("audience", "account-d.docusign.com");
//
//        // strip the headers
//        String privateKey = TOKEN_SECRET.replace("-----BEGIN RSA PRIVATE KEY-----", "");
//        privateKey = privateKey.replace("-----END RSA PRIVATE KEY-----", "");
//        privateKey = privateKey.replaceAll("\\s+", "");
//
//        byte[] encodedKey = android.util.Base64.decode(this.privateKey, android.util.Base64.DEFAULT);
//        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encodedKey);
//
//        try {
//
//            KeyFactory kf = KeyFactory.getInstance("RSA");
//            PrivateKey privKey = kf.generatePrivate(keySpec);
//
//            retStr = Jwts.builder().setClaims(claims).signWith(privKey).compact();
//
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (InvalidKeySpecException e) {
//            e.printStackTrace();
//        }
//        return retStr;
//    }

    @Test
    public void getCheckReport(){
        ThirdCheckReportParam thirdCheckReportParam = new ThirdCheckReportParam();
        getCheckReport(thirdCheckReportParam);
    }

    private static void getCheckReport(ThirdCheckReportParam thirdCheckReportParam) {
        String url = "https://jd.xiaoniaokuaiyan.com/v1/r/jd/downloadreportinfo";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appointmentNo","JD20071514093558366");
        jsonObject.put("reportId","JD20071514093558366");
        String result = HttpSimpleClient.simplePostReport(url,getHeaderMap(),jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
        BirdResult reportResult = JsonUtil.parseObject(result, BirdResult.class);
        if ("0".equals(reportResult.getCode()) && null != reportResult.getData()) {
            BirdReportResult birdReportResult = JsonUtil.parseObject(reportResult.getData().toString(),BirdReportResult.class);
            String reportStr = birdReportResult.getReportStr();
            //返回报告String
            System.out.println(reportStr);
        }
    }

}
