package com.jd.health.third;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.param.ThirdGoodsItemParam;
import com.jd.health.medical.examination.export.param.ThirdGoodsParam;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/4/29 9:52
 * @Doc
 **/
public class PushDataTest extends BaseTest {

    @Autowired
    ThirdDataExportService thirdDataExportService;

    /**
     * 商家套餐推送
     */
    @Test
    public void testPushGoods(){

        ThirdGoodsParam thirdGoodsParam = new ThirdGoodsParam();
        thirdGoodsParam.setChannelType(4897879845L);
        thirdGoodsParam.setGoodsFeature("**********");
        thirdGoodsParam.setGoodsId("g12");
        thirdGoodsParam.setGoodsSuitable(2);
        thirdGoodsParam.setGoodsMarry(2);
        thirdGoodsParam.setGoodsPrice(12.21);
        thirdGoodsParam.setVenderId(12L);
        thirdGoodsParam.setStatus(1);
        thirdGoodsParam.setOperateType(2);
        thirdGoodsParam.setGoodsName("gname222");
//        List<ThirdGoodsItemParam> itemList = new ArrayList<>();
//        ThirdGoodsItemParam thirdGoodsItemParam = new ThirdGoodsItemParam();
//        thirdGoodsItemParam.setGoodsId2("g123");
//        thirdGoodsItemParam.setItemName("itemname2");
//        List<String> list = new ArrayList();
//        list.add("1");
//        thirdGoodsItemParam.setItemSuitable2(list);
//        itemList.add(thirdGoodsItemParam);
//        thirdGoodsParam.setGoodsItemList(itemList);
        JsfResult jsfResult = thirdDataExportService.pushGoodsInfo(thirdGoodsParam);
        System.out.println(JsonUtil.toJSONString(jsfResult));
    }

}
