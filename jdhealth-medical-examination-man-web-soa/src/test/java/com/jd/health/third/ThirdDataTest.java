package com.jd.health.third;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.constant.RedisConstant;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.enums.SuitableEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataOperateEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataStatusEnum;
import com.jd.health.medical.examination.common.enums.VIPTypeEnum;
import com.jd.health.medical.examination.export.param.ThirdAdditionPackageParam;
import com.jd.health.medical.examination.export.param.ThirdGoodsItemParam;
import com.jd.health.medical.examination.export.param.ThirdGoodsParam;
import com.jd.health.medical.examination.export.param.ThirdGoodsStoreParam;
import com.jd.health.medical.examination.export.param.ThirdPackageItemParam;
import com.jd.health.medical.examination.export.param.ThirdStoreParam;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.spring.service.JimRedisService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/6/23 19:50
 * @Doc
 **/
public class ThirdDataTest extends BaseTest {

    @Autowired
    ThirdDataExportService thirdDataExportService;

    /**
     * 测试推送套餐
     */
    @Test
    public void testPushGoods(){
        ThirdGoodsParam thirdGoodsParam = new ThirdGoodsParam();
        thirdGoodsParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdGoodsParam.setGoodsName("瑞慈-测试套餐");
        thirdGoodsParam.setGoodsId("goodsIdtest123");
        thirdGoodsParam.setGoodsMarry(MarryTypeEnum.YES.getType());
        thirdGoodsParam.setGoodsSuitable(SuitableEnum.MAN.getCode());
        thirdGoodsParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdGoodsParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());
        List<ThirdGoodsItemParam> list =new ArrayList<>();
        ThirdGoodsItemParam param = new ThirdGoodsItemParam();
        param.setItemName("身高-测试");
        param.setGoodsId2("goodsItemid1");
        param.setItemTopCategory("一级项目");
        param.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        param.setItemSuitable2(suitList);
        param.setItemMeans("项目意义");
        list.add(param);
        thirdGoodsParam.setGoodsItemList(list);
        System.out.println("param:" + JsonUtil.toJSONString(thirdGoodsParam));
        JsfResult jsfResult = thirdDataExportService.pushGoodsInfo(thirdGoodsParam);
        System.out.println(JsonUtil.toJSONString(jsfResult));
    }
    
    /**
     * 测试推送加项包
     */
    @Test
    public void testPushAdditionPackage() {
        ThirdAdditionPackageParam param = new ThirdAdditionPackageParam();
        param.setGoodsId("goodsIdtest123");
        param.setPackageId("packageIdtest234");
        param.setPackageName("瑞慈-测试加项包2");
        //param.setPackagePrice();
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setPackageDesc("这是一个瑞慈测试加项包");
        param.setPackageSuitable(SuitableEnum.UNMARRY_WOMEN.getCode());
        param.setPackageMarry(MarryTypeEnum.NO.getType());
        List<ThirdPackageItemParam> list = new ArrayList<>();
        param.setPackageItemList(list);
        ThirdPackageItemParam packageItemParam = new ThirdPackageItemParam();
        packageItemParam.setItemName("体重-测试");
        packageItemParam.setItemMeans("项目意义");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        suitList.add("3");
        packageItemParam.setItemSuitable(suitList);
        packageItemParam.setItemTopCategory("一级项目");
        packageItemParam.setItemSecCategory("二级项目");
        list.add(packageItemParam);
        System.out.println("param:" + JsonUtil.toJSONString(param));
        JsfResult jsfResult = thirdDataExportService.pushAdditionPackage(param);
        System.out.println(JsonUtil.toJSONString(jsfResult));
    }
    
    /**
     * 门店推送测试
     * 1、无绑定sku_store数据
     * {"channelType":**********,"operateType":1,"reportSupport":1,"status":1,"storeAddr":"北京市大兴区京东总部4号楼","storeHours":"7:00-10:00","storeId":"testyt333","storeName":"测试门店","storePhone":"16619887367"}
     */
    @Test
    public void pushStoreInfoTest() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdStoreParam.setStoreId("testyt333");
        thirdStoreParam.setStoreName("测试门店");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("16619887367");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());
        thirdStoreParam.setStoreHours("7:00-10:00");
        thirdStoreParam.setStoreAddr("北京市大兴区京东总部4号楼");
        System.out.println("param:"+JsonUtil.toJSONString(thirdStoreParam));
        //新增上架门店
        JsfResult jsfResult = thirdDataExportService.pushStoreInfo(thirdStoreParam);
        System.out.println(JsonUtil.toJSONString(jsfResult));
        //下架门店
//        thirdStoreParam.setStatus(ThirdDataStatusEnum.OFF.getCode());
//        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
//        thirdDataExportService.pushStoreInfo(thirdStoreParam);
        //门店重新上架
//        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
//        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
//        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }

    /**
     * 门店推送测试
     * 2、有绑定对应的sku_store
     */
    @Test
    public void testPushStoreWithSkuStore() throws InterruptedException {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdStoreParam.setStoreId("testyt123");
        thirdStoreParam.setStoreName("测试门店");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("16619887367");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());
        thirdStoreParam.setStoreHours("7:00-10:00");
        thirdStoreParam.setStoreAddr("北京市大兴区京东总部4号楼");
        //下架门店
        thirdStoreParam.setStatus(ThirdDataStatusEnum.OFF.getCode());
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
        TimeUnit.SECONDS.sleep(20);
        //门店重新上架
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }


    @Autowired
    JimRedisService jimRedisService;
    /**
     * 套餐对应门店关系推送测试
     * {"channelType":**********,"goodsId":"goodsIdtest123","status":1,"storeId":"testyt123"}
     */
    @Test
    public void testPushGoodsStore() {
        String storeId = "37";
        String goodsId = "Jd_Test20200088888.1.001";
        jimRedisService.del(RedisConstant.CHANNEL_STORE_KEY + ProviderTypeEnum.RICH.getTypeNo() + storeId);
        ThirdGoodsStoreParam thirdGoodsStoreParam = new ThirdGoodsStoreParam();
        thirdGoodsStoreParam.setChannelType(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo());
        thirdGoodsStoreParam.setGoodsId(goodsId);
        thirdGoodsStoreParam.setStoreId(storeId);
        thirdGoodsStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdGoodsStoreParam.setVipType(VIPTypeEnum.NORMAL.getType());
        System.out.println("param:" + JsonUtil.toJSONString(thirdGoodsStoreParam));
        //推送上架
        JsfResult jsfResult = thirdDataExportService.pushGoodsStoreInfo(thirdGoodsStoreParam);
        System.out.println(JsonUtil.toJSONString(jsfResult));
        //推送下架
//        thirdGoodsStoreParam.setStatus(ThirdDataStatusEnum.STORE_OFF.getCode());
//        JsfResult jsfResult2 = thirdDataExportService.pushGoodsStoreInfo(thirdGoodsStoreParam);
//        System.out.println(JsonUtil.toJSONString(jsfResult2));
//        //推送重新上架.
//        thirdGoodsStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
//        JsfResult jsfResult3 = thirdDataExportService.pushGoodsStoreInfo(thirdGoodsStoreParam);
//        System.out.println(JsonUtil.toJSONString(jsfResult3));
    }


}
