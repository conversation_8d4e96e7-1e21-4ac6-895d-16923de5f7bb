package com.jd.health.third;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.AppointResultTypeEnum;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.domain.third.ReportAuthEntity;
import com.jd.health.medical.examination.domain.third.ThirdCallBackEntity;
import com.jd.health.medical.examination.rpc.domain.param.ikang.IKangResult;
import com.jd.health.medical.examination.service.common.tools.HealthcareOssService;
import com.jd.health.medical.examination.service.third.ThirdCallBackService;
import com.jd.health.medical.examination.service.third.ThirdReportAuthService;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/6/11 13:07
 * @Doc
 **/
public class ReportJobTest extends BaseTest {


    @Autowired
    ThirdCallBackService thirdCallBackService;

    @Autowired
    HealthcareOssService healthcareOssService;

    @Autowired
    ThirdReportAuthService thirdReportAuthService;


    /**
     * 测试获取报告标识的推送记录
     */
    @Test
    public void testReportJobData(){
        ThirdCallBackEntity thirdCallBackEntity = new ThirdCallBackEntity();
        thirdCallBackEntity.setResultType(AppointResultTypeEnum.REPORT.getType());
        //临时处理最近7天的报告推送记录
        thirdCallBackEntity.setResultDate(DateUtil.addDays(new Date(),-7));
        List<ThirdCallBackEntity> thirdCallBackList = thirdCallBackService.queryThirdCallBackList(thirdCallBackEntity);
        System.out.println(JsonUtil.toJSONString(thirdCallBackList));
        long start = System.currentTimeMillis();
        String reportUrl = "https://api-uat.daoyitong.com/cloud/tailor/jDPhyExam/downloadReport";
        HashMap<String,String> param = new HashMap<>();
        param.put("appointmentNo","1261236025539321856");
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        String result = HttpSimpleClient.simplePostReport(reportUrl,headerMap,JsonUtil.toJSONString(param));
        System.out.println(result);
        //上传OSS
        IKangResult<String> reportResult = JsonUtil.parseObject(result, IKangResult.class);
        Base64Utils.base64StringToPDF(reportResult.getResults());
        System.out.println("cost:"+(System.currentTimeMillis() - start));
    }

    @Test
    public void getAuthUrl(){
        ReportAuthEntity reportAuthEntity = thirdReportAuthService.queryReportAuthDetail(8234586723L, 123456789L);
        System.out.println(JsonUtil.toJSONString(reportAuthEntity));
        String reportAuthBucket = "examin.report.auth";
//        String ossUrl = "/examin.report.auth/report_auth_test.pdf";
        String ossHttpUrl = healthcareOssService.getOssHttpUrl(reportAuthBucket, reportAuthEntity.getReportAuthUrl(), 60);
        System.out.println(ossHttpUrl);
    }

    @Test
    public void createPdf(){
        ReportAuthEntity reportAuthEntity = thirdReportAuthService.queryReportAuthDetail(8234586723L, 123456789L);
        System.out.println(JsonUtil.toJSONString(reportAuthEntity));
        String reportAuthBucket = "examin.report.auth";
        String reportStr = "";
        ByteArrayInputStream inputStream = new ByteArrayInputStream(reportStr.getBytes());
        String s = healthcareOssService.uploadStreamBucket(reportAuthBucket,String.valueOf(System.currentTimeMillis()), inputStream);
        System.out.println(s);
    }

}
