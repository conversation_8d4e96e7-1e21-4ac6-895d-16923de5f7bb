package com.jd.health.third;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.enums.AppointStoreStatusEnum;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.rpc.domain.param.xikang.XiKangCapDetail;
import com.jd.health.medical.examination.rpc.domain.param.xikang.XiKangCapResult;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.NumberUtil;
import com.jd.pop.patient.client.domain.JsfResult;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/4/14 17:06
 * @Doc
 **/
public class XiKangTest {

    public static final char[] array =
            {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                    'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'a', 's', 'd',
                    'f', 'g', 'h', 'j', 'k', 'l', 'z', 'x', 'c', 'v', 'b', 'n', 'm',
                    'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', 'A', 'S', 'D',
                    'F', 'G', 'H', 'J', 'K', 'L', 'Z', 'X', 'C', 'V', 'B', 'N', 'M'};

    public static Map<Character, Integer> charValueMap = new HashMap<Character, Integer>();

    //初始化map
    static {
        for (int i = 0; i < array.length; i++) {
            charValueMap.put(array[i], i);
        }
    }

    public static final String pw = "XK&&JD0123456789";

    public static void main(String[] args) throws Exception {

//        getStoreCap();

//        appoint();
//        updateAppoint();
//        cancelAppoint();

        //getReport();
    }

    /**
     * 把数字转换成相对应的进制,目前支持(2-62)进制
     *
     * @param number
     * @param decimal
     * @return
     */
    public static String numberConvertToDecimal(long number, int decimal) {
        StringBuilder builder = new StringBuilder();
        while (number != 0) {
            builder.append(array[(int) (number - (number / decimal) * decimal)]);
            number /= decimal;
        }
        return builder.reverse().toString();
    }

    /**
     * 把进制字符串转换成相应的数字
     * @param decimalStr
     * @param decimal
     * @return
     */
    public static long decimalConvertToNumber(String decimalStr, int decimal) {
        long sum = 0;
        long multiple = 1;
        char[] chars = decimalStr.toCharArray();
        for (int i = chars.length - 1; i >= 0; i--) {
            char c = chars[i];
            sum += charValueMap.get(c) * multiple;
            multiple *= decimal;
        }
        return sum;
    }
    /**
     * 预约
     */
    //{"orderId":1586859049520,"customerGender":"1","idCardNo":"******************","hospitalSubId":"BJ001","customerName":"京东测试","phoneNo":"16619887367","hasAuthorized":"1","medicalStatus":"1","idCardType":"1","appointmentTime":"20200418080000","medicalPackage":"cs_m","customerBirthday":"1990-09-01","company":"","department":""}
    private static void appoint() throws Exception {
        String appointUrl = "http://t2.yunyiyuan.com:58080/managedcare/app/JDAppointment/JDOrder";
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(System.currentTimeMillis());
        appointmentParam.setGoodsId("STC-200317000017");
        appointmentParam.setStoreId("000001");
        appointmentParam.setAppointmentDate("2020-04-25");
        appointmentParam.setUserName("京东测试");
        appointmentParam.setUserPhone("16619887367");
        appointmentParam.setUserGender(1);
        appointmentParam.setUserBirth("2002-01-01");
        appointmentParam.setCredentialType(1);
        appointmentParam.setCredentialNo("110101200201017137");
        appointmentParam.setUserMarriage(1);
        System.out.println(JsonUtil.toJSONString(appointmentParam));
        String secStr = Aes.encryptData(JsonUtil.toJSONString(appointmentParam), pw);
        System.out.println(secStr);
        HashMap<String,String> paramMap = new HashMap<>();
        paramMap.put("secStr",secStr);
        String result = HttpSimpleClient.simplePost(appointUrl,JsonUtil.toJSONString(paramMap));
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 修改预约
     */
    private static void updateAppoint() {
        String appointUrl = "http://t2.yunyiyuan.com:58080/managedcare/app/JDAppointment/JDupdateOrderDate";
        JSONObject jsonObject = new JSONObject();
        String result = HttpSimpleClient.simplePost(appointUrl,jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 取消预约
     */
    private static void cancelAppoint() {
        String url = "http://t2.yunyiyuan.com:58080/managedcare/app/JDAppointment/JDcancelOrder";
        JSONObject jsonObject = new JSONObject();
        String orderId = "1586871905333";
        String hospitalOrderId = "000199220041408782";
        String result = HttpSimpleClient.simplePost(url,jsonObject.toJSONString());
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 熙康预约日历查询
     */
    private static void getStoreCap() {
        String appointStoreDateUrl = "http://t2.yunyiyuan.com:58080/managedcare/app/JDAppointment/getReserveSchedule";
        JSONObject param = new JSONObject();
        param.put("storeId","000001");
        param.put("goodsId","STC-200317000017");
        param.put("startTime","2020-04-23");
        param.put("endTime","2020-06-22");
        String result = HttpSimpleClient.simpleGet(appointStoreDateUrl, JsonUtil.toJSONString(param));
        System.out.println(JsonUtil.toJSONString(result));
        JsfResult xiKangResult = JsonUtil.parseObject(result, JsfResult.class);
        XiKangCapResult xiKangCapResult = JsonUtil.parseObject(xiKangResult.getData().toString(),XiKangCapResult.class);
        if(CollectionUtil.isEmpty(xiKangCapResult.getPhysicalSchedule())){
            return;
        }
        List<AppointDateDTO> list = new ArrayList<>();
        for(XiKangCapDetail xiKangCapDetail : xiKangCapResult.getPhysicalSchedule()){
            AppointDateDTO appointDateDTO = new AppointDateDTO();
            appointDateDTO.setDate(xiKangCapDetail.getStrRegdate());
            appointDateDTO.setChannelNo(123L);
            appointDateDTO.setStoreId("BJ001");
            String tDate = xiKangCapDetail.getStrRegdate();
            try {
                appointDateDTO.setDate(DateUtil.formatDate(DateUtil.parseDate(tDate,"yyyy-MM-dd"),"yyyy-MM-dd"));
            } catch (ParseException e) {
                throw new BusinessException(BaseErrorCode.APPOINT_PARAM_APPOINT_DATE_NULL_ERROR);
            }
            //判断是否可预约
            Integer maxNum = NumberUtil.toInt(xiKangCapDetail.getMaxNum());
            Integer usedNum = NumberUtil.toInt(xiKangCapDetail.getUsedNum());
            if(usedNum >= maxNum ){
                appointDateDTO.setNum(0);
                appointDateDTO.setStatus(AppointStoreStatusEnum.MAX.getCode());
            }else {
                appointDateDTO.setNum(maxNum - usedNum);
                appointDateDTO.setStatus(AppointStoreStatusEnum.ON.getCode());
            }
            list.add(appointDateDTO);
        }
        System.out.println(JsonUtil.toJSONString(list));
    }


}
