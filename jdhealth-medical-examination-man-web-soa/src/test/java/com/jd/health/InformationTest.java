package com.jd.health;
import com.google.common.collect.Lists;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.InformationDTO;
import com.jd.health.medical.examination.export.dto.PackageItemDTO;
import com.jd.health.medical.examination.export.param.InformationParam;
import com.jd.health.medical.examination.export.param.PackageItemParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.service.InformationExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName InformationTest
 * @Description
 * <AUTHOR>
 * @Date 2020/7/1 21:49
 **/
public class InformationTest extends BaseTest{

	@Autowired
	private InformationExportService exportService;

	@Test
	public void getPageInformation() {
		InformationParam informationParam = new InformationParam();
		informationParam.setPkgName("测试");
		informationParam.setPkgRemark("啊实打实");
		PageParam pageParam = new PageParam();
		pageParam.setPageNum(1);
		pageParam.setPageSize(10);
		JsfResult<PageInfo<InformationDTO>> result =
				exportService.getPageInformation(informationParam, pageParam);
		System.out.println(JsonUtil.toJSONString(result));
	}

	@Test
	public void getInformationByskuNo() {
		JsfResult<InformationDTO> result = exportService.getInformationByskuNo("300253");
		System.out.println(JsonUtil.toJSONString(result));
	}

	@Test
	public void updateByInformationBypkgId() {
		JsfResult<InformationDTO> informationByskuNo =
				exportService.getInformationByskuNo("300253");
		InformationDTO data = informationByskuNo.getData();
		InformationParam param = new InformationParam();
		param.setPkgRemark("qqqq");
		param.setPkgName("测试ES权益包包");
		param.setPkgDesc("7777");
		param.setPkgId(data.getPkgId());
		param.setPkg(buildParam(data.getPkg()));
		JsfResult<Boolean> result = exportService.updateByInformationBypkgId(param);
		System.out.println(JsonUtil.toJSONString(result));
	}

	private List<PackageItemParam> buildParam(List<PackageItemDTO> pkg) {
	    List<PackageItemParam> packageItemParamlist=Lists.newArrayList();
	    for (PackageItemDTO packageItemDTO :pkg) {
	    	packageItemParamlist.add(convertFromPackageItemDTO(packageItemDTO));
	    }
	    return packageItemParamlist;
	}

	private PackageItemParam convertFromPackageItemDTO(PackageItemDTO packageItemDTO) {
	    PackageItemParam packageItemParam = new PackageItemParam();
	    packageItemParam.setId(packageItemDTO.getId());
	    packageItemParam.setPkgId(packageItemDTO.getPkgId());
	    packageItemParam.setPkgItemId(packageItemDTO.getPkgItemId());
	    packageItemParam.setPkgItemName(packageItemDTO.getPkgItemName());
	    packageItemParam.setItemName(packageItemDTO.getItemName());
	    packageItemParam.setItemDesc(packageItemDTO.getItemDesc());
	    packageItemParam.setCreateUser(packageItemDTO.getCreateUser());
	    packageItemParam.setYn(packageItemDTO.getYn());
	    packageItemParam.setCreateTime(packageItemDTO.getCreateTime());
	    packageItemParam.setUpdateTime(packageItemDTO.getUpdateTime());
	    return packageItemParam;
	}
}
