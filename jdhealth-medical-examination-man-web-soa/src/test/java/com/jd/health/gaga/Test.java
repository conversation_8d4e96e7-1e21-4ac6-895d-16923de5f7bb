package com.jd.health.gaga;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.rpc.domain.param.gaga.GaGaCapResult;
import com.jd.health.medical.examination.rpc.domain.param.gaga.GaGaDataResult;
import com.jd.health.medical.examination.rpc.impl.AppointGaGaRpcImpl;
import com.jd.health.medical.examination.service.AddressService;
import com.jd.lbs.geocode.api.dto.GisPointDto;
import com.jd.medicine.base.common.exception.BusinessErrorCode;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/27 上午11:12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class Test {

    private static final Logger log = LoggerFactory.getLogger(Test.class);
    @Autowired
    private AppointGaGaRpcImpl appointGaGaRpc;
    /**
     * 测试嘎嘎预约
     */
    @org.junit.Test
    public void appointTest(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setJdAppointmentId(234L);//jd 预约单号
        appointmentParam.setAppointmentDate("2021-12-29");
        appointmentParam.setStoreId("80291446443942c2b4f3712506455471");
        appointmentParam.setGoodsId("709b1e0fabe24b7b880630bc1123b78f");
        appointmentParam.setUserName("李志会");
        appointmentParam.setUserPhone("18330115894");
        appointmentParam.setUserGender(1);//男
        appointmentParam.setUserMarriage(2);//已婚
        appointmentParam.setCredentialType(1);//身份证类型
        appointmentParam.setCredentialNo("132201199304103973");//身份证号
        appointmentParam.setUserBirth("1993-04-10");
        appointGaGaRpc.appoint(appointmentParam);
    }

    /**
     * 测试嘎嘎取消预约
     */
    @org.junit.Test
    public void cancelAppointTest(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setAppointmentNo("JD1640672859950ejgyy");
        appointGaGaRpc.cancelAppoint(appointmentParam);

    }
    /**
     * 测试嘎嘎修改预约
     */
    @org.junit.Test
    public void updateAppointTest(){
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setAppointmentNo("JD1640672859950ejgyy");
        appointmentParam.setAppointmentDate("2021-12-30");
        appointGaGaRpc.updateAppoint(appointmentParam);
    }

    /**
     * 测试获取门店排期
     */
    @org.junit.Test
    public void getAppointDateByStore(){
        //开始时间和结束时间先写死
        AppointmentStoreCapParam appointmentStoreCapParam = new AppointmentStoreCapParam();
        appointmentStoreCapParam.setGoodsId("709b1e0fabe24b7b880630bc1123b78f");//套餐id
        appointmentStoreCapParam.setStoreId("80291446443942c2b4f3712506455471");//门店id
        appointGaGaRpc.getAppointDateByStore(appointmentStoreCapParam);

    }
    @Autowired
    private AddressService addressService;

    @org.junit.Test
    public void get(){
        GisPointDto lngLatByAddress = null;
        try{
            lngLatByAddress  = addressService.getLngLatByAddress("温州大道1679号");
        }catch (BusinessException e){
            if(BusinessErrorCode.ILLEGAL_ARG_ERROR.equals(e.getErrorCode())){
                lngLatByAddress = addressService.getLngLatByAddress("浙江省温州鹿城区温州大道1679号");
            }else{
                log.info("ThirdDataExportServiceImpl -> convertStoreEntity business exception,", e);
                throw new BusinessException(BusinessErrorCode.RPC_CALL_ERROR);
            }
        }
        System.out.println(lngLatByAddress.toString());
    }

    public static void main(String[] args) {
        /*String s = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd");
        System.out.println(s);
        String s1 = DateUtil.formatDate(DateUtil.addDays(new Date(), 60), "yyyy-MM-dd");
        System.out.println(s1);*/
        String str ="{\"code\":\"0\",\"msg\":\"OK\",\"data\"" +
                ":{\"ins_id\":\"80291446-4439-42c2-b4f3-712506455471\"," +
                "\"list\":[{\"date\":\"2021-12-29\",\"max_num\":20,\"use_num\":0,\"num\":20}," +
                          "{\"date\":\"2021-12-30\",\"max_num\":20,\"use_num\":1,\"num\":19}]}}";
        String str2 ="{\"code\":\"0\",\"msg\":\"OK\",\"data\":[{\"date\":\"2021-12-29\",\"max_num\":20,\"use_num\":0," +
                "\"num\":20},{\"date\":\"2021-12-30\",\"max_num\":20,\"use_num\":1,\"num\":19}]}\n";
        JSONObject jsonObject = JsonUtil.parseObject(str);
        String data = jsonObject.getString("data");
        System.out.println(data.toString());
        GaGaDataResult<List<GaGaCapResult>> gaGaResult2 = JsonUtil.parseObject(data, GaGaDataResult.class);
        System.out.println(gaGaResult2.getList().toString());





    }

}
