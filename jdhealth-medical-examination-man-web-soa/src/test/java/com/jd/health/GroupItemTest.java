package com.jd.health;

import com.jd.health.medical.examination.export.dto.ExaminationItemDTO;
import com.jd.health.medical.examination.export.dto.GroupDTO;
import com.jd.health.medical.examination.export.param.ExaminationItemParam;
import com.jd.health.medical.examination.export.service.ExaminationManGroupExportService;
import com.jd.health.medical.examination.export.service.ItemExportService;
import com.jd.health.medical.examination.service.bo.GroupItemBO;
import com.jd.health.medical.examination.service.selfpersonal.ExaminationManGroupService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/20 18:12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class GroupItemTest {
    
    /**
     * examinationManGroupService
     */
    @Autowired
    ExaminationManGroupService examinationManGroupService;
    
    /**
     * examinationManGroupExportService
     */
    @Autowired
    private ExaminationManGroupExportService examinationManGroupExportService;
    
    /**
     * itemExportService
     */
    @Autowired
    ItemExportService itemExportService;
    
    /**
     * getGroupItemBySkuNoTest
     *
     * @return void
     */
    @Test
    public void getGroupItemBySkuNoTest() {
        List<GroupItemBO> groupItemBySkuNo = examinationManGroupService.getGroupItemBySkuNo("23487823");
        System.out.println(groupItemBySkuNo);
        System.out.println(JsonUtil.toJSONString(groupItemBySkuNo));
    }
    
    /**
     * getGroupItemBySkuTest
     *
     * @return void
     */
    @Test
    public void getGroupItemBySkuTest() {
        JsfResult<GroupDTO> result = examinationManGroupExportService.getGroupItemBySkuNo("23487823");
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    /**
     * test
     *
     * @return void
     */
    @Test
    public void test() {
        ExaminationItemParam examinationItemParam = new ExaminationItemParam();
        JsfResult<List<ExaminationItemDTO>> result = itemExportService.selectAllThreeItemTree(examinationItemParam);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
}
