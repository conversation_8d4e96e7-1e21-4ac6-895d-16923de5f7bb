package com.jd.health;

import com.jd.health.medical.examination.service.common.tools.HealthcareOssService;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * @Author: xbj
 * @Date: 2020/7/24 17:17
 */
public class OssTest  {
    @Autowired
    private HealthcareOssService healthcareOssService;

    @Test
    public void insertReportTest123() {
        String thirdStoreImg = "https://lc-crm.oss-cn-hangzhou.aliyuncs.com/bms/temp/637231498298751661.png";
        URL url = null;
        try {
            url = new URL(thirdStoreImg);

            //链接url
            URLConnection uc = url.openConnection();
            //获取输入流
            InputStream in = uc.getInputStream();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(readInputStream(in));
            //String imgUrl = healthcareOssService.uploadStream("637231498298751661.png", inputStream);
            //System.out.println(imgUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static byte[] readInputStream(InputStream inStream) throws IOException {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        //创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        //每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        //使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        //关闭输入流
        inStream.close();
        //把outStream里的数据写入内存
        return outStream.toByteArray();
    }

    public static void main(String[] args) {
       String jdId="1453081014984247298,1453581181105736194,1454983522287618562,1455104640365365762";

        if (StringUtil.isNotBlank(jdId) && jdId.contains(",")) {
            String[] jdAppointId = jdId.split(",");
            System.out.println("========="+ JsonUtil.toJSONString(jdAppointId));
            for (String id : jdAppointId) {
                System.out.println(id);
                System.out.println(Long.parseLong(id));
            }
        }
    }
}
