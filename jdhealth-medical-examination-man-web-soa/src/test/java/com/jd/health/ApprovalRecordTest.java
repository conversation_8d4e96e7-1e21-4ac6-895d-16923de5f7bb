package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.common.util.Aes;
import com.jd.health.medical.examination.export.dto.XfylManApprovalRecordDTO;
import com.jd.health.medical.examination.export.dto.XfylManApprovalRecordDetailDTO;
import com.jd.health.medical.examination.export.dto.report.StructReportSnapshotDTO;
import com.jd.health.medical.examination.export.param.StructReportParam;
import com.jd.health.medical.examination.export.param.supplier.XfylManApprovalRecordParam;
import com.jd.health.medical.examination.export.service.ManReportExportService;
import com.jd.health.medical.examination.export.service.supplier.XfylManApprovalRecordExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @ClassName ApprovalRecordTest
 * @Description
 * <AUTHOR>
 * @Date 2021/12/7 10:13
 **/
public class ApprovalRecordTest extends BaseTest {

    /**
     * approvalRecordExportService
     */
    @Autowired
    private XfylManApprovalRecordExportService approvalRecordExportService;

    /**
     * manReportExportService
     */
    @Autowired
    private ManReportExportService manReportExportService;

    /**
     * queryApprovalRecordDetail
     */
    @Test
    public void queryApprovalRecordDetail() {
        XfylManApprovalRecordParam param = new XfylManApprovalRecordParam();
        param.setApprovalNo("1");
        JsfResult<XfylManApprovalRecordDetailDTO> result = approvalRecordExportService.queryApprovalRecordDetail(param);
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * queryApprovalRecordPage
     */
    @Test
    public void queryApprovalRecordPage() {
        XfylManApprovalRecordParam param = new XfylManApprovalRecordParam();
        param.setPageNum(1);
        param.setPageSize(10);
        JsfResult<PageInfo<XfylManApprovalRecordDTO>> result = approvalRecordExportService.queryApprovalRecordPage(param);
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * queryApprovalRecordPage
     */
    @Test
    public void addXfylManApprovalRecord() {
        XfylManApprovalRecordParam param = JsonUtil.parseObject("{\"approvalNo\":\"1865421660929142274\",\"businessType\":0,\"createTime\":1639645138893,\"createUser\":\"system\",\"extendNo\":\"1886380019002149890\",\"relationNo\":\"1865421660929142274\",\"status\":2,\"type\":3}\n", XfylManApprovalRecordParam.class);
        JsfResult<Boolean> result = approvalRecordExportService.addXfylManApprovalRecord(param);
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * updateStructReportSnapshot
     */
    @Test
    public void updateStructReportSnapshot() {
        StructReportParam param = new StructReportParam();
        param.setStructReportId("100000001");
        param.setUpdateTime(new Date());
        param.setUpdateUser("lvyifan3");
        param.setApprovalNo("1");
        param.setReportSnapshot(JsonUtil.parseObject("{\"brandId\":30000004,\"brandName\":\"美年\",\"conclusionInfos\":[{\"conclusionDescription\":\"原始的异常结论描述信息\",\"conclusionName\":\"原始的异常结论名称\",\"conclusionSuggest\":\"原始的异常结论建议\",\"updateInfo\":{\"conclusionDescription\":\"更新后的异常结论描述信息\",\"conclusionName\":\"更新后的异常结论\",\"conclusionSuggest\":\"更新后的异常结论建议\",\"updateTime\":1638846762165,\"updateUser\":\"lvyifan3\"}}],\"dataSource\":1,\"examinationTime\":1638846762163,\"extendId\":\"123\",\"items\":[{\"indicators\":[{\"abnormalType\":\"偏高\",\"indicatorName\":\"收缩压\",\"normalRangeValue\":\"1 &lt; v &lt; 100\",\"unit\":\"mnhg\",\"updateInfo\":{\"indicatorName\":\"修改后的收缩压\",\"normalRangeValue\":\"100 &lt; v &lt; 200\",\"unit\":\"mnhg\",\"updateTime\":1638846762164,\"updateUser\":\"yangxiyu\"}}],\"itemName\":\"项目1\",\"updateItemName\":\"修改后的项目名称\",\"updateTime\":1638846762164,\"updateUser\":\"yangxiyu\"}],\"reportId\":1,\"uploadTime\":1638846762162,\"user\":{\"age\":25,\"idCard\":\"23456435678675432\",\"name\":\"测试名称\",\"patientId\":1566,\"phone\":\"**********\",\"sex\":1}}", StructReportSnapshotDTO.class));
        JsfResult<Boolean> result = manReportExportService.updateStructReportSnapshot(param);
        System.out.println(JsonUtil.toJSONString(result));
    }

    public static void main(String[] args) {
        String s = null;
        try {
            s = Aes.decryptData("", "JDH#structreport");
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(s);
    }
}