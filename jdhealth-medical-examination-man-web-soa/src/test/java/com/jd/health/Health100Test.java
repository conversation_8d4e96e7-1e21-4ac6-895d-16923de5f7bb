//package com.jd.health;
//
//import com.github.pagehelper.PageInfo;
//import com.jd.health.medical.examination.common.enums.Health100AppConstants;
//import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
//import com.jd.health.medical.examination.common.enums.SkuSpeciesEnum;
//import com.jd.health.medical.examination.common.util.HttpSimpleClient;
//import com.jd.health.medical.examination.common.util.MD5Util;
//import com.jd.health.medical.examination.domain.bo.JobDataBo;
//import com.jd.health.medical.examination.domain.enterprise.entity.AppointmentLogEntity;
//import com.jd.health.medical.examination.domain.third.health100.Health100GoodsEntity;
//import com.jd.health.medical.examination.export.dto.AppointDateDTO;
//import com.jd.health.medical.examination.export.param.AppointmentParam;
//import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
//import com.jd.health.medical.examination.export.service.DevManExportService;
//import com.jd.health.medical.examination.export.service.Health100AppointExportService;
//import com.jd.health.medical.examination.rpc.domain.dto.ThirdOrderSimpleDto;
//import com.jd.health.medical.examination.service.AppointmentApiService;
//import com.jd.health.medical.examination.service.manage.bo.ManSkuPluginStoreBO;
//import com.jd.health.medical.examination.service.manage.processor.business.SkuPluginStoreProcessor;
//import com.jd.health.medical.examination.service.third.Health100AppointService;
//import com.jd.health.medical.examination.service.third.Health100AppointmentService;
//import com.jd.health.medical.examination.service.third.Health100GoodsService;
//import com.jd.medicine.b2c.base.export.domain.JsfResult;
//import com.jd.medicine.base.common.exception.BusinessException;
//import com.jd.medicine.base.common.util.CollectionUtil;
//import com.jd.medicine.base.common.util.JsonUtil;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import javax.annotation.Resource;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
///**
// * @Author: xbj
// * @Date: 2020/4/29 17:01
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@WebAppConfiguration
//@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
//public class Health100Test {
//
//    /**
//     * health100GoodsService
//     */
//    @Autowired
//    private Health100GoodsService health100GoodsService;
//
//    /**
//     * health100AppointExportService
//     */
//    @Resource
//    private Health100AppointExportService health100AppointExportService;
//
//    /**
//     * health100AppointmentService
//     */
//    @Autowired
//    private Health100AppointmentService health100AppointmentService;
//
//    /**
//     * health100AppointService
//     */
//    @Autowired
//    private Health100AppointService health100AppointService;
//
//    /**
//     * appointmentApiService
//     */
//    @Resource
//    AppointmentApiService appointmentApiService;
//    @Resource
//    private SkuPluginStoreProcessor skuPluginStoreProcessor;
//
//
//    @Test
//    public void dealThirdStoreInfoTest() {
//        try {
//            Health100AppConstants.Health100AppEnum health100AppEnum =  Health100AppConstants.Health100AppEnum.GROUP_EXAMINATION;
//            List<Health100GoodsEntity> health100GoodsEntities = health100GoodsService.refreshThirdGoods(health100AppEnum.getAppKey(), health100AppEnum.getPassword());
//            if (CollectionUtil.isEmpty(health100GoodsEntities)) {
//                System.out.println("Health100GoodsAndStoreJob -> execute exception 美年获取套餐为空");
//            }
//            health100GoodsService.dealThirdStoreInfo(health100GoodsEntities, health100AppEnum.getAppKey(), health100AppEnum.getPassword());
//            //维护商品支持的加项包
//            health100GoodsService.dealThirdAdditionPackageInfo(health100GoodsEntities, health100AppEnum.getAppKey(), health100AppEnum.getPassword());
//
//        } catch (BusinessException e) {
//            System.out.println("==------------------" + JsonUtil.toJSONString(e));
//        }
//    }
//
//
//    @Test
//    public void create(){
//        List<ManSkuPluginStoreBO> skuStoreBos = new ArrayList<>();
//        // 套餐已经下架
////        skuStoreBos.add(
////                ManSkuPluginStoreBO.builder()
////                        .skuNo("100001173573")
////                        .parentSkuNo("100001173543")
////                        .groupNo(76812030925L)
////                        .channelNo(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo())
////                        .goodsId("Jd_Test20200088888.1.001")
////                        .parentGoodsId("asd")
////                        .storeId("45")
////                        .skuSpecies(SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())
////                        .vipType(0)
////                        .build()
////        );
////
////        // 加项包已经下架
////        skuStoreBos.add(
////                ManSkuPluginStoreBO.builder()
////                        .skuNo("100001173573")
////                        .parentSkuNo("100001173543")
////                        .groupNo(76812030925L)
////                        .channelNo(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo())
////                        .goodsId("NaN")
////                        .parentGoodsId("Jd_Test20200088888.0.003")
////                        .storeId("45")
////                        .skuSpecies(SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())
////                        .vipType(0)
////                        .build()
////        );
////        // 关系不存在
////        skuStoreBos.add(
////                ManSkuPluginStoreBO.builder()
////                        .skuNo("100001173573")
////                        .parentSkuNo("100001173543")
////                        .groupNo(76812030925L)
////                        .channelNo(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo())
////                        .goodsId("Jd_Test20200088888.1.003")
////                        .parentGoodsId("Jd_Test20200088888.0.003")
////                        .storeId("45")
////                        .skuSpecies(SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())
////                        .vipType(0)
////                        .build()
////        );
//
//        // 基础套餐-门店关系不存在
//        skuStoreBos.add(
//                ManSkuPluginStoreBO.builder()
//                        .skuNo("100001173573")
//                        .parentSkuNo("100001173543")
//                        .groupNo(76812030925L)
//                        .channelNo(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo())
//                        .goodsId("Jd_Test20200088888.1.001")
//                        .parentGoodsId("Jd_Test20200088888.0.002")
//                        .storeId("45")
//                        .skuSpecies(SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())
//                        .vipType(0)
//                        .build()
//        );
//        // 加项包-门店关系不存在
//        skuStoreBos.add(
//                ManSkuPluginStoreBO.builder()
//                        .skuNo("100001173573")
//                        .parentSkuNo("100001173543")
//                        .groupNo(76812030925L)
//                        .channelNo(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo())
//                        .goodsId("Jd_Test20200088888.1.002")
//                        .parentGoodsId("Jd_Test20200088888.0.003")
//                        .storeId("45")
//                        .skuSpecies(SkuSpeciesEnum.PACKAGE_SKU.getTypeNo())
//                        .vipType(0)
//                        .build()
//        );
//        skuPluginStoreProcessor.createMeiNianSkuStores(skuStoreBos, SkuPluginStoreProcessor.GOODS_RELATION);
//
//    }
//
//    @Test
//    public void appointTest() {
////        String param = "{\"appointmentDate\":\"2020-05-09\",\"channelType\":**********,\"credentialNo\":\"411081199610023020\",\"credentialType\":1,\"goodsId\":\"JDQX000888820200222.0.001\",\"jdAppointmentId\":1444698312564278789,\"storeId\":\"47\",\"userBirth\":\"1996-10-02\",\"userGender\":1,\"userMarriage\":1,\"userName\":\"东方不败\",\"userPhone\":\"15810384902\"}";
//        String param ="{\"appointmentDate\":\"2020-08-19\",\"channelType\":**********,\"credentialNo\":\"110101197401011077\",\"credentialType\":1,\"goodsId\":\"**********\",\"jdAppointmentId\":1456160429226067970,\"storeId\":\"I6\",\"userBirth\":\"1974-01-01\",\"userGender\":1,\"userMarriage\":1,\"userName\":\"测试徐\",\"userPhone\":\"18311290200\"}";
//        AppointmentParam appointmentParam = JsonUtil.parseObject(param, AppointmentParam.class);
//        health100AppointExportService.appoint(appointmentParam);
//    }
//
//    @Test
//    public void modifyAppoint() {
//        String param = "{\"appointmentDate\":\"2020-06-15\",\"appointmentNo\":\"80000202005000009\",\"channelType\":**********,\"goodsId\":\"JDQX000888820200222.0.001\",\"jdAppointmentId\":1444698312564278789,\"storeId\":\"47\"}";
//        AppointmentParam appointmentParam = JsonUtil.parseObject(param, AppointmentParam.class);
//        health100AppointExportService.modifyAppoint(appointmentParam);
//    }
//
//    @Test
//    public void cancelAppoint() {
//        String param = "{\"appointmentNo\":\"80000202005000009\",\"channelType\":**********,\"goodsId\":\"JDQX000888820200222.0.001\",\"jdAppointmentId\":1444698312564278789,\"storeId\":\"47\"}";
//        AppointmentParam appointmentParam = JsonUtil.parseObject(param, AppointmentParam.class);
//        health100AppointExportService.cancelAppoint(appointmentParam);
//    }
//
//    @Test
//    public void getCheckedResult() {
//        health100AppointExportService.getCheckedResult();
//    }
//
//    @Test
//    public void getDealCheckedAndReport() {
//        health100AppointExportService.getDealCheckedAndReport();
//    }
//
//    /**
//     * testAppointDate
//     */
//    @Test
//    public void testAppointDate() {
//        AppointmentStoreCapParam appointmentStoreCapParam = new AppointmentStoreCapParam();
//        appointmentStoreCapParam.setGoodsId("JDQX000888820200222.0.001");
//        appointmentStoreCapParam.setStoreId("47");
//        appointmentStoreCapParam.setChannelNo(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo());
//        List<AppointDateDTO> list=health100AppointExportService.getAppointDate(appointmentStoreCapParam);
//        System.out.println("================="+JsonUtil.toJSONString(list));
//    }
//
//    /**
//     * testDealThreeDaysCheckedAndReport
//     */
//    @Test
//    public  void testDealThreeDaysCheckedAndReport(){
//        health100AppointmentService.dealThreeDaysCheckedAndReport();
//    }
//
//    public static void main(String[] args) {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        String appKey = "201207088";
//        String password = "9b0f392b36324ecc86c930d2e0fda373";
//        String idCard = "150102197905200537";
//        String name = "贺伟";
//        String outOrderID = "2035297410011371010";
//
//
//        String timestamp = sdf.format(new Date());
//
//        StringBuffer stringBuffer = new StringBuffer();
//        stringBuffer.append(password);
//        stringBuffer.append(appKey);
//        stringBuffer.append(timestamp);
//        String sign = stringBuffer.toString();
//
//        Map<String, String> paramsMap = new HashMap<>();
//        paramsMap.put("sign", MD5Util.getMD5String(sign));
//        paramsMap.put("timestamp", timestamp);
//        paramsMap.put("appKey", appKey);
//        paramsMap.put("idCard", idCard);
//        paramsMap.put("name", name);
//        paramsMap.put("outOrderID", outOrderID);
//        String result = HttpSimpleClient.simplePost("http://api.health-100.cn/StandardService/api/Reservation/PostQueryServiceBillState", paramsMap);
//        paramsMap.put("ServiceNumber", "80000202207034135");
//        String jsonString = JsonUtil.toJSONString(paramsMap);
//        System.out.println("jsonString=" + jsonString);
//        String appointInfoResult = HttpSimpleClient.simplePost("http://api.health-100.cn/StandardService/api/Company/PostQueryReservation", paramsMap);
//        System.out.println(JsonUtil.toJSONString(result));
//        System.out.println(JsonUtil.toJSONString(appointInfoResult));
//    }
//
//    @Test
//    public void queryStatus() {
//        AppointmentParam param = new AppointmentParam();
//        param.setJdAppointmentId(1840104237468537858L);
//        param.setChannelType(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo());
//        param.setGoodsId("**********");
//        param.setCredentialNo("341126199012186340");
//        param.setUserName("鲁夕玉");
//        ThirdOrderSimpleDto orderSimpleDto = appointmentApiService.queryAppointDetailOfProvider(param);
//        System.out.println(JsonUtil.toJSONString(orderSimpleDto));
//    }
//
//    @Autowired
//    DevManExportService devManExportService;
//
//    @Test
//    public void testMeinianData() {
//        JsfResult healthCheckAndReport = devManExportService.getHealthCheckAndReport(123L);
//        System.out.println(JsonUtil.toJSONString(healthCheckAndReport));
//    }
//
//    /**
//     * 任务执行
//     */
//    @Test
//    public void testJobData() {
//        System.out.println("Health100ReportResultJob -> execute start, 每两小时获取到检用户是否出报告开始");
//        //查询最多100条已到检的记录，调用接口获取是否已出报告，已出报告的记录进行调用报告获取接口
//        JobDataBo jobDataBo = new JobDataBo();
//        jobDataBo.setPageNum(1);
//        jobDataBo.setPageSize(100);
//        jobDataBo.setItemCount(2);
//        jobDataBo.setItemNo(1);
//        PageInfo<AppointmentLogEntity> appointmentLogEntityPageInfo = health100AppointService.queryAppointSuccessNoReportRecord(jobDataBo);
//        System.out.println(JsonUtil.toJSONString(appointmentLogEntityPageInfo.getList()));
//    }
//
//    @Test
//    public void testQueryId(){
//        String jdAppointmentId = "1601984928386386434";
//        AppointmentLogEntity appointmentLogEntity = health100AppointService.queryAppointInfoById(Long.valueOf(jdAppointmentId));
//        System.out.println(JsonUtil.toJSONString(appointmentLogEntity));
//    }
//}
