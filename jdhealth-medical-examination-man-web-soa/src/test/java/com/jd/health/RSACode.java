package com.jd.health;

import com.sun.jndi.toolkit.url.UrlUtil;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * pop-patient.RSACode
 *
 * <AUTHOR>
 * @date 2018-05-03 14:06
 */
public class RSACode {
    /**
     * KEY_ALGORITHM
     */
    private static final String KEY_ALGORITHM = "RSA";

    /**
     * SIGNATURE_ALGORITHM
     */
    private static final String SIGNATURE_ALGORITHM = "MD5withRSA";

    /**
     * PUBLIC_KEY
     */
    private static final String PUBLIC_KEY = "RSAPublicKey";

    /**
     * PRIVATE_KEY
     */
    private static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * publicKey
     */
    private static final String PUBLICKEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCP/zZ88L2+DP9ePx77g1tXn0HWchrSnCc7o8iuplIZu6qnZiSoGzpv68U+RX43S1SOpW25pkes4CqbXBhj/kE1qbr5OT1XKjWoVEaHkdO8gZmALALWtTPQTerIoMEfU1lz1fo6De1XuK5WLXwKVPeLfZpRGyxQOmQNdvv78ezA0QIDAQAB";

    /**
     * privateKey
     */
    private static final String PRIVATEKEY = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAI//Nnzwvb4M/14/HvuDW1efQdZyGtKcJzujyK6mUhm7qqdmJKgbOm/rxT5FfjdLVI6lbbmmR6zgKptcGGP+QTWpuvk5PVcqNahURoeR07yBmYAsAta1M9BN6sigwR9TWXPV+joN7Ve4rlYtfApU94t9mlEbLFA6ZA12+/vx7MDRAgMBAAECgYAopC57yvKyrjZxIj2bXTKto1xQqznY0YvdMFfWu0iWzLnYr2KPM8gliJTOA40hDBjcfeJfMIgwScCcwrx7lGpQzqT2YlRnOuhrND4pT2JDeo2YImMa4R3RcbDKlpfDN8wGFh5nDgLNyLfWGShFIrVrfiApLyOQ5wZF5BmvCQ4epQJBANw9+dFVZAulE/eMAFTdJfXF+11NdmM8/+PVEh6rR6Hww0L8BU0h9HR/nl6fpMI+3Mo8OD9cg25ZJSQGOb/Foe8CQQCnYDlH88Z0ASOuUx8R9EPMg4cFqgYQ4N6tLaN5A+UDZO1GWotxEFBvxSmIN07nQ0YzUuyCyCxsgIkZYJaab4k/AkEA2feMMhxscwnhBPFNsq9bTi2uY9yx+ez44yaQVEH3FtIMrVJ2fKvKHzHeLo+Yo5VLNer7T9njSWZ4V3rB1IVniQJAIXIxABfOiE/R+XvSsZvGEJAX4yHepHDLDoAYMo58rPj2ESD2zFkeObeEXRvwQHZJCOiBKoEln2jv/Io4jhPLsQJBAK9Q/vpymhO3zZJ009qh1owMeAXosJj7KR1YFwzE3LRVbVLav0+hUWya9gQxOA29rSBoxJDdx9NXcIsMEtlocUo=";

    /**
     * @param key String
     * @return byte[]
     */
    private static byte[] decryptbase64(String key) {
        return Base64.decodeBase64(key);
    }

    /**
     * @param bytes byte[]
     * @return String
     */
    private static String encryptbase64(byte[] bytes) {
        return new String(Base64.encodeBase64(bytes));
    }

    /**
     * 用私钥对信息生成数字签名
     *
     * @param data       加密数据
     * @param privateKey 私钥
     * @return String
     */
    public static String sign(byte[] data, String privateKey) throws Exception {
        // 解密由base64编码的私钥
        byte[] keyBytes = decryptbase64(privateKey);
        // 构造PKCS8EncodedKeySpec对象
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        // 取私钥匙对象
        PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
        // 用私钥对信息生成数字签名
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data);
        return encryptbase64(signature.sign());
    }

    /**
     * 校验数字签名
     *
     * @param data      加密数据
     * @param publicKey 公钥
     * @param sign      数字签名
     * @return 校验成功返回true 失败返回false
     */
    private static boolean verify(byte[] data, String publicKey, String sign)
            throws Exception {
        // 解密由base64编码的公钥
        byte[] keyBytes = decryptbase64(publicKey);
        // 构造X509EncodedKeySpec对象
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        // 取公钥匙对象
        PublicKey pubKey = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(data);
        // 验证签名是否正常
        return signature.verify(decryptbase64(sign));
    }

    /**
     * @param data byte[]
     * @param key  String
     * @return byte[]
     */
    private static byte[] decryptByPrivateKey(byte[] data, String key)
            throws Exception {
        // 对密钥解密
        byte[] keyBytes = decryptbase64(key);
        // 取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        // 对数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher.doFinal(data);
    }

    /**
     * @param data String
     * @return String
     */
    public static String decryptByPrivateKey(String data) throws Exception {
        int a =172;
        if (data.length() == a) {
            return new String(decryptByPrivateKey(decryptbase64(data), PRIVATEKEY)).substring(6);
        } else {
            return new String(decryptByPrivateKey(decryptbase64(data.substring(6)), PRIVATEKEY));
        }
    }

    /**
     * 解密
     * 用私钥解密
     *
     * @param data String
     * @param key  String
     * @return byte[]
     */
    public static byte[] decryptByPrivateKey(String data, String key)
            throws Exception {
        return decryptByPrivateKey(decryptbase64(data), key);
    }

    /**
     * 解密
     * 用公钥解密
     *
     * @param data byte[]
     * @param key  String
     * @return byte[]
     */
    public static byte[] decryptByPublicKey(byte[] data, String key)
            throws Exception {
        // 对密钥解密
        byte[] keyBytes = decryptbase64(key);
        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);
        // 对数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        return cipher.doFinal(data);
    }


    /**
     * 用公钥加密字符串
     *
     * @param data String
     * @return String
     */
    public static String encryptByPublicKey(String data) throws Exception {
        return encryptbase64(encryptByPublicKey(data, PUBLICKEY));
    }

    /**
     * 加密

     * 用公钥加密
     *
     * @param data String
     * @param key  String
     * @return byte[]
     */
    private static byte[] encryptByPublicKey(String data, String key)
            throws Exception {
        // 对公钥解密
        byte[] keyBytes = decryptbase64(key);
        // 取得公钥
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);
        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(data.getBytes());
    }

    /**
     * 加密

     * 用私钥加密
     *
     * @param data byte[]
     * @param key  String
     * @return byte[]
     */
    public static byte[] encryptByPrivateKey(byte[] data, String key)
            throws Exception {
        // 对密钥解密
        byte[] keyBytes = decryptbase64(key);
        // 取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        return cipher.doFinal(data);
    }

    /**
     * 取得私钥
     *
     * @param keyMap Map
     * @return String
     */
    private static String getPrivateKey(Map<String, Key> keyMap)
            throws Exception {
        Key key = keyMap.get(PRIVATE_KEY);
        return encryptbase64(key.getEncoded());
    }

    /**
     * 取得公钥
     *
     * @param keyMap Map
     * @return String
     */
    private static String getPublicKey(Map<String, Key> keyMap) throws Exception {
        Key key = keyMap.get(PUBLIC_KEY);
        return encryptbase64(key.getEncoded());
    }

    /**
     * 初始化密钥
     *
     * @return Map
     */
    public static Map<String, Key> initKey() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator
                .getInstance(KEY_ALGORITHM);
        SecureRandom secureRandom = new SecureRandom((System.currentTimeMillis() + "pop_patient").getBytes());
        keyPairGen.initialize(1024, secureRandom);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        Map<String, Key> keyMap = new HashMap(2);
        keyMap.put(PUBLIC_KEY, keyPair.getPublic());// 公钥
        keyMap.put(PRIVATE_KEY, keyPair.getPrivate());// 私钥
        return keyMap;
    }

    /**
     * 主方法
     *
     * @param args String[]
     */
    public static void main(String[] args) throws Exception {
        // 前端查询权益时，会携带erp密文，需要转为明文；erp的密文转换
        String body1 = "e2QK8HDtZMIdzAMAZSsRMZxCD0sk63Ay4sprtPf6A72V%2BXrZGe7velCTivAqc%2F8ceACOGVSM4gRbfrhbFCyhAee%2BbxRVsjzv2OCP7bJOR9G9y6c76DaeSdlHN%2B6PpuacNJpIWuWVvSprEgQy5poRhD3EzgB9tfkMj%2FYz9H4sVEE%3D";
        body1 = UrlUtil.decode(body1);
        String s1 = RSACode.decryptByPrivateKey(body1);
        System.out.println("s1=" + s1);

    }
}