package com.jd.health.domain;

import com.jd.medicine.base.common.annotation.ExcelField;
import lombok.Data;

/**
 * @ClassName ImportAliasItemBO
 * @Description
 * <AUTHOR>
 * @Date 2022/4/14 14:58
 **/
@Data
public class ImportAliasItemBO {
    /**
     * 套餐名称
     */
    @ExcelField(header = "品牌项目名称（别名库）", sort = 0)
    private String thirdItemName;

    /**
     * 适用人群
     */
    @ExcelField(header = "JDH词库项目名称", sort = 1)
    private String baseItemName;
}