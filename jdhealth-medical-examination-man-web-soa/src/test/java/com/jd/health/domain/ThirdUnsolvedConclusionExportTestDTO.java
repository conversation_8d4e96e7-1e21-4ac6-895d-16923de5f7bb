package com.jd.health.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.medicine.base.common.annotation.ExcelField;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName ThirdUnsolvedConclusionExportTestDTO
 * @Description
 * <AUTHOR>
 * @Date 2021/3/17 17:46
 **/
@Data
public class ThirdUnsolvedConclusionExportTestDTO {

	/**
	 * 渠道编号
	 */
	@ExcelField(header = "服务商编号", sort = 0)
	private Long channelNo;

	/**
	 * 渠道名称
	 */
	@ExcelField(header = "服务商名称", sort = 1)
	private String channelName;

	/**
	 * 商家结论名称
	 */
	@ExcelField(header = "未匹配名称", sort = 2)
	private String thirdConclusionName;

	/**
	 * 创建时间
	 */
	@ExcelField(header = "已等待时间", sort = 3)
	private Date createTime;

	public Long getChannelNo() {
		return channelNo;
	}

	public void setChannelNo(Long channelNo) {
		this.channelNo = channelNo;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getThirdConclusionName() {
		return thirdConclusionName;
	}

	public void setThirdConclusionName(String thirdConclusionName) {
		this.thirdConclusionName = thirdConclusionName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
