package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.EnterPriseLeaderCreateDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseAccountInfoDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseBuyerMenuDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseBuyerMenuResDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseAccountQueryParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseChannelMenuParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseCreateSubUserParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseLeaderCreateParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseAccountSystemExportService;
import com.jd.health.medical.examination.rpc.enterprise.AccountSystemRpc;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.yao.user.soa.client.service.menu.BuyerMenuService;
import com.jd.yao.user.soa.client.vo.base.YjcUserSoaResult;
import com.jd.yao.user.soa.client.vo.menu.BuyerMenuInfo;
import com.jd.yao.user.soa.client.vo.menu.BuyerMenuRes;
import com.jd.yao.user.soa.client.vo.menu.ChannelMenuQueryParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 16:12
 */
public class EnterpriseAccountSystemTest extends BaseTest{
    @Autowired
    private EnterpriseAccountSystemExportService enterpriseAccountSystemExportService;
    @Autowired
    private AccountSystemRpc accountSystemRpc;
    @Resource
    private BuyerMenuService buyerMenuService;
    @Test
    public void test(){
        EnterpriseLeaderCreateParam param = new EnterpriseLeaderCreateParam();
        JsfResult<EnterPriseLeaderCreateDTO> result = enterpriseAccountSystemExportService.createLeaderPin(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
//    @Test
//    public void queryChannelMenuTest(){
//        EnterpriseChannelMenuParam enterpriseChannelMenuParam = new EnterpriseChannelMenuParam();
//        enterpriseChannelMenuParam.setCompanyId(1004689285L);
//        enterpriseChannelMenuParam.setChannelDicCode("TJQXPT");
//        JsfResult<List<EnterpriseBuyerMenuDTO>> result = enterpriseAccountSystemExportService.queryChannelMenu(enterpriseChannelMenuParam);
//        System.out.println(JsonUtil.toJSONString(result));
//    }

    @Test
    public void queryAccountPageListTest(){
        EnterpriseAccountQueryParam enterpriseAccountQueryParam = new EnterpriseAccountQueryParam();
        enterpriseAccountQueryParam.setCompanyId(1005422102L);
        enterpriseAccountQueryParam.setPageNum(1);
        enterpriseAccountQueryParam.setPageSize(10);
        JsfResult<PageInfo<EnterpriseAccountInfoDTO>> result = enterpriseAccountSystemExportService.queryAccountPageList(enterpriseAccountQueryParam);
        System.out.println(JsonUtil.toJSONString(result));
    }

    @Test
    public void createSubUserTest(){
        EnterpriseCreateSubUserParam enterpriseCreateSubUserParam = new EnterpriseCreateSubUserParam();
        enterpriseCreateSubUserParam.setSuperPin("huluobu");
        enterpriseCreateSubUserParam.setCompanyId(1005422102L);
        enterpriseCreateSubUserParam.setJdPin("asdfasdfjkl");
        //enterpriseCreateSubUserParam.setPwd("adsijlkjfda");
        enterpriseCreateSubUserParam.setName("迟彪sdf");
        enterpriseCreateSubUserParam.setMobilePhone("***********");
        enterpriseCreateSubUserParam.setIp("*************");
        //enterpriseCreateSubUserParam.setChannelResMap(copy.getChannelResMap());
        JsfResult<Boolean> subUser = enterpriseAccountSystemExportService.createSubUser(enterpriseCreateSubUserParam);
        System.out.println(JsonUtil.toJSONString(subUser));
    }

    @Test
    public void selectPwdByAccountNoTest() {
        String accountNo = "daluobu01";
        JsfResult<String> result = enterpriseAccountSystemExportService.selectPwdByAccountNo(accountNo);
        System.out.println(result);
    }

    @Test
    public void queryMenuForCommonBuyerTest(){
        //BuyerMenuRes buyerMenuRes = accountSystemRpc.queryMenuForCommonBuyer("xnwanglu5");
        //System.out.println(JsonUtil.toJSONString(buyerMenuRes));
        JsfResult<EnterpriseBuyerMenuResDTO> result = enterpriseAccountSystemExportService.queryMenuForCommonBuyer("xnwanglu5");
        System.out.println(JsonUtil.toJSONString(result));
    }

    @Test
    public void queryChannelMenuTest(){
        EnterpriseChannelMenuParam enterpriseChannelMenuParam = new EnterpriseChannelMenuParam();
        enterpriseChannelMenuParam.setCompanyId(1004689285L);
        enterpriseChannelMenuParam.setChannelDicCode("TJQXPT");
        JsfResult<List<EnterpriseBuyerMenuDTO>> result = enterpriseAccountSystemExportService.queryChannelMenu(enterpriseChannelMenuParam);
        System.out.println(JsonUtil.toJSONString(result));
        /*ChannelMenuQueryParam channelMenuQueryParam = new ChannelMenuQueryParam();
        channelMenuQueryParam.setCompanyId(93L);
        channelMenuQueryParam.setChannelDicCode("YAO");
        YjcUserSoaResult<List<BuyerMenuInfo>> listYjcUserSoaResult = buyerMenuService.queryChannelMenu(channelMenuQueryParam);
        System.out.println(JsonUtil.toJSONString(listYjcUserSoaResult.getReturnCode()));*/
    }
}
