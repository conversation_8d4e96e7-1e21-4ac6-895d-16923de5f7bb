package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.domain.StructReportEntity;
import com.jd.health.medical.examination.export.dto.ReportDTO;
import com.jd.health.medical.examination.export.dto.ReportUrlDTO;
import com.jd.health.medical.examination.export.dto.report.StructReportDetailDTO;
import com.jd.health.medical.examination.export.param.ExaminationReportParam;
import com.jd.health.medical.examination.export.param.ExaminationReportUrlParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.param.ReportParam;
import com.jd.health.medical.examination.export.service.ManReportExportService;
import com.jd.health.medical.examination.rpc.PatientManagerServiceRpc;
import com.jd.health.medical.examination.service.MyReportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.pop.patient.client.domain.PatientInfoDTO;
import com.mysql.jdbc.Blob;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 企销体检报告测试类
 *
 * <AUTHOR>
 * @date 2020-02-05 15:25
 **/
public class ManReportTest extends BaseTest{
    /**
     * 企销体检报告
     */
    @Autowired
    ManReportExportService manReportExportService;

    /**
     * myReportService
     */
    @Autowired
    MyReportService myReportService;

    /**
     * 常用体检人信息
     */
    @Autowired
    PatientManagerServiceRpc patientManagerServiceRpc;

    @Test
    public void queryEmReportPageTest() {
        ExaminationReportParam examinationReportParam = new ExaminationReportParam();
        examinationReportParam.setCompanyNo(952996354L);
        examinationReportParam.setEmpId("hezhensheng3");
        examinationReportParam.setUserPin("hezhensheng3");
        examinationReportParam.setOrderType("200");
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<ReportDTO>> result = manReportExportService.queryEmReportPage(examinationReportParam, pageParam);
        System.out.println(result);
    }

    /**
     * 获取体检报告列表 todo hezhensheng3 员工端测试
     */
    @Test
    public void queryReportPageTest() {
        /**
         * pin *********-331928
         * pageNum
         * pageSize
         * orderType 订单类型 200自营套餐 4自营卡密 75POP卡密 4
         * reportSource 报告来源：1自营 2企销 3POP  1
         */
        ExaminationReportParam examinationReportParam = new ExaminationReportParam();
        examinationReportParam.setOrderType("");
        examinationReportParam.setUserPin("*********-331921");
        System.out.println(JsonUtil.toJSONString(examinationReportParam));
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<ReportDTO>> result = manReportExportService.queryReportPage(examinationReportParam,pageParam);
        System.out.println(JsonUtil.toJSONString(result));

    }

    @Test
    public void getStructReport(){
        JsfResult<StructReportDetailDTO> result = manReportExportService.getStructReport(2134546321L);
        Assert.assertNotNull(result.getData());
    }

    /**
     * 获取体检报告URL todo hezhensheng3 员工端测试
     */
    @Test
    public void getReportUrlInfoTest() {
        /**
         * getPatientId
         * getReportId(或jdAppointmentNo)  1576570978738
         * getUserPin  testPin
         */
        ExaminationReportUrlParam examinationReportUrlParam = new ExaminationReportUrlParam();
        examinationReportUrlParam.setReportId(1576570978738L);
        examinationReportUrlParam.setUserPin("testPin");
        examinationReportUrlParam.setPatientId(1L);
        JsfResult<ReportUrlDTO> result = manReportExportService.getReportUrlInfo(examinationReportUrlParam);
        System.out.println(JsonUtil.toJSONString(result));

    }

    /**
     * 根据patientId和userPin获取常用体检人信息
     */
    @Test
    public void getPatientInfoByPatientIdAndUserPinTest() {
        PatientInfoDTO patientInfoDTO = new PatientInfoDTO();
        patientInfoDTO.setPatientId(1L);
        patientInfoDTO.setUserPin("hezhensheng3");
        PatientInfoDTO result = patientManagerServiceRpc.getPatientInfoByPatientIdAndUserPin(patientInfoDTO);
        System.out.println(result);
    }

    /**
     * 测试：根据UserPin（必填）+PatientId（非必传）查询报告列表
     *
     * 返回结果示例：
     * {"code":"0000","data":{"endRow":3,"firstPage":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"lastPage":1,"list":[{"appointDate":1584633600000,"createTime":1582787429000,"jdAppointmentId":1391910630465735170,"orderId":110062918330,"patientId":1150724288402,"reportVerifyStatus":1,"skuName":"测试商品，请勿下单，下单会被取消！","storeName":"普惠西安高新体检中心","updateTime":1582787429000,"userGender":2,"userMarriage":0,"userName":"京东测试","userPhone":"13500745246","userPin":"18701507232_p"},{"appointDate":1584633600000,"createTime":1582787429000,"jdAppointmentId":13,"orderId":110062918330,"patientId":1150724288402,"reportVerifyStatus":1,"skuName":"测试商品，请勿下单，下单会被取消！","storeName":"普惠西安高新体检中心","updateTime":1583586667000,"userGender":2,"userMarriage":0,"userName":"京东测试","userPhone":"13500745246","userPin":"18701507232_p"},{"appointDate":1584633600000,"createTime":1582787429000,"jdAppointmentId":14,"orderId":110062918330,"patientId":1150724288402,"reportVerifyStatus":1,"skuName":"测试商品，请勿下单，下单会被取消！","storeName":"普惠西安高新体检中心","updateTime":1583587857000,"userGender":2,"userMarriage":0,"userName":"京东测试","userPhone":"13500745246","userPin":"18701507232_p"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":3,"startRow":1,"total":3},"msg":"OK","success":true}
     */
    @Test
    public void queryReportByUserPin(){
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        ReportParam reportParam = new ReportParam();
        reportParam.setUserPin("18701507232_p");
        reportParam.setPatientId(1150724288402L);
        JsfResult<PageInfo<ReportDTO>> result = manReportExportService.queryReportByUserPin(reportParam, pageParam);
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 解绑报告
     */
    @Test
    public void unbindReport(){
        ExaminationReportParam param = new ExaminationReportParam();
        param.setUserPin("1231231312312312313");
        param.setJdAppointmentId(1444698312564278789L);
        JsfResult<Boolean> result = manReportExportService.unbindReport(param);
        System.out.println(JsonUtil.toJSONString(result));
    }

    /**
     * 结构化报告
     */
    @Test
    public void testStructReport(){
        StructReportEntity structReportEntity = new StructReportEntity();
        structReportEntity.setJdAppointmentId(123567L);
        structReportEntity.setChannelNo(ProviderTypeEnum.BIRD.getTypeNo());
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("123");
        structReportEntity.setStructReportStr(stringBuffer.toString());
        Integer integer = myReportService.insertStructReport(structReportEntity);
        System.out.println("result:"+integer);
    }
}
