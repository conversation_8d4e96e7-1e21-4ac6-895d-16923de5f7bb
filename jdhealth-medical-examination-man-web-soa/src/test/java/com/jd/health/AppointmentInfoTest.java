package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.common.constant.CommonConstant;
import com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.dto.ExaminationGroupDTO;
import com.jd.health.medical.examination.export.dto.GoodsStoreDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ExaminationGroupParam;
import com.jd.health.medical.examination.export.service.AppointmentApiExportService;
import com.jd.health.medical.examination.export.service.ExaminationManGroupExportService;
import com.jd.health.medical.examination.service.selfpersonal.SkuInfoService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预约信息测试类
 *
 * <AUTHOR>
 * @date 2020-01-09 15:06
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class AppointmentInfoTest {

    private static final Logger log = LoggerFactory.getLogger(AppointmentInfoTest.class);

    /**
     * 体检项目组信息exportService
     */
    @Autowired
    private ExaminationManGroupExportService examinationManGroupExportService;

    /**
     * 预约信息exportService
     */
    @Autowired
    private AppointmentApiExportService appointmentApiExportService;

    @Resource
    private SkuInfoService enhanceCacheSkuInfoService;

    /**
     * 根据groupNo获取体检项目组信息
     */
    @Test
    public void selectExaminationGroupAndItemTest() {
        ExaminationGroupParam examinationGroupParam = new ExaminationGroupParam();
        examinationGroupParam.setGroupNo(1L);
        JsfResult<ExaminationGroupDTO> result = examinationManGroupExportService.selectExaminationGroupAndItem(examinationGroupParam);
        System.out.println(result);
    }

    @Test
    public void findAppointDateTest(){
        AppointmentStoreCapParam appoint = new AppointmentStoreCapParam();
        appoint.setStoreId("0316002");
        appoint.setGoodsId("03160021907300012");
        appoint.setChannelNo(3632970185L);
        JsfResult<List<AppointDateDTO>> result = appointmentApiExportService.findAppointmentDate(appoint);
        System.out.println(JsonUtil.toJSONString(result));
    }

    @Test
    public void test() {
        AppointmentParam appointmentParam = new AppointmentParam();
        //JsfResult<PageInfo<GoodsStoreDTO>> result = appointmentApiExportService.findAppointmentStore(appointmentParam);
        //System.out.println(result);
        //
        Integer count = null;
        System.out.println(count+"");
    }

    /**
     * 修改预约状态的测试类
     */
    @Test
    public void modifyAppointmentTest() {
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setChannelType(6621685943L);
        appointmentParam.setJdAppointmentId(1L);
        appointmentParam.setAppointmentNo("10001");
        appointmentParam.setAppointmentDate("2020-02-12");
        appointmentParam.setUserPin("hezhensheng3");
        JsfResult<Boolean> result = null;
        try {
            result = appointmentApiExportService.modifyAppointment(appointmentParam);
            System.out.println(JsonUtil.toJSONString(result));
        } catch (Exception e) {
            System.out.println(1);
        }
    }

    /**
     * 取消预约状态的测试类
     */
    @Test
    public void cancelAppointmentTest() {
        AppointmentParam appointmentParam = new AppointmentParam();
        appointmentParam.setChannelType(6621685943L);
        appointmentParam.setJdAppointmentId(1L);
        appointmentParam.setAppointmentNo("10001");
        appointmentParam.setUserPin("hezhensheng3");
        JsfResult<Boolean> result = null;
        try {
            result = appointmentApiExportService.cancelAppointment(appointmentParam);
            System.out.println(JsonUtil.toJSONString(result));
        } catch (Exception e) {
            System.out.println(1);
        }
    }
    @Test
    public void cancelAppointmentTest11() {
        int size = 0;
        int pageSize = 500;
        int pageNum = 1;
        int expireDate = 30;
        StringBuilder stringBuilder = new StringBuilder();
        do {
            String formatDate = DateUtil.formatDate(DateUtil.addDays(new Date(), expireDate), CommonConstant.YMDHMS2359);
            Date expiredDate = DateUtil.parseDateWithPatterns(formatDate, CommonConstant.YMDHMS);
            PageInfo<SkuInfoEntity> pageInfo = enhanceCacheSkuInfoService.queryExpiredSkuInfoPage(pageNum,pageSize,expiredDate);
            size = pageInfo.getSize();
            if(size == 0){
                log.info("SkuExpiredHandlerJob ## pageInfo size=0;");
            }else {
                for (SkuInfoEntity skuInfoEntity : pageInfo.getList()) {
                    String formatDate1 = DateUtil.formatDate(skuInfoEntity.getValidEnd(), CommonConstant.yyyyMMddHMS);
                    stringBuilder.append("SKU :{ ").append(skuInfoEntity.getSkuNo()).append(" }").append(" 有效期即将于").append("{ ").append(formatDate1).append(" }").append("到期，请及时处理  ").append("\r\n");;
                }
            }
            pageNum++;
        }while (size != 0);

        System.out.println("asdas");
    }



}
