package com.jd.health.service;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.report.StructReportReviseStatisticsDTO;
import com.jd.health.medical.examination.export.param.report.StructReportReviseStatisticsParam;
import com.jd.health.medical.examination.service.business.report.query.ReportReviseStatisticsQueryService;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/12/22 11:05 上午
 * @version: 1.0
 * @see
 */
public class ReportReviseStatisticsQueryServiceTest extends BaseTest {

    @Resource
    private ReportReviseStatisticsQueryService reportReviseStatisticsQueryService;


    @Test
    public void statistic() throws Exception{
        StructReportReviseStatisticsParam param = new StructReportReviseStatisticsParam();
        Date start = DateUtils.parseDate("2021-12-01", "yyyy-MM-dd");
        param.setStartDate(start);
        param.setEndDate(new Date());
        List<StructReportReviseStatisticsDTO> res = reportReviseStatisticsQueryService.queryReviseStatistics(param);
        System.out.println("---------------");
        System.out.println(JsonUtil.toJSONString(res));
    }
}
