package com.jd.health.service;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.bo.sku.SkuPluginStoreBindGoodsBO;
import com.jd.health.medical.examination.service.manage.processor.ManThirdGoodsRelationProcessor;
import com.jd.health.medical.examination.service.simple.ExaminationSkuPluginRelService;
import com.jd.health.medical.examination.service.simple.SkuPluginStoreService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 测试
 * @author: yangxiyu
 * @date: 2021/6/21 19:37
 * @version: 1.0
 */
public class SkuPluginStoreServiceTest extends BaseTest {

    @Autowired
    private SkuPluginStoreService skuPluginStoreService;
    @Autowired
    private List<ManThirdGoodsRelationProcessor> processors;
    @Autowired
    private ExaminationSkuPluginRelService examinationSkuPluginRelService;

    @Test
    public void bind(){
        SkuPluginStoreBindGoodsBO bo = new SkuPluginStoreBindGoodsBO();
        bo.setChannelNo(5879687598L);
        bo.setParentGoodsId("JDQX000888820200222.1.003");
        bo.setGoodsPluginId("jd_TestPlugin1111111");
        //商品管理中该商家加项包配置的门店下架（skuStore status上架）
        processors.forEach(processor -> processor.goodsRelationReOn(bo));
    }

}
