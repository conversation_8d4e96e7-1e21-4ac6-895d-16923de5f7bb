package com.jd.health.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.enterprise.entity.CustomerEntity;
import com.jd.health.medical.examination.export.enterprise.dto.CustomerDTO;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.enterprise.CustomerService;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @author: yangxiyu
 * @date: 2021/9/15 4:40 下午
 * @version: 1.0
 * @see
 */
public class CustomerServiceTest extends BaseTest {

    @Resource
    private CustomerService customerService;

    @Test
    public void getPage(){
        CustomerEntity entity = new CustomerEntity();
        PageParam pageParam = new PageParam();
        PageInfo<CustomerDTO>  pageInfo = customerService.queryPageCustomer(entity, pageParam);
        Assert.assertNotNull(pageInfo);
    }
}
