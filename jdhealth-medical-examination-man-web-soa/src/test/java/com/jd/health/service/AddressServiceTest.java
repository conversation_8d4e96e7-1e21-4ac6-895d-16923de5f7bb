package com.jd.health.service;

import com.jd.addresstranslation.api.address.GBAddressToJDAddressService;
import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.addresstranslation.api.base.BaseResponse;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.constant.ExaminationConstant;
import com.jd.health.medical.examination.export.dto.basic.LetterDistrictDTO;
import com.jd.health.medical.examination.service.AddressService;
import com.jd.lbs.geocode.api.GeocodingService;
import com.jd.lbs.geocode.api.dto.GisPointDto;
import com.jd.medicine.base.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @author: yang<PERSON>yu
 * @date: 2021/8/25 11:12 上午
 * @version: 1.0
 * @see
 */
@Slf4j
public class AddressServiceTest extends BaseTest {

    @Resource
    AddressService addressService;

    @Resource
    AddressService enhanceCacheAddressService;
    @Resource
    GeocodingService geocodingService;
    @Resource
    private GBAddressToJDAddressService gbAddressToJDAddressService;
    @Test
    public void test(){
        String address = "工业园区苏州大道西9号中海财富中心东塔1~3层（苏州中心旁）";
        com.jd.lbs.geocode.api.base.BaseResponse<GisPointDto> result = geocodingService.geo(ExaminationConstant.APP_KEY_FOR_GEO_CODING, address);
        System.out.println(JsonUtil.toJSONString(result));
        System.out.println("---");
        BaseResponse<BaseAddressInfo> result2 = gbAddressToJDAddressService.getJDDistrictFromAddress(ExaminationConstant.APP_KEY_FOR_JDDISTRICTFROMLATLNG,address);
        System.out.println(JsonUtil.toJSONString(result2));

    }
    @Test
    public void getCities(){
        List<LetterDistrictDTO> cities = addressService.getCitiesPageByLetter();
        System.out.println("===================");
        System.out.println(JsonUtil.toJSONString(cities));
    }

    @Test
    public void getCitiesByCache() throws Exception{
//        LetterDistrictPageDTO cities = enhanceCacheAddressService.getCitiesPageByLetter();
//        System.out.println("===================");
//        System.out.println(JsonUtil.toJSONString(cities));

        int i = 0;
        while(i < 4){
            CompletableFuture.supplyAsync(()-> enhanceCacheAddressService.getCitiesPageByLetter())
            .whenComplete((r, t) -> {
                if (Objects.nonNull(r)){
                    log.info("city={}", r);
                }
                if(Objects.nonNull(t)){
                    log.error("getCitiesPageByLetter is fail", t);
                }
            });
            i++;
        }

        Thread.sleep(30000);
    }
}
