package com.jd.health.service;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.enterprise.param.AppointmentInfoExportParam;
import com.jd.health.medical.examination.service.appoint.AppointInfoEsService;
import com.jd.health.medical.examination.service.appoint.AppointmentInfoEsDTO;
import com.jd.health.medical.examination.service.appoint.QueryAppointmentEsBO;
import com.jd.health.medical.examination.service.enterprise.AppointmentExportService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/9/26 5:04 下午
 * @version: 1.0
 * @see
 */
public class AppointInfoEsServiceTest extends BaseTest {

    @Resource
    private AppointInfoEsService appointInfoEsService;

    @Resource
    private AppointmentExportService appointmentExportService;

    @Test
    public void getList(){
        QueryAppointmentEsBO bo = new QueryAppointmentEsBO();
        bo.setBusinessCode("0");
        List<AppointmentInfoEsDTO> res =  appointInfoEsService.queryBigListByExport(bo);
        System.out.println(JsonUtil.toJSONString(res));
    }

    @Test
    public void testExport(){
        AppointmentInfoExportParam exportParam = new AppointmentInfoExportParam();
        exportParam.setBusinessCode("0");
        appointmentExportService.exportAppointmentInfo(exportParam);
    }
}
