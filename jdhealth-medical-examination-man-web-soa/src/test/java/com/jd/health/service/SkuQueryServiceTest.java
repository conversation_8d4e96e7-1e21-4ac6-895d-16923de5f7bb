package com.jd.health.service;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.enterprise.bo.EnterpriseSkuBO;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.business.physical.sku.SkuQueryService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @author: yang<PERSON>yu
 * @date: 2021/11/16 10:07 下午
 * @version: 1.0
 * @see
 */
public class SkuQueryServiceTest extends BaseTest {

    @Resource
    private SkuQueryService skuQueryService;

    @Test
    public void get(){
        EnterpriseSkuBO bo = new EnterpriseSkuBO();
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        bo.setCompanyNo(1231L);
        skuQueryService.pageSkuGroupDTO(bo, pageParam);
    }
}
