package com.jd.health.service;

import com.google.common.collect.Sets;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.VendorTypeEnum;
import com.jd.health.medical.examination.domain.bo.BatchThirdBaseItemBO;
import com.jd.health.medical.examination.service.manage.ThirdBaseItemService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Set;

/**
 * @author: yang<PERSON>yu
 * @date: 2022/4/15 9:52 上午
 * @version: 1.0
 */
public class ThirdBaseItemServiceTest extends BaseTest {

    @Resource
    private ThirdBaseItemService thirdBaseItemService;

    @Test
    public void create(){
        Set<String> names = Sets.newHashSet();
        names.add("项目1");
        names.add("项目2");
        names.add("项目3");
        BatchThirdBaseItemBO batchThirdBaseItemBO = new BatchThirdBaseItemBO();
        batchThirdBaseItemBO.setItemNames(names);
        batchThirdBaseItemBO.setVendorType(VendorTypeEnum.SELF.getType());
        thirdBaseItemService.batchCreate(batchThirdBaseItemBO);
    }
}
