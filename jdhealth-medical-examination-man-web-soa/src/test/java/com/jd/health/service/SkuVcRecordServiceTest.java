package com.jd.health.service;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.bo.sku.SkuApplyInfoBO;
import com.jd.health.medical.examination.service.business.physical.sku.SkuVcRecordService;
import jdk.nashorn.internal.ir.annotations.Reference;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: yangxiyu
 * @date: 2022/4/20 6:24 下午
 * @version: 1.0
 */
public class SkuVcRecordServiceTest extends BaseTest {

    @Resource
    private SkuVcRecordService skuVcRecordService;


    @Test
    public void passTest(){
        SkuApplyInfoBO bo = new SkuApplyInfoBO();
        bo.setApplyId("a7b9ffeb647d4c08bf40b28807de1b73");
        bo.setApplyStateName("采销审核通过");
        bo.setApplyStateCode(1);
        bo.setApplyApproveTime(new Date());
        skuVcRecordService.updateStatusByApplyId(bo);
    }
}
