package com.jd.health.service;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseInfoEntity;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.enterprise.EnterpriseInfoService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @author: yang<PERSON>yu
 * @date: 2022/1/18 3:37 下午
 * @version: 1.0
 * @see
 */
public class EnterpriseInfoServiceTest extends BaseTest {

    @Resource
    private EnterpriseInfoService enterpriseInfoService;


    @Test
    public void test1(){
        EnterpriseInfoEntity enterpriseInfoEntity = new EnterpriseInfoEntity();
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(10);
        pageParam.setPageNum(1);
        enterpriseInfoService.queryPassPageEnterprise(enterpriseInfoEntity, pageParam);
    }
}
