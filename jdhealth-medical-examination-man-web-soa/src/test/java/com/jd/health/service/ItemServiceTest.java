package com.jd.health.service;

import com.github.pagehelper.PageInfo;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.dao.ItemDao;
import com.jd.health.medical.examination.dao.ItemRelDao;
import com.jd.health.medical.examination.domain.bo.ItemBO;
import com.jd.health.medical.examination.domain.personal.entity.ItemEntity;
import com.jd.health.medical.examination.domain.personal.entity.ItemRelEntity;
import com.jd.health.medical.examination.export.param.item.GetThreeLevelItemPageParam;
import com.jd.health.medical.examination.service.selfpersonal.ItemService;
import com.jd.medicine.base.common.spring.service.JimRedisService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/10/26 11:30 上午
 * @version: 1.0
 * @see
 */
public class ItemServiceTest extends BaseTest {

    @Resource
    private ItemService enhanceCacheItemService;
    /**
     *
     */
    @Resource
    private ItemDao itemDao;
    /**
     *
     */
    @Resource
    private ItemRelDao itemRelDao;
    /** */
    @Resource
    private JimRedisService jimRedisService;

    @Test
    public void init(){
        List<ItemEntity> itemEntities = itemDao.selectAll(null, null);
        int weight = 1;

        for(int i = 0; i < itemEntities.size() ; i++){

            ItemEntity e = itemEntities.get(i);
            ItemRelEntity relation = new ItemRelEntity();
            relation.setItemNo(e.getItemNo());
            relation.setItemLevel(e.getItemLevel());
            Integer parentLevel = e.getItemLevel();
            if(parentLevel > 1){
                parentLevel = parentLevel - 1;
                relation.setParentItemNo(e.getParentItemNo());
            }
            relation.setParentItemLevel(parentLevel);
            relation.setOperationUser("yangxyu");
            relation.setItemWeight(i);
            relation.setIsNew(0);
            itemRelDao.insertRel(relation);
        }
    }

    @Test
    public void getAllOneLevelItem(){
        List<ItemEntity> list = enhanceCacheItemService.getAllOneLevelItem();
        Assert.assertNotNull(list);
    }
    @Test
    public void getTwoLevelItemByParentNo(){
    }

    @Test
    public void getPageThreeLevelItem(){
        GetThreeLevelItemPageParam param = new GetThreeLevelItemPageParam();
        PageInfo<ItemBO> page = enhanceCacheItemService.getPageThreeLevelItem(param, 1, 10);
        System.out.println(JsonUtil.toJSONString(page));

        param.setItemName("菜单");
        PageInfo<ItemBO> page1 = enhanceCacheItemService.getPageThreeLevelItem(param, 1, 10);
        System.out.println(JsonUtil.toJSONString(page1));

        param.setOneLevelItemNo(123456789L);
        PageInfo<ItemBO> page2 = enhanceCacheItemService.getPageThreeLevelItem(param, 1, 10);
        System.out.println(JsonUtil.toJSONString(page2));

        param.setTwoLevelItemNo(123456700L);
        PageInfo<ItemBO> page3 = enhanceCacheItemService.getPageThreeLevelItem(param, 1, 10);
        System.out.println(JsonUtil.toJSONString(page3));
    }





}
