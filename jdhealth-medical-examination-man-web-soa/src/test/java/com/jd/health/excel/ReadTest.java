package com.jd.health.excel;

import com.alibaba.excel.EasyExcel;
import com.jd.health.BaseTest;
import com.jd.health.listener.ItemDataListener;
import com.jd.health.listener.ItemDataUpdateListener;
import com.jd.health.medical.examination.domain.personal.entity.ItemData;
import com.jd.health.medical.examination.service.selfpersonal.ItemService;
import com.jd.health.util.TestFileUtil;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;

/**
 * 读的常见写法
 *
 * <AUTHOR>
 */
public class ReadTest{
    private static final Logger LOGGER = LoggerFactory.getLogger(ReadTest.class);
    private ItemService itemService;
    @Test
    public void simpleRead() {
        // 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
        // 写法1：
        String fileName = TestFileUtil.getPath() + "demo" + File.separator + "demo2.xlsx";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(fileName, ItemData.class, new ItemDataListener(itemService)).sheet().doRead();
    }

    @Test
    public void simpleUpdateRead() {
        // 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
        // 写法1：
        String fileName = TestFileUtil.getPath() + "demo" + File.separator + "demo2.xlsx";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(fileName, ItemData.class, new ItemDataUpdateListener(itemService)).sheet().doRead();
    }
}
