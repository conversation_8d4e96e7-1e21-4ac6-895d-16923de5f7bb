package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.common.enums.SkuStoreEnum;
import com.jd.health.medical.examination.domain.personal.entity.SkuInfoEntity;
import com.jd.health.medical.examination.domain.personal.entity.SkuStoreEntity;
import com.jd.health.medical.examination.export.dto.GoodsStoreDTO;
import com.jd.health.medical.examination.export.dto.SkuStoreDTO;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.NumberUtil;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 简单测试常用方法
 *
 * <AUTHOR>
 * @date 2020-01-08 20:03
 **/
public class DemoTest {
    public static void main(String[] args) {
        Long time = System.currentTimeMillis();
        Date date = new Date(time);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:SS");
        System.out.println(sdf.format(date));
        //测试数据 美年大健康大望路分院 116.483541,39.899636
        //朝林广场 116.512174,39.798718
        //用户地址数据 中信新城 116.494315,39.79048
        //用户地址数据2 长沙高铁南站 113.072619,28.152865
        //lat 纬度   lng或lon 经度
        Double userLat = 39.79048;
        Double userLng = 116.494315;
        Double storeLat = 39.798718;
        Double storeLng = 116.512174;
        //百度显示距离
        System.out.println("距离1:"+getDistance(userLng,userLat,storeLng,storeLat));

        List<SkuStoreEntity> list = new ArrayList<>();
        SkuStoreEntity skuStoreEntity =new SkuStoreEntity();
        skuStoreEntity.setLat(28.152865);
        skuStoreEntity.setLng(113.072619);
        list.add(skuStoreEntity);
        SkuStoreEntity skuStoreEntity3 =new SkuStoreEntity();
        skuStoreEntity3.setLat(39.798718);
        skuStoreEntity3.setLng(116.512174);
        list.add(skuStoreEntity3);
        SkuStoreEntity skuStoreEntity2 =new SkuStoreEntity();
        skuStoreEntity2.setLat(39.899636);
        skuStoreEntity2.setLng(116.483541);
        list.add(skuStoreEntity2);

        SkuStoreEntity skuStoreEntity4 =new SkuStoreEntity();
        skuStoreEntity4.setLat(39.798718);
        skuStoreEntity4.setLng(116.512174);
        list.add(skuStoreEntity4);

        SkuStoreEntity skuStoreEntity5 =new SkuStoreEntity();
        skuStoreEntity5.setLat(39.798518);
        skuStoreEntity4.setLng(116.513674);
        list.add(skuStoreEntity5);

        SkuStoreEntity skuStoreEntity6 =new SkuStoreEntity();
        skuStoreEntity6.setLat(39.798618);
        skuStoreEntity6.setLng(116.512674);
        list.add(skuStoreEntity6);

        SkuStoreEntity skuStoreEntity7 =new SkuStoreEntity();
        skuStoreEntity7.setLat(39.798718);
        skuStoreEntity7.setLng(116.512774);
        list.add(skuStoreEntity7);

        SkuStoreEntity skuStoreEntity8 =new SkuStoreEntity();
        skuStoreEntity8.setLat(39.798818);
        skuStoreEntity8.setLng(116.512874);
        list.add(skuStoreEntity8);

        SkuStoreEntity skuStoreEntity9 =new SkuStoreEntity();
        skuStoreEntity9.setLat(39.798918);
        skuStoreEntity9.setLng(116.512974);
        list.add(skuStoreEntity9);

        SkuStoreEntity skuStoreEntity10 =new SkuStoreEntity();
        skuStoreEntity10.setLat(39.799018);
        skuStoreEntity10.setLng(116.513074);
        list.add(skuStoreEntity10);

        System.out.println("原数组:"+JsonUtil.toJSONString(list));
        Long start =System.currentTimeMillis();
        System.out.println("排序后:"+JsonUtil.toJSONString(getSortListByDistance(userLng,userLat,list)));
        System.out.println("cost:"+(System.currentTimeMillis() - start));
    }

    private static double EARTH_RADIUS = 6378.137;

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }


    public static List<GoodsStoreDTO> getSortListByDistance(double lon1, double lat1, List<SkuStoreEntity> skuStoreList){
        //计算用户与门店列表中的距离并进行排序
        if(CollectionUtil.isEmpty(skuStoreList)){
            return new ArrayList();
        }
        List<GoodsStoreDTO> skuStoreDTOList = new ArrayList<>();
        skuStoreList.forEach(skuStoreEntity -> {
            GoodsStoreDTO goodsStoreDTO = new GoodsStoreDTO();
            goodsStoreDTO.setGoodsId(skuStoreEntity.getGoodsId());
            goodsStoreDTO.setStoreName(skuStoreEntity.getStoreName());
            goodsStoreDTO.setStorePhone(skuStoreEntity.getStorePhone());
            goodsStoreDTO.setStoreType(skuStoreEntity.getStoreType());
            goodsStoreDTO.setStoreTypeDesc(SkuStoreEnum.getDescOfType(skuStoreEntity.getStoreType()));
            goodsStoreDTO.setStoreId(skuStoreEntity.getStoreId());
            goodsStoreDTO.setStoreAddr(skuStoreEntity.getStoreAddr());
            goodsStoreDTO.setChannelType(skuStoreEntity.getChannelNo());
            goodsStoreDTO.setReportSupport(skuStoreEntity.getReportSupport());
            goodsStoreDTO.setSupportWriteOff(skuStoreEntity.getSupportWriteOff());
            goodsStoreDTO.setStoreHours(skuStoreEntity.getStoreHours());
            goodsStoreDTO.setLat(skuStoreEntity.getLat());
            goodsStoreDTO.setLng(skuStoreEntity.getLng());
            skuStoreDTOList.add(goodsStoreDTO);
        });
        List<GoodsStoreDTO> resultList = new ArrayList<>();
        for (int i = 0; i < skuStoreDTOList.size(); i++) {
            GoodsStoreDTO goodsStoreDTO = skuStoreDTOList.get(i);
            if(null != goodsStoreDTO.getLng() && null != goodsStoreDTO.getLat()) {
                goodsStoreDTO.setDistance(getDistance(lon1, lat1, goodsStoreDTO.getLng(), goodsStoreDTO.getLat()));
                resultList.add(goodsStoreDTO);
            }
        }
        resultList.sort(new Comparator<GoodsStoreDTO>() {
            @Override
            public int compare(GoodsStoreDTO goodsStoreDTO1, GoodsStoreDTO goodsStoreDTO2) {
                Double dis1 = goodsStoreDTO1.getDistance();
                Double dis2 = goodsStoreDTO2.getDistance();
                if(dis1 > dis2){
                    return 1;
                }else if(dis1< dis2){
                    return -1;
                }
                return 0;
            }
        });
        return resultList;
    }
    /**
     * 基于googleMap中的算法得到两经纬度之间的距离,计算精度与谷歌地图的距离精度差不多，相差范围在0.2米以下
     * @param lon1 第一点的经度
     * @param lat1 第一点的纬度
     * @param lon2 第二点的经度
     * @param lat2 第二点的纬度
     * @return 返回的距离，单位km
     * */
    public static Double getDistance(double lon1,double lat1,double lon2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lon1) - rad(lon2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2)+Math.cos(radLat1)*Math.cos(radLat2)*Math.pow(Math.sin(b/2),2)));
        s = s * EARTH_RADIUS;
        return Double.parseDouble(NumberUtil.format(s));
    }



}
