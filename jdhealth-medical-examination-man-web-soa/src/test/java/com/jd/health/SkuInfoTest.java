package com.jd.health;

import com.jd.health.medical.examination.dao.SkuInfoDao;
import com.jd.health.medical.examination.export.dto.ExaminationGroupDTO;
import com.jd.health.medical.examination.export.service.SkuInfoExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/27 15:56
 */
public class SkuInfoTest extends BaseTest{
//    @Autowired
//    private SkuInfoExportService skuInfoExportService;
//    @Autowired
//    private SkuInfoDao skuInfoDao;
//    @Test
//    public void skuDescTest(){
//        Set<String> skuNos = new HashSet<>();
//        skuNos.add("13241");
//        skuNos.add("235432");
//        JsfResult<Map<String, String>> mapJsfResult = skuInfoExportService.querySkuDesc(skuNos);
//        System.out.println(JsonUtil.toJSONString(mapJsfResult));
//    }
    @Test
    public void updateSkuDescTest(){
//        Long groupNo = 1L;
//        String groupDesc = "测试是否能够修改成功";
        Long groupNo = 1L;
        String groupDesc = "测试是否能够修改成功";
//        int i = skuInfoDao.updateSkuDesc(groupNo, groupDesc);
//        System.out.println(i);
    }

    @Test
    public void querySkuSuitableBySkuNo(){
//        JsfResult<String> jsfResult=skuInfoExportService.querySkuSuitableBySkuNo("235432");
//        System.out.println("====================="+JsonUtil.toJSONString(jsfResult));
    }
    @Test
    public void queryGroupBySkuNo(){
//        JsfResult<List<ExaminationGroupDTO>> jsfResult=skuInfoExportService.queryGroupBySkuNo("235432");
//        System.out.println("====================="+JsonUtil.toJSONString(jsfResult));
    }

}
