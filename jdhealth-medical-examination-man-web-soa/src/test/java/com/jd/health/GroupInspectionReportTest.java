package com.jd.health;

import com.jd.health.medical.examination.common.enums.GroupInspectionReportStatisticTypeEnum;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.enums.StatisticTabTypeEnum;
import com.jd.health.medical.examination.export.dto.AgeRangeDTO;
import com.jd.health.medical.examination.export.dto.AreaRegionDTO;
import com.jd.health.medical.examination.export.dto.GroupInspectionReportDTO;
import com.jd.health.medical.examination.export.param.GroupInspectionReportParam;
import com.jd.health.medical.examination.export.service.GroupInspectionReportExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName GroupInspectionReportTest
 * @Description
 * <AUTHOR>
 * @Date 2021/3/22 16:50
 **/
public class GroupInspectionReportTest extends BaseTest{

	/**
	 * exportService
	 */
	@Autowired
	private GroupInspectionReportExportService exportService;

	/**
	 * queryGroupInspectionReport
	 */
	@Test
	public void queryGroupInspectionReport() {
		GroupInspectionReportParam param = new GroupInspectionReportParam();
		param.setCompanyNo(2436112898L);
		param.setStatisticTabType(StatisticTabTypeEnum.BASIC_TOP_20_SEARCH.getType());
		param.setMainStatisticType(GroupInspectionReportStatisticTypeEnum.DEPARTMENT.getType());
		param.setMainStatisticUnitId(0L);
		param.setSubStatisticType(GroupInspectionReportStatisticTypeEnum.CHANNEL.getType());
		param.setSubStatisticUnitId(ProviderTypeEnum.MEI_NIAN_SYSTEM_DOCKING_CHANNEL.getTypeNo());
		param.setThirdStatisticType(GroupInspectionReportStatisticTypeEnum.NONE.getType());
		param.setThirdStatisticUnitId(0L);
		JsfResult<GroupInspectionReportDTO> result = exportService.queryGroupInspectionReport(param);
		System.out.println(JsonUtil.toJSONString(result));
	}

	/**
	 * queryGroupReportAgeRangeList
	 */
	@Test
	public void queryGroupReportAgeRangeList() {
		JsfResult<List<AgeRangeDTO>> result = exportService.queryGroupReportAgeRangeList();
		System.out.println(JsonUtil.toJSONString(result));
	}

	/**
	 * queryGroupInspectionReport
	 */
	@Test
	public void queryGroupReportAreaRegionList() {
		JsfResult<List<AreaRegionDTO>> result = exportService.queryGroupReportAreaRegionList();
		System.out.println(JsonUtil.toJSONString(result));
	}
}
