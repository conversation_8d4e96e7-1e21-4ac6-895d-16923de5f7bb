package com.jd.health;

import com.jd.health.medical.examination.export.enterprise.param.AppointmentInfoExportParam;
import com.jd.health.medical.examination.export.enterprise.service.AppointmentInfoExportService;
import com.jd.health.medical.examination.service.enterprise.AppointmentExportService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/9/22 - 8:45 下午
 **/
public class AppointmentExportServiceTest extends BaseTest{
    
    @Autowired
    private AppointmentExportService appointmentExportService;
    @Autowired
    private AppointmentInfoExportService appointmentInfoExportService;
    
    @Test
    public void exportTest() {
        AppointmentInfoExportParam exportParam = new AppointmentInfoExportParam();
        exportParam.setOperatorId("yintao996138852");
        exportParam.setOperationType(10);
        exportParam.setBusinessCode("1");
//        appointmentExportService.exportAppointmentInfo(exportParam);
        appointmentInfoExportService.asyncExportAppointmentInfo(exportParam);
    }
    
}
