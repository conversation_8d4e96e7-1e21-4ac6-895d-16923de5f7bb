package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.report.ThirdConclusionDTO;
import com.jd.health.medical.examination.export.param.ConclusionKeywordParam;
import com.jd.health.medical.examination.export.param.ExaminationManConclusionParam;
import com.jd.health.medical.examination.export.param.LinkConclusion2StandardParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.param.report.QueryThirdConclusionPageParam;
import com.jd.health.medical.examination.export.service.ConclusionExportService;
import com.jd.health.medical.examination.export.service.DevManExportService;
import com.jd.health.medical.examination.service.MyReportService;
import com.jd.health.medical.examination.service.ThirdConclusionService;
import com.jd.health.medical.examination.service.history.analysis.AnalysisMeiNianReportImpl;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xbj
 * @Date: 2020/12/25 9:54
 */
public class ConclusionTest extends  BaseTest{

    /**
     *
     */
    @Autowired
    private ThirdConclusionService thirdConclusionService;


    /**
     * conclusionExportService
     */
    @Autowired
    private ConclusionExportService conclusionExportService;

    /**
     * devManExportService
     */
    @Autowired
    private DevManExportService devManExportService;

    /**
     * analysisMeiNianReport
     */
    @Autowired
    private AnalysisMeiNianReportImpl analysisMeiNianReportImpl;

    @Autowired
    private MyReportService myReportService;



    @Test
    public  void saveConclusionAndKeyword(){
        ExaminationManConclusionParam examinationManConclusionParam=new ExaminationManConclusionParam();
        examinationManConclusionParam.setChannelNo(123L);
        examinationManConclusionParam.setCheckMean("我是意思");
        examinationManConclusionParam.setCheckSuggest("我是建议");
        examinationManConclusionParam.setConclusionName("我是结论");
        examinationManConclusionParam.setConclusionNo(20192L);
//        examinationManConclusionParam.setExaminationGroupIi(83293L);
//        examinationManConclusionParam.setExaminationGroupI(2892L);
        examinationManConclusionParam.setOperationUser("xubingjian");
        List<ConclusionKeywordParam> keywordList=new ArrayList<>();
        ConclusionKeywordParam keywordParam=new ConclusionKeywordParam();
        keywordParam.setChannelNo(390390L);
        keywordParam.setKeyWordName("2902");
        keywordList.add(keywordParam);
        examinationManConclusionParam.setKeywordList(keywordList);
        JsfResult<Boolean> jsfResult=conclusionExportService.saveConclusionAndKeyword(examinationManConclusionParam);
        System.out.println("============"+ JsonUtil.toJSONString(jsfResult));
    }

    @Test
    public void selectUnsolvedConclusionPageTest(){

        QueryThirdConclusionPageParam condition = new QueryThirdConclusionPageParam();
        PageParam pageParam=new PageParam();
        pageParam.setPageSize(20);
        pageParam.setPageNum(1);

        condition.setRelevanceStatus(1);
        JsfResult<PageInfo<ThirdConclusionDTO>> jsfResult= conclusionExportService.queryPageByConditions(condition,pageParam);
        Assert.assertNotNull(jsfResult);
        System.out.println("============"+ JsonUtil.toJSONString(jsfResult));
    }
    @Test
    public  void updatePendingConclusion(){
        LinkConclusion2StandardParam linkConclusion2StandardParam =new LinkConclusion2StandardParam();
        linkConclusion2StandardParam.setConclusionNo(20192L);
        linkConclusion2StandardParam.setOperationUser("xubingjian");
        JsfResult<Boolean> jsfResult = conclusionExportService.link2Standard(linkConclusion2StandardParam);
        System.out.println("============"+ JsonUtil.toJSONString(jsfResult));
    }
    @Test
    public  void updateConclusionAndKeyword(){
        ExaminationManConclusionParam examinationManConclusionParam=new ExaminationManConclusionParam();
        examinationManConclusionParam.setChannelNo(123L);
        examinationManConclusionParam.setCheckMean("我是意思1");
        examinationManConclusionParam.setCheckSuggest("我是建议1");
        examinationManConclusionParam.setConclusionName("我是结论1");
        examinationManConclusionParam.setConclusionNo(20192L);
//        examinationManConclusionParam.setExaminationGroupIi(83293L);
//        examinationManConclusionParam.setExaminationGroupI(2892L);
        examinationManConclusionParam.setOperationUser("xubingjian1");
        List<ConclusionKeywordParam> keywordList=new ArrayList<>();
        ConclusionKeywordParam keywordParam=new ConclusionKeywordParam();
        keywordParam.setChannelNo(390390L);
        keywordParam.setKeyWordName("290221都是进口的");
        keywordList.add(keywordParam);
        examinationManConclusionParam.setKeywordList(keywordList);
        JsfResult<Boolean> jsfResult= conclusionExportService.updateConclusionAndKeyword(examinationManConclusionParam);
        System.out.println("============"+ JsonUtil.toJSONString(jsfResult));
    }


    @Test
    public void fixBlankSpaceConclusion() {
        JsfResult<Integer> jsfResult = devManExportService.fixBlankSpaceConclusion();
        System.out.println("jsfResult = "+JsonUtil.toJSONString(jsfResult));
    }


}
