package com.jd.health.enterprise;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.EnterpriseSkuPluginTypeEnum;
import com.jd.health.medical.examination.domain.enterprise.bo.EnterpriseSkuPluginBO;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuPluginEntity;
import com.jd.health.medical.examination.export.dto.sku.SkuPluginInfoDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuPluginDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuPluginParam;
import com.jd.health.medical.examination.export.param.sku.SkuCompatibilityCheckParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuPluginExportService;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.enterprise.EnterpriseSkuPluginService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;

/**
 * 企销sku插件测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/4 11:13
 */

public class EnterpriseSkuPluginTest extends BaseTest {
    
    @Autowired
    private EnterpriseSkuPluginService enterpriseSkuPluginService;
    
    @Autowired
    private EnterpriseSkuPluginExportService enterpriseSkuPluginExportService;
    
    @Test
    public void queryPluginSkuPageInfoTest() {
        EnterpriseSkuPluginParam param = new EnterpriseSkuPluginParam();
        param.setProjectNo(9140762114L);
        param.setSkuNo("100001173543");
        param.setPluginType(EnterpriseSkuPluginTypeEnum.ADDITION_PACKAGE.getType());
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(20);
        JsfResult<PageInfo<EnterpriseSkuPluginDTO>> pageInfo = enterpriseSkuPluginExportService.querySkuPluginInfoPage(param, pageParam);
        System.out.println(JsonUtil.toJSONString(pageInfo));
    }
    
    @Test
    public void queryListWithBindStatusTest() {
        EnterpriseSkuPluginParam param = new EnterpriseSkuPluginParam();
        param.setProjectNo(9140762114L);
        param.setSkuNo("100001173543");
        param.setGroupNo(189027104258L);
        param.setPluginType(EnterpriseSkuPluginTypeEnum.ADDITION_PACKAGE.getType());
        JsfResult<List<SkuPluginInfoDTO>> result = enterpriseSkuPluginExportService.queryListWithBindStatus(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    @Test
    public void queryListByBatchPluginSkuNoTest() {
        EnterpriseSkuPluginParam param = new EnterpriseSkuPluginParam();
        param.setProjectNo(9140762114L);
        param.setSkuNo("100001173543");
        param.setPluginType(EnterpriseSkuPluginTypeEnum.ADDITION_PACKAGE.getType());
        List<String> list = new ArrayList<>();
        list.add("3211233");
        param.setPluginSkuNoList(list);
        param.setNeedItems(true);
        JsfResult<List<EnterpriseSkuPluginDTO>> listJsfResult = enterpriseSkuPluginExportService.queryListByBatchPluginSkuNo(param);
        System.out.println(JsonUtil.toJSONString(listJsfResult));
    }
    
    @Test
    public void isSkuPluginInUsedTest() {
        EnterpriseSkuPluginEntity enterpriseSkuPluginEntity = new EnterpriseSkuPluginEntity();
        enterpriseSkuPluginEntity.setSkuNo("100001173545");
        enterpriseSkuPluginEntity.setPluginType(EnterpriseSkuPluginTypeEnum.ADDITION_PACKAGE.getType());
        enterpriseSkuPluginEntity.setPluginSkuNo("100001173545");
        EnterpriseSkuPluginBO result = enterpriseSkuPluginService.isSkuPluginInUsed(enterpriseSkuPluginEntity);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    @Test
    public void updateTest() {
        Long projectNo = 946286082L;
        String skuNo = "234524234";
        EnterpriseSkuPluginParam param = new EnterpriseSkuPluginParam();
        param.setProjectNo(projectNo);
        param.setSkuNo(skuNo);
        param.setPluginType(EnterpriseSkuPluginTypeEnum.ADDITION_PACKAGE.getType());
        param.setPluginSkuNo("999999999");
        param.setPluginSkuPrice(BigDecimal.valueOf(324524));
        param.setPluginSkuQxPrice(BigDecimal.valueOf(324524));
        JsfResult<Boolean> flag = enterpriseSkuPluginExportService.updateSkuPlugin(param);
        System.out.println(flag);
    }
    
    @Test
    public void addTest() {
        JsfResult<Boolean> addFlag = addTestSkuPluginEntity();
        assertTrue(addFlag.getData());
    }
    
    @Test
    public void deleteTest() {
        Long projectNo = 23485789L;
        String skuNo = "2345324";
        Integer pluginType = 1;
        EnterpriseSkuPluginParam param = new EnterpriseSkuPluginParam();
        param.setProjectNo(projectNo);
        param.setSkuNo(skuNo);
        param.setPluginType(pluginType);
        param.setPluginSkuNo(skuNo);
        JsfResult<Boolean> flag = enterpriseSkuPluginExportService.deleteSkuPlugin(param);
        assertTrue(flag.getData());
    }
    
    @Test
    public void addExportTest() {
        JsfResult<Boolean> addFlag = addTestSkuPluginEntity();
        assertTrue(addFlag.getData());
    }
    
    private JsfResult<Boolean> addTestSkuPluginEntity() {
        Long projectNo = 23485789L;
        String skuNo = "2345324";
        BigDecimal pluginSkuPrice = BigDecimal.valueOf(0.01);
        BigDecimal pluginSkuQxPrice = BigDecimal.valueOf(0.01);
        EnterpriseSkuPluginParam param = new EnterpriseSkuPluginParam();
        param.setProjectNo(projectNo);
        param.setSkuNo(skuNo);
        param.setPluginType(EnterpriseSkuPluginTypeEnum.ADDITION_PACKAGE.getType());
        param.setPluginSkuNo(skuNo);
        param.setPluginSkuName("测试专用");
        param.setPluginSkuAlias("测试专用");
        param.setPluginSkuPrice(pluginSkuPrice);
        param.setPluginSkuQxPrice(pluginSkuQxPrice);
        param.setPluginSkuQxDesc("京东加项包企销描述-测试专用");
        param.setPluginSalesMsg("已有10000+人加购");
        return enterpriseSkuPluginExportService.addSkuPlugin(param);
    }
    
    @Test
    public void querySkuPluginByProjectNoAndSkuNoTest() {
    
        EnterpriseSkuPluginParam param= new EnterpriseSkuPluginParam();
        param.setPluginType(1);
        param.setProjectNo(9140762114L);
        param.setSkuNo("100001173543");
        param.setNeedItems(true);
        JsfResult<List<EnterpriseSkuPluginDTO>> listJsfResult = enterpriseSkuPluginExportService.querySkuPluginByProjectNoAndSkuNo(param);
        System.out.println(listJsfResult);
    }

    @Test
    public void skuCompatibilityCheck(){
        SkuCompatibilityCheckParam checkParam = new SkuCompatibilityCheckParam();
        checkParam.setApplySkuNo("1000000");
        List<String> existSkuNos = Lists.newArrayList();
        existSkuNos.add("100001173545");
        checkParam.setExistSkuNos(existSkuNos);
        checkParam.setSearchSkuType(2);
        enterpriseSkuPluginExportService.skuCompatibilityCheck(checkParam);
    }
}
