package com.jd.health.enterprise;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.ContractConfigEnum;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseContractConfigDTO;
import com.jd.health.medical.examination.export.enterprise.dto.ExaminationManCompanySkuDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseContractConfigParam;
import com.jd.health.medical.examination.export.enterprise.param.sku.ExaminationManCompanySkuParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseContractConfigExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目履约类型单元测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/17 13:55
 */
public class EnterpriseContractTest extends BaseTest {

    @Autowired
    private EnterpriseContractConfigExportService enterpriseContractConfigExportService;
    
    /**
     * 保存商品信息
     */
    @Test
    public void saveCompanySkuTest(){
        ExaminationManCompanySkuParam param = new ExaminationManCompanySkuParam();
        param.setCompanyNo(9347586734L);
        param.setSkuNo("*********");
        param.setSkuName("kent测试商品2");
        param.setSkuPrice(new BigDecimal("75.23"));
        param.setSkuMarketPrice(new BigDecimal("99.23"));
        param.setSkuType(1);
        param.setSkuBuyerNum("20");
        param.setSkuDesc("测试商品2介绍，限制很多字！");
        param.setCategoryId("112233");
        param.setCategoryName("基因检测");
        param.setCreateUser("kent");
        param.setUpdateUser("kent");
        JsfResult<Boolean> result = enterpriseContractConfigExportService.saveExaminationCompanySku(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    /**
     * 更新商品信息
     */
    @Test
    public void updateCompanySkuTest(){
        ExaminationManCompanySkuParam param = new ExaminationManCompanySkuParam();
        param.setCompanyNo(9347586734L);
        param.setSkuNo("44445555");
        param.setSkuName("kent测试商品修改");
        param.setSkuPrice(new BigDecimal("80"));
        param.setSkuMarketPrice(new BigDecimal("99.23"));
        param.setSkuBuyerNum("555");
        param.setSkuDesc("更新一首歌测试商品介绍，限制很多字！");
        param.setUpdateUser("王力宏");
        //更新
        param.setOperationType(1);
        JsfResult<Boolean> result = enterpriseContractConfigExportService.updateCompanySkuDetail(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    /**
     * 删除商品信息
     */
    @Test
    public void delCompanySkuTest(){
        ExaminationManCompanySkuParam param = new ExaminationManCompanySkuParam();
        param.setCompanyNo(9347586734L);
        param.setSkuNo("44445555");
        param.setUpdateUser("周杰伦");
        //2 删除
        param.setOperationType(2);
        JsfResult<Boolean> result = enterpriseContractConfigExportService.updateCompanySkuDetail(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    /**
     * 查询公司的实物商品信息
     */
    @Test
    public void queryExaminationCompanySkuTest(){
        ExaminationManCompanySkuParam param = new ExaminationManCompanySkuParam();
        param.setCompanyNo(9347586734L);
        JsfResult<List<ExaminationManCompanySkuDTO>> result = enterpriseContractConfigExportService.queryExaminationCompanySku(param);
        List<ExaminationManCompanySkuDTO> list = result.getData();
        Map<String,List<ExaminationManCompanySkuDTO>> map = list.stream().collect(Collectors.groupingBy(ExaminationManCompanySkuDTO::getCategoryName));
        for(Map.Entry<String, List<ExaminationManCompanySkuDTO>> entry: map.entrySet()) {
            System.out.println(entry.getKey());
            List<ExaminationManCompanySkuDTO> sonSkuList = entry.getValue();
            System.out.println(sonSkuList);
        }
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    /**
     * 查询实物商品信息
     */
    @Test
    public void queryCompanySkuDetailTest(){
        ExaminationManCompanySkuParam param = new ExaminationManCompanySkuParam();
        param.setCompanyNo(9347586734L);
        param.setSkuNo("44445555");
        JsfResult<ExaminationManCompanySkuDTO> result = enterpriseContractConfigExportService.queryCompanySkuDetail(param);
        System.out.println(JsonUtil.toJSONString(result));
        param.setSkuNo("44445556");
        JsfResult<ExaminationManCompanySkuDTO> result1 = enterpriseContractConfigExportService.queryCompanySkuDetail(param);
        System.out.println(JsonUtil.toJSONString(result1));
    }

    @Test
    public void queryTest(){
        EnterpriseContractConfigParam param = new EnterpriseContractConfigParam();
        param.setCompanyNo(9347586734L);
        JsfResult<List<EnterpriseContractConfigDTO>> result = enterpriseContractConfigExportService.selectEnterpriseContract(param);
        System.out.println(JsonUtil.toJSONString(result));
    }

    @Test
    public void updateTest(){
        EnterpriseContractConfigParam param = new EnterpriseContractConfigParam();
        param.setContractType(ContractConfigEnum.NO_EQUITY.getContractType());
        param.setContractName(ContractConfigEnum.NO_EQUITY.getContractName());
        param.setContractDesc("测试权益1");
        param.setContractDetailDesc("测试权益1");
        param.setContractUrl("https://www.jd.com");
        param.setContractStatus(ContractConfigEnum.NO_EQUITY.getContractStatus());

        param.setCompanyNo(9347586734L);
        JsfResult<Boolean> result = enterpriseContractConfigExportService.updateEnterpriseContract(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
}
