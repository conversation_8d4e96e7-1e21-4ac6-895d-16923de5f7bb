package com.jd.health.enterprise;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.dao.enterprise.EnterpriseOrderDao;
import com.jd.health.medical.examination.domain.enterprise.bo.EnterpriseOrderBO;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseOrderEntity;
import com.jd.health.medical.examination.service.enterprise.EnterpriseOrderService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/31 18:27
 */
public class EnterpriseOrderTest extends BaseTest {
    @Autowired
    private EnterpriseOrderService enterpriseOrderService;
    @Autowired
    private EnterpriseOrderDao enterpriseOrderDao;
    @Test
    public void queryOrderTest(){
        List<EnterpriseOrderBO> enterpriseOrderBOS = enterpriseOrderService.queryEnterpriseOrders(1L);
        System.out.println(JsonUtil.toJSONString(enterpriseOrderBOS));
    }

    @Test
    public void insertOrder(){
        EnterpriseOrderEntity enterpriseOrderEntity = new EnterpriseOrderEntity();
        enterpriseOrderEntity.setOrderId(12312L);
        enterpriseOrderEntity.setOrderPrice(324523);
        enterpriseOrderEntity.setOrderPayTime(new Date());
        enterpriseOrderEntity.setCompanyOrderType(6);
        enterpriseOrderEntity.setCompanyLicenseNo("12312");
        enterpriseOrderEntity.setCompanyAddr("未知");
        enterpriseOrderEntity.setCompanyName("未知");
        enterpriseOrderEntity.setSkuNo("*********");
        enterpriseOrderEntity.setSkuName("能行吗");
        enterpriseOrderEntity.setUserPin("迟彪");
        enterpriseOrderDao.addEnterpriseOrder(enterpriseOrderEntity);
    }
}
