package com.jd.health.enterprise;

import com.github.pagehelper.PageInfo;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.dao.SkuInfoDao;
import com.jd.health.medical.examination.domain.enterprise.entity.EnterpriseSkuUpgradeEntity;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseGroupItemDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuUpgradeDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuUpgradeInfoDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuUpgradeParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseInfoExportService;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuUpgradeExportService;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.manage.EnterpriseSkuUpgradeManageService;
import com.jd.health.medical.examination.service.enterprise.EnterpriseSkuUpgradeService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/18 17:13
 */

public class EnterpriseSkuUpgradeTest extends BaseTest {

    @Autowired
    private EnterpriseSkuUpgradeService enterpriseSkuUpgradeService;
    @Autowired
    private EnterpriseSkuUpgradeManageService enterpriseSkuUpgradeManageService;
    @Autowired
    private EnterpriseSkuUpgradeExportService enterpriseSkuUpgradeExportService;
    @Autowired
    private SkuInfoDao skuInfoDao;

    @Autowired
    private EnterpriseInfoExportService enterpriseInfoExportService;

    @Test
    public void queryTest(){
        Long companyNo = 23485789L;
        String parentSkuNo="132423";
        JsfResult<List<EnterpriseSkuUpgradeDTO>> listJsfResult = enterpriseSkuUpgradeExportService.querySkuUpgrade(companyNo, parentSkuNo);
        System.out.println(JsonUtil.toJSONString(listJsfResult));
    }

    @Test
    public void queryUpgradeSkuTest(){
        EnterpriseSkuParam enterpriseSkuParam = new EnterpriseSkuParam();
        enterpriseSkuParam.setCompanyNo(22848835L);
        enterpriseSkuParam.setParentSkuNo("132423");
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<EnterpriseSkuDTO>> pageInfoJsfResult = enterpriseSkuUpgradeExportService.querySkuInfoPage(enterpriseSkuParam, pageParam);
        System.out.println(JsonUtil.toJSONString(pageInfoJsfResult));
    }
    @Test
    public void deleteTest(){
        Long companyNo = 23485789L;
        String parentSkuNo = "132423";
        String skuNo = "23423";
        Boolean flag = enterpriseSkuUpgradeManageService.deleteSkuUpgrade(companyNo, parentSkuNo, skuNo);
        System.out.println(flag);
    }
    @Test
    public void updateTest(){
        Long companyNo = 946286082L;
        String parentSkuNo = "2345245";
        String skuNo = "*********";
        EnterpriseSkuUpgradeEntity entity = new EnterpriseSkuUpgradeEntity();
        entity.setCompanyNo(companyNo);
        entity.setParentSkuNo(parentSkuNo);
        entity.setSkuNo(skuNo);
        entity.setSkuUpgradeName("小萝卜");
        entity.setSkuUpgradePrice(234324);
        Boolean flag = enterpriseSkuUpgradeService.updateSkuUpgrade(entity);
        System.out.println(flag);
    }

    @Test
    public void addTest(){
        Long companyNo = 23485789L;
        String parentSkuNo = "132423";
        String skuNo = "2345324";
        EnterpriseSkuUpgradeEntity entity = new EnterpriseSkuUpgradeEntity();
        entity.setSkuName("测试专用");
        entity.setGroupNo(2345432L);
        entity.setParentSkuCompanyPrice(43252345);
        entity.setSkuPersonPrice(324524);
        entity.setSkuUpgradePrice(2345);
        entity.setCompanyNo(companyNo);
        entity.setParentSkuNo(parentSkuNo);
        entity.setSkuNo(skuNo);
        entity.setSkuUpgradeName("小萝卜");
        Boolean flag = enterpriseSkuUpgradeManageService.addSkuUpgrade(entity);
        System.out.println(flag);
    }

    @Test
    public void testCompare(){
        Long parentGroupNo= 101429805570L;
        Long groupNo=728495030786L;
        JsfResult<List<EnterpriseGroupItemDTO>> listJsfResult = enterpriseSkuUpgradeExportService.compareGroupItem(parentGroupNo, groupNo);
        System.out.println(JsonUtil.toJSONString(listJsfResult));
    }

    @Test
    public void upGradeList(){
        EnterpriseSkuUpgradeParam param = new EnterpriseSkuUpgradeParam();
        param.setCompanyNo(23485789L);
        param.setParentSkuNo("132423");
        JsfResult<List<EnterpriseSkuUpgradeInfoDTO>> listJsfResult = enterpriseSkuUpgradeExportService.querySkuUpgradeByParentSkuNo(param);
        //SkuInfoEntity parentSku = skuInfoDao.selectSkuInfoBySkuNo(param.getParentSkuNo());
        //System.out.println(JsonUtil.toJSONString(parentSku));
        System.out.println(JsonUtil.toJSONString(listJsfResult));
    }

    @Test
    public void getHttpUrl(){
        JsfResult<String> result = enterpriseInfoExportService.getHttpUrl("/enterprise.logo/1662985026041546242.jpg");
        System.out.println(result.getData());
    }

}
