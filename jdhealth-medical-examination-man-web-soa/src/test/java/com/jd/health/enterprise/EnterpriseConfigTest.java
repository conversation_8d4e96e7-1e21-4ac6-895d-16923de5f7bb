package com.jd.health.enterprise;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseInfoConfigDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseConfigParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseInfoConfigExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Please enter class description information
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/8 10:43
 */
public class EnterpriseConfigTest extends BaseTest {
    
    @Autowired
    private EnterpriseInfoConfigExportService enterpriseInfoConfigExportService;
    
    @Test
    public void queryTest(){
        EnterpriseConfigParam enterpriseConfigParam = new EnterpriseConfigParam();
        enterpriseConfigParam.setCompanyNo(234523L);
        JsfResult<EnterpriseInfoConfigDTO> result = enterpriseInfoConfigExportService.queryBaseConfig(enterpriseConfigParam);
        System.out.println(JsonUtil.toJSONString(result));
    }
    
    @Test
    public void updateStatusTest(){
        EnterpriseConfigParam param = new EnterpriseConfigParam();
        param.setCompanyNo(234523L);
        param.setDrainageStatus(0);
        param.setShopLogoStatus(1);
        param.setWatermarkStatus(1);
        JsfResult<Boolean> result = enterpriseInfoConfigExportService.updateConfigStatus(param);
        System.out.println(JsonUtil.toJSONString(result));
    }
}
