package com.jd.health.export;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.param.sku.SkuApplyRecordParam;
import com.jd.health.medical.examination.export.param.sku.UpdateSkuParam;
import com.jd.health.medical.examination.export.service.SkuInfoExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @author: yangxiyu
 * @date: 2022/4/14 2:29 下午
 * @version: 1.0
 */
public class SkuInfoExportServiceTest extends BaseTest {
    @Resource
    private SkuInfoExportService skuInfoExportService;


    /**
     * 更新基础套餐商品
     */
    @Test
    public void testAddSkuByVc(){
        System.out.println("11111");
        SkuApplyRecordParam param = new SkuApplyRecordParam();
        param.setSkuName("kent测试商品");
        param.setJdPrice(20000);
        //JsfResult<String> stringJsfResult = skuInfoExportService.addSkuByVc(param);
        //System.out.println("22222"+stringJsfResult.getData());
    }
    
    /**
     * 更新基础套餐商品
     */
    @Test
    public void updateSkuTest(){
        UpdateSkuParam param = new UpdateSkuParam();
        param.setSkuNo("57672074612");
        param.setGroupNo(326957356546L);
        param.setSkuUnionType(1);
        param.setValidType(1);
        param.setSkuValid(365);
        skuInfoExportService.updateSku(param);
    }
}
