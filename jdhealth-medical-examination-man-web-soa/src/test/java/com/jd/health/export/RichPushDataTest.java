package com.jd.health.export;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataOperateEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataStatusEnum;
import com.jd.health.medical.examination.common.enums.YnStatusEnum;
import com.jd.health.medical.examination.export.param.ThirdAdditionPackageParam;
import com.jd.health.medical.examination.export.param.ThirdGoodsStoreParam;
import com.jd.health.medical.examination.export.param.ThirdPackageItemParam;
import com.jd.health.medical.examination.export.param.ThirdStoreParam;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: yang<PERSON>yu
 * @date: 2021/6/28 15:39
 * @version: 1.0
 * @see
 */
public class RichPushDataTest extends BaseTest {

    @Resource
    private ThirdDataExportService thirdDataExportService;
    /**
     * 自测新增供应商推送门店
     */
    @Test
    public void createStore() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdStoreParam.setStoreId("rich_store_001");
        thirdStoreParam.setStoreName("瑞慈测试门店001");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("18330115894");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());//为新增
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }


    /**
     * 自测新增供应商推送门店
     */
    @Test
    public void createStore002() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdStoreParam.setStoreId("rich_store_004");
        thirdStoreParam.setStoreName("瑞慈测试门店004");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("18330115894");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());//为新增
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);


    }

    @Test
    public void createGoodsStore003() {
        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("rich_store_004");
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        thirdDataExportService.pushGoodsStoreInfo(param);
    }
    /**
     * 修改门店基础信息
     */
    @Test
    public void changeStoreOff() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdStoreParam.setStoreId("rich_store_002");
        thirdStoreParam.setStoreName("单元测试门店002_off");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.OFF.getCode());
        thirdStoreParam.setStorePhone("18330115001");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());//为新增
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void storeReOn() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        thirdStoreParam.setStoreId("rich_store_002");
        thirdStoreParam.setStoreName("单元测试门店002_ReOn");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("18330115001");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());//为新增
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }


    /**
     * 修改门店基础信息
     */
    @Test
    public void createGoodsStore() {
        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("rich_store_001");
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        thirdDataExportService.pushGoodsStoreInfo(param);
    }


    /**
     * 修改门店基础信息
     */
    @Test
    public void goodsStoreOff() {
        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("rich_store_001");
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setStatus(YnStatusEnum.NO.getCode());
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        System.out.println("param:" + JsonUtil.toJSONString(param));
        thirdDataExportService.pushGoodsStoreInfo(param);
    }

    /**
     * 重新开启
     */
    @Test
    public void goodsStoreReOn() {
        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("rich_store_001");
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setStatus(YnStatusEnum.YES.getCode());
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        System.out.println("param:" + JsonUtil.toJSONString(param));
        thirdDataExportService.pushGoodsStoreInfo(param);
    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void createGoodsRelation() {
        ThirdAdditionPackageParam param = new ThirdAdditionPackageParam();
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        param.setPackageId("JUNIT_package_001");
        param.setPackageName("单元测试加项包");
        param.setPackagePrice(0.02D);
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setOperateType(ThirdDataOperateEnum.ADD.getCode());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setPackageDesc("加项包说明");
        param.setVenderId(12L);
        param.setPackageSuitable(1);
        param.setPackageMarry(2);

        List<ThirdPackageItemParam> itemList = new ArrayList<>();
        ThirdPackageItemParam item1 = new ThirdPackageItemParam();
        item1.setPackageId("JUNIT_package_001");
        item1.setItemName("身高-测试");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable(suitList);
        item1.setItemMeans("项目意义");
        itemList.add(item1);
        param.setPackageItemList(itemList);
        thirdDataExportService.pushAdditionPackage(param);
    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void goodsRelationOff() {
        ThirdAdditionPackageParam param = new ThirdAdditionPackageParam();
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        param.setPackageId("JUNIT_package_001");
        param.setPackageName("单元测试加项包");
        param.setPackagePrice(0.02D);
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        param.setStatus(ThirdDataStatusEnum.OFF.getCode());
        param.setPackageDesc("加项包说明");
        param.setVenderId(12L);
        param.setPackageSuitable(1);
        param.setPackageMarry(2);

        List<ThirdPackageItemParam> itemList = new ArrayList<>();
        ThirdPackageItemParam item1 = new ThirdPackageItemParam();
        item1.setPackageId("JUNIT_package_001");
        item1.setItemName("身高-测试");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable(suitList);
        item1.setItemMeans("项目意义");
        itemList.add(item1);
        param.setPackageItemList(itemList);
        thirdDataExportService.pushAdditionPackage(param);
    }


    /**
     * 修改门店基础信息
     */
    @Test
    public void goodsRelationReOn() {
        ThirdAdditionPackageParam param = new ThirdAdditionPackageParam();
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        param.setPackageId("JUNIT_package_001");
        param.setPackageName("单元测试加项包");
        param.setPackagePrice(0.02D);
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setPackageDesc("加项包说明");
        param.setVenderId(12L);
        param.setPackageSuitable(1);
        param.setPackageMarry(2);

        List<ThirdPackageItemParam> itemList = new ArrayList<>();
        ThirdPackageItemParam item1 = new ThirdPackageItemParam();
        item1.setPackageId("JUNIT_package_001");
        item1.setItemName("身高-测试");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable(suitList);
        item1.setItemMeans("项目意义");
        itemList.add(item1);
        param.setPackageItemList(itemList);
        thirdDataExportService.pushAdditionPackage(param);
    }
}
