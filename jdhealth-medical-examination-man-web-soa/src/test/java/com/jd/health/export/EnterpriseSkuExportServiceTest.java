package com.jd.health.export;

import com.github.pagehelper.PageInfo;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.param.sku.PageEnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @author: yangxiyu
 * @date: 2022/7/3 8:47 下午
 * @version: 1.0
 */
public class EnterpriseSkuExportServiceTest extends BaseTest {

    @Resource
    private EnterpriseSkuExportService enterpriseSkuExportService;


    @Test
    public void pageSku(){
        PageEnterpriseSkuParam param = new PageEnterpriseSkuParam();
        param.setPageNum(1);
        param.setPageSize(5);
        param.setCompanyNo(9140762114L);
        JsfResult<PageInfo<EnterpriseSkuDTO>> res =  enterpriseSkuExportService.pageEnterpriseSku(param);
        Assert.assertTrue(res.getData() instanceof  PageInfo);
    }
}
