package com.jd.health.export;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.service.CompositionExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * 权益包export测试
 *
 * <AUTHOR>
 * @date 2020-04-29 14:11
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class CompositionExportServiceTest {

    @Autowired
    private CompositionExportService compositionExportService;

    @Test
    public void test() {
        EnterpriseSkuParam enterpriseSkuParam = new EnterpriseSkuParam();
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<EnterpriseSkuDTO>> result =  compositionExportService.querySkuInfoPage(enterpriseSkuParam, pageParam);
        System.out.println(result);
    }


}
