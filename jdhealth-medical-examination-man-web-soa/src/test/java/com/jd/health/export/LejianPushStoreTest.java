package com.jd.health.export;

import com.google.common.collect.Sets;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.*;
import com.jd.health.medical.examination.export.param.*;
import com.jd.health.medical.examination.export.param.third.PushGoodsRelParam;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.health.medical.examination.export.service.ThirdStoreManExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.error.DefaultErrorCode;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @author: yang<PERSON>yu
 * @date: 2021/6/28 15:39
 * @version: 1.0
 * @see
 */
public class LejianPushStoreTest extends BaseTest {

    @Resource
    private ThirdDataExportService thirdDataExportService;
    /**
     * 自测新增供应商推送门店
     */
    @Test
    public void createStore() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        thirdStoreParam.setStoreId("junit_store_001");
        thirdStoreParam.setStoreName("单元测试门店001");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("18330115894");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());//为新增
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }


    /**
     * 修改门店基础信息
     */
    @Test
    public void storeReOn() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        thirdStoreParam.setStoreId("junit_store_001");
        thirdStoreParam.setStoreName("单元测试门店001_change");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("18330115001");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());//为新增
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }


    /**
     * 修改门店基础信息
     */
    @Test
    public void createGoodsStore() {
        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("lejian_test_store_001");
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setGoodsId("lejian_goods_001");
        thirdDataExportService.pushGoodsStoreInfo(param);
    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void pushBasicGoods() {
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("lejian_goods_001");
        param.setGoodsName("乐荐测试套餐001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(1);
        param.setStatus(1);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);
        param.setGoodsType(ThirdGoodsTypeEnum.BASIC_GOODS.getType());
        fillItem(param);

        JsfResult result = thirdDataExportService.pushGoodsInfo(param);


    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void pushPackage() {
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("lejian_package_002");
        param.setGoodsName("乐荐测试加项包002");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(1);
        param.setStatus(1);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);
        param.setGoodsType(ThirdGoodsTypeEnum.ADDITION_PACKAGE.getType());
        fillItem(param);

        JsfResult result = thirdDataExportService.pushGoodsInfo(param);

        param.setGoodsId("lejian_package_003");
        param.setGoodsName("乐荐测试加项包003");
        result = thirdDataExportService.pushGoodsInfo(param);

    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void pushGoodsRel() {
        PushGoodsRelParam param = new PushGoodsRelParam();
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setRelType(PushGoodsRelTypeEnum.BASIC_GOODS_REL_TYPE.getRelType());
        param.setGoodsId("lejian_goods_001");

        Set<String> targetGoodsId = Sets.newHashSet("lejian_package_002", "lejian_package_003");
        param.setTargetGoodsId(targetGoodsId);
        thirdDataExportService.pushGoodsRel(param);
    }

    /**
     * 修改门店基础信息
     */
    @Test
    public void pushGoodsRelOff() {
        PushGoodsRelParam param = new PushGoodsRelParam();
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setRelType(PushGoodsRelTypeEnum.BASIC_GOODS_REL_TYPE.getRelType());
        param.setGoodsId("lejian_goods_001");

        Set<String> targetGoodsId = Sets.newHashSet();
        param.setTargetGoodsId(targetGoodsId);
        thirdDataExportService.pushGoodsRel(param);
    }


    /**
     * 修改门店基础信息
     */
    @Test
    public void pushGoodsRelReOn() {
        PushGoodsRelParam param = new PushGoodsRelParam();
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setRelType(PushGoodsRelTypeEnum.BASIC_GOODS_REL_TYPE.getRelType());
        param.setGoodsId("lejian_goods_001");
        Set<String> targetGoodsId = Sets.newHashSet("lejian_package_002", "lejian_package_003");
        param.setTargetGoodsId(targetGoodsId);
        thirdDataExportService.pushGoodsRel(param);
    }

    private void fillItem(ThirdGoodsParam param){
        List<ThirdGoodsItemParam> goodsItemList = new ArrayList<>();
        ThirdGoodsItemParam item1 = new ThirdGoodsItemParam();
        item1.setGoodsId("LJ_goods_id_002");
        item1.setItemName("身高-测试");
        item1.setGoodsId2("goodsItemid1");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable2(suitList);
        item1.setItemMeans("项目意义");
        goodsItemList.add(item1);
        param.setGoodsItemList(goodsItemList);
    }



}
