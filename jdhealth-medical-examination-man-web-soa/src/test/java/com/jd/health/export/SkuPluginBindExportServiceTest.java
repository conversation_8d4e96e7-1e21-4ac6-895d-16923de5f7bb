package com.jd.health.export;

import com.jd.health.medical.examination.common.enums.SkuSpeciesEnum;
import com.jd.health.medical.examination.export.dto.NameValueDTO;
import com.jd.health.medical.examination.export.dto.SkuInfoDTO;
import com.jd.health.medical.examination.export.dto.sku.GroupBindPluginDTO;
import com.jd.health.medical.examination.export.param.sku.*;
import com.jd.health.medical.examination.export.service.SkuInfoExportService;
import com.jd.health.medical.examination.export.service.sku.SkuPluginBindExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.error.DefaultErrorCode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.omg.CORBA.PUBLIC_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
@Rollback
public class SkuPluginBindExportServiceTest {

    @Autowired
    SkuPluginBindExportService skuPluginBindExportService;

    @Autowired
    SkuInfoExportService skuInfoExportService;


    /**
     * SKU绑定上级套餐
     */
    @Test
    public void bindGoods(){
        SkuBindGoodsParam param = new SkuBindGoodsParam();
        param.setGoodsId("LJ_goods_id_001");
        param.setSkuNo("100001173543");
        param.setChannelNo(1101975721L);

        skuInfoExportService.bindGoods(param);
    }
    @Test
    public void bindGoodsPlugin(){
        SkuBindPluginParam bindParam = new SkuBindPluginParam();
        bindParam.setSkuNo("100001173543");
        bindParam.setSkuPluginNo("100001173573");
        bindParam.setGroupNo(768120309250L);
        bindParam.setChannelNo(1101975721L);
        bindParam.setGoodsPluginId("LJ_goods_package_id_001");

        skuPluginBindExportService.bindSkuAndPlugin(bindParam);
    }

    /**
     *
     */
    @Test
    public void getSkuNameValueList(){
        SkuGetListParam querySkuParam = new SkuGetListParam();
        querySkuParam.setMaxCount(50);
        querySkuParam.setSkuSpecies(SkuSpeciesEnum.PACKAGE_SKU.getTypeNo());
        JsfResult<List<NameValueDTO>> result = skuInfoExportService.querySkuNameValueList(querySkuParam);
        Assert.notEmpty(result.getData());
    }

    // 1、获取加项包配置详情

    /**
     *
     */
    @Test
    public void getDetail(){
        JsfResult<List<GroupBindPluginDTO>> jsfResult = skuPluginBindExportService.querySkuGroupPluginDetail("100001173543");
        org.junit.Assert.assertEquals(jsfResult.getCode(), DefaultErrorCode.SUCCESS.getCode());

    }

    @Test
    public void setPrice(){
        List<SkuStorePriceParam> list = new ArrayList<>();
        SkuStorePriceParam param = new SkuStorePriceParam();
        param.setId(1202L);
        param.setPrice(null);
        list.add(param);

        SkuStorePriceParam param1 = new SkuStorePriceParam();
        param1.setId(1209L);
        param1.setPrice(0D);
        list.add(param1);

        SkuStorePriceParam param2 = new SkuStorePriceParam();
        param2.setId(1210L);
        param2.setPrice(1.0D);
        list.add(param2);

        skuPluginBindExportService.setStorePrice(list);
    }

    @Test
    public void setPriceByBatch(){
        SkuStoreBatchPriceParam param = new SkuStoreBatchPriceParam();
        Set<Long> ids = new HashSet<>();
        ids.add(1211L);
        param.setIds(ids);
        param.setPrice(2D);
        skuPluginBindExportService.setStorePriceByBatch(param);
        param.setPrice(null);
        skuPluginBindExportService.setStorePriceByBatch(param);
        param.setPrice(0D);
        skuPluginBindExportService.setStorePriceByBatch(param);
    }
    
    /**
     * 查询商品信息 包含类目信息
     */
    @Test
    public void querySkuAndCategoryInfoTest(){
        JsfResult<SkuInfoDTO> result = skuInfoExportService.querySkuAndCategoryInfo("100001173543");
        System.out.println(result);
    }

}
