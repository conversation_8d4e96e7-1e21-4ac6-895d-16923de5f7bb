package com.jd.health.export;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.basic.HrUserInfoDTO;
import com.jd.health.medical.examination.export.dto.sku.SkuApplyRecordDTO;
import com.jd.health.medical.examination.export.dto.sku.SkuApplySaleDTO;
import com.jd.health.medical.examination.export.param.HrUserParam;
import com.jd.health.medical.examination.export.param.sku.SkuApplyRecordParam;
import com.jd.health.medical.examination.export.param.sku.SkuApplySaleParam;
import com.jd.health.medical.examination.export.service.basic.XfylManHrInfoExportService;
import com.jd.health.medical.examination.export.service.sku.SkuApplyRecordExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

/**
 * 描述
 *  SkuApplyRecordServiceTest
 * <AUTHOR>
 * @date 2022/4/11 16:11
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
@Rollback
public class SkuApplyRecordServiceTest {
    
    /**
     * skuApplyRecordExportService
     */
    @Resource
    private SkuApplyRecordExportService skuApplyRecordExportService;
    
    /**
     * xfylManHrInfoExportService
     */
    @Resource
    private XfylManHrInfoExportService xfylManHrInfoExportService;
    
    /**
     * SKU绑定上级套餐
     */
    @Test
    public void testQueryUserInfoByErp(){
        System.out.println("11111");
        HrUserParam param = new HrUserParam();
        param.setErp("999");
        JsfResult<HrUserInfoDTO> result = xfylManHrInfoExportService.queryUserInfoByErp(param);
        System.out.println("result:"+JsonUtil.toJSONString(result));
        System.out.println("2222");
    }
    
    /**
     * SKU绑定上级套餐
     */
    @Test
    public void testQuerySkuApplyRecordPage(){
        System.out.println("11111");
        SkuApplyRecordParam param = new SkuApplyRecordParam();
        param.setPageNum(1);
        param.setPageSize(10);
        param.setSkuNo("11");
        param.setGoodsName("222");
        JsfResult<PageInfo<SkuApplyRecordDTO>> pageInfoJsfResult = skuApplyRecordExportService.querySkuApplyRecordPage(param);
        System.out.println("pageInfoJsfResult:"+JsonUtil.toJSONString(pageInfoJsfResult));
        System.out.println("2222");
    }
    
    /**
     * 查询VC建品的采销信息
     */
    @Test
    public void testQuerySkuApplySaleInfo(){
        System.out.println("11111");
        JsfResult<SkuApplySaleDTO> result = skuApplyRecordExportService.querySkuApplySaleInfo();
        System.out.println("result:"+JsonUtil.toJSONString(result));
        System.out.println("2222");
    }
    
    /**
     * 查询VC建品的采销信息
     */
    @Test
    public void testUpdateSkuApplySaleInfo(){
        System.out.println("11111");
        SkuApplySaleParam param = new SkuApplySaleParam();
        param.setBuyerErp("wangdage");
        param.setBuyerName("王大哥.啊");
        param.setSalerErp("xing1122");
        param.setSalerName("大学天");
        param.setUserPin("test");
        JsfResult<Boolean> result = skuApplyRecordExportService.updateSkuApplySaleInfo(param);
        System.out.println("result:"+JsonUtil.toJSONString(result));
        System.out.println("2222");
    }
}
