package com.jd.health.export;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.report.analysis.ExaminationReportAnalysisDTO;
import com.jd.health.medical.examination.export.service.report.ExaminationReportExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 检后-解析报告服务单元测试
 *
 * <AUTHOR>
 * @description 检后-解析报告服务单元测试
 * @date 2022/11/15 18:25
 */
public class ExaminationReportExportServiceTest extends BaseTest {

    /**
     * 解析报告入口
     */
    @Resource
    private ExaminationReportExportService examinationReportExportService;

    /**
     * 单元测试:查询解析报告信息
     */
    @Test
    public void queryReportAnalysisData() {
        JsfResult<ExaminationReportAnalysisDTO> jsfResult = examinationReportExportService.queryReportAnalysisData(null);
        System.out.println(jsfResult);
        System.out.println("查询成功");
    }

}
