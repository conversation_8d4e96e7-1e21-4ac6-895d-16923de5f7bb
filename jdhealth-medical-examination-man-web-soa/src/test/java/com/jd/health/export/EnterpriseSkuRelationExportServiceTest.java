package com.jd.health.export;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.common.enums.CallerTypeEnum;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuDTO;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseSkuRelationDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseManageParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuParam;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseSkuRelationParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuRelationExportService;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.service.enterprise.bo.SkuRelationBO;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.math.BigDecimal;

/**
 * 亲属同购export测试类
 *
 * <AUTHOR>
 * @date 2020-03-18 18:42
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class EnterpriseSkuRelationExportServiceTest {

    private static final Logger log = LoggerFactory.getLogger(EnterpriseSkuRelationExportServiceTest.class);

    @Autowired
    private EnterpriseSkuRelationExportService enterpriseSkuRelationExportService;

    /**
     * 更新亲属套餐停启用状态
     */
    @Test
    public void updateRelationStatusTest() {
        EnterpriseManageParam param = new EnterpriseManageParam();
        param.setCompanyNo(7096528386L);
        param.setRelationStatus(0);
        JsfResult result = enterpriseSkuRelationExportService.updateRelationStatus(param);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 分页获取亲属套餐列表
     */
    @Test
    public void querySkuRelationPageTest() {
        EnterpriseSkuRelationParam param = new EnterpriseSkuRelationParam();
        param.setCompanyNo(22848835L);
        param.setPageNum(1);
        param.setPageSize(5);
        param.setRelationType(21);
        param.setCallerType(CallerTypeEnum.QX_MAN_FRONT.getCode());
        JsfResult<PageInfo<EnterpriseSkuRelationDTO>> result = enterpriseSkuRelationExportService.querySkuRelationPage(param);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 查询已维护的sku（sku标识是否在亲属套餐中）
     */
    @Test
    public void querySkuInfoPageTest() {
        EnterpriseSkuParam skuParam = new EnterpriseSkuParam();
        skuParam.setCompanyNo(1001L);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<EnterpriseSkuDTO>> result = enterpriseSkuRelationExportService.querySkuInfoPage(skuParam, pageParam);
        log.info("EnterpriseSkuRelationExportServiceTest -> querySkuInfoPageTest end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 新增亲属套餐
     */
    @Test
    public void addSkuRelationTest() {
        EnterpriseSkuRelationParam param = buildSkuRelationParam();
        JsfResult result = enterpriseSkuRelationExportService.addSkuRelation(param);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 更新亲属套餐
     */
    @Test
    public void updateSkuRelationTest() {
        EnterpriseSkuRelationParam param = buildSkuRelationParam();
        JsfResult result = enterpriseSkuRelationExportService.updateSkuRelation(param);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 删除亲属套餐
     */
    @Test
    public void deleteSkuRelationTest() {
        EnterpriseSkuRelationParam param = buildSkuRelationParam();
        JsfResult result = enterpriseSkuRelationExportService.deleteSkuRelation(param);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 获取亲属关系类型列表
     */
    @Test
    public void queryRelationType() {
        JsfResult result = enterpriseSkuRelationExportService.queryRelationType(93L);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 是否展示亲属同购菜单
     */
    @Test
    public void queryRelationPurchase() {
        JsfResult result = enterpriseSkuRelationExportService.queryRelationPurchase(93L);
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    /**
     * 是否展示亲属同购菜单
     */
    @Test
    public void querySkuRelationDetail() {
        JsfResult result = enterpriseSkuRelationExportService.querySkuRelationDetail(93L,"132423");
        log.info("EnterpriseSkuRelationExportServiceTest -> test end, result={}", JsonUtil.toJSONString(result));
    }

    private EnterpriseSkuRelationParam buildSkuRelationParam() {
        EnterpriseSkuRelationParam skuRelationparam = new EnterpriseSkuRelationParam();
        skuRelationparam.setCompanyNo(1001L);
        skuRelationparam.setCompanyName("客户3");
        skuRelationparam.setSkuNo("1003");
        skuRelationparam.setSkuName("套餐3");
        skuRelationparam.setGroupNo(1003L);
        // 大客户售价，可修改
        skuRelationparam.setSkuCompanyPrice(new BigDecimal(3));
        skuRelationparam.setSkuPersonPrice(new BigDecimal(13));
        // 亲属套餐名称，可修改
        skuRelationparam.setRelationSkuName("新的亲属套餐3");
        // 亲属类型，可修改
       skuRelationparam.setRelationType(3);
       skuRelationparam.setCreateUser("hzs3");
       skuRelationparam.setUpdateUser("hzs3");
        return skuRelationparam;
    }

}
