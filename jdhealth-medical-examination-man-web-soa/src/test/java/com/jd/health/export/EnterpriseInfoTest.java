//package com.jd.health.export;
//
//import com.github.pagehelper.PageInfo;
//import com.google.common.collect.Lists;
//import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseManageDTO;
//import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseOrderDTO;
//import com.jd.health.medical.examination.export.enterprise.dto.GisPointDTO;
//import com.jd.health.medical.examination.export.enterprise.param.EnterpriseManageParam;
//import com.jd.health.medical.examination.export.enterprise.param.EnterpriseOperatorParam;
//import com.jd.health.medical.examination.export.enterprise.service.EnterpriseInfoExportService;
//import com.jd.health.medical.examination.export.enterprise.service.EnterpriseOrderExportService;
//import com.jd.health.medical.examination.export.enterprise.service.EnterpriseSkuExportService;
//import com.jd.health.medical.examination.export.param.PageParam;
//import com.jd.health.medical.examination.export.service.ItemExportService;
//import com.jd.health.medical.examination.service.AddressService;
//import com.jd.health.medical.examination.service.enterprise.config.OperatorPinConfig;
//import com.jd.jsf.gd.util.JsonUtils;
//import com.jd.medicine.b2c.base.export.domain.JsfResult;
//import com.jd.medicine.base.common.util.JsonUtil;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///**
// * 大客户管理测试类
// *
// * <AUTHOR>
// * @date 2020-01-08 13:49
// **/
//@RunWith(SpringJUnit4ClassRunner.class)
//@WebAppConfiguration
//@ContextConfiguration(locations = {"classpath:spring-config.xml"})
//public class EnterpriseInfoTest {
//
//    private static final Logger log = LoggerFactory.getLogger(EnterpriseInfoTest.class);
//    @Autowired
//    private ItemExportService itemExportService;
//    /**
//     * 客户信息对外接口
//     */
//    @Autowired(required = false)
//    private EnterpriseInfoExportService enterpriseInfoExportService;
//
//    /**
//     * 运营pin配置
//     */
//    @Autowired
//    private OperatorPinConfig operatorPinConfig;
//
//    /**
//     * 地址与经纬度转换
//     */
//    @Resource
//    private AddressService addressService;
//
//    /**
//     * 企销sku信息测试
//     */
//    @Autowired
//    private EnterpriseSkuExportService enterpriseSkuExportService;
//
//    /**
//     * 企业订单
//     */
//    @Autowired
//    private EnterpriseOrderExportService enterpriseOrderExportService;
//
//    @Test
//    public void queryEnterpriseOrderByUserPinTest() {
//        String userPin = "hezhensheng3";
//        JsfResult<EnterpriseOrderDTO> result = enterpriseOrderExportService.queryEnterpriseOrderByUserPin(userPin);
//        System.out.println(result);
//    }
//
//    @Test
//    public void queryEnterpriseOrderListTest() {
//        Long companyNo = 1L;
//        JsfResult<List<EnterpriseOrderDTO>> result = enterpriseOrderExportService.querySettleOrderList(companyNo);
//        System.out.println(result);
//    }
//
//    /**
//     * 更新服务模式为先服后单
//     */
//    @Test
//    public void updateServiceModelTest() {
//        Long companyNo = 22848835L;
//        JsfResult<Integer> result = enterpriseInfoExportService.updateServiceModel(companyNo);
//        log.info("EnterpriseInfoTest -> updateServiceModelTest end, result={}", result);
//    }
//
//    /**
//     * 更新项目经理
//     */
//    @Test
//    public void updateManageInfoTest() {
//        Long companyNo = 22848835L;
//        String managerPin = "hezhensheng3";
//        JsfResult<Integer> result = enterpriseInfoExportService.updateManageInfo(companyNo, managerPin);
//        log.info("EnterpriseInfoTest -> updateServiceModelTest end, result={}", result);
//    }
//
//    /**
//     * 查询hr电话
//     */
//    @Test
//    public void test1() {
//        JsfResult<String> result = enterpriseSkuExportService.getSkuImageUrl("23487823");
//        log.info("EnterpriseInfoTest -> test1 end, result={}", JsonUtil.toJSONString(result));
//    }
//    /**
//     * 查询hr电话
//     */
//    @Test
//    public void queryHrPhoneByCompanyNoTest() {
//        JsfResult<List<String>> result = enterpriseInfoExportService.queryHrPhoneByCompanyNo(22848835L);
//        System.out.println(result);
//    }
//
//    /**
//     * 录入客户信息
//     */
//    @Test
//    public void addEnterpriseInfoTest() {
//        EnterpriseManageParam enterpriseManageParam = new EnterpriseManageParam();
//        enterpriseManageParam.setCompanyName("客户2");
//        enterpriseManageParam.setStartDate(new Date());
//        enterpriseManageParam.setEndDate(new Date());
//        enterpriseManageParam.setManagerPin("anyongcong");
//        List<EnterpriseOperatorParam> operatorPinList = new ArrayList<>(2);
//        EnterpriseOperatorParam enterpriseOperatorParam = new EnterpriseOperatorParam();
//        enterpriseOperatorParam.setOperatorPin("chibiao");
//        EnterpriseOperatorParam enterpriseOperatorParam1 = new EnterpriseOperatorParam();
//        enterpriseOperatorParam1.setOperatorPin("hezhensheng3");
//        operatorPinList.add(enterpriseOperatorParam);
//        operatorPinList.add(enterpriseOperatorParam1);
//        enterpriseManageParam.setOperatorPinList(operatorPinList);
//        enterpriseManageParam.setServiceStatus(0);
//        enterpriseManageParam.setPin("chibiao12345");
//        enterpriseManageParam.setPassword("2069760aA+-");
//        enterpriseManageParam.setMobile("***********");
//        enterpriseManageParam.setLicenseNo("**********");
//        enterpriseManageParam.setCreateUser("hezhensheng3");
//        enterpriseManageParam.setLat(new BigDecimal(123));
//        enterpriseManageParam.setLng(new BigDecimal(345));
//        enterpriseManageParam.setCompanyAddress("河北沧州");
//        enterpriseManageParam.setCompanyContacterPhone("***********");
//        enterpriseManageParam.setCompanyContacter("chibiao");
//        enterpriseManageParam.setEmployeeLoginAccount(1);
//        enterpriseManageParam.setEmployeeLoginPwd(2);
//        JsfResult<Boolean> jsfResult = enterpriseInfoExportService.addEnterprise(enterpriseManageParam);
//        System.out.println(JsonUtil.toJSONString(jsfResult));
//    }
//
//    /**
//     * 更新客户信息
//     */
//    @Test
//    public void updateEnterpriseInfoTest() {
//        EnterpriseManageParam enterpriseManageParam = new EnterpriseManageParam();
//        enterpriseManageParam.setCompanyNo(7096528386L);
//        enterpriseManageParam.setCompanyName("客户01");
//        enterpriseManageParam.setStartDate(new Date());
//        enterpriseManageParam.setEndDate(new Date());
//        enterpriseManageParam.setManagerPin("yintao12");
//        List<EnterpriseOperatorParam> operatorPinList = new ArrayList<>(2);
//        EnterpriseOperatorParam enterpriseOperatorParam = new EnterpriseOperatorParam();
//        enterpriseOperatorParam.setCompanyNo(7096528386L);
//        enterpriseOperatorParam.setOperatorPin("hezhensheng3");
//        operatorPinList.add(enterpriseOperatorParam);
//        enterpriseManageParam.setOperatorPinList(operatorPinList);
//        enterpriseManageParam.setServiceStatus(0);
//        JsfResult<Boolean> result = enterpriseInfoExportService.updateEnterprise(enterpriseManageParam);
//        System.out.println(result);
//    }
//
//    /**
//     * 分页查询大客户信息
//     */
//    @Test
//    public void queryEnterpriseInfoPageTest() {
//        EnterpriseManageParam param = new EnterpriseManageParam();
//        param.setManageType("高级经理");
//        param.setManagerPin("anyongcong");
////        param.setRelOrderStatus(0);
//        PageParam pageParam = new PageParam();
//        pageParam.setPageNum(1);
//        pageParam.setPageSize(5);
//        JsfResult<PageInfo<EnterpriseManageDTO>> jsfResult = enterpriseInfoExportService.queryEnterprisePage(param, pageParam);
//        log.info("EnterpriseManageTest -> queryEnterpriseInfoPageTest start, jsfResult={}", JsonUtils.toJSONString(jsfResult));
//    }
//
//    /**
//     * 分页条件查询运营人员管理的客户信息
//     */
//    @Test
//    public void queryEnterpriseInfoPageByOperatorTest() {
//        EnterpriseOperatorParam param = new EnterpriseOperatorParam();
//        param.setOperatorPin("hezhensheng3");
//        param.setCompanyName("客户");
//        PageParam pageParam = new PageParam();
//        pageParam.setPageNum(1);
//        pageParam.setPageSize(4);
//        JsfResult<PageInfo<EnterpriseManageDTO>> jsfResult = enterpriseInfoExportService.queryEnterprisePageByOperator(param, pageParam);
//        log.info("EnterpriseManageTest -> queryEnterpriseInfoPageByOperatorTest end, jsfResult={}", jsfResult);
//    }
//
//    /**
//     * DUCC中获取运营人员列表
//     */
//    @Test
//    public void queryOperatorPins() {
//        String operatorPins = operatorPinConfig.getOperatorPins();
//        System.out.println(operatorPins);
//    }
//
//    /**
//     * 根据全地址获取经纬度
//     */
//    @Test
//    public void getLngLatByAddressTest() {
//        String address = "北京市大兴区京东集团总部";
//        JsfResult<GisPointDTO> result = null;
//        try {
////            result = addressService.getLngLatByAddress(address);
//            result = enterpriseInfoExportService.getLngLatByAddress(address);
//            System.out.println();
//        } catch (Exception e) {
//            System.out.println(e);
//        }
//    }
//
//    // *************新增字段后的测试方法****************************
//
//    /**
//     * 录入客户信息
//     */
//    @Test
//    public void addEnterpriseInfo1Test() {
//        EnterpriseManageParam enterpriseManageParam = new EnterpriseManageParam();
//        enterpriseManageParam.setCompanyName("新的客户1");
//        enterpriseManageParam.setStartDate(new Date());
//        enterpriseManageParam.setEndDate(new Date());
//        enterpriseManageParam.setManagerPin("anyongcong");
//        enterpriseManageParam.setServiceStatus(0);
//        enterpriseManageParam.setCompanyAddress("北京市大兴区京东集团总部");
//        enterpriseManageParam.setLng(BigDecimal.valueOf(139.92));
//        enterpriseManageParam.setLat(BigDecimal.valueOf(26.67));
//        enterpriseManageParam.setCompanyContacter("yintao12");
//        enterpriseManageParam.setCompanyContacterPhone("***********");
//        enterpriseManageParam.setEmployeeLoginAccount(1);
//        enterpriseManageParam.setEmployeeLoginPwd(2);
//        enterpriseManageParam.setSalesman("sales1,sales2");
//        List<EnterpriseOperatorParam> operatorPinList = new ArrayList<>(2);
//        EnterpriseOperatorParam enterpriseOperatorParam = new EnterpriseOperatorParam();
//        enterpriseOperatorParam.setOperatorPin("chibiao");
//        EnterpriseOperatorParam enterpriseOperatorParam1 = new EnterpriseOperatorParam();
//        enterpriseOperatorParam1.setOperatorPin("hezhensheng3");
//        operatorPinList.add(enterpriseOperatorParam);
//        operatorPinList.add(enterpriseOperatorParam1);
//        enterpriseManageParam.setOperatorPinList(operatorPinList);
//        JsfResult<Boolean> jsfResult = enterpriseInfoExportService.addEnterprise(enterpriseManageParam);
//        log.info("EnterpriseManageTest -> addEnterpriseInfoTest end, ", JsonUtil.toJSONString(jsfResult));
//    }
//
//    /**
//     * 更新客户信息
//     */
//    @Test
//    public void updateEnterpriseInfo1Test() {
//        EnterpriseManageParam enterpriseManageParam = new EnterpriseManageParam();
//        enterpriseManageParam.setCompanyNo(7096528386L);
//        enterpriseManageParam.setCompanyName("客户01");
//        enterpriseManageParam.setManagerPin("anyongcong");
//        enterpriseManageParam.setEmployeeLoginAccount(3);
//        enterpriseManageParam.setCompanyAddress("北京市大兴区京东集团总部12");
//        enterpriseManageParam.setLng(BigDecimal.valueOf(33.333));
//        enterpriseManageParam.setLat(BigDecimal.valueOf(66.666));
//        JsfResult<Boolean> result = null;
//        try {
//            result = enterpriseInfoExportService.updateEnterprise(enterpriseManageParam);
//            System.out.println(result);
//        } catch (Exception e) {
//            System.out.println();
//        }
//    }
//
//    /**
//     * 查询大客户服务周期 yyyy.MM.dd-yyyy.MM.dd
//     */
//    @Test
//    public void queryEnterpriseInfoByCompanyNoTest() {
//        Long companyNo = 7096528386L;
//        JsfResult<String> result = enterpriseInfoExportService.queryCompanyServiceDate(companyNo);
//        System.out.println(result);
//    }
//
//    /**
//     * 查询大客户服务信息
//     * 根据companyNo
//     */
//    @Test
//    public void queryEnterpriseServiceDateByCompanyNoTest() {
//        Long companyNo = 7096528386L;
//        JsfResult<EnterpriseManageDTO> result = enterpriseInfoExportService.queryEnterpriseInfoByCompanyNo(companyNo);
//        System.out.println(result);
//    }
//
//    /**
//     * 批量获取skuNo
//     */
//    @Test
//    public void querySkuNoByCompanyNoListTest() {
//        List<Long> companyNoList = new ArrayList<>();
//        companyNoList.add(946286082L);
//        companyNoList.add(494245378L);
//        JsfResult<List<String>> result = enterpriseSkuExportService.querySkuNoByCompanyNoList(companyNoList);
//        System.out.println(result);
//    }
//
//    @Test
//    public void queryCompanyInfoByPin(){
//        JsfResult<EnterpriseManageDTO> result = enterpriseInfoExportService.queryCompanyInfoByPin("daluobu01");
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//
//    @Test
//    public void queryProjectByCompanyNo() {
//        List<Long> param = Lists.newArrayList(9140762114L,2436112898L);
//        JsfResult<List<EnterpriseManageDTO>> result = enterpriseInfoExportService.queryProjectByCompanyNo(param);
//        System.out.println(JsonUtil.toJSONString(result));
//    }
//}
//
