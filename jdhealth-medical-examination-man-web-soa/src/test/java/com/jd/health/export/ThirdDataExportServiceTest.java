package com.jd.health.export;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataOperateEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataStatusEnum;
import com.jd.health.medical.examination.common.enums.ThirdGoodsTypeEnum;
import com.jd.health.medical.examination.export.param.*;
import com.jd.health.medical.examination.export.param.third.ManThirdBaseItemSaveParam;
import com.jd.health.medical.examination.export.service.ThirdBaseItemExportService;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.error.DefaultErrorCode;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 供应商推送测试类
 *
 * @author: yangxiyu
 * @date: 2021/6/28 15:39
 * @version: 1.0
 * @see
 */
public class ThirdDataExportServiceTest extends BaseTest {

    /**
     *
     */
    @Resource
    private ThirdDataExportService thirdDataExportService;
    /**
     * thirdStoreManExportService
     */
    @Resource
    private com.jd.health.medical.examination.export.service.ThirdStoreManExportService thirdStoreManExportService;

    @Resource
    private ThirdBaseItemExportService thirdBaseItemExportService;

    @Test
    public void createItemTest(){
        ManThirdBaseItemSaveParam manThirdBaseItemSaveParam = new ManThirdBaseItemSaveParam();
        thirdBaseItemExportService.saveThirdItemAttrRel(manThirdBaseItemSaveParam);
    }



    /**
     * 1、推送新的商家套餐
     */
    @Test
    public void pushGoodsChange() throws Exception{
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        param.setGoodsName("测试套餐001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setOperateType(2);
        param.setStatus(2);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);

        List<ThirdGoodsItemParam> goodsItemList = new ArrayList<>();
        ThirdGoodsItemParam item1 = new ThirdGoodsItemParam();
        item1.setGoodsId("LJ_goods_id_002");
        item1.setItemName("身高-测试");
        item1.setGoodsId2("goodsItemid1");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable2(suitList);
        item1.setItemMeans("项目意义");
        goodsItemList.add(item1);
        param.setGoodsItemList(goodsItemList);

        JsfResult result = thirdDataExportService.pushGoodsInfo(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }

    /**
     * 1、推送新的商家套餐(新方法)
     */
    @Test
    public void pushNewGoodsCreate() throws Exception{
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("JUNIT_LEJIAN_GOODS_001");
        param.setGoodsName("测试套餐001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(1);
        param.setStatus(1);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);

        fillItem(param);


        JsfResult result = thirdDataExportService.pushGoodsInfo(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }

    /**
     * 1、修改套餐(新方法)
     */
    @Test
    public void pushNewGoodsChange() throws Exception{
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("JUNIT_LEJIAN_GOODS_001");
        param.setGoodsName("测试套餐001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(2);
        param.setStatus(2);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(1);
        param.setGoodsMarry(1);
        param.setGoodsType(1);

        fillItem(param);


        JsfResult result = thirdDataExportService.pushGoodsInfo(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }

    /**
     * 1、推送新的商家套餐(新方法)
     */
    @Test
    public void pushNewPackageCreate() throws Exception{
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("JUNIT_LEJIAN_PACKAGE_001");
        param.setGoodsName("测试加项包001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(1);
        param.setStatus(1);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);
        param.setGoodsType(ThirdGoodsTypeEnum.ADDITION_PACKAGE.getType());

        fillItem(param);


        JsfResult result = thirdDataExportService.pushGoodsInfo(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }
    /**
     * 1、推送新的商家套餐(新方法)
     */
    @Test
    public void pushNewPackageOff() throws Exception{
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("JUNIT_LEJIAN_PACKAGE_001");
        param.setGoodsName("测试加项包001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(1);
        param.setStatus(2);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);
        param.setGoodsType(ThirdGoodsTypeEnum.ADDITION_PACKAGE.getType());
        fillItem(param);

        JsfResult result = thirdDataExportService.pushGoodsInfo(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }

    /**
     * 1、推送新的商家套餐
     */
    @Test
    public void pushGoodsCreate() throws Exception {
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("JUNIT_TEST_GOODS_001");
        param.setGoodsName("测试套餐001");
        param.setGoodsPrice(0.5D);
        param.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
        param.setOperateType(1);
        param.setStatus(1);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);

        List<ThirdGoodsItemParam> goodsItemList = new ArrayList<>();
        ThirdGoodsItemParam item1 = new ThirdGoodsItemParam();
        item1.setGoodsId("LJ_goods_id_002");
        item1.setItemName("身高-测试");
        item1.setGoodsId2("goodsItemid1");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable2(suitList);
        item1.setItemMeans("项目意义");
        goodsItemList.add(item1);
        param.setGoodsItemList(goodsItemList);

        JsfResult result = thirdDataExportService.pushGoodsInfo(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }
    /**
     * 1、推送新的商家加项包
     */
    @Test
    public void pushGoodsPluginAddTest() throws Exception{
        ThirdAdditionPackageParam param = new ThirdAdditionPackageParam();
        param.setGoodsId("LJ_goods_id_001");
        param.setPackageId("LJ_goods_package_id_001");
        param.setPackageName("乐荐加项包001");
        param.setPackagePrice(0.02D);
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setOperateType(2);
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setPackageDesc("加项包说明");
        param.setVenderId(12L);
        param.setPackageSuitable(1);
        param.setPackageMarry(2);

        List<ThirdPackageItemParam> itemList = new ArrayList<>();
        ThirdPackageItemParam item1 = new ThirdPackageItemParam();
        item1.setPackageId("LJ_goods_package_id_001");
        item1.setItemName("身高-测试");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable(suitList);
        item1.setItemMeans("项目意义");
        itemList.add(item1);
        param.setPackageItemList(itemList);

        JsfResult result = thirdDataExportService.pushAdditionPackage(param);
        Assert.assertEquals(result.getCode(), DefaultErrorCode.SUCCESS.getCode());
    }

    /**
     * 套餐下架
     */
    @Test
    public void pushStoreInfoOffTest(){
        ThirdGoodsParam param = new ThirdGoodsParam();
        param.setGoodsId("goodsIdtest123");
        param.setGoodsName("瑞慈-测试套餐");
        param.setGoodsPrice(0.5D);
        param.setChannelType(7975142641L);
        param.setOperateType(2);
        param.setStatus(2);
        param.setGoodsFeature("test_goods_feature");
        param.setVenderId(1L);
        param.setGoodsSuitable(0);
        param.setGoodsMarry(0);

        List<ThirdGoodsItemParam> goodsItemList = new ArrayList<>();
        ThirdGoodsItemParam item1 = new ThirdGoodsItemParam();
        item1.setGoodsId("Test_kangkang001");
        item1.setItemName("身高-测试");
        item1.setGoodsId2("goodsItemid1");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable2(suitList);
        item1.setItemMeans("项目意义");
        goodsItemList.add(item1);
        param.setGoodsItemList(goodsItemList);

        thirdDataExportService.pushGoodsInfo(param);
    }

    /**
     * 推送新建门店
     */
    @Test
    public void pushStoreInfoCreate(){
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        thirdStoreParam.setStoreId("001");
        thirdStoreParam.setStoreName("乐荐加项包测试门店001");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("16619887367");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());
        thirdStoreParam.setStoreHours("7:00-10:00");
        thirdStoreParam.setStoreAddr("西安市高新四路1号高科广场A座3层（南二环与高新四路什字东南角）");
        thirdStoreParam.setStoreLat(31.237888D);
        thirdStoreParam.setStoreLng(121.461271D);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:"+ JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);

/*        thirdStoreParam.setStoreId("lj_storeid_002");
        thirdStoreParam.setStoreName("乐荐加项包测试门店002");
        thirdStoreParam.setStoreLat(100.237888D);
        thirdStoreParam.setStoreLng(20.461271D);

        thirdDataExportService.pushStoreInfo(thirdStoreParam);

        thirdStoreParam.setStoreId("lj_storeid_003");
        thirdStoreParam.setStoreName("乐荐加项包测试门店003");
        thirdStoreParam.setStoreLat(70.237888D);
        thirdStoreParam.setStoreLng(60.461271D);

        thirdDataExportService.pushStoreInfo(thirdStoreParam);*/
    }

    /**
     * 推送门店-套餐关系
     */
    @Test
    public void pushStoreRelation(){

        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("lj_storeid_001");
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setGoodsId("LJ_goods_id_001");
        thirdDataExportService.pushGoodsStoreInfo(param);

        param.setStoreId("lj_storeid_002");
        thirdDataExportService.pushGoodsStoreInfo(param);

        param.setStoreId("lj_storeid_003");
        thirdDataExportService.pushGoodsStoreInfo(param);
    }

    /**
     * 推送新建门店store4
     */
    @Test
    public void pushStore4InfoUpdate(){
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        thirdStoreParam.setStoreId("lj_storeid_005");
        thirdStoreParam.setStoreName("乐荐加项包测试门店004");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("16619887367");
        thirdStoreParam.setReportSupport(1);
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        thirdStoreParam.setStoreHours("7:00-10:00");
        thirdStoreParam.setStoreAddr("西安市高新四路1号高科广场A座3层（南二环与高新四路什字东南角）");
        thirdStoreParam.setStoreLat(31.237888D);
        thirdStoreParam.setStoreLng(121.461271D);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:"+ JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }

    /**
     * 推送门店-套餐关系
     */
    @Test
    public void pushStore4RelationOff() {
        ThirdGoodsStoreParam param = new ThirdGoodsStoreParam();
        param.setStoreId("lj_storeid_004");
        param.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        param.setStatus(ThirdDataStatusEnum.ON.getCode());
        param.setGoodsId("LJ_goods_id_001");
        thirdDataExportService.pushGoodsStoreInfo(param);
    }

    /**
     * @param param
     */
    private void fillItem(ThirdGoodsParam param) {

        List<ThirdGoodsItemParam> goodsItemList = new ArrayList<>();
        ThirdGoodsItemParam item1 = new ThirdGoodsItemParam();
        item1.setGoodsId("LJ_goods_id_002");
        item1.setItemName("身高-测试");
        item1.setGoodsId2("goodsItemid1");
        item1.setItemTopCategory("一级项目");
        item1.setItemSecCategory("二级项目");
        List<String> suitList = new ArrayList<>();
        suitList.add("1");
        suitList.add("2");
        item1.setItemSuitable2(suitList);
        item1.setItemMeans("项目意义");
        goodsItemList.add(item1);
        param.setGoodsItemList(goodsItemList);
    }

    @Test
    public void goodsRelOff() {

    }

    @Test
    public void updateSupportWriteOffAndBrandId() {
        /*SupplierMerchantStoreInfoParam param = new SupplierMerchantStoreInfoParam();
        param.setChannelNo(5879687598l);
        param.setStoreId("H6");
        param.setBrandId(100000004l);
        param.setSupportWriteOff(1);
        param.setLng(116.392444);
        param.setLat(39.845555);*/
        //String storeId, Long brandId, Integer supportWriteOff,Long channelNo,Double lng,Double lat
        //thirdStoreManExportService.updateSupportWriteOffAndBrandId("002",null,1,1101975721l,116.392444,39.845555,"asdas");
    }

    /**
     * 自测新增供应商推送门店
     */
    @Test
    public void pushStoreInfoCreate2() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        thirdStoreParam.setStoreId("004");
        thirdStoreParam.setStoreName("乐荐测试门店004");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.ON.getCode());
        thirdStoreParam.setStorePhone("18330115894");
        thirdStoreParam.setReportSupport(1);
        //为新增
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.ADD.getCode());
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845158);
        thirdStoreParam.setStoreLng(116.405403);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }

    //更新门店
    @Test
    public void pushStoreInfoUpdate2() {
        ThirdStoreParam thirdStoreParam = new ThirdStoreParam();
        thirdStoreParam.setChannelType(ProviderTypeEnum.LE_JIAN.getTypeNo());
        thirdStoreParam.setStoreId("004");
        thirdStoreParam.setStoreName("乐荐测试门店004");
        thirdStoreParam.setStatus(ThirdDataStatusEnum.OFF.getCode());
        thirdStoreParam.setStorePhone("18330115894");
        thirdStoreParam.setReportSupport(1);
        //为新增
        thirdStoreParam.setOperateType(ThirdDataOperateEnum.UPDATE.getCode());
        thirdStoreParam.setStoreHours("7:00-15:00");
        thirdStoreParam.setStoreAddr("北京市大红门高庄小区");
        thirdStoreParam.setStoreLat(39.845159);
        thirdStoreParam.setStoreLng(116.405404);
        thirdStoreParam.setStoreType(3);
        System.out.println("param:" + JsonUtil.toJSONString(thirdStoreParam));
        thirdDataExportService.pushStoreInfo(thirdStoreParam);
    }


}
