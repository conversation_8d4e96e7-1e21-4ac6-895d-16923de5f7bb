package com.jd.health.export.report;

import com.google.common.collect.Sets;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.export.dto.report.detail.ReportDetailDTO;
import com.jd.health.medical.examination.export.enums.ReportElementEnum;
import com.jd.health.medical.examination.export.param.report.*;
import com.jd.health.medical.examination.export.param.report.query.ReportIdParam;
import com.jd.health.medical.examination.export.service.report.PushReportExportService;
import com.jd.health.medical.examination.export.service.report.QueryReportExportService;
import com.jd.health.medical.examination.infrastructure.report.ReportOssUtilImpl;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: yangxiyu
 * @date: 2022/12/7 3:24 下午
 * @version: 1.0
 */
public class ManReportExportServiceTest extends BaseTest {

    @Resource
    private PushReportExportService pushReportExportService;
    @Resource
    private QueryReportExportService queryReportExportService;
    @Resource
    private ReportOssUtilImpl reportOssUtilImpl;

    @Test
    public void pushStructReport(){
        PushStandardReportParam reportParam = new PushStandardReportParam();
        PushReportUserInfo user = new PushReportUserInfo();
        user.setUserId("userId");
        user.setUserPin("userPin");
        user.setPatientId(1283487L);
        user.setName("测试名称");
        user.setGender(1);
        user.setAge(18);
        user.setMarriage(1);
        user.setCredentialNo("657890876586998");
        user.setCredentialType(1);
        user.setPhone("**********");
        user.setRelativesType(1);
        reportParam.setUser(user);

        PushReportInfo reportInfo = new PushReportInfo();
        reportInfo.setChannelNo(1000000001L);
        reportInfo.setBusinessType(0);
        reportInfo.setReportId("offline_reportId_001");
        reportInfo.setStoreName("线下体检门店");
        reportInfo.setServiceName("测试体检服务");
        reportInfo.setExaminationTime("");
        reportInfo.setExtendId("J1029386542713892843");
        reportInfo.setDataSource(4);
        reportInfo.setUploadTime(new Date());
        reportInfo.setBizCode(10);
        reportParam.setReportInfo(reportInfo);

        PushStructReportInfo structReportInfo = new PushStructReportInfo();
        structReportInfo.setReportId("offline_reportId_001");
        structReportInfo.setExamineTime("2022-12-01 00:00:00");
        reportParam.setOriginalStructReportInfo(structReportInfo);
        reportParam.setJdStructReportInfo(structReportInfo);

        pushReportExportService.pushFormattedStructReport(reportParam);
    }

    @Test
    public void pushReport(){
        PushReportParam reportParam = new PushReportParam();
        PushReportUserInfo user = new PushReportUserInfo();
        user.setUserId("userId");
        user.setUserPin("userPin");
        user.setPatientId(1283487L);
        user.setName("测试名称");
        user.setGender(1);
        user.setAge(18);
        user.setMarriage(1);
        user.setCredentialNo("657890876586998");
        user.setCredentialType(1);
        user.setPhone("**********");
        user.setRelativesType(1);
        reportParam.setUser(user);

        PushReportInfo reportInfo = new PushReportInfo();
        reportInfo.setChannelNo(1000000001L);
        reportInfo.setBusinessType(0);
        reportInfo.setReportId("offline_reportId_001");
        reportInfo.setStoreName("线下体检门店");
        reportInfo.setServiceName("测试体检服务");
        reportInfo.setExaminationTime("");
        reportInfo.setExtendId("J1029386542713892843");
        reportInfo.setDataSource(4);
        reportInfo.setUploadTime(new Date());
        reportInfo.setBizCode(10);
        reportParam.setReportInfo(reportInfo);
        reportParam.setOssUrl("https://storage.360buyimg.com/examin.report/0_1464177844677510658_200611_2874539010.pdf?Expires=1671996170&AccessKey=3HjdNBAYZlaeZ2xy&Signature=fuU4E%2BPJGr%2BQw2Cyk0hYjrmrxw4%3D");

        pushReportExportService.pushOssReport(reportParam);
    }

    @Test
    public void queryReport(){
        ReportIdParam idParam = new ReportIdParam();
        idParam.setBizCode(10);
        idParam.setElementCode(Sets.newHashSet(ReportElementEnum.ORIGINAL_STRUCT_REPORT.getCode(), ReportElementEnum.STRUCT_CONTENT.getCode()));
        JsfResult<ReportDetailDTO>  res = queryReportExportService.queryDetailById(idParam);
        System.out.println(JsonUtil.toJSONString(res));
    }


    @Test
    public void transferReport() throws Exception {
        String res = reportOssUtilImpl.transfer("https://storage.360buyimg.com/examin.report/0_1464177844677510658_200611_2874539010.pdf?Expires=1671996170&AccessKey=3HjdNBAYZlaeZ2xy&Signature=fuU4E%2BPJGr%2BQw2Cyk0hYjrmrxw4%3D", "application/pdf", "123456435436_transfer.pdf");
        System.out.println("transferReport res=" + res);
    }
}
