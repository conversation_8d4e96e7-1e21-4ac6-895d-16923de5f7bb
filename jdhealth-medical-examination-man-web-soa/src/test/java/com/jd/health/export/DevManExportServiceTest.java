package com.jd.health.export;

import com.google.common.collect.Lists;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.domain.bo.report.MatchThirdConclusionBO;
import com.jd.health.medical.examination.export.param.ExaminationGroupItemParam;
import com.jd.health.medical.examination.export.param.ExaminationGroupParam;
import com.jd.health.medical.examination.export.service.DevManExportService;
import com.jd.health.medical.examination.service.report.ThirdConclusionKeyWordService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2021/6/21 16:08
 * @version: 1.0
 * @see
 */
public class DevManExportServiceTest extends BaseTest {
    
    /**
     * devManExportService
     */
    @Autowired
    DevManExportService devManExportService;

    @Test
    public void refreshConclusions(){
        devManExportService.refreshThirdConclusions(160L, 2000L);
    }

    @Autowired
    private ThirdConclusionKeyWordService thirdConclusionKeyWordService;
    /**
     * flushSkuStoreThirdRel
     *
     * @return void
     */
    @Test
    public void flushSkuStoreThirdRel() {
        System.out.println("============");
        List<MatchThirdConclusionBO> bos = new ArrayList<>();
        MatchThirdConclusionBO bo1 = new MatchThirdConclusionBO();
        bo1.setChannelNo(9222919682L);
        bo1.setConclusionDescription("描述1");
        bo1.setConclusionSuggest("建议1");
        bo1.setConclusionName("肝内钙化灶");
        bo1.setOperationUser("yangxiyu");
        bo1.setSource(0);
        bos.add(bo1);

        MatchThirdConclusionBO bo2 = new MatchThirdConclusionBO();
        bo2.setChannelNo(9222919682L);
        bo2.setConclusionDescription("描述2");
        bo2.setConclusionSuggest("建议2");
        bo2.setConclusionName("肝内钙化灶asdfdgf1234");
        bo2.setOperationUser("yangxiyu");
        bo2.setSource(0);
        bos.add(bo2);

        thirdConclusionKeyWordService.matchConclusions(bos, "23423");
    }
    
    /**
     * checkBatchImportOneToFourItemTest
     *
     * @return void
     */
    @Test
    public void checkBatchImportOneToFourItemTest() {
        JsfResult<String> stringJsfResult = devManExportService.checkBatchImportOneToFourItem("体检标准项目库导入文件.xlsx", "examin.common");
        System.out.println(JsonUtil.toJSONString(stringJsfResult));
    }
    
    /**
     * batchImportOneToFourItemTest
     *
     * @return void
     */
    @Test
    public void batchImportOneToFourItemTest() {
        JsfResult<String> stringJsfResult = devManExportService.batchImportOneToFourItem("examin.common", "体检标准项目库导入文件.xlsx");
        System.out.println(JsonUtil.toJSONString(stringJsfResult));
    }
    
    /**
     * delItemCacheTest 清空item缓存
     *
     * @return void
     */
    @Test
    public void delItemCacheTest() {
        devManExportService.delItemCache();
        System.out.println("delItemCacheTest end");
    }
    
    /**
     *
     */
    @Test
    public void bindByGroupAndItemsTest() {
        ExaminationGroupParam param = new ExaminationGroupParam();
        param.setGroupNo(504334235138L);
        List<ExaminationGroupItemParam> examinationGroupItemParamList = Lists.newArrayList();
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421748L).important(0).build());
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421761L).important(0).build());
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421775L).important(0).build());
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421788L).important(0).build());
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421801L).important(1).build());
        param.setExaminationGroupItemParamList(examinationGroupItemParamList);
        System.out.println(devManExportService.bindGroupItem(param));
    }
    
    /**
     *
     */
    @Test
    public void unBindByGroupAndItemsTest() {
        ExaminationGroupParam param = new ExaminationGroupParam();
        param.setGroupNo(504334235138L);
        List<ExaminationGroupItemParam> examinationGroupItemParamList = Lists.newArrayList();
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421748L).important(0).build());
        examinationGroupItemParamList.add(ExaminationGroupItemParam.builder().itemNo(1585578421761L).important(0).build());
        param.setExaminationGroupItemParamList(examinationGroupItemParamList);
        System.out.println(devManExportService.unBindGroupItem(param));
    }

    @Test
    public void refreshItemName() throws Exception{
        devManExportService.initThirdBaseItem();

        Thread.sleep(1000000000L);
    }
}
