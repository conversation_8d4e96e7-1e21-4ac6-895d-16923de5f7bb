package com.jd.health.export;

import com.google.common.collect.Lists;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataOperateEnum;
import com.jd.health.medical.examination.common.enums.ThirdDataStatusEnum;
import com.jd.health.medical.examination.export.dto.ThirdStoreAccountDTO;
import com.jd.health.medical.examination.export.dto.supplier.ThirdStoreDTO;
import com.jd.health.medical.examination.export.param.*;
import com.jd.health.medical.examination.export.param.supplier.ThirdStoreInfoParam;
import com.jd.health.medical.examination.export.param.supplier.ThirdStoreQueryParam;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.health.medical.examination.export.service.supplier.SupplierThirdStoreExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.base.export.error.DefaultErrorCode;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: lx
 * @date: 2021/8/20 15:39
 * @version: 1.0
 * @see
 */
public class SupplierThirdStoreExportServiceTest extends BaseTest {

    @Resource
    private SupplierThirdStoreExportService supplierThirdStoreExportService;

    @Test
    public void saveStoreAccount(){

        List<String> accountList = Lists.newArrayList();
        accountList.add("11");
        accountList.add("12");
        accountList.add("13");
        ThirdStoreInfoParam param = new ThirdStoreInfoParam();
        param.setChannelNo(9234756822L);
        param.setStoreId("BJ001");
        param.setAccountNoList(accountList);
        JsfResult<java.lang.Boolean> res = supplierThirdStoreExportService.saveThirdStoreAccountRelation(param);
        System.out.println(res);
    }
    @Test
    public void updateStoreAccount(){

        List<String> accountList = Lists.newArrayList();
        accountList.add("111");
        accountList.add("121");
        accountList.add("131");
        ThirdStoreInfoParam param = new ThirdStoreInfoParam();
        param.setChannelNo(9234756822L);
        param.setStoreId("BJ001");
        param.setAccountNoList(accountList);
        JsfResult<java.lang.Boolean> res = supplierThirdStoreExportService.updateThirdStoreAccountRelation(param);
        System.out.println(res);
    }
//    @Test
//    public void getAccountNoAvailable(){
//
//        String accountNo = "";
//        Long channelNo = 9234756822L;
//        JsfResult<List<String>> res = supplierThirdStoreExportService.getAccountNoAvailable(accountNo,channelNo);
//        System.out.println(res);
//    }
    @Test
    public void queryManagedStoreList(){

        String accountNo = "121";
        JsfResult<ThirdStoreDTO> res = supplierThirdStoreExportService.queryManagedStoreList(accountNo);
        System.out.println(res);
    }
    @Test
    public void getThirdStoreAccountDetail(){

        ThirdStoreQueryParam param = new ThirdStoreQueryParam();
        param.setStoreId("BJ001");
        param.setChannelNo(9234756822L);
        JsfResult<List<ThirdStoreAccountDTO>> res = supplierThirdStoreExportService.getThirdStoreAccountDetail(param);
        System.out.println(res);
    }



}
