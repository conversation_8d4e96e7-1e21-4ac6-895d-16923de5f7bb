package com.jd.health;

import com.google.common.collect.Lists;
import com.jd.health.medical.examination.domain.HospitalDepartment;
import com.jd.health.medical.examination.export.dto.HospitalDepartmentDTO;
import com.jd.health.medical.examination.export.service.ExaminationHospitalDepartExportService;
import com.jd.health.medical.examination.service.HospitalDepartmentService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName HospitalDepartTest
 * @Description
 * <AUTHOR>
 * @Date 2021/3/25 17:32
 **/
public class HospitalDepartTest extends BaseTest{

	/**
	 * hospitalDepartExportService
	 */
	@Autowired
	private ExaminationHospitalDepartExportService hospitalDepartExportService;

	/**
	 *
	 */
	@Autowired
	private HospitalDepartmentService hospitalDepartmentService;

	/**
	 * queryGroupInspectionReport
	 */
	@Test
	public void getAllOneLevelHospitalDepartment() {
		JsfResult<List<HospitalDepartmentDTO>> result = hospitalDepartExportService.getAllOneLevelHospitalDepartment();
		System.out.println(JsonUtil.toJSONString(result));
	}

	/**
	 * queryGroupInspectionReport
	 */
	@Test
	public void getHospitalDepartmentByParentNo() {
		JsfResult<List<HospitalDepartmentDTO>> result = hospitalDepartExportService.getHospitalDepartmentByParentNo(13L);
		System.out.println(JsonUtil.toJSONString(result));
	}

	/**
	 * getAllDepartment
	 */
	@Test
	public void getAllDepartment(){
		List<HospitalDepartment> departmentList = Lists.newArrayList();
		departmentList.addAll(hospitalDepartmentService.getHospitalDepartmentByLevel(1));
		departmentList.addAll(hospitalDepartmentService.getHospitalDepartmentByLevel(2));
		Collections.sort(departmentList, (o1, o2) -> o1.getId().compareTo(o2.getId()));
		System.out.println(JsonUtil.toJSONString(departmentList));
	}
}
