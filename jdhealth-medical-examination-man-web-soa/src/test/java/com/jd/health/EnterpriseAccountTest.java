package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.enterprise.dto.EnterpriseAccountDTO;
import com.jd.health.medical.examination.export.enterprise.param.EnterpriseAccountParam;
import com.jd.health.medical.examination.export.enterprise.service.EnterpriseAccountExportService;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Date;

/**
 * 企销账号测试类
 *
 * <AUTHOR>
 * @date 2020-01-10 16:16
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class EnterpriseAccountTest {

    private static final Logger log = LoggerFactory.getLogger(EnterpriseAccountTest.class);

    /**
     * 企销账号exportService
     */
    @Autowired
    private EnterpriseAccountExportService enterpriseAccountExportService;

    /**
     * 查询账号信息
     */
    @Test
    public void queryAccountInfoTest() {
        EnterpriseAccountParam enterpriseAccountParam = new EnterpriseAccountParam();
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        JsfResult<PageInfo<EnterpriseAccountDTO>> result = enterpriseAccountExportService.queryEnterpriseAccountPage(enterpriseAccountParam, pageParam);
        System.out.println(result);
    }

    /**
     * 增加企销账号
     */
    @Test
    public void addAccountInfoTest() {
        EnterpriseAccountParam enterpriseAccountParam = new EnterpriseAccountParam();
        enterpriseAccountParam.setAccountNo("2");
        enterpriseAccountParam.setAccountPhone("***********");
        //enterpriseAccountParam.setAccountPwd("222111");
//        enterpriseAccountParam.setAccountRole(2);
        enterpriseAccountParam.setCompanyName("2");
        enterpriseAccountParam.setCompanyNo(2L);
        enterpriseAccountParam.setCreateUser("hezhensheng3");
        enterpriseAccountParam.setExpireDate(new Date());
        enterpriseAccountParam.setAccountStatus(1);
        JsfResult<Boolean> result = enterpriseAccountExportService.addEnterpriseAccount(enterpriseAccountParam);
        System.out.println(result);
    }


}
