package com.jd.health;

import com.jd.health.medical.examination.export.param.PullCheckReportReqParam;
import com.jd.health.medical.examination.rpc.CheckSheetRpc;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/1/17 - 10:47 上午
 **/
public class MoFeiShiTest extends BaseTest{
    
    @Autowired
    private CheckSheetRpc checkSheetMoFeiShiRpc;
    
    @Test
    public void checkReport() {
        PullCheckReportReqParam param = new PullCheckReportReqParam();
        param.setAppointmentNo("123456");
        param.setOrderId("123456");
        param.setReportId("1481811807939584002");
        checkSheetMoFeiShiRpc.getCheckReport(param);
    }
}
