package com.jd.health.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import com.jd.health.medical.examination.common.enums.CredentialEnum;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.service.bo.AppointmentBO;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;


/**
 * @Description
 * <AUTHOR>
 * @Date 2020/6/11 18:48
 * @Doc
 **/
public class ReportAuthUtil {

    // 定义全局的字体静态变量
    private static Font titleFont;
    private static Font headFont;
    private static Font keyFont;
    private static Font textFont;
    private static Font underlineFont;

    // 静态代码块
    static {
        try {
            // 不同字体（这里定义为同一种字体：包含不同字号、不同style）
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            titleFont = new Font(bfChinese, 16, Font.BOLD);
            headFont = new Font(bfChinese, 14, Font.BOLD);
            keyFont = new Font(bfChinese, 11, Font.BOLD);
            textFont = new Font(bfChinese, 11, Font.NORMAL);
            underlineFont = new Font(bfChinese,11,Font.UNDERLINE); //下划线斜体
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 生成PDF文件
    public static void generateReportAuthPDF(Document doc, AppointmentBO appointmentBO,String companyName) throws Exception {
        // 段落
//        Paragraph paragraph = new Paragraph("", titleFont);
//        paragraph.setAlignment(1); //设置文字居中 0靠左   1，居中     2，靠右
//        paragraph.setIndentationLeft(12); //设置左缩进
//        paragraph.setIndentationRight(12); //设置右缩进
//        paragraph.setFirstLineIndent(24); //设置首行缩进
//        paragraph.setLeading(20f); //行间距
//        paragraph.setSpacingBefore(5f); //设置段落上空白
//        paragraph.setSpacingAfter(10f); //设置段落下空白

        //段落
        Paragraph p1 = new Paragraph();
        //短语
        Phrase ph1 = new Phrase();
        //块
        p1 = new Paragraph("个人健康体检服务授权书", titleFont);
        p1.setLeading(50);
        p1.setAlignment(Element.ALIGN_CENTER);
        doc.add(p1);

        p1 = new Paragraph("尊敬的消费者，您好", textFont);
        p1.setIndentationLeft(12); //设置左缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("请您首先核对个人身份信息是否正确：", textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph();
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        ph1 = new Phrase();
        Chunk c1 = new Chunk("姓名（体检人）:", textFont) ;
        Chunk c2 = new Chunk(appointmentBO.getPatientName(), underlineFont) ;
        Chunk c3 = new Chunk("，  证件类型:", textFont) ;
        Chunk c4 = new Chunk(CredentialEnum.getCredentailType(appointmentBO.getCredentialType()).getDesc(), underlineFont) ;
        ph1.add(c1);
        ph1.add(c2);
        ph1.add(c3);
        ph1.add(c4);
        p1.add(ph1);
        doc.add(p1);

        p1 = new Paragraph();
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        ph1 = new Phrase();
        Chunk c5 = new Chunk("证件号：", textFont) ;
        Chunk c6 = new Chunk(appointmentBO.getCredentialNo(), underlineFont) ;
        Chunk c7 = new Chunk("，  手机号（体检人）：", textFont) ;
        Chunk c8 = new Chunk(appointmentBO.getUserPhone(), underlineFont) ;
        ph1.add(c5);
        ph1.add(c6);
        ph1.add(c7);
        ph1.add(c8);
        p1.add(ph1);
        doc.add(p1);

        p1 = new Paragraph();
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        ph1 = new Phrase();
        Chunk c9 = new Chunk("生日（体检人）：", textFont) ;
        Chunk c10 = new Chunk(appointmentBO.getUserBirth(), underlineFont) ;
        Chunk c11 = new Chunk("，  性别（体检人）：", textFont) ;
        Chunk c12 = new Chunk(GenderEnum.getDescOfType(appointmentBO.getUserGender()), underlineFont) ;
        Chunk c13 = new Chunk("，  婚姻状态（体检人）：", textFont) ;
        Chunk c14 = new Chunk(MarryTypeEnum.getMarryByType(appointmentBO.getUserMarriage()).getDesc(), underlineFont) ;
        ph1.add(c9);
        ph1.add(c10);
        ph1.add(c11);
        ph1.add(c12);
        ph1.add(c13);
        ph1.add(c14);
        p1.add(ph1);
        doc.add(p1);

        p1 = new Paragraph();
        ph1 = new Phrase();
        Chunk c15 = new Chunk( "感谢您购买本产品，为便于您实现通过京东健康平台进行体检预约服务并获取体检报告,享受报告解读服务,", textFont) ;
        Chunk c16 = new Chunk( "在您开始本次体检服务前, 请您仔细阅读本授权委托书的全部内容，您勾选\"已阅读并同意\"并点击“确认”按钮后，本授权书即为生效。", keyFont) ;
        ph1.add(c15);
        ph1.add(c16);
        p1.add(ph1);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("1.  基于您的授权，您可实现通过京东健康平台进行体检预约，获取体检报告以及＂体检报告解读＂等增值服务。您可在京东健康一站式完成购买、预约、查看报告等体检全流程服务。",textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("2.  您在京东健康平台进行体检预约后才可浏览并下载体检报告，故您需授权京东健康从体检机构处获取您的体检报告，基于此授权您可享用体检预约功能的服务。如您不同意授权，您可通过体检服务商的 APP、网站及在线客服进行预约。",textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph();
        ph1 = new Phrase();
        Chunk c163 = new Chunk( "3.  为便于您通过京东健康平台浏览及下载“体检报告”，", textFont) ;
        Chunk c164 = new Chunk( "您同意并授权京东健康从体检服务提供方获取并存储您的体检报告，", keyFont) ;
        Chunk c165 = new Chunk( "京东健康会采用符合业界标准的安全防护措施以及各种合理的制度、安全技术和措施来保护您的个人信息安全。", textFont) ;
        ph1.add(c163);
        ph1.add(c164);
        ph1.add(c165);
        p1.add(ph1);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph();
        ph1 = new Phrase();
        Chunk c17 = new Chunk( "4.  为更好地提供持续健康服务,方便您日常系统化掌握个人健康,及时掌握身体状况,根据具体产品情况，如您购买的体检服务包含＂报告解读＂或您单独购买“报告解读”的，本报告服务由京东健康下京东互联网医院为您提供，为服务的必要性，您的个人信息需与京东互联网医院共享，故您授权京东互联网医院医生阅读您的体检报告，针对您的体检结果为您提供专业的健康建议。当您点击同意时，即授权京东互联网医院可获取您的体检报告并为您做进一步解读。京东健康除为您提供体检预约及报告解读服务外还可向您提供日常健康咨询，慢病管理，问诊等服务，", textFont) ;
        Chunk c18 = new Chunk( "为实现此类服务您同意并授权京东互联网医院医生阅读您的体检报告内容。", keyFont) ;
        ph1.add(c17);
        ph1.add(c18);
        p1.add(ph1);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph();
        ph1 = new Phrase();
        Chunk c19 = new Chunk( "5.  以上受限于体检服务供应商的合作技术要求(目前只有部分地区和部分体检服务供应商处可达到此项服务),", textFont) ;
        Chunk c20 = new Chunk( "我们将基于您对京东健康及体检供应商的双重授权后获取您的体检报告。", keyFont) ;
        ph1.add(c19);
        ph1.add(c20);
        p1.add(ph1);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph();
        ph1 = new Phrase();
        Chunk c21 = new Chunk( "6.  您本人已了解本授权书的相关事宜, 京东健康将依据《京东平台隐私政策》保护您的相关权益,", textFont) ;
        Chunk c22 = new Chunk( "您自愿授权此次为您提供体检服务的医疗机构京和京东健康获取及解读您的体检报告", keyFont) ;
        ph1.add(c21);
        ph1.add(c22);
        p1.add(ph1);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("京东健康",keyFont);
        p1.setAlignment(2);
        doc.add(p1);

        p1 = new Paragraph(" ");
        p1.setLeading(30);
        doc.add(p1);

        getNotice(doc);

        p1 = new Paragraph(" ");
        p1.setLeading(30);
        doc.add(p1);

        getPersonAuth(doc,companyName);


    }

    private static void getNotice(Document doc) throws DocumentException {
        Paragraph p1 = new Paragraph("健康体检服务使用人：", keyFont);
        p1.setIndentationLeft(12); //设置左缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph(" ");
        p1.setLeading(30);
        doc.add(p1);

        p1 = new Paragraph("体检报告属于用户隐私，仅限受检人本人查看。若您为自己预约，体检结束后您可以通过本人京东账号查看电子体检报告；若您帮他人预约，请在实际受检人确认或授权后，填写以下信息：受检人姓名、受检人手机号、受检人有效身份证信息, 受检人生日, 受检人性别。", textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("您同意并确认：",textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("实际受检人已充分知悉并同意体检结束后受检人可凭您所填写的受检手机号登陆京东健康体检服务平台查看电子体检报告；受检人已知悉并同意《个人健康体检服务授权书》（含个人体检信息授权书 ） ，您已征得实际受检人同意代其预约；如您在未征得实际受检人本人同意和确认的情况下,使用“预约服务”导致实际受检人个人信息泄露, 您将承担由此产生的全部法律责任。", textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("您在使用预约服务前，请完整阅读并充分理解以上内容，当您勾选授权时即视为您对上述内容的确认。",keyFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

    }

    private static void getPersonAuth(Document doc,String companyName) throws Exception {
        Paragraph p1 = new Paragraph("个人健康体检服务授权书", titleFont);
        p1.setLeading(50);
        p1.setAlignment(Element.ALIGN_CENTER);
        doc.add(p1);

        p1 = new Paragraph();
        Phrase ph1 = new Phrase();
        Chunk c1 = new Chunk( "被授权人：", textFont) ;
        Chunk c2 = new Chunk( companyName, underlineFont) ;
        Chunk c3 = new Chunk( "及其下属医疗机构", textFont) ;
        ph1.add(c1);
        ph1.add(c2);
        ph1.add(c3);
        p1.add(ph1);
        p1.setFirstLineIndent(12); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("本人自愿授权被授权人向京东健康提供本次体检数据，包括预约登记时提供的信息，体检报告及报告所载明的包括但不限于个人信息、体检项目的所有检出信息(包括乙型肝炎病毒血检结果和 HIV 检测结果等)，以及各种可能的成因和需要采取的应对措施及后果。被授权人可向上述指定单位交付纸质体检报告和电子数据及其他形式的数据。",textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("本人已充分知晓自己的权利并愿意承担本次授权可能带来的所有风险和对本人利益可能损害，本次授权系本人自愿行为，因本人授权行为所导致的结果由授权人承担。",textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

        p1 = new Paragraph("本人已完整阅读并充分理解授权书含义并做出授权。",textFont);
        p1.setFirstLineIndent(24); //设置首行缩进
        p1.setLeading(20f); //行间距
        doc.add(p1);

    }

    // main测试
    public static void main(String[] args) throws Exception {
        try {
            long start = System.currentTimeMillis();
            // 1.新建document对象
            Document document = new Document(PageSize.A4);// 建立一个Document对象
            // 2.建立一个书写器(Writer)与document对象关联
            File file = new File("D:\\PDFDemo.pdf");
            file.createNewFile();

            ByteArrayOutputStream byteOutStream = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, byteOutStream);
            writer.setPageEvent(new Watermark("JDH"));// 水印
            writer.setPageEvent(new MyHeaderFooter());// 页眉/页脚
            // 3.打开文档
            document.open();
            document.addTitle("用户体检报告授权");// 标题
            document.addAuthor("jdh");// 作者
            document.addSubject("用户体检报告授权");// 主题
            document.addKeywords("体检报告授权");// 关键字
            document.addCreator("jdh");// 创建者
            AppointmentBO appointmentBO = new AppointmentBO();
            appointmentBO.setUserGender(GenderEnum.MAN.getType());
            appointmentBO.setUserMarriage(MarryTypeEnum.YES.getType());
            appointmentBO.setPatientName("银涛");
            appointmentBO.setCredentialType(CredentialEnum.CID.getType());
            appointmentBO.setCredentialNo("4305231659856254");
            appointmentBO.setUserBirth("1983-04-12");
            appointmentBO.setUserPhone("17773901645");
            // 4.向文档中添加内容
            ReportAuthUtil.generateReportAuthPDF(document,appointmentBO,"导医通有限责任公司");
            // 5.关闭文档
            document.close();
            byteOutStream.toByteArray();
            System.out.println("cost:"+(System.currentTimeMillis() - start));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
