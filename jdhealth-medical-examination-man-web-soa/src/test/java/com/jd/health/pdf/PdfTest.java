package com.jd.health.pdf;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.jd.health.BaseTest;
import com.jd.health.medical.examination.common.enums.CredentialEnum;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.errorcode.BaseErrorCode;
import com.jd.health.medical.examination.service.common.tools.HealthcareOssService;
import com.jd.health.medical.examination.service.bo.AppointmentBO;
import com.jd.health.medical.examination.service.third.ThirdReportAuthService;
import com.jd.jss.JingdongStorageService;
import com.jd.medicine.base.common.exception.BusinessException;
import com.jd.medicine.base.common.util.StringUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.ArrayList;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/6/15 10:39
 * @Doc
 **/
public class PdfTest extends BaseTest {

    @Autowired
    HealthcareOssService healthcareOssService;

    @Autowired
    JingdongStorageService jssClient;

    @Autowired
    ThirdReportAuthService thirdReportAuthService;


    @Test
    public void testGeneratePdf() throws Exception {
        for (int i = 0; i < 10; i++) {
            //组装预约参数
            AppointmentBO appointmentBO = new AppointmentBO();
            appointmentBO.setUserGender(GenderEnum.MAN.getType());
            appointmentBO.setUserMarriage(MarryTypeEnum.YES.getType());
            appointmentBO.setPatientName("银涛");
            appointmentBO.setCredentialType(CredentialEnum.CID.getType());
            appointmentBO.setCredentialNo("4305231659856254");
            appointmentBO.setAppointmentNo("123");
            appointmentBO.setJdAppointmentId(123123123L);
            appointmentBO.setChannelType(123456789L);
            appointmentBO.setUserBirth("1983-04-12");
            appointmentBO.setUserPhone("17773901645");
            //生成pdf
            long start = System.currentTimeMillis();
            // 1.新建document对象
            Document document = new Document(PageSize.A4);// 建立一个Document对象
            ByteArrayOutputStream byteOutStream = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, byteOutStream);
            writer.setPageEvent(new Watermark("JDH"));// 水印
            writer.setPageEvent(new MyHeaderFooter());// 页眉/页脚
            // 3.打开文档
            document.open();
            document.addTitle("用户体检报告授权");// 标题
            document.addAuthor("jdh");// 作者
            document.addSubject("用户体检报告授权");// 主题
            document.addKeywords("体检报告授权");// 关键字
            document.addCreator("jdh");// 创建者
            // 4.向文档中添加内容
            ReportAuthUtil.generateReportAuthPDF(document,appointmentBO,"导医通有限责任公司");
            // 5.关闭文档
            document.close();
            //上传OSS
            String fileName = "test_pdf_"+System.currentTimeMillis()+"_"+i+".pdf";
            ByteArrayInputStream inputStream = new ByteArrayInputStream(byteOutStream.toByteArray());
            String s = healthcareOssService.uploadStreamBucket("examin.devtest",fileName, inputStream);
    //        String uploadMD5 = jssClient.bucket("examin.report").object(fileName).entity(inputStream.available(),inputStream).put();
            System.out.println("cost:"+(System.currentTimeMillis() - start));

//            ReportAuthEntity reportAuthEntity = new ReportAuthEntity();
//            reportAuthEntity.setAppointmentNo(appointmentBO.getAppointmentNo());
//            reportAuthEntity.setChannelNo(appointmentBO.getChannelType());
//            reportAuthEntity.setJdAppointmentId(appointmentBO.getJdAppointmentId());
//            reportAuthEntity.setReportAuthUrl("/examin.devtest/"+fileName);
//            thirdReportAuthService.insertReportAuth(reportAuthEntity);
        }
    }

    @Test
    public void upload() {
        String data = "{\"reportId\":1,\"brandName\":\"机构1\",\"brandId\":2,\"extendId\":\"123\",\"dataSource\":1,\"examinationTime\":1638846762163,\"uploadTime\":1638846762162,\"user\":{\"age\":25,\"idCard\":\"23456435678675432\",\"name\":\"测试名称\",\"patientId\":1566,\"phone\":\"**********\",\"sex\":1},\"conclusionInfos\":[{\"conclusionName\":\"原始的异常结论\",\"conclusionDescription\":\"原始的异常结论描述信息\",\"conclusionSuggest\":\"原始的异常结论建议\",\"updateInfo\":{\"conclusionName\":\"修改后的异常结论\",\"conclusionDescription\":\"修改后的异常结论描述信息\",\"conclusionSuggest\":\"修改后的异常结论建议\",\"updateTime\":1638846762165,\"updateUser\":\"quancheng1\"}}],\"items\":[{\"itemName\":\"项目1\",\"updateItemName\":\"修改后的项目名称\",\"updateTime\":1638846762164,\"updateUser\":\"yangxiyu\",\"indicators\":[{\"indicatorName\":\"收缩压\",\"unit\":\"mnhg\",\"normalRangeValue\":\"1 < v < 100\",\"abnormalType\":\"偏高\",\"updateInfo\":{\"indicatorName\":\"修改后的收缩压\",\"unit\":\"mnhg\",\"normalRangeValue\":\"100 < v < 200\",\"updateTime\":1638846762164,\"updateUser\":\"yangxiyu\"}}]}]}";
        String uploadFile = healthcareOssService.uploadStructReportStr("abandon_snapshot", data);
        System.out.println(uploadFile);
    }

    @Test
    public void getStructReportStr() {
        String uploadFile = healthcareOssService.getStructReportStr("/exam.dev/abandon");
        System.out.println(uploadFile);
    }

    @Test
    public void getOssUrl() {
        String url = healthcareOssService.getOssHttpUrl("examin.devtest", "/examin.devtest/test_pdf_1614866752439_0.pdf", 2000, "storage.jd.local");
        System.out.println(url);
    }

    @Test
    public void getOssUrl2() {
        String url = healthcareOssService.getOssHttpUrl("/examin.devtest/test_pdf_1614866752439_0.pdf");
        System.out.println(url);
    }

    /**
     *
     * @param fileName
     * @param inputStream
     * @return
     */
    public String uploadStream(String fileName, ByteArrayInputStream inputStream) {
        String ossUrl = null;
        try {
            String uploadMD5 = jssClient.bucket("examin.report").object(fileName).entity(inputStream.available(), inputStream).put();
            if (StringUtil.isNotEmpty(uploadMD5)) {
                ossUrl = "/examin.report/" + fileName;
            } else {
                throw new BusinessException(BaseErrorCode.OSS_UPLOAD_ERROR);
            }
            inputStream.close();
            return ossUrl;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                throw new BusinessException(BaseErrorCode.OSS_UPLOAD_ERROR);
            }
        }
        return ossUrl;
    }

    public static void main(String[] args) {
        splitPDFFile(2);
        //splitPDFFile("/Users/<USER>/Documents/京东资料/1892428475416079874_211228_6690924034.pdf", 2,0);
        //splitPDFFile("/Users/<USER>/Documents/京东资料/1892428475416079874_211228_6690924034.pdf", "/Users/<USER>/Documents/京东资料/test.pdf", 2, 0);
    }

    /**
     * 截取pdfFile的第from页至第end页，组成一个新的文件名
     *
     * @param respdfFile 需要分割的PDF
     * @param from       起始页
     * @param end        结束页
     */
    public static void splitPDFFile(String respdfFile, int from, int end) {
        Document document = null;
        PdfCopy copy = null;
        try {
            File f = new File(respdfFile);
            FileInputStream fileInputStream = new FileInputStream(f);

            PdfReader reader = new PdfReader(fileInputStream);
            int n = reader.getNumberOfPages();
            if (end == 0) {
                end = n;
            }
            document = new Document(reader.getPageSize(1));
//            ByteArrayOutputStream newFileStream = new ByteArrayOutputStream();
//            copy = new PdfCopy(document, newFileStream);
//            document.open();
//            for(int j=from; j<=end; j++) {
//                document.newPage();
//                PdfImportedPage page = copy.getImportedPage(reader, j);
//                copy.addPage(page);
//            }
//            //ByteArrayInputStream inputStream = new ByteArrayInputStream(newFileStream.toByteArray());
//
//            FileOutputStream downloadFile = new FileOutputStream("/Users/<USER>/Documents/京东资料/test.pdf");
//            downloadFile.write(newFileStream.toByteArray());
//            downloadFile.close();
            ByteArrayOutputStream newFileStream = new ByteArrayOutputStream();
            copy = new PdfCopy(document, newFileStream);
            document.open();
            for (int j = from; j <= end; j++) {
                document.newPage();
                PdfImportedPage page = copy.getImportedPage(reader, j);
                copy.addPage(page);
            }

            FileOutputStream fileOutputStream = new FileOutputStream("/Users/<USER>/Documents/京东资料/test.pdf");
            fileOutputStream.write(newFileStream.toByteArray(), 0, newFileStream.size());

            document.close();

        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
    }

    public static void splitPDFFile(int from) {
        File f = new File("/Users/<USER>/Documents/京东资料/1892428475416079874_211228_6690924034.pdf");
        try {
            FileInputStream fileInputStream = new FileInputStream(f);
            PdfReader reader = new PdfReader(fileInputStream);
            int pages_number = reader.getNumberOfPages();
            if (pages_number > 0) {
                Document document = new Document(reader.getPageSizeWithRotation(1));
                ByteArrayOutputStream newFileStream = new ByteArrayOutputStream();
                PdfCopy copy = new PdfCopy(document, newFileStream);
                document.open();
                for (int i = from; i <= pages_number; i++) {
                    PdfImportedPage page = copy.getImportedPage(reader, i);
                    copy.addPage(page);
                }
                document.close();

                FileOutputStream fileOutputStream = new FileOutputStream("/Users/<USER>/Documents/京东资料/test.pdf");
                fileOutputStream.write(newFileStream.toByteArray(), 0, newFileStream.size());
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }


    }

    /**
     * 截取pdfFile的第from页至第end页，组成一个新的文件名
     *
     * @param respdfFile 需要分割的PDF
     * @param savepath   新PDF
     * @param from       起始页
     * @param end        结束页
     */
    public static void splitPDFFile(String respdfFile,
                                    String savepath, int from, int end) {
        Document document = null;
        PdfCopy copy = null;
        try {
            PdfReader reader = new PdfReader(respdfFile);
            int n = reader.getNumberOfPages();
            if (end == 0) {
                end = n;
            }
            ArrayList<String> savepaths = new ArrayList<String>();
            String staticpath = respdfFile.substring(0, respdfFile.lastIndexOf("\\") + 1);
            //String savepath = staticpath+ newFile;
            savepaths.add(savepath);
            document = new Document(reader.getPageSize(1));
            copy = new PdfCopy(document, new FileOutputStream(savepaths.get(0)));
            document.open();
            for (int j = from; j <= end; j++) {
                document.newPage();
                PdfImportedPage page = copy.getImportedPage(reader, j);
                copy.addPage(page);
            }
            document.close();

        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
    }
}
