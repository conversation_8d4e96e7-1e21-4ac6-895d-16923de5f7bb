package com.jd.health.job;

import com.jd.health.medical.examination.service.enterprise.EnterpriseInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 *
 * <AUTHOR>
 * @date 2020-01-13 11:06
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath:spring-config.xml"})
public class EnterpriseInfoTest {


    /**
     * 客户信息service test
     */
    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    /**
     * 批量更新服务状态的测试
     */
    @Test
    public void batchUpdateServiceStatusTest() {
        /**
         * 先服后单，开始日期 > 当前日期，未开始 -> 服务中
         * 先单后服，开始日期 > 当前日期，已关联订单，未开始 -> 服务中
         */
        Integer batchUpdateResult = enterpriseInfoService.batchUpdateServiceStatus();
        System.out.println(batchUpdateResult);
    }

}
