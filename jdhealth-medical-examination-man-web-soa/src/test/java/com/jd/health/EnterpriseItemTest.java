package com.jd.health;

import com.jd.health.medical.examination.common.enums.SkuGroupItemDetailEnum;
import com.jd.health.medical.examination.common.enums.SkuGroupItemDetailQueryDiffEnum;
import com.jd.health.medical.examination.export.dto.SkuGroupItemDetailDTO;
import com.jd.health.medical.examination.export.param.SkuGroupItemDetailParam;
import com.jd.health.medical.examination.export.param.SkuGroupItemDetailUnitParam;
import com.jd.health.medical.examination.export.service.ItemExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企销套餐体检项目测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-06-15 14:04:24
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class EnterpriseItemTest {
    
    @Autowired
    private ItemExportService itemExportService;
    
    @Test
    public void getSkuGroupItemDetailByGroupNoTest() {
        
        
        List<SkuGroupItemDetailUnitParam> list = new ArrayList<>();
        SkuGroupItemDetailUnitParam param1 = new SkuGroupItemDetailUnitParam();
        param1.setSkuNo("100001173543");
        param1.setSkuType(SkuGroupItemDetailEnum.NORMAL.getType());
        list.add(param1);
        SkuGroupItemDetailUnitParam param2 = new SkuGroupItemDetailUnitParam();
        param2.setSkuNo("123456");
        param2.setSkuType(SkuGroupItemDetailEnum.UPGRADE.getType());
        list.add(param2);
        SkuGroupItemDetailParam skuGroupItemDetailParam = new SkuGroupItemDetailParam();
        skuGroupItemDetailParam.setQueryDiff(SkuGroupItemDetailQueryDiffEnum.NORMAL.getType());
        skuGroupItemDetailParam.setSkuParamList(list);
        skuGroupItemDetailParam.setCompanyNo(9140762114L);
        JsfResult<SkuGroupItemDetailDTO> result = itemExportService.getSkuGroupItemDetailByGroupNo(skuGroupItemDetailParam);
        System.out.println(result);
    }
    
    @Test
    public void mapTest() {
        Map<String, String> paramMap = new HashMap<>();
        List<SkuGroupItemDetailUnitParam> list = new ArrayList<>();
        SkuGroupItemDetailUnitParam param1 = new SkuGroupItemDetailUnitParam();
        param1.setSkuNo("100006147293");
        param1.setSkuType(SkuGroupItemDetailEnum.NORMAL.getType());
        list.add(param1);
        SkuGroupItemDetailUnitParam param2 = new SkuGroupItemDetailUnitParam();
        param2.setSkuNo("100000825813");
        param2.setSkuType(SkuGroupItemDetailEnum.UPGRADE.getType());
        list.add(param2);
        
        paramMap.put("queryDiff", "0");
        paramMap.put("skuParamList", JsonUtil.toJSONString(list));
        paramMap.put("companyNo", "22160539");
        System.out.println(JsonUtil.toJSONString(paramMap));
        System.out.println("============================");
    }
}
