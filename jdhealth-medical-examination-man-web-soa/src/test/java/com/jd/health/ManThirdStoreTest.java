package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.jd.health.medical.examination.export.dto.ThirdStoreDTO;
import com.jd.health.medical.examination.export.param.ManThirdStoreParam;
import com.jd.health.medical.examination.export.param.PageParam;
import com.jd.health.medical.examination.export.service.ThirdStoreManExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/21 20:00
 */
public class ManThirdStoreTest extends BaseTest {
    @Autowired
    private ThirdStoreManExportService thirdStoreManExportService;
    @Test
    public void query(){
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(10);
        ManThirdStoreParam manThirdStoreParam = new ManThirdStoreParam();
        manThirdStoreParam.setChannelNo(6621685943L);
        JsfResult<PageInfo<ThirdStoreDTO>> result = thirdStoreManExportService.queryPageThirdStore(manThirdStoreParam, pageParam);
        System.out.println(JsonUtil.toJSONString(result));
    }
    @Test
    public void update(){
        ManThirdStoreParam manThirdStoreParam = new ManThirdStoreParam();
        manThirdStoreParam.setChannelNo(6621685943L);
        manThirdStoreParam.setStoreId("123456");
        manThirdStoreParam.setSupportWriteOff(1);
        JsfResult<Boolean> result = thirdStoreManExportService.updateSupportWriteOff(manThirdStoreParam);
        System.out.println(JsonUtil.toJSONString(result));
    }
}
