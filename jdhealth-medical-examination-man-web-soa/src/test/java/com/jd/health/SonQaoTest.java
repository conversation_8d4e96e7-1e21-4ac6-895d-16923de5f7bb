package com.jd.health;

import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.enums.ProviderTypeEnum;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdAppointmentParam;
import com.jd.health.medical.examination.export.param.ThirdReportParam;
import com.jd.health.medical.examination.export.service.AppointmentApiExportService;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.health.medical.examination.service.AppointmentApiService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @ClassName SonQaoTest
 * @Description
 * <AUTHOR>
 * @Date 2020/9/3 14:31
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class SonQaoTest {

	private static final Logger log = LoggerFactory.getLogger(AppointmentInfoTest.class);

	/**
	 * 预约信息exportService
	 */
	@Autowired
	private AppointmentApiExportService appointmentApiExportService;

	/**
	 * appointmentApiService
	 */
	@Autowired
	private AppointmentApiService appointmentApiService;

	/**
	 * thirdDataExportService
	 */
	@Autowired
	private ThirdDataExportService thirdDataExportService;

	@Test
	public void findAppointDateTest(){
		AppointmentStoreCapParam appoint = new AppointmentStoreCapParam();
		appoint.setStoreId("0316002");
		appoint.setGoodsId("03160021907300012");
		appoint.setChannelNo(3632970185L);
		JsfResult<List<AppointDateDTO>> result = appointmentApiExportService.findAppointmentDate(appoint);
		System.out.println(JsonUtil.toJSONString(result));
	}

	@Test
	public void appointmentTest(){
		AppointmentParam param = new AppointmentParam();
		param.setJdAppointmentId(77777L);
		param.setAppointmentDate("2020-09-22");
		param.setCredentialType(1);
		param.setCredentialNo("110203199207070058");
		param.setUserBirth("1992-07-07");
		param.setStoreId("0316002");
		param.setGoodsId("20090200118");
		param.setUserGender(GenderEnum.MAN.getType());
		param.setUserMarriage(MarryTypeEnum.NO.getType());
		param.setUserName("京东测试D");
		param.setUserPhone("18842626795");
		param.setChannelType(3632970185L);
		appointmentApiExportService.appointment(param);
	}

	/**
	 * 修改预约状态的测试类
	 */
	@Test
	public void modifyAppointmentTest() {
		AppointmentParam appointmentParam = new AppointmentParam();
		appointmentParam.setChannelType(3632970185L);
		appointmentParam.setJdAppointmentId(77777L);
		appointmentParam.setAppointmentNo("010004");
		appointmentParam.setAppointmentDate("2020-09-11");
		appointmentParam.setStoreId("0316002");
		appointmentParam.setUserPin("hezhensheng3");
		JsfResult<Boolean> result = null;
		try {
			result = appointmentApiExportService.modifyAppointment(appointmentParam);
			System.out.println(JsonUtil.toJSONString(result));
		} catch (Exception e) {
			System.out.println(1);
		}
	}

	/**
	 * 取消预约状态的测试类
	 */
	@Test
	public void cancelAppointmentTest() {
		AppointmentParam appointmentParam = new AppointmentParam();
		appointmentParam.setChannelType(3632970185L);
		appointmentParam.setJdAppointmentId(3333L);
		appointmentParam.setAppointmentNo("010006");
		appointmentParam.setUserPin("hezhensheng3");
		JsfResult<Boolean> result = null;
		try {
			result = appointmentApiExportService.cancelAppointment(appointmentParam);
			System.out.println(JsonUtil.toJSONString(result));
		} catch (Exception e) {
			System.out.println(1);
		}
	}

	@Test
	public void handleReport() {
		ThirdAppointmentParam appointmentParam = new ThirdAppointmentParam();
		appointmentParam.setChannelType(3632970185L);
		appointmentParam.setJdAppointmentId(6666L);
		appointmentParam.setReportId("010009");
		appointmentParam.setAppointmentNo("010009");
		appointmentApiService.handleReport(appointmentParam);
	}

	@Test
	public void pushReport() {
		Long templateId = 1528561500388591106L;
		for (;templateId < 1528561500388591206L;) {
			templateId ++;
			final Long jdAppointmentId = templateId;
			CompletableFuture.runAsync(() -> pushTest(jdAppointmentId));
		}

	}

	private void pushTest(Long jdAppointmentId) {
		ThirdReportParam thirdReportParam = new ThirdReportParam();
		thirdReportParam.setAppointmentNo("123456");
		thirdReportParam.setChannelType(ProviderTypeEnum.RICH.getTypeNo());
		thirdReportParam.setJdAppointmentId(jdAppointmentId);
		//		thirdReportParam.setReportStr();
		thirdReportParam.setStructReportStr("{\"goodsId\":\"TC201705041627340001\",\"goodsName\":\"入职体检\",\"goodsPrice\":300.0,\"channelType\":123,\"operateType\":1,\"status\":1,\"goodsFeature\":\"\",\n"
				+ "\"goodsItemList\":[\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"身高体重\",\"itemMeans\":\"身高 、体重、血压基本健康状态参数，了解体重是否正常，有无体重不足、超重或肥胖，为相关科室的诊断提供依据。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"\",\"itemSecCategory\":\"身高、体重\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"眼科检查\",\"itemMeans\":\"主要检查有无结膜炎、角膜炎、屈光不正、测视力、色觉等。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"\",\"itemSecCategory\":\"视力、外眼、辨色力\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"肝胆脾胰彩超\",\"itemMeans\":\"更清晰的检查肝脏、胆囊、胆管、脾脏、胰腺等是否异常病变发生。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"\",\"itemSecCategory\":\"肝胆脾胰彩超\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"胸部正位片\",\"itemMeans\":\"有无肺部疾病及心脏、主动脉、纵膈、横膈疾病等。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"\",\"itemSecCategory\":\"胸部正位片\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"丙氨酸氨基转移酶(ALT)\",\"itemMeans\":\"主要了解到肝脏是否受到损害。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"实验室检查\",\"itemSecCategory\":\"丙氨酸氨基转移酶(ALT)\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"天门冬氨酸氨基转移酶(AST)\",\"itemMeans\":\"可检测急、慢性病毒性肝炎、酒精性肝病、肝癌、肝硬化、脂肪肝、心肌梗死等。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"实验室检查\",\"itemSecCategory\":\"天门冬氨酸氨基转移酶(AST)\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"血压\",\"itemMeans\":\"可以清楚自己的血压情况，对起居，饮食，用药，保健等等都有重要的指导作用。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"医技检查\",\"itemSecCategory\":\"血压（BP）\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"内科\",\"itemMeans\":\"心肺有无异常，肝脾有无肿大、腹部有无包块等\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"医技检查\",\"itemSecCategory\":\"心、肺听诊，腹部触诊\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"尿常规\",\"itemMeans\":\"可提示有无泌尿系统疾患：如急、慢性肾炎，肾盂肾炎，膀胱炎，尿道炎，肾病综合征，狼疮性肾炎，血红蛋白尿，肾梗塞、肾小管重金属盐及药物导致急性肾小管坏死，肾或膀胱肿瘤以及有无尿糖等\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"医技检查\",\"itemSecCategory\":\"颜色、比重、酸碱度、尿糖、隐血、尿胆素,尿胆原、胆红素、尿蛋白、亚硝酸盐、镜检\"},\n"
				+ "{\"goodsId\":\"TC201705041627340001\",\"itemName\":\"血常规\",\"itemMeans\":\"通过检测血液细胞的计数及不同种类细胞、成分的分类来反映身体状况，如：贫血、感染、血液系统疾病、物理化学因素损伤等。\",\"itemSuitable\":[\"1\",\"2\",\"3\"],\"itemTopCategory\":\"医技检查\",\"itemSecCategory\":\"检查白细胞、红细胞、血小板\"}\n"
				+ "]}");
		JsfResult result = thirdDataExportService.pushReport(thirdReportParam);
		System.out.println(JsonUtil.toJSONString(result));
	}
}
