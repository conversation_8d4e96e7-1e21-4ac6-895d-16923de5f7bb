package com.jd.health;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jd.health.medical.examination.common.enums.SkuStoreSearchTypeEnum;
import com.jd.health.medical.examination.export.dto.GoodsStoreDTO;
import com.jd.health.medical.examination.export.dto.StoreInfoDTO;
import com.jd.health.medical.examination.export.param.AppointStoreParam;
import com.jd.health.medical.examination.export.param.SkuStoreParam;
import com.jd.health.medical.examination.export.service.SkuStoreExportService;
import com.jd.health.medical.examination.service.bo.SkuStoreBO;
import com.jd.health.medical.examination.service.selfpersonal.SkuStoreApiService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/5 10:57
 */
public class SkuStoreTest extends BaseTest{
    @Autowired
    private SkuStoreExportService skuStoreExportService;

    @Autowired
    private SkuStoreApiService  skuStoreApiService;
    @Test
    public void skuStoreListTest(){
        Map<String,String> map = new HashMap<>();
        map.put("pageNum","1");
        map.put("pageSize","1");
        map.put("skuNo","132423");
        map.put("provinceId","1");
        JsfResult<PageInfo<GoodsStoreDTO>> result = skuStoreExportService.querySkuStoreList(map);
        System.out.println(JsonUtil.toJSONString(result));
    }
    @Test
    public void queryStoreListBySkuNoAndAddrTest(){
        SkuStoreParam param = new SkuStoreParam();
        param.setSkuNo("132423");
        JsfResult<List<StoreInfoDTO>> result = skuStoreExportService.queryAddressListBySkuNoAndAddr(param);
        System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"+JsonUtil.toJSONString(result));
    }
    @Test
    public  void testQueryAddressListBySkuNoAndAddr(){
        SkuStoreBO skuStoreBO=new SkuStoreBO();
        List<StoreInfoDTO> list=skuStoreApiService.queryAddressListBySkuNoAndAddr(skuStoreBO);
        System.out.println("-----------------"+JsonUtil.toJSONString(list));

    }

    @Test
    public void querySkuStoreListFromES() {
        AppointStoreParam appointStoreParam = new AppointStoreParam();
        appointStoreParam.setSkuNo("100001173543");
        appointStoreParam.setPageNum(1);
        appointStoreParam.setPageSize(10);
        appointStoreParam.setSearchType(SkuStoreSearchTypeEnum.DEFAULT.getType());
        appointStoreParam.setAdditionSkuList(Lists.newArrayList("111222444"));
        JsfResult<PageInfo<GoodsStoreDTO>> result = skuStoreExportService.queryAppointStoreList(appointStoreParam);
        System.out.println("-----------------" + JsonUtil.toJSONString(result));
    }

    /**
     * 查询门店结算价
     */
    @Test
    public void querySkuStorePrice(){
        SkuStoreParam skuStoreParam = new SkuStoreParam();
        skuStoreParam.setStoreId("49");
        skuStoreParam.setGoodsId("JDQX000888820200222.1.003");
        skuStoreParam.setChannelNo(5879687598L);
        skuStoreParam.setSkuNo("122");
        JsfResult<Integer> integerJsfResult = skuStoreExportService.querySkuStorePrice(skuStoreParam);
        System.out.println(JsonUtil.toJSONString(integerJsfResult));
    }
}
