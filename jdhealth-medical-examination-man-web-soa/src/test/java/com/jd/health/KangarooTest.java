package com.jd.health;

import com.alibaba.fastjson.JSONObject;
import com.jd.health.medical.examination.common.enums.GenderEnum;
import com.jd.health.medical.examination.common.enums.MarryTypeEnum;
import com.jd.health.medical.examination.common.util.HttpSimpleClient;
import com.jd.health.medical.examination.export.dto.AppointDateDTO;
import com.jd.health.medical.examination.export.param.AppointmentParam;
import com.jd.health.medical.examination.export.param.AppointmentStoreCapParam;
import com.jd.health.medical.examination.export.param.ThirdAppointmentParam;
import com.jd.health.medical.examination.export.service.AppointmentApiExportService;
import com.jd.health.medical.examination.export.service.ThirdDataExportService;
import com.jd.health.medical.examination.service.AppointmentApiService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * @ClassName KangarooTest
 * @Description
 * <AUTHOR>
 * @Date 2020/11/23 10:06
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class KangarooTest {
	private static final Logger log = LoggerFactory.getLogger(KangarooTest.class);

	/**
	 * 预约信息exportService
	 */
	@Autowired
	private AppointmentApiExportService appointmentApiExportService;

	/**
	 * appointmentApiService
	 */
	@Autowired
	private AppointmentApiService appointmentApiService;

	/**
	 * thirdDataExportService
	 */
	@Autowired
	private ThirdDataExportService thirdDataExportService;

	@Test
	public void findAppointDateTest(){
		AppointmentStoreCapParam appoint = new AppointmentStoreCapParam();
		appoint.setStoreId("411");
		appoint.setGoodsId("100891");
		appoint.setChannelNo(9787771002L);
		JsfResult<List<AppointDateDTO>> result = appointmentApiExportService.findAppointmentDate(appoint);
		System.out.println(JsonUtil.toJSONString(result));
	}

	@Test
	public void appointmentTest(){
		AppointmentParam param = new AppointmentParam();
		param.setJdAppointmentId(1599059153714686466L);
		param.setAppointmentDate("2020-12-24");
		param.setCredentialType(1);
		param.setCredentialNo("110203199207070058");
		param.setUserBirth("1992-07-07");
		param.setStoreId("411");
		param.setGoodsId("100891");
		param.setUserGender(GenderEnum.MAN.getType());
		param.setUserMarriage(MarryTypeEnum.YES.getType());
		param.setUserName("京东测试A");
		param.setUserPhone("18842626795");
		param.setChannelType(9787771002L);
		JsfResult<Boolean> appointment = appointmentApiExportService.appointment(param);
		System.out.println(JsonUtil.toJSONString(appointment));
	}

	/**
	 * 修改预约状态的测试类
	 */
	@Test
	public void modifyAppointmentTest() {
		AppointmentParam appointmentParam = new AppointmentParam();
		appointmentParam.setChannelType(9787771002L);
		appointmentParam.setJdAppointmentId(123457L);
		appointmentParam.setAppointmentNo("210252");
		appointmentParam.setAppointmentDate("2020-12-29");
		appointmentParam.setStoreId("411");
		appointmentParam.setUserPin("lvyifan3");
		JsfResult<Boolean> result = null;
		try {
			result = appointmentApiExportService.modifyAppointment(appointmentParam);
			System.out.println(JsonUtil.toJSONString(result));
		} catch (Exception e) {
			System.out.println(1);
		}
	}

	/**
	 * 取消预约状态的测试类
	 */
	@Test
	public void cancelAppointmentTest() {
		AppointmentParam appointmentParam = new AppointmentParam();
		appointmentParam.setChannelType(9787771002L);
		appointmentParam.setJdAppointmentId(1599192323470657026L);
		appointmentParam.setAppointmentNo("210304");
		appointmentParam.setUserPin("lvyifan3");
		JsfResult<Boolean> result = null;
		try {
			result = appointmentApiExportService.cancelAppointment(appointmentParam);
			System.out.println(JsonUtil.toJSONString(result));
		} catch (Exception e) {
			System.out.println(1);
		}
	}

	@Test
	public void handleReport() {
		ThirdAppointmentParam appointmentParam = new ThirdAppointmentParam();
		appointmentParam.setChannelType(9787771002L);
		appointmentParam.setJdAppointmentId(1599192323470657026L);
		appointmentParam.setReportId("167338");
		appointmentParam.setAppointmentNo("1267159");
		appointmentApiService.handleReport(appointmentParam);
	}

	public static void main(String[] args) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("appointmentNo", "1267159");
		jsonObject.put("jdAppointmentId", 1599192323470657026L);
		String result = HttpSimpleClient.simplePost("https://api.daishujiankang.com/jd/Appointment/queryAppointStatus", jsonObject.toJSONString());
		System.out.println(result);
	}
}
