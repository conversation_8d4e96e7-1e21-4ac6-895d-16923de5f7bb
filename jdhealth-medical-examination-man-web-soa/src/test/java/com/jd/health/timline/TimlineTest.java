package com.jd.health.timline;

import com.jd.health.BaseTest;
import com.jd.health.medical.examination.service.XfylStoreSignService;
import com.jd.health.medical.examination.service.agile.AgileUserService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 描述
 * TimlineTest TimlineTest
 * <AUTHOR>
 * @date 2022/5/14 15:02
 */
public class TimlineTest  extends BaseTest {
    
    @Autowired
    private AgileUserService agileUserService;

    @Autowired
    private XfylStoreSignService xfylStoreSignService;
    
    @Test
    public void timLineTest() {
        System.out.println("1111");
        agileUserService.sendWorkTimeToUser();
        System.out.println("1111");
    }

    @Test
    public void syncStoreCenterTest() {
        System.out.println("1111");
        xfylStoreSignService.syncXfylSignToStoreCenter();
        System.out.println("1111");
    }
}
