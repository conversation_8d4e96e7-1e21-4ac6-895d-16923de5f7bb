package com.jd.health;

import com.jd.health.medical.examination.common.config.PushDataDuccConfig;
import com.jd.health.medical.examination.service.enterprise.config.OperatorPinConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * 请输入类的描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/17 14:43
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath*:spring-config.xml"})
public class DuccTest {
    @Autowired
    private PushDataDuccConfig pushDataDuccConfig;
    @Autowired
    private OperatorPinConfig operatorPinConfig;
    @Test
    public void pushDataSwitchTest(){
        Boolean pushDataSwitch = pushDataDuccConfig.getPushDataSwitch();
        System.out.println(pushDataSwitch);
    }

    @Test
    public void getOperatorPinsTest(){
        String operatorPins = operatorPinConfig.getOperatorPins();
        System.out.println(operatorPins);
    }
}
