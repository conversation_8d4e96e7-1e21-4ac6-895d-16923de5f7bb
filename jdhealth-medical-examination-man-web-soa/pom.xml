<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.health.medical.examination</groupId>
        <artifactId>jdhealth-medical-examination-man</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>jdhealth-medical-examination-man-web-soa</artifactId>
    <packaging>war</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- 上帝之手 -->
        <dependency>
            <groupId>com.jd.pioneer</groupId>
            <artifactId>godhand-core</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.18</version>
        </dependency>

        <dependency>
            <groupId>xpp3</groupId>
            <artifactId>xpp3_min</artifactId>
            <version>1.1.4c</version>
        </dependency>

        <dependency>
            <groupId>com.jd.health.medical.examination</groupId>
            <artifactId>jdhealth-medical-examination-man-facade</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.21</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.1.8.RELEASE</version>
            <scope>test</scope>
        </dependency>

        <!-- jsf支持 callgraph-->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>home-job-spring</artifactId>
            <version>2.1.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.ump</groupId>
                    <artifactId>profiler</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jannotation</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--拆单mq解析包-->
        <dependency>
            <groupId>com.jd.ofc</groupId>
            <artifactId>splitMessageSerialization-pb</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alibaba/easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.7</version>
        </dependency>

        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>5.0.4</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.16.Final</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>devtest</id>
            <properties>
                <profiles.active>devtest</profiles.active>
                <medical.examination.log.level>INFO</medical.examination.log.level>
                <medical.examination.log.path>/export/Logs/medicine-examination.jd.local</medical.examination.log.path>
                <medical.examination.log.maxHistory>7</medical.examination.log.maxHistory>
                <medical.examination.log.totalSizeCap>512MB</medical.examination.log.totalSizeCap>
                <medical.examination.log.ConversionPattern>%d{"yyyy-MM-dd HH:mm:ss.SSS"} %-5level [%-20.20thread] [%class{0}:%L,%M\(\)] [%X{PFTID}] [%X{logid}] %m%n</medical.examination.log.ConversionPattern>
                <medical.examination.log.sql.print><![CDATA[<appender-ref ref="LOGGER-SQL"/>]]></medical.examination.log.sql.print>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>yfb</id>
            <properties>
                <profiles.active>yfb</profiles.active>
                <medical.examination.log.level>INFO</medical.examination.log.level>
                <medical.examination.log.path>/export/Logs/medicine-examination.jd.local</medical.examination.log.path>
                <medical.examination.log.maxHistory>7</medical.examination.log.maxHistory>
                <medical.examination.log.totalSizeCap>512MB</medical.examination.log.totalSizeCap>
                <medical.examination.log.ConversionPattern>%d{"yyyy-MM-dd HH:mm:ss.SSS"} %-5level [%-20.20thread] [%class{0}:%L,%M\(\)] [%X{PFTID}] [%X{logid}] %m%n</medical.examination.log.ConversionPattern>
                <medical.examination.log.sql.print><![CDATA[<appender-ref ref="LOGGER-SQL"/>]]></medical.examination.log.sql.print>
            </properties>
        </profile>
        <profile>
            <id>yfbcheck</id>
            <properties>
                <profiles.active>yfbcheck</profiles.active>
                <medical.examination.log.level>DEBUG</medical.examination.log.level>
                <medical.examination.log.path>/export/Logs/medicine-examination.jd.local</medical.examination.log.path>
                <medical.examination.log.maxHistory>7</medical.examination.log.maxHistory>
                <medical.examination.log.totalSizeCap>512MB</medical.examination.log.totalSizeCap>
                <medical.examination.log.ConversionPattern>%d{"yyyy-MM-dd HH:mm:ss.SSS"} %-5level [%-20.20thread] [%class{0}:%L,%M\(\)] [%X{PFTID}] [%X{logid}] %m%n</medical.examination.log.ConversionPattern>
                <medical.examination.log.sql.print><![CDATA[<appender-ref ref="LOGGER-SQL"/>]]></medical.examination.log.sql.print>
            </properties>
        </profile>

        <profile>
            <id>production</id>
            <properties>
                <profiles.active>production</profiles.active>
                <medical.examination.log.level>INFO</medical.examination.log.level>
                <medical.examination.log.path>/export/Logs/medicine-examination.jd.local</medical.examination.log.path>
                <medical.examination.log.maxHistory>7</medical.examination.log.maxHistory>
                <medical.examination.log.totalSizeCap>512MB</medical.examination.log.totalSizeCap>
                <medical.examination.log.ConversionPattern>%d{"yyyy-MM-dd HH:mm:ss.SSS"} %-5level [%-20.20thread] [%class{0}:%L,%M\(\)] [%X{PFTID}] [%X{logid}] %m%n</medical.examination.log.ConversionPattern>
                <medical.examination.log.sql.print><![CDATA[]]></medical.examination.log.sql.print>
            </properties>
        </profile>

        <profile>
            <id>gray</id>
            <properties>
                <profiles.active>gray</profiles.active>
            </properties>
        </profile>
    </profiles>

</project>
